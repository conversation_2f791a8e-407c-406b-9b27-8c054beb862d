<?php

// autoload-classmap.php @generated by <PERSON>

$strauss_src = dirname(__FILE__);

return array(
   'RCP\StellarWP\ContainerContract\ContainerInterface' => $strauss_src . '/stellarwp/container-contract/src/ContainerInterface.php',
   'RCP\StellarWP\Telemetry\Opt_In\Opt_In_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Subscriber.php',
   'RCP\StellarWP\Telemetry\Opt_In\Opt_In_Template' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Template.php',
   'RCP\StellarWP\Telemetry\Opt_In\Status' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Opt_In/Status.php',
   'RCP\StellarWP\Telemetry\Core' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Core.php',
   'RCP\StellarWP\Telemetry\Events\Event' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Events/Event.php',
   'RCP\StellarWP\Telemetry\Events\Event_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Events/Event_Subscriber.php',
   'RCP\StellarWP\Telemetry\Uninstall' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Uninstall.php',
   'RCP\StellarWP\Telemetry\Telemetry\Telemetry_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry_Subscriber.php',
   'RCP\StellarWP\Telemetry\Telemetry\Telemetry' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry.php',
   'RCP\StellarWP\Telemetry\Config' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Config.php',
   'RCP\StellarWP\Telemetry\Last_Send\Last_Send' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send.php',
   'RCP\StellarWP\Telemetry\Last_Send\Last_Send_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send_Subscriber.php',
   'RCP\StellarWP\Telemetry\Exit_Interview\Template' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Template.php',
   'RCP\StellarWP\Telemetry\Exit_Interview\Exit_Interview_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Exit_Interview_Subscriber.php',
   'RCP\StellarWP\Telemetry\Contracts\Abstract_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Contracts/Abstract_Subscriber.php',
   'RCP\StellarWP\Telemetry\Contracts\Runnable' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Contracts/Runnable.php',
   'RCP\StellarWP\Telemetry\Contracts\Data_Provider' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Contracts/Data_Provider.php',
   'RCP\StellarWP\Telemetry\Contracts\Template_Interface' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Contracts/Template_Interface.php',
   'RCP\StellarWP\Telemetry\Contracts\Subscriber_Interface' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Contracts/Subscriber_Interface.php',
   'RCP\StellarWP\Telemetry\Data_Providers\Debug_Data' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Debug_Data.php',
   'RCP\StellarWP\Telemetry\Data_Providers\Null_Data_Provider' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Null_Data_Provider.php',
   'RCP\StellarWP\Telemetry\Admin\Resources' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Admin/Resources.php',
   'RCP\StellarWP\Telemetry\Admin\Admin_Subscriber' => $strauss_src . '/stellarwp/telemetry/src/Telemetry/Admin/Admin_Subscriber.php',
   'RCP\lucatume\DI52\Container' => $strauss_src . '/lucatume/di52/src/Container.php',
   'RCP\lucatume\DI52\ServiceProvider' => $strauss_src . '/lucatume/di52/src/ServiceProvider.php',
   'RCP\lucatume\DI52\ContainerException' => $strauss_src . '/lucatume/di52/src/ContainerException.php',
   'RCP\lucatume\DI52\NotFoundException' => $strauss_src . '/lucatume/di52/src/NotFoundException.php',
   'RCP\lucatume\DI52\App' => $strauss_src . '/lucatume/di52/src/App.php',
   'RCP\lucatume\DI52\Builders\Factory' => $strauss_src . '/lucatume/di52/src/Builders/Factory.php',
   'RCP\lucatume\DI52\Builders\CallableBuilder' => $strauss_src . '/lucatume/di52/src/Builders/CallableBuilder.php',
   'RCP\lucatume\DI52\Builders\Resolver' => $strauss_src . '/lucatume/di52/src/Builders/Resolver.php',
   'RCP\lucatume\DI52\Builders\ClosureBuilder' => $strauss_src . '/lucatume/di52/src/Builders/ClosureBuilder.php',
   'RCP\lucatume\DI52\Builders\Parameter' => $strauss_src . '/lucatume/di52/src/Builders/Parameter.php',
   'RCP\lucatume\DI52\Builders\ClassBuilder' => $strauss_src . '/lucatume/di52/src/Builders/ClassBuilder.php',
   'RCP\lucatume\DI52\Builders\BuilderInterface' => $strauss_src . '/lucatume/di52/src/Builders/BuilderInterface.php',
   'RCP\lucatume\DI52\Builders\ReinitializableBuilderInterface' => $strauss_src . '/lucatume/di52/src/Builders/ReinitializableBuilderInterface.php',
   'RCP\lucatume\DI52\Builders\ValueBuilder' => $strauss_src . '/lucatume/di52/src/Builders/ValueBuilder.php',
   'RCP\lucatume\DI52\NestedParseError' => $strauss_src . '/lucatume/di52/src/NestedParseError.php',
   'RCP\Psr\Container\NotFoundExceptionInterface' => $strauss_src . '/psr/container/src/NotFoundExceptionInterface.php',
   'RCP\Psr\Container\ContainerExceptionInterface' => $strauss_src . '/psr/container/src/ContainerExceptionInterface.php',
   'RCP\Psr\Container\ContainerInterface' => $strauss_src . '/psr/container/src/ContainerInterface.php',
);