<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitedf38dd4f5b6a0a0ba1d926bd3ec515e
{
    public static $prefixLengthsPsr4 = array (
        'l' => 
        array (
            'lucatume\\DI52\\' => 14,
        ),
        'S' => 
        array (
            'StellarWP\\Telemetry\\Views_Dir\\' => 30,
            'StellarWP\\Telemetry\\Assets_Dir\\' => 31,
            'StellarWP\\Telemetry\\' => 20,
            'StellarWP\\ContainerContract\\' => 28,
        ),
        'R' => 
        array (
            'RCP\\' => 4,
        ),
        'P' => 
        array (
            'Psr\\Container\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'lucatume\\DI52\\' => 
        array (
            0 => __DIR__ . '/..' . '/lucatume/di52/src',
        ),
        'StellarWP\\Telemetry\\Views_Dir\\' => 
        array (
            0 => __DIR__ . '/..' . '/stellarwp/telemetry/src/views',
        ),
        'StellarWP\\Telemetry\\Assets_Dir\\' => 
        array (
            0 => __DIR__ . '/..' . '/stellarwp/telemetry/src/resources',
        ),
        'StellarWP\\Telemetry\\' => 
        array (
            0 => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry',
        ),
        'StellarWP\\ContainerContract\\' => 
        array (
            0 => __DIR__ . '/..' . '/stellarwp/container-contract/src',
        ),
        'RCP\\' => 
        array (
            0 => __DIR__ . '/../..' . '/core/includes',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerExceptionInterface.php',
        'Psr\\Container\\ContainerInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerInterface.php',
        'Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/NotFoundExceptionInterface.php',
        'RCP\\Container' => __DIR__ . '/../..' . '/core/includes/Container.php',
        'StellarWP\\ContainerContract\\ContainerInterface' => __DIR__ . '/..' . '/stellarwp/container-contract/src/ContainerInterface.php',
        'StellarWP\\Telemetry\\Admin\\Admin_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Admin/Admin_Subscriber.php',
        'StellarWP\\Telemetry\\Admin\\Resources' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Admin/Resources.php',
        'StellarWP\\Telemetry\\Config' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Config.php',
        'StellarWP\\Telemetry\\Contracts\\Abstract_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Contracts/Abstract_Subscriber.php',
        'StellarWP\\Telemetry\\Contracts\\Data_Provider' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Contracts/Data_Provider.php',
        'StellarWP\\Telemetry\\Contracts\\Runnable' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Contracts/Runnable.php',
        'StellarWP\\Telemetry\\Contracts\\Subscriber_Interface' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Contracts/Subscriber_Interface.php',
        'StellarWP\\Telemetry\\Contracts\\Template_Interface' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Contracts/Template_Interface.php',
        'StellarWP\\Telemetry\\Core' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Core.php',
        'StellarWP\\Telemetry\\Data_Providers\\Debug_Data' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Debug_Data.php',
        'StellarWP\\Telemetry\\Data_Providers\\Null_Data_Provider' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Null_Data_Provider.php',
        'StellarWP\\Telemetry\\Events\\Event' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Events/Event.php',
        'StellarWP\\Telemetry\\Events\\Event_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Events/Event_Subscriber.php',
        'StellarWP\\Telemetry\\Exit_Interview\\Exit_Interview_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Exit_Interview_Subscriber.php',
        'StellarWP\\Telemetry\\Exit_Interview\\Template' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Template.php',
        'StellarWP\\Telemetry\\Last_Send\\Last_Send' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send.php',
        'StellarWP\\Telemetry\\Last_Send\\Last_Send_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send_Subscriber.php',
        'StellarWP\\Telemetry\\Opt_In\\Opt_In_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Subscriber.php',
        'StellarWP\\Telemetry\\Opt_In\\Opt_In_Template' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Template.php',
        'StellarWP\\Telemetry\\Opt_In\\Status' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Opt_In/Status.php',
        'StellarWP\\Telemetry\\Telemetry\\Telemetry' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry.php',
        'StellarWP\\Telemetry\\Telemetry\\Telemetry_Subscriber' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry_Subscriber.php',
        'StellarWP\\Telemetry\\Uninstall' => __DIR__ . '/..' . '/stellarwp/telemetry/src/Telemetry/Uninstall.php',
        'lucatume\\DI52\\App' => __DIR__ . '/..' . '/lucatume/di52/src/App.php',
        'lucatume\\DI52\\Builders\\BuilderInterface' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/BuilderInterface.php',
        'lucatume\\DI52\\Builders\\CallableBuilder' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/CallableBuilder.php',
        'lucatume\\DI52\\Builders\\ClassBuilder' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/ClassBuilder.php',
        'lucatume\\DI52\\Builders\\ClosureBuilder' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/ClosureBuilder.php',
        'lucatume\\DI52\\Builders\\Factory' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/Factory.php',
        'lucatume\\DI52\\Builders\\Parameter' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/Parameter.php',
        'lucatume\\DI52\\Builders\\ReinitializableBuilderInterface' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/ReinitializableBuilderInterface.php',
        'lucatume\\DI52\\Builders\\Resolver' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/Resolver.php',
        'lucatume\\DI52\\Builders\\ValueBuilder' => __DIR__ . '/..' . '/lucatume/di52/src/Builders/ValueBuilder.php',
        'lucatume\\DI52\\Container' => __DIR__ . '/..' . '/lucatume/di52/src/Container.php',
        'lucatume\\DI52\\ContainerException' => __DIR__ . '/..' . '/lucatume/di52/src/ContainerException.php',
        'lucatume\\DI52\\NestedParseError' => __DIR__ . '/..' . '/lucatume/di52/src/NestedParseError.php',
        'lucatume\\DI52\\NotFoundException' => __DIR__ . '/..' . '/lucatume/di52/src/NotFoundException.php',
        'lucatume\\DI52\\ServiceProvider' => __DIR__ . '/..' . '/lucatume/di52/src/ServiceProvider.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitedf38dd4f5b6a0a0ba1d926bd3ec515e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitedf38dd4f5b6a0a0ba1d926bd3ec515e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitedf38dd4f5b6a0a0ba1d926bd3ec515e::$classMap;

        }, null, ClassLoader::class);
    }
}
