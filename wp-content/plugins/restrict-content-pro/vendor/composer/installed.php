<?php return array(
    'root' => array(
        'name' => 'restrictcontentpro/restrict-content-pro',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'dfb6704b312de78cd615abd799931010b77606ba',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'lucatume/di52' => array(
            'pretty_version' => '3.3.4',
            'version' => '3.3.4.0',
            'reference' => 'a86c6d38bb932bb70995143ca47212af73fc79da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lucatume/di52',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'restrictcontentpro/restrict-content-pro' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'dfb6704b312de78cd615abd799931010b77606ba',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stellarwp/container-contract' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'b1568be99b4d2d38446895d31a1c0f3718bf4d36',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stellarwp/container-contract',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stellarwp/telemetry' => array(
            'pretty_version' => '2.3.4',
            'version' => '2.3.4.0',
            'reference' => '74c7b819d574aa5fc1392f982fb32cedc18d4c6f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stellarwp/telemetry',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
