<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInitedf38dd4f5b6a0a0ba1d926bd3ec515e
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInitedf38dd4f5b6a0a0ba1d926bd3ec515e', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInitedf38dd4f5b6a0a0ba1d926bd3ec515e', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInitedf38dd4f5b6a0a0ba1d926bd3ec515e::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
