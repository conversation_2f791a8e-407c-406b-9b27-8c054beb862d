<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'RCP\\Container' => $baseDir . '/core/includes/Container.php',
    'StellarWP\\ContainerContract\\ContainerInterface' => $vendorDir . '/stellarwp/container-contract/src/ContainerInterface.php',
    'StellarWP\\Telemetry\\Admin\\Admin_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Admin/Admin_Subscriber.php',
    'StellarWP\\Telemetry\\Admin\\Resources' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Admin/Resources.php',
    'StellarWP\\Telemetry\\Config' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Config.php',
    'StellarWP\\Telemetry\\Contracts\\Abstract_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Contracts/Abstract_Subscriber.php',
    'StellarWP\\Telemetry\\Contracts\\Data_Provider' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Contracts/Data_Provider.php',
    'StellarWP\\Telemetry\\Contracts\\Runnable' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Contracts/Runnable.php',
    'StellarWP\\Telemetry\\Contracts\\Subscriber_Interface' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Contracts/Subscriber_Interface.php',
    'StellarWP\\Telemetry\\Contracts\\Template_Interface' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Contracts/Template_Interface.php',
    'StellarWP\\Telemetry\\Core' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Core.php',
    'StellarWP\\Telemetry\\Data_Providers\\Debug_Data' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Debug_Data.php',
    'StellarWP\\Telemetry\\Data_Providers\\Null_Data_Provider' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Data_Providers/Null_Data_Provider.php',
    'StellarWP\\Telemetry\\Events\\Event' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Events/Event.php',
    'StellarWP\\Telemetry\\Events\\Event_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Events/Event_Subscriber.php',
    'StellarWP\\Telemetry\\Exit_Interview\\Exit_Interview_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Exit_Interview_Subscriber.php',
    'StellarWP\\Telemetry\\Exit_Interview\\Template' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Exit_Interview/Template.php',
    'StellarWP\\Telemetry\\Last_Send\\Last_Send' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send.php',
    'StellarWP\\Telemetry\\Last_Send\\Last_Send_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Last_Send/Last_Send_Subscriber.php',
    'StellarWP\\Telemetry\\Opt_In\\Opt_In_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Subscriber.php',
    'StellarWP\\Telemetry\\Opt_In\\Opt_In_Template' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Opt_In/Opt_In_Template.php',
    'StellarWP\\Telemetry\\Opt_In\\Status' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Opt_In/Status.php',
    'StellarWP\\Telemetry\\Telemetry\\Telemetry' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry.php',
    'StellarWP\\Telemetry\\Telemetry\\Telemetry_Subscriber' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Telemetry/Telemetry_Subscriber.php',
    'StellarWP\\Telemetry\\Uninstall' => $vendorDir . '/stellarwp/telemetry/src/Telemetry/Uninstall.php',
    'lucatume\\DI52\\App' => $vendorDir . '/lucatume/di52/src/App.php',
    'lucatume\\DI52\\Builders\\BuilderInterface' => $vendorDir . '/lucatume/di52/src/Builders/BuilderInterface.php',
    'lucatume\\DI52\\Builders\\CallableBuilder' => $vendorDir . '/lucatume/di52/src/Builders/CallableBuilder.php',
    'lucatume\\DI52\\Builders\\ClassBuilder' => $vendorDir . '/lucatume/di52/src/Builders/ClassBuilder.php',
    'lucatume\\DI52\\Builders\\ClosureBuilder' => $vendorDir . '/lucatume/di52/src/Builders/ClosureBuilder.php',
    'lucatume\\DI52\\Builders\\Factory' => $vendorDir . '/lucatume/di52/src/Builders/Factory.php',
    'lucatume\\DI52\\Builders\\Parameter' => $vendorDir . '/lucatume/di52/src/Builders/Parameter.php',
    'lucatume\\DI52\\Builders\\ReinitializableBuilderInterface' => $vendorDir . '/lucatume/di52/src/Builders/ReinitializableBuilderInterface.php',
    'lucatume\\DI52\\Builders\\Resolver' => $vendorDir . '/lucatume/di52/src/Builders/Resolver.php',
    'lucatume\\DI52\\Builders\\ValueBuilder' => $vendorDir . '/lucatume/di52/src/Builders/ValueBuilder.php',
    'lucatume\\DI52\\Container' => $vendorDir . '/lucatume/di52/src/Container.php',
    'lucatume\\DI52\\ContainerException' => $vendorDir . '/lucatume/di52/src/ContainerException.php',
    'lucatume\\DI52\\NestedParseError' => $vendorDir . '/lucatume/di52/src/NestedParseError.php',
    'lucatume\\DI52\\NotFoundException' => $vendorDir . '/lucatume/di52/src/NotFoundException.php',
    'lucatume\\DI52\\ServiceProvider' => $vendorDir . '/lucatume/di52/src/ServiceProvider.php',
);
