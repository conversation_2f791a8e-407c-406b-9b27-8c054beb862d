{"packages": [{"name": "lucatume/di52", "version": "3.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lucatume/di52.git", "reference": "a86c6d38bb932bb70995143ca47212af73fc79da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lucatume/di52/zipball/a86c6d38bb932bb70995143ca47212af73fc79da", "reference": "a86c6d38bb932bb70995143ca47212af73fc79da", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6", "psr/container": "^1.0"}, "require-dev": {"phpunit/phpunit": "<10.0"}, "time": "2023-06-20T06:50:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"lucatume\\DI52\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP 5.6 compatible dependency injection container.", "support": {"issues": "https://github.com/lucatume/di52/issues", "source": "https://github.com/lucatume/di52/tree/3.3.4"}, "install-path": "../lucatume/di52"}, {"name": "psr/container", "version": "1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:50:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "install-path": "../psr/container"}, {"name": "stellarwp/container-contract", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/stellarwp/container-contract", "reference": "b1568be99b4d2d38446895d31a1c0f3718bf4d36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stellarwp/container-contract/zipball/b1568be99b4d2d38446895d31a1c0f3718bf4d36", "reference": "b1568be99b4d2d38446895d31a1c0f3718bf4d36", "shasum": ""}, "require": {"php": ">=7.0.0"}, "time": "2023-02-13T21:24:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"StellarWP\\ContainerContract\\": "src/"}}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "StellarWP", "homepage": "https://stellarwp.com"}], "description": "StellarWP Container Interface", "homepage": "https://github.com/stellarwp/container-contract", "keywords": ["container", "container-interface", "container-interop", "psr-11"], "install-path": "../stellarwp/container-contract"}, {"name": "stellarwp/telemetry", "version": "2.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/stellarwp/telemetry.git", "reference": "74c7b819d574aa5fc1392f982fb32cedc18d4c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stellarwp/telemetry/zipball/74c7b819d574aa5fc1392f982fb32cedc18d4c6f", "reference": "74c7b819d574aa5fc1392f982fb32cedc18d4c6f", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1", "stellarwp/container-contract": "^1.0"}, "require-dev": {"automattic/vipwpcs": "^3.0.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.2", "lucatume/di52": "3.0.0", "lucatume/wp-browser": "^3.2.3", "phpcompatibility/phpcompatibility-wp": "*", "phpunit/php-code-coverage": "^9.2", "szepeviktor/phpstan-wordpress": "^1.1", "the-events-calendar/coding-standards": "dev-master", "wp-coding-standards/wpcs": "^3.0.0"}, "time": "2025-04-25T18:58:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"StellarWP\\Telemetry\\": "src/Telemetry", "StellarWP\\Telemetry\\Views_Dir\\": "src/views", "StellarWP\\Telemetry\\Assets_Dir\\": "src/resources"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Telemetry library for StellarWP plugins.", "support": {"issues": "https://github.com/stellarwp/telemetry/issues", "source": "https://github.com/stellarwp/telemetry/tree/2.3.4"}, "install-path": "../stellarwp/telemetry"}], "dev": false, "dev-package-names": []}