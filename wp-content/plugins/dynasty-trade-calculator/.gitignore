.vscode
dynasty-trade-calculator.zip

.DS_Store
.editorconfig
.git
.gitmodules
*.sql
*.tar.gz
*.zip
bin
composer.lock
Gruntfile.js
node_modules
Thumbs.db

# Composer vendor directory - exclude all except essential autoload files
vendor/*
!vendor/autoload.php
!vendor/composer/
vendor/composer/*
!vendor/composer/autoload_*.php
!vendor/composer/ClassLoader.php
!vendor/composer/InstalledVersions.php
!vendor/composer/installed.php
!vendor/composer/installed.json
!vendor/composer/LICENSE

# Test-related files and directories (security)
tests/MembershipTest.php
tests/MembershipMockTest.php
tests/MembershipApiTest.php
tests/MembershipEdgeCasesTest.php
.phpunit.result.cache

# Membership-related documentation (security)
docs/membership-tests-summary.md

# Example files (remove when not needed)
src/Example/