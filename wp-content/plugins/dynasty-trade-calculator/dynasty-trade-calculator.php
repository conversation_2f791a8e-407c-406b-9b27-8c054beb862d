<?php

/**
 * Plugin Name: Dynasty Trade Calculator
 * Plugin URI: #
 * Description: Dynasty Trade Calculator 
 * Version: 2.5.0
 * Author: DTC
 * Author URI: http://www.dynastytradecalculator.com
 * Text Domain: dynasty-trade-calculator
 */

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

include_once 'vendor/autoload.php';

// Initialize plugin using Composer autoload
use DynastyTradeCalculator\Plugin;
Plugin::init();

define('DTC_VER', '2.5.0');
define('DTC_DIR', plugin_dir_path( __FILE__ ) );
define('DTC_URL', plugins_url('', __FILE__));

function dtc_loader() {	
	global $wpdb;

	if ( ! empty( $_GET['client_id'] ) && $_GET['access_token'] != '' && $_GET['remote_login'] != '') {
		if (substr($_SERVER['HTTP_HOST'], 0, 4) === 'www.') {
    		header('Location: http'.(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS']=='on' ? 's':'').'://' . substr($_SERVER['HTTP_HOST'], 4).$_SERVER['REQUEST_URI']);
    		exit;
		}
	
		$date = current_time( 'mysql' );
		$query = $wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "oauth_access_tokens where access_token = %s AND client_id =  %s AND expires > %s",$_GET['access_token'], $_GET['client_id'],$date ); 
		
		$r = $wpdb->get_results($query, ARRAY_A);	
	
		if ($r != false) {
			$vars = $_GET;
			$string = '?dtc_share=1';
			unset($vars['access_token']);
			unset($vars['client_id']);
			unset($vars['remote_login']);
			
			foreach($vars as $key =>$var) {
				$string .= '&'.$key.'='.$var.'';
			}
			
			wp_set_current_user( $r[0]['user_id']); 
			wp_set_auth_cookie(  $r[0]['user_id'], 1, is_ssl() );
			
			$user = wp_get_current_user();
			
			wp_redirect('/calculator/'.$string.'');
			exit;
		}
	
		if ($_GET['regenerate_thumbnails'] == 1) {
		
		}
	}

	new DTC;	
}

add_action('plugins_loaded','dtc_loader');

 /**
 * Add svg support
 *
 */
add_filter( 'wp_check_filetype_and_ext', function( $data, $file, $filename, $mimes) {
    global $wp_version;
    
	if ( $wp_version == '4.7' || ( (float) $wp_version < 4.7 ) ) {
      return $data;
    }

    $filetype = wp_check_filetype( $filename, $mimes );
    
	return [
      'ext'             => $filetype['ext'],
      'type'            => $filetype['type'],
      'proper_filename' => $data['proper_filename']
    ];

}, 10, 4 );

function ns_mime_types( $mimes ) {
   $mimes['svg'] = 'image/svg+xml';
   return $mimes;
}

add_filter( 'upload_mimes', 'ns_mime_types' );

function ns_fix_svg() {
  echo '<style type="text/css">.attachment-266x266, .thumbnail img { width: 100% !important; height: auto !important;} </style>';
}

add_action( 'admin_head', 'ns_fix_svg' );

function sample_admin_notice__success() {
    ?>
    <div class="notice notice-success is-dismissible">
        <p><?php _e( 'You are on the new site!!!! IP Address '.$_SERVER['SERVER_ADDR'].'', 'sample-text-domain' ); ?></p>
    </div>
    <?php
}

add_action( 'admin_notices', 'sample_admin_notice__success' );

add_action('wp','dtc_move_images'); 

function dtc_move_images(){
    global $wpdb;
		
	if ( ! empty( $_GET['run_image_copy'] ) && $_GET['run_image_copy'] ==1) {
    	$image_path = wp_upload_dir();
            
		$players['players'] = $wpdb->get_results("SELECT * FROM " . $wpdb->prefix . "dtc_players", ARRAY_A);
        $players['devy'] = $wpdb->get_results("SELECT * FROM " . $wpdb->prefix . "dtc_players_devy", ARRAY_A);
        $players['idp'] = $wpdb->get_results("SELECT * FROM " . $wpdb->prefix . "dtc_players_idp", ARRAY_A);
  
		foreach($players as $k=>$r) {
        	for ($i = 0; $i < count($r); $i++) {
		 		$name = str_replace(' ','_',stripslashes($r[$i]['name']));
				$path = $image_path['basedir'].'/headshots_new/'. $name.'.png';
				$exists = file_exists($path);
        
        		$image[$k][$r[$i]['id']] = array('name'=>$r[$i]['name'],'id'=>$r[$i]['id'],'path'=>$path,'exists'=>$exists) ; 
        	}
  		}

      	$moved = 0;

    	foreach($image as $key=>$type) {
            foreach($type as $player) {
           		if (file_exists($player['path'])) {
               		rename($player['path'], $image_path['basedir'].'/headshots_new/'.$key.'/'.$player['id'].'.png');
               		$moved ++;
           		}
            }
    	}

        echo '<pre>';
        echo 'Moved '.$moved.' Files';
        echo '</pre>';
        
		exit;
    }
}

class DTC {
	protected $tdm;
	protected $read_only;
	protected $settings;
	
	function __construct() {
		global $current_user;
		
		include_once 'includes/google.link.php';
			
		$this->tdm = 'dtc';
		$this->install();
		$this->includes();
		$this->actions();
		#$has_access  = false;
		
		if( dtc_is_read_only() == true){
			$this->read_only = 1;	
		}else{
			$this->read_only = 0;
		}
			
		add_action('init',array($this,'update_mfl_id'));
		add_action( 'rest_api_init',array($this,'rest_endpoint'));
		
		add_filter('show_admin_bar', '__return_false');
		add_action('admin_init',array($this,'check_role_status'));
	  	add_action('admin_init',array($this,'capabilities'));
            
        add_action('init', array($this,'custom_rewrite_rule'), 99, 0);    
            
        add_action('wp', array($this,'headshots'));
        add_filter( 'query_vars', array($this,'query_vars') );
	}
	
    function query_vars( $qvars ) {
		$qvars[] = 'player_id';
		$qvars[] = 'player_type';
		$qvars[] = 'dtcs';
		$qvars[] = 'dtc_headshot';  
 
    	return $qvars;
	}
    
    function headshots() {
        if ( get_query_var('dtc_headshot') == 'headshot') {
        	global $wpdb;
            
			$image = array();
           	$not_found = DTC_URL.'/asetts/images/notavailable.png'; 
            
            $player_id = sanitize_text_field(get_query_var('player_id'));
            $player_type =  sanitize_text_field(get_query_var('player_type'));
          
          	# $image['type'] =  get_query_var('dtct');
            $image['size'] =  str_replace('.png','',sanitize_text_field(get_query_var('dtcs')));
            $image['upload_path'] = wp_upload_dir();
            
            if (!in_array( $image['size'],array('original','small','medium','large'))) {
            	$image['size'] = 'small';    
            }

            $image['file_dir'] =  $image['upload_path']['basedir'].'/headshots_new/'.$player_type.'/'.$player_id.'.png';
           
            $image['file_url'] =  $image['upload_path']['baseurl'].'/headshots_new/'.$player_type.'/'.$player_id.'.png';
        	# print_r($image);exit;
          
			if (file_exists( $image['file_dir'])) {
				$size = getimagesize($image['file_dir']);
				$image['original'] = $size;
				$height =  ($size[1] / $size[0]) * 250;
				
				$sizes['small'] =array('width'=>250,'height'=> round($height));
				$height =  ($size[1] / $size[0]) * 450;
				
				$sizes['medium'] =array('width'=>450,'height'=>round($height));
			
				$height =  ($size[1] / $size[0]) * 1050;  
				$sizes['large'] =array('width'=>1050,'height'=>round($height));
           
            	$image['sizes'] = $sizes;
            
            	# print_r($image);exit;
				$settings['width'] = $sizes[$image['size']]['width'];
				$settings['height'] = $sizes[$image['size']]['height'];
				$settings['crop'] = false;
				$image['settings'] = $settings; 
              
              	#$image['url'] = bfi_thumb($image['file_url'], $settings);
         		$image['url'] = $image['file_url'];    
              
              	if ($image['url'] !='') {
               		header("Location: ".$image['url']."");
                	exit;
            	} else {
             		header("Location: ".$not_found."");
					exit();
            	}
          	} else {
            	header("Location: ".$not_found."");
				exit();
              
                exit;
          	}
        
        	die();
        }
    }
    
    function custom_rewrite_rule() {
    	add_rewrite_rule(
			'^headshots/([^/]*)/([^/]*)/([^/]*).png',
			'index.php?dtc_headshot=headshot&player_type=$matches[1]&player_id=$matches[2]&dtcs=$matches[3]',
			'top'
		);
    }
    
    function capabilities() {
	    $role = get_role( 'administrator' );
   		$role->add_cap( 'dtc_admin_idp' ); 
		$role->add_cap( 'dtc_admin_devy' ); 
    }	

	function check_role_status() {
		$redirect = true;

		if (is_user_logged_in()) {
		   	$user = wp_get_current_user();
		   	$file = basename($_SERVER['PHP_SELF']);
			
			if (in_array( "administrator", (array) $user->roles )) {
				$redirect=  false;	
			}

			if (in_array( "author", (array) $user->roles )) {
				$redirect=  false;	
			}

			if (in_array( "contributor", (array) $user->roles )) {
				$redirect=  false;	
			}

			if (in_array( "editor", (array) $user->roles )) {
				$redirect=  false;	
			}

			if (in_array( "partner", (array) $user->roles )) {
				$redirect=  false;	
			}

			if (in_array( "idp_admin", (array) $user->roles )) {
				$redirect=  false;	
			}	

			if (in_array( "devyadmin", (array) $user->roles )) {
				$redirect=  false;	
			}	
			
			if ($file == 'admin-ajax.php') {
				$redirect= 	false;	
			}
				
			if ($redirect == true) {
				wp_redirect('/calculator/');	
			}
		}
	}       
    
    function rest_get_user($data) {
        global $wpdb,$current_user;

		$get_user = get_userdata($data['id']);
		$user = array();
		$user['data'] =$get_user;

        return apply_filters('dtc/rest/get_user',$user,$data);;
    }

	function rest_get_picks($data) {
		global $wpdb;
				
		if (isset($data['pick'])) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_draft_picks where pick = %s", $data['pick']), ARRAY_A);
			
			if ($r == false) {
				return false;	
			} else {
				return $r[0];	
			}

		} else {
			$r = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_draft_picks order by pick", ARRAY_A);
			return $r;	
		}
			
	}
				
	function rest_get_picks_all($data) {
		global $wpdb;
				
		$r = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_draft_picks order by pick", ARRAY_A);
		return $r;
	}
		
	function rest_get_players_batch($data) {
		global $wpdb;
			
		if (isset($data['startLimit']) &&  isset($data['endLimit'])) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players LIMIT " . $data['startLimit'] . "," . $data['endLimit']), ARRAY_A);		
			
			if ($r == false) {
				return NULL;	
			} else {
				return $r;	
			}
		}	
	}

	function rest_get_players_idp_batch($data) {
		global $wpdb;
			
		if (isset($data['startLimit']) &&  isset($data['endLimit'])) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_idp LIMIT " . $data['startLimit'] . "," . $data['endLimit']), ARRAY_A);		
			
			if($r == false){
				return NULL;	
			} else {
				return $r;	
			}
		}	
	}
		
	function rest_get_player($data) {
		global $wpdb;
			
		if (isset($data['id'])) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players where id =  %d", $data['id']), ARRAY_A);		
			if ($r == false) {
				return NULL;	
			} else {
				return $r[0];	
			}
		}
			
		if (isset($data['mfl_id'])) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players where mfl_id =  %d", $data['mfl_id']), ARRAY_A);		
			
			if ($r == false) {
				return NULL;	
			} else {
				return $r[0];	
			}
		}
	}
		
	function rest_get_settings($data) {
		return get_option('dtc_settings');
	}

	function rest_endpoint() {
		register_rest_route( 'dtc/v1', '/settings/', array(
			'methods' => 'GET',
			'callback' => array($this,'rest_get_settings'),
		) );
				
		register_rest_route( 'dtc/v1', '/picks/', array(
			'methods' => 'GET',
			'callback' => array($this,'rest_get_picks_all'),
		) );
			
		register_rest_route( 'dtc/v1', '/pick/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_picks'),
			'args' =>array('pick')
		) );
			
		register_rest_route( 'dtc/v1', '/player/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_player'),
			'args' =>array('id','name','mfl_id','position')
		) );

		register_rest_route( 'dtc/v1', '/get_players_batch/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_players_batch'),
			'args' =>array('startLimit','endLimit')
		) );

		register_rest_route( 'dtc/v1', '/get_players_idp_batch/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_players_idp_batch'),
			'args' =>array('startLimit','endLimit')
		) );
			
		register_rest_route( 'dtc/v1', '/get_players_batch/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_players_batch'),
			'args' =>array('startLimit','endLimit')
		) );

		register_rest_route( 'dtc/v1', '/get_players_idp_batch/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_players_idp_batch'),
			'args' =>array('startLimit','endLimit')
		) );
			
	 	register_rest_route( 'dtc/v1', '/calculator/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_player'),
			'args' =>array('id','name','mfl_id','position')
		) );

     	register_rest_route( 'dtc/v1', '/user/', array(
			'methods' => 'POST',
			'callback' => array($this,'rest_get_user'),
			'args' =>array('id')
		) );
	}
		
	function update_mfl_id() {
		global $wpdb;
		
		if ( ! empty( $_GET['run_mfl'] ) && $_GET['run_mfl'] == 1) {
			#do_action('run_mfl');
			$uploads = wp_upload_dir();
			$upload_path = $uploads['basedir']; // now how to get just the directory name?
			$xml_file = ''.$upload_path.'/mfl.xml';
		
			if ($_GET['download'] == 1) {
				$url = 'https://api.myfantasyleague.com/'.date("Y").'/export?TYPE=players&DETAILS=1';
		
				$tmpfile = download_url( $url);
				copy( $tmpfile, $xml_file );
				unlink( $tmpfile ); // must unlink afterwards
			}
			
			$get = file_get_contents($xml_file);
			$arr = simplexml_load_string($get);
			#print_r($arr);exit;
			
			if ($_GET['download'] == 1) {
				foreach($arr as $player) {
					$query = $wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_mfl where mfl_id = %d", $player->attributes()->id);	
					#echo $query;
					
					$r = $wpdb->get_results($query, ARRAY_A);
					$atts = $player->attributes();
					
					foreach((array)$atts as $key=>$att) {
						$insert[$key]	 = $att;
					}
						
					$insert_final = $insert['@attributes'];
					
					if ($r == false) {
						$insert_final['mfl_id'] = $insert_final['id'];
						unset($insert_final['id']);
						
						$wpdb->insert("" . $wpdb->prefix . "dtc_players_mfl", $insert_final);
						
						if ($wpdb->last_error !== '') {
    						$wpdb->print_error();
						}
					} else {
						unset($insert_final['id']);
						$wpdb->update("" . $wpdb->prefix . "dtc_players_mfl",$insert_final,array('id'=>$r[0]['id']));
					}
					
				}
				
			}
	
			$positions = array('QB','RB','TE','WR', 'CB', 'S','DE','DT','LB');
			
			$rp = $wpdb->get_results("SELECT * FROM   " . $wpdb->prefix . "dtc_players_mfl where position != 'TMWR'", ARRAY_A);
			
			for ($i = 0; $i < count($rp); $i++) {
				if (in_array($rp[$i]['position'], $positions)) {
					$player_arr = explode(",",$rp[$i]['name']);
					$player_name = ''.trim(stripslashes($player_arr[1])).' '.stripslashes($player_arr[0]).'';
					$player_team = $rp[$i]['team'];
					$player_position = $rp[$i]['position'];
					$player_id = $rp[$i]['mfl_id'];
					$player_name = str_replace("'","%", $player_name);
					$player_name = str_replace(".","", $player_name);
					$query = $wpdb->prepare("SELECT * FROM   " . $wpdb->prefix . "dtc_players where name LIKE  '".$player_name."' AND position = %s",$player_position);
					
					$rpc = $wpdb->get_results($query, ARRAY_A);
				
					if ($rpc != false) {
						$update['mfl_id'] = $player_id;
						$where['id'] = $rpc[0]['id'];
						$wpdb->update("" . $wpdb->prefix . "dtc_players", $update,$where);
						echo '<span style="color:green">Updated '.$player_name.' '.$player_position.'with  '.$player_id.'';echo '</span><br>';
					} else {
						echo '<span style="color:red">Didnt Find '.$player_name.' '.$player_position.'  '.$player_id.'</span><br>';
					}
				
					unset($update);
					unset($where);
					$query = $wpdb->prepare("SELECT * FROM   " . $wpdb->prefix . "dtc_players_devy where name LIKE  '".$player_name."' AND position = %s",$player_position);
				
					$rpc = $wpdb->get_results($query, ARRAY_A);
				
					if ($rpc != false) {
						$update['mfl_id'] = $player_id;
						$where['id'] = $rpc[0]['id'];
						$wpdb->update("" . $wpdb->prefix . "dtc_players_devy", $update,$where);
						echo '<span style="color:green">Updated '.$player_name.' '.$player_position.'with  '.$player_id.'';echo '</span><br>';
					} else {
						echo '<span style="color:red">Didnt Find '.$player_name.' '.$player_position.'  '.$player_id.'</span><br>';
					}
				
					unset($update);
					unset($where);
					$query = $wpdb->prepare("SELECT * FROM   " . $wpdb->prefix . "dtc_players_idp where name LIKE  '".$player_name."' AND position = %s",$player_position);
				
					$rpc = $wpdb->get_results($query, ARRAY_A);
					
					if ($rpc != false) {
						$update['mfl_id'] = $player_id;
						$where['id'] = $rpc[0]['id'];
						$wpdb->update("" . $wpdb->prefix . "dtc_players_idp", $update,$where);
						echo '<span style="color:green">Updated '.$player_name.' '.$player_position.'with  '.$player_id.'';echo '</span><br>';
					} else {
						echo '<span style="color:red">Didnt Find '.$player_name.' '.$player_position.'  '.$player_id.'</span><br>';
					}
				}
			}
			
			exit;
		}		
	}
		
	function actions() {
		add_action('wp_enqueue_scripts', array($this,'scripts'));
		add_action('admin_enqueue_scripts', array($this,'scripts'));
		add_action('admin_menu', array($this,'menu'));
		
		add_action('wp_head',array($this,'wp_head'));
		add_action('wp_footer',array($this,'wp_footer'));
	}
		
	function wp_footer() {
		?>
        
        <script type="text/javascript">
			jQuery(document).ready(function($) {
				$(".export-png").on("click",function(){
					var wrapper = $(".dtc-calculator-wrapper-remove")[0];
					console.log(wrapper );
					html2canvas(wrapper ).then(function(canvas) {
						// $("#img-out").append(canvas);
						var a = document.createElement('a');
						// toDataURL defaults to png, so we need to request a jpeg, then convert for file download.
						a.href = canvas.toDataURL("image/jpeg").replace("image/jpeg", "image/octet-stream");
						console.log(a.href);
						a.download = 'DynastyTradeCalculator.jpg';
						a.click();
					});
			
					return false;
				});
			});
		</script>
        
        <?php	
			
	}
		
	function wp_head() {
		?> 

        <meta name="mobile-web-app-capable" content="yes">
        <link rel="manifest" href="/manifest.json">
   		<link rel="icon" sizes="192x192" href="/images/icons/icon-192x192.png">
        <script type="text/javascript">
   			jQuery(function($) {
 				$("#fallback-slide img").wrap("<a href='/calculator'></a>");
    		});
		</script>
           
        <?php	
	}
		
	function menu() {
		add_menu_page( 
        	__( 'Dynasty Trade Calculator', $this->tdm ),
        	'Dynasty Trade Calculator',
        	'manage_options',
        	'dtc-settings',
        	array('dtc_admin_table', 'view'),
        	plugins_url('asetts/images/icon_small.png', __FILE__),
        	6
    	); 
	
		add_submenu_page( 'dtc-settings', 'Offense', 'Offense', 'manage_options', 'dtc-settings', array('dtc_admin_table', 'view'));
	
		add_submenu_page( 'dtc-settings', 'Devy', 'Devy', 'dtc_admin_devy', 'dtc-settings-devy', array('dtc_admin_devy_table', 'view'));
	
		add_submenu_page( 'dtc-settings', 'IDP', 'IDP', 'dtc_admin_idp', 'dtc-settings-idp', array('dtc_admin_idp_table', 'view'));
	
		add_submenu_page( 'dtc-settings', 'Picks', 'Picks', 'manage_options', 'dtc-settings-picks', array('dtc_admin_picks', 'view'));
		
		add_submenu_page( 'dtc-settings', 'Badges', 'Badges', 'manage_options', 'dtc-settings-badges', array('dtc_admin_badges', 'view'));
		
		add_submenu_page( 'dtc-settings', 'Settings', 'Settings', 'manage_options', 'dtc-options', array('dtc_options', 'view'));
	
		do_action('dtc_menu_pages');	
	}

	function scripts() {
		global $post, $current_user;
		
		$settings = array();
		$settings = get_option('dtc_settings');
				
		$localize = array('calculator_url'=> get_permalink(11916),'plugin_url'=>DTC_URL, 'ajaxurl'=> admin_url( 'admin-ajax.php' ),'sfqb'=>dtc_setting('sfqb'),'nonppr'=>dtc_setting('nonppr'),'settings'=>$settings,'r'=>$this->read_only,'user'=>array('ID'=>$current_user->ID,'ignore_idp_modal'=>get_user_meta($current_user->ID,'_ignore_idp_modal',true)));

		wp_enqueue_script('jquery');
		
		if (!is_admin()) {
        	wp_enqueue_script('chartjs','//cdn.jsdelivr.net/npm/chart.js');   
        }
		
		wp_enqueue_script('table-export', plugins_url('asetts/js/table.export.js', __FILE__));	
		wp_enqueue_script('dtc-jquery-cookie', plugins_url('asetts/js/jquery.cookie.js', __FILE__));
		wp_enqueue_script('izimodal', plugins_url('asetts/js/lity.min.js', __FILE__));	
		wp_enqueue_style('izimodal-css', plugins_url('asetts/css/lity.min.css', __FILE__));	
			
		wp_enqueue_script('remodal', plugins_url('asetts/js/remodal.js', __FILE__));	
		wp_enqueue_style('remodal-css', plugins_url('asetts/css/remodal.css', __FILE__));	
		#wp_enqueue_script('dtc-html-canvas-share', plugins_url('asetts/js/htmlcanvasshare.js', __FILE__));
		#wp_enqueue_script('dtc-html-base64', plugins_url('asetts/js/base64binary.js', __FILE__));
		wp_enqueue_script('dtc-jquery-print', plugins_url('asetts/js/print.js', __FILE__));
		#admin only scripts
		
		wp_enqueue_script('jquery-ui-core');
		wp_enqueue_script('jquery-ui-datepicker');
		wp_enqueue_script('jquery-ui-tabs');
							
		wp_enqueue_script('jquery-ui-slider');
		wp_enqueue_script('jquery-ui-autocomplete');
		wp_enqueue_style('jquery-ui-css','//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css');
		wp_enqueue_style('jquery-ui-css', '//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css');
		
		if (is_admin() && ! empty( $_GET['page'] ) && in_array($_GET['page'], array('dtc-settings','dtc-settings-devy','dtc-settings-idp','dtc-settings-picks','dtc-options'))) {
			#wp_enqueue_script('dtc-jquery-dynatable', plugins_url('asetts/js/jquery.dynatable.js', __FILE__));
			wp_enqueue_script('dtc-jquery-table', plugins_url('asetts/js/jquery.tabledit.min.js', __FILE__));
			wp_enqueue_style('jquery-data-tables','//cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css');
			wp_enqueue_script('jquery-data-tables-js','//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js');
			#wp_enqueue_style('dtc-dynatable-css', plugins_url('asetts/css/jquery.dynatable.css', __FILE__));
			wp_enqueue_style("jquery-data-tables","//cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css");
			wp_enqueue_script("jquery-data-tables-js","//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js");
			wp_register_script('dtc-admin-js', plugins_url('asetts/js/admin.js?t='.time().'', __FILE__),array(),DTC_VER);
			wp_localize_script('dtc-admin-js', 'dtc',$localize);
			wp_enqueue_script('dtc-admin-js');
				
			if ($_GET['page'] == 'dtc-settings-devy') {
				wp_enqueue_script('dtc-devy-js', plugins_url('asetts/js/devy.js', __FILE__),array('jquery'));
			}
							
			if ($_GET['page'] == 'dtc-settings-idp') {
				wp_enqueue_script('dtc-idp-js', plugins_url('asetts/js/idp.js', __FILE__),array('jquery'));
			}
		}
	
	
		// League-integration: only on that same page
		if ( ! is_admin() && is_page(11916) ) {
			wp_enqueue_script(
				'dtc-league-integration',
				plugin_dir_url(__FILE__) . 'asetts/js/calculator/league-integration.js',
				['jquery'],
				'1.0',
				true
			);
		}
		
		if ( is_object( $post ) && $post->ID == 11916) {
			wp_enqueue_script('html2canvas', plugins_url('asetts/js/html2canvas.js', __FILE__));		
		}
			wp_enqueue_style('fontawesome','//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css');
				
		wp_enqueue_style('dtc-admin-css', plugins_url('asetts/css/admin.css', __FILE__),array(),DTC_VER);
		wp_enqueue_script('select2-js', plugins_url('asetts/js/select2.full.min.js', __FILE__),array('jquery'),'4.1.0');	
		wp_enqueue_style('select2-css', plugins_url('asetts/css/select2.min.css', __FILE__),array(),'4.1.0');	
				
		if (!is_admin()) {
			wp_enqueue_style('fontawesome','//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css');	
				
			wp_enqueue_script('animate-number', plugins_url('asetts/js/jquery.animate.number.js', __FILE__));
			wp_enqueue_script('jquery-cycle', plugins_url('asetts/js/jquery.cycle.js', __FILE__));
			
			wp_register_script('dtc-js', plugins_url('asetts/js/main.js', __FILE__), array('jquery'),DTC_VER);
			wp_localize_script('dtc-js', 'dtc',$localize);
			wp_enqueue_script('dtc-js');
			wp_enqueue_style('dtc-css', plugins_url('asetts/css/style.css', __FILE__),array(),DTC_VER);
		}
			
		do_action('dtc_enqueue_scripts');
	}
		
	function includes() {

		include_once 'includes/bfi_thumb.php';
		include_once 'includes/functions.php';
		include_once 'admin/settings.php';
		include_once 'admin/table.php';
		include_once 'admin/devy.php';
		include_once 'admin/idp.php';
		include_once 'admin/picks.php';
		include_once 'admin/badges.php';
		include_once 'admin/migration.php';

		include_once 'user/calculator.php';
		include_once 'user/trade-wire.php';
		include_once 'user/player.php';
		include_once 'user/mfl.php';
		include_once 'user/sleeper.php';
		include_once 'user/fleaflicker.php';
		include_once 'user/yahoo.php';
		include_once 'user/ffpc.php';
		include_once 'user/fantrax.php';
		include_once 'user/top.php';
		include_once 'user/calculator-startup.php';
		include_once 'admin/tools.php';
		include_once 'admin/cron-tools.php';
		include_once 'user/idp.php';
		include_once'user/polls.php';
		include_once'user/rcp.php';

        #load modules
        # include_once 'modules/draft-caddy/index.php';
	}
    
	function install() {
		global $wpdb;
			
		if (get_option('dtc_install')== '1.01') {
			$sql = array();
			
			$table_name = "".$wpdb->prefix . "dtc_adp";
			$sql[] = "CREATE TABLE IF NOT EXISTS ` ".$wpdb->prefix . "dtc_adp` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT '',
  `position` varchar(255) DEFAULT '',
  `age` date DEFAULT '0000-00-00',
  `adp` float DEFAULT '0',
  `trend` int(11) DEFAULT '0',
  `value` int(11) DEFAULT '0',
  `rank` int(11) NOT NULL DEFAULT '0',
  `sub_rank` int(11) NOT NULL DEFAULT '0',
  `adp_dtc` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) AUTO_INCREMENT=1 ;" ;
			
			$table_name = "".$wpdb->prefix . "dtc_tags";
			$sql[] = "CREATE TABLE IF NOT EXISTS ` ".$wpdb->prefix . "dtc_draft_picks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `point_value` float DEFAULT '0',
  `pick` varchar(255) DEFAULT '0',
  `ten` int(1) DEFAULT '0',
  `twelve` int(1) DEFAULT '0',
  `fourteen` int(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;" ;
			
			$table_name = "".$wpdb->prefix . "dtc_values";
			$sql[] = "CREATE TABLE IF NOT EXISTS ` ".$wpdb->prefix . "dtc_dtc` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dtc` float NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;" ;
			
			$table_name = "".$wpdb->prefix . "dtc_players";
			$sql[] = "CREATE TABLE IF NOT EXISTS ` ".$wpdb->prefix . "dtc_players` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT '',
  `image` varchar(255) DEFAULT '',
  `position` varchar(255) DEFAULT '',
  `team` varchar(255) DEFAULT '',
  `birthdate` date DEFAULT '0000-00-00',
  `adp` float DEFAULT '0',
  `trend` int(11) DEFAULT '0',
  `yse` float DEFAULT '0',
  `nyh` float DEFAULT '0',
  `nrm` float DEFAULT '0',
  `average` float DEFAULT '0',
  `rank` int(11) NOT NULL DEFAULT '0',
  `sub_rank` int(11) NOT NULL DEFAULT '0',
  `old_rank` int(11) NOT NULL DEFAULT '0',
  `old_sub_rank` int(11) DEFAULT '0',
  `rank_trend` int(11) NOT NULL DEFAULT '0',
  `sub_rank_trend` int(11) NOT NULL DEFAULT '0',
  `value` int(11) NOT NULL DEFAULT '0',
  `adp_dtc` float NOT NULL DEFAULT '0',
  `nonppr` int(1) NOT NULL DEFAULT '0',
  `url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;" ;
		
			if (count($sql)>0) {
				require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
				
				foreach($sql as $query){
					dbDelta( $query );
				}
			}

			update_option('dtc_install',DTC_VER);
		}		
	}
	
}



