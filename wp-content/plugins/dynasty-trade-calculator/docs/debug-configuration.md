# Dynasty Trade Calculator - Debug Configuration

This document explains how to configure debug logging for the Dynasty Trade Calculator plugin.

## Debug Constants

Add these constants to your `wp-config.php` file to control debug logging:

### Basic Debug Mode

```php
// Enable DTC debug logging (basic info, warnings, errors)
define('DTC_DEBUG', true);
```

When `DTC_DEBUG` is enabled:
- Basic informational messages are logged
- Warning and error messages are logged
- API endpoint calls are logged (without full request/response data)
- Membership operations are logged

### Verbose Debug Mode

```php
// Enable verbose debug logging (includes full object dumps)
define('DTC_DEBUG_VERBOSE', true);
```

When `DTC_DEBUG_VERBOSE` is enabled (requires `DTC_DEBUG` to also be true):
- Full request/response data from API calls is logged using `print_r()`
- Object dumps and detailed data structures are logged
- More detailed debugging information is included

### WordPress Debug Integration

The plugin also respects WordPress's built-in debug constants:

```php
// WordPress debug mode (fallback if DTC_DEBUG is not set)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Configuration Examples

### Development Environment
```php
// Full debug logging for development
define('DTC_DEBUG', true);
define('DTC_DEBUG_VERBOSE', true);
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Production Environment
```php
// Minimal logging for production (errors only)
define('DTC_DEBUG', false);
define('DTC_DEBUG_VERBOSE', false);
define('WP_DEBUG', false);
```

### Staging Environment
```php
// Basic logging for staging (no verbose output)
define('DTC_DEBUG', true);
define('DTC_DEBUG_VERBOSE', false);
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Log Levels

The debug utility uses different log levels:

- **ERROR**: Critical errors that should always be logged (can be forced even when debug is disabled)
- **WARNING**: Warning messages
- **INFO**: General informational messages
- **DEBUG**: Detailed debug information

## Log Format

All DTC debug messages are formatted as:
```
[YYYY-MM-DD HH:MM:SS] [DTC-LEVEL] Message content
```

Example:
```
[2024-01-15 14:30:25] [DTC-INFO] DTC RotoGPT Processing: Processing membership #123
[2024-01-15 14:30:26] [DTC-ERROR] DTC RotoGPT Update Error: HTTP 500
```

## Usage in Code

The debug utility provides several methods:

```php
use DynastyTradeCalculator\Debug;

// Basic logging
Debug::info('Informational message');
Debug::warning('Warning message');
Debug::error('Error message', $force = false);
Debug::debug('Debug message');

// Object logging (only when verbose mode is enabled)
Debug::logObject($data, 'Label for the data');
Debug::dump($variable, 'Variable name');

// API call logging
Debug::logApiCall($endpoint, $request_data, $response, 'Operation Name');

// Membership operation logging
Debug::logMembership('Create', $membership_id, ['key' => 'value']);
```

## Checking Debug Status

You can check the current debug configuration:

```php
use DynastyTradeCalculator\Debug;

$status = Debug::getStatus();
// Returns array with debug configuration details
```

## Log File Location

Debug messages are written to the standard WordPress debug log, typically located at:
- `/wp-content/debug.log` (if `WP_DEBUG_LOG` is true)
- Server error log (if WordPress debug log is not configured)

## Performance Considerations

- **Production**: Always set `DTC_DEBUG` and `DTC_DEBUG_VERBOSE` to `false` in production
- **Verbose Mode**: Only enable verbose mode when actively debugging, as it can generate large log files
- **Log Rotation**: Consider implementing log rotation for long-running debug sessions

## Troubleshooting

If debug logging is not working:

1. Verify constants are defined in `wp-config.php`
2. Check that `WP_DEBUG_LOG` is enabled if using WordPress debug logging
3. Ensure the web server has write permissions to the debug log file
4. Check server error logs for any PHP errors in the debug utility
