# Dynasty Trade Calculator - Product Requirements Document (PRD)

## Current Status (Updated June 20, 2025)

### 🔧 Recently Completed
- **Fatal Error Fix**: Resolved PHP fatal error related to corrupted deep-copy.php dependency
  - Cleaned up vendor dependencies using `composer install --no-dev`
  - Removed problematic myclabs/deep-copy package that was causing autoload failures
  - Calculator page now loads without fatal errors
- **Comprehensive Test Suite Restoration**: Recreated all missing membership tests
  - `MembershipTest.php` - Core membership functionality, customer retrieval, status checks
  - `MembershipApiTest.php` - RotoGPT API integration, subscription management, error handling
  - `MembershipMockTest.php` - Mock-based tests without external dependencies
  - `MembershipEdgeCasesTest.php` - Edge cases, boundary conditions, invalid data scenarios
  - **Test Status**: 50 tests, 370 assertions - ALL PASSING ✅
- **Debug Utility System**: Implemented comprehensive conditional logging system
  - `DTC_DEBUG` constant for basic debug control
  - `DTC_DEBUG_VERBOSE` constant for detailed object logging
  - Replaced all `error_log()` calls in Membership.php with conditional Debug methods
  - Created documentation and test utilities for debug configuration
- **Missing Shortcodes**: Recovered and added the missing shortcodes from the deleted `includes/membership.php`:
  - `dtc_register_form` - Displays membership registration form based on user eligibility
  - `dtc-easy-pricing-table` - Shows appropriate pricing table based on user status and pilot access
- **Complete Functionality**: All code from the old membership file has now been migrated to the new class

### 🧪 Testing & Security Strategy
- **Multiple Test Files**: Subscription functionality is tested across 6 separate test files for comprehensive coverage
- **Mock Data Only**: All tests use mock data (e.g., `mock_access_token_123`) - no real secrets or WP config data exposed
- **Git-Safe Tests**: All test files are safe to include in version control with no sensitive information
- **PHPUnit Cache**: `.phpunit.result.cache` file present for faster test execution (can be gitignored if desired)

### ✅ Completed
- **Composer Autoload Migration**: Successfully migrated membership functionality from function-based to class-based approach
- **Plugin Class**: Created centralized `Plugin` class for initialization
- **Membership Class**: Consolidated all membership functionality into `src/Membership.php`
- **File Structure**: Organized code with proper PSR-4 namespace (`DynastyTradeCalculator\`)
- **WordPress Hooks**: Integrated hooks directly into the Membership class
- **Shortcodes**: Added missing shortcodes (`dtc_register_form`, `dtc-easy-pricing-table`)
- **Code Updates**: Updated `user/calculator.php` to use new class methods
- **Complete Migration**: All functionality from old `includes/membership.php` has been migrated

### 🔄 Current Architecture

```
src/
├── Plugin.php          # Main plugin initialization class
├── Membership.php      # Complete membership functionality + hooks
├── Debug.php           # Conditional logging utility (NEW)
└── RestApi.php         # REST API endpoints

wp-content/plugins/dynasty-trade-calculator/
├── dynasty-trade-calculator.php  # Main plugin file (minimal)
├── user/calculator.php           # Updated to use new classes
├── tests/                        # Comprehensive test suite (50 tests, 370 assertions, 100% passing)
│   ├── BasicTest.php            # Basic PHP functionality tests
│   ├── DebugTest.php            # Debug utility tests
│   ├── MembershipTest.php       # Core membership functionality tests
│   ├── MembershipApiTest.php    # RotoGPT API integration tests
│   ├── MembershipMockTest.php   # Mock-based membership tests
│   └── MembershipEdgeCasesTest.php # Edge cases and boundary condition tests
├── docs/                         # Documentation and guides
│   ├── PRD.md                   # This document
│   └── debug-configuration.md   # Debug setup guide
```

### 📋 Documentation & File Management
- **README Files**: Two README files present:
  - `README.md` - Currently minimal (3 lines) - suitable for GitHub display
  - `readme.txt` - WordPress plugin format with changelog and metadata
  - **GitHub Compatibility**: `.md` files work perfectly on GitHub and are preferred over `.txt`
- **Debug Documentation**: Comprehensive debug configuration guide available in `docs/debug-configuration.md`
  - Essential info: Use `DTC_DEBUG` and `DTC_DEBUG_VERBOSE` constants in wp-config.php
  - Production safety: Always disable debug constants in production environments
  - Log levels: ERROR, WARNING, INFO, DEBUG with conditional logging

### 🎯 Next Steps (TODO)

#### Phase 1: Complete Migration
1. **Update All Files**: Find and update remaining files that use old membership functions
   - Search for: `dtc_get_current_user_customer`, `dtc_get_current_user_membership`, etc.
   - Replace with: `Membership::getCurrentUserCustomer()`, `Membership::getCurrentUserMembership()`, etc.
   - Add `use DynastyTradeCalculator\Membership;` to files that need it

2. **Test Migration**: Run existing tests to ensure functionality still works
   - Execute: `composer test` or `vendor/bin/phpunit`
   - Fix any broken tests
   - Verify membership functionality works in browser

#### Phase 2: Expand Class-Based Approach
3. **Calculator Class**: Convert calculator functionality to class-based
   - Create `src/Calculator.php`
   - Move calculator logic from `user/calculator.php`
   - Update hooks and AJAX handlers

4. **API Class**: Convert REST API functionality to class-based
   - Create `src/Api.php`
   - Move API logic from `includes/rest-api.php`
   - Update route registrations

5. **Admin Class**: Convert admin functionality to class-based
   - Create `src/Admin.php`
   - Move admin logic from `admin/` files
   - Update admin hooks and pages

#### Phase 3: Clean Up & Documentation
6. **Remove Old Files**: Delete old function-based files after migration
   - Remove `includes/functions.php` (if exists)
   - Remove other old include files
   - Clean up unused directories

7. **Update Documentation**: Update all documentation to reflect new structure
   - Expand README.md with proper project documentation
   - Update code comments
   - Create developer documentation

## Migration Guidelines

### Class Naming Convention
- **File name = Class name**: `Membership.php` contains `class Membership`
- **Namespace**: All classes use `DynastyTradeCalculator\` namespace
- **Methods**: Use camelCase for method names (e.g., `getCurrentUserCustomer()`)

### Function to Class Method Mapping
```php
// OLD (function-based)
dtc_get_current_user_customer() 
dtc_get_current_user_membership()
dtc_is_current_user_invited_to_chatdtc_pilot()

// NEW (class-based)
Membership::getCurrentUserCustomer()
Membership::getCurrentUserMembership()
Membership::isCurrentUserInvitedToChatdtcPilot()
```

### File Updates Required
1. **Add use statement** at top of file:
   ```php
   use DynastyTradeCalculator\Membership;
   ```

2. **Replace function calls** with class methods:
   ```php
   // OLD
   $customer = dtc_get_current_user_customer();
   
   // NEW
   $customer = Membership::getCurrentUserCustomer();
   ```

## Test Cases by Feature

### 🧪 Test Status: 50 tests, 370 assertions - ALL PASSING ✅

#### **Core Membership Functionality** (MembershipTest.php)
- ✅ Membership level to RotoGPT mapping (8→free, 7→standard_50, 9→standard_100, 12→standard_200, 5→admin)
- ✅ Pilot program invitation detection (via notes and special pricing)
- ✅ Legacy membership options lost detection
- ✅ Membership status validation (active, cancelled, expired, pending)
- ✅ Expiration date handling and validation
- ✅ Cancelled membership validation logic
- ✅ Membership validity at specific dates

#### **RotoGPT API Integration** (MembershipApiTest.php)
- ✅ Signin authentication flow and request structure
- ✅ Create subscription requests and response validation
- ✅ Update subscription requests (immediate vs scheduled)
- ✅ API endpoint selection (production vs development)
- ✅ HTTP error handling and retry logic
- ✅ Subscription type validation
- ✅ JSON encoding/decoding for API requests
- ✅ Upgrade/downgrade detection logic

#### **Mock-Based Testing** (MembershipMockTest.php)
- ✅ Customer-membership relationships
- ✅ Special pricing detection (2.99, 29.99)
- ✅ Membership status transitions
- ✅ Membership expiration logic
- ✅ Customer notes parsing
- ✅ Date boundary conditions
- ✅ Empty/null value handling

#### **Edge Cases & Data Validation** (MembershipEdgeCasesTest.php)
- ✅ Invalid membership levels and subscription types
- ✅ Date boundary conditions and formatting edge cases
- ✅ Special characters in customer data (Unicode, HTML, SQL injection attempts)
- ✅ JSON encoding/decoding edge cases
- ✅ Concurrent membership scenarios
- ✅ Membership upgrade/downgrade edge cases
- ✅ API timeout and retry scenarios
- ✅ Memory and performance edge cases

#### **Debug & Logging System** (DebugTest.php)
- ✅ Conditional logging based on DTC_DEBUG constant
- ✅ Verbose mode with DTC_DEBUG_VERBOSE
- ✅ Log message formatting and levels
- ✅ Force logging for critical errors
- ✅ API call logging structure
- ✅ Membership logging structure

#### **Basic PHP Functionality** (BasicTest.php)
- ✅ Basic PHP operations and data types
- ✅ Date functions and array operations
- ✅ String operations and object creation

**Run Tests:** `vendor/bin/phpunit` (execution time: ~0.07 seconds)

## Benefits Achieved

1. **Better Organization**: Related functionality grouped in classes
2. **Namespace Protection**: Avoids function name conflicts
3. **Autoloading**: No manual include/require statements
4. **IDE Support**: Better autocomplete and type hints
5. **Maintainability**: Easier to extend and modify
6. **Modern PHP**: Following current best practices

## Risk Mitigation

### Backward Compatibility
- **No backward compatibility layer**: Clean migration approach
- **All old function calls must be updated**: Prevents mixed approaches
- **Comprehensive testing required**: Ensure nothing breaks

### Rollback Plan
- Git version control allows easy rollback if needed
- Keep old files temporarily until migration is confirmed working
- Test thoroughly in development before production deployment

## Success Criteria

### Phase 1 Complete When:
- [x] All files updated to use new class methods
- [x] All tests passing (50 tests, 370 assertions)
- [x] No PHP errors in browser (fatal error fixed)
- [x] Membership functionality works correctly

### Full Migration Complete When:
- [ ] All functionality converted to class-based approach
- [ ] Old files removed
- [ ] Documentation updated
- [ ] Performance maintained or improved
- [ ] Code is cleaner and more maintainable

## Notes

- **No backward compatibility**: Clean break from old approach
- **One class per file**: Following PSR-4 standards
- **Minimal main plugin file**: Keep initialization simple
- **Comprehensive testing**: Essential due to no backward compatibility
- **Gradual migration**: One functionality area at a time
