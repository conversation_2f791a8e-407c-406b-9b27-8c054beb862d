<?php
use DynastyTradeCalculator\Membership;

$dtc_calculator = new dtc_calculator;

class dtc_calculator {
	protected $read_only;
	
	public function __construct() {
		global $current_user, $post;

		add_shortcode('dtc_calculator', array($this,'shortcode'));
		add_action( 'wp_ajax_dtc_ajax_get_dropdown', array($this,'ajax_get_dropdown'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_get_dropdown', array($this,'ajax_get_dropdown'));
		add_action( 'wp_ajax_dtc_ajax_get_player_input', array($this,'ajax_get_player_input'));	
		add_action( 'wp_ajax_dtc_ajax_user_save_setting', array($this,'ajax_save_user_setting'));	
		add_action( 'wp_ajax_dtc_short_url', array($this,'short_url'));	
		add_action( 'wp_ajax_nopriv_dtc_short_url', array($this,'short_url'));
		add_action('wp',array($this,'wp'));
		add_action('wp',array($this,'dtc_restrict_pages'));
		add_action( 'wp_ajax_dtc_ajax_get_player_stats', array($this,'ajax_load_stats'));		
		add_action( 'wp_ajax_dtc_ajax_get_player_trades_tabs', array($this,'ajax_load_trades'));		
		add_action( 'wp_ajax_dtc_ajax_search_players', array($this,'ajax_search_players'));		
		add_action( 'wp_ajax_nopriv_dtc_ajax_search_players', array($this,'ajax_search_players'));		
		add_action( 'wp_ajax_dtc_ignore_modal', array($this,'dtc_ignore_modal'));	
		
		$this->read_only =false;	
	}
	
	function dtc_ignore_modal(){
		global $current_user;
		$modal = sanitize_text_field( $_POST['modal'] ) ?? '';
		update_user_meta($current_user->ID,'_ignore_'.$modal.'',1);
	}

	function ajax_search_players() {
		global $wpdb;

		$types = array();
		$types = $_POST['list_types'] ?? '';
		$options = '';
		$players = array();
		$unions = '';
		
		if ( isset($_REQUEST['term']) ) {
			$where = 'WHERE name like "%'.sanitize_text_field(dtc_fix_name_query($_REQUEST['term'])).'%" OR position  like "%'.sanitize_text_field(dtc_fix_name_query($_REQUEST['term'])).'%"  OR team  like "%'.sanitize_text_field(dtc_fix_name_query($_REQUEST['term'])).'%"';
		}

		if ( $_REQUEST['offense'] == 1 ) {
			$query = "SELECT * FROM  ".$wpdb->prefix . "dtc_players ".$where." order by name";
			$r['list'] = $wpdb->get_results($query, ARRAY_A);	
		}

		if ( $_REQUEST['devy'] == 1 ) {
			$query = "SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy ".$where."  order by name";
			$r['devy']  = $wpdb->get_results($query, ARRAY_A);	
		}

		if ( $_REQUEST['idp'] == 1 ) {
			$query = "SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp ".$where."  order by name";
			$r['idp']  = $wpdb->get_results($query, ARRAY_A);	
		}

		if ( $r ) {
			foreach($r as $ra){
				if ( $ra ) {
					for ($i = 0; $i < count($ra); $i++) {
						$players[] = array('id'=>$ra[$i]['id'],'value'=>''.stripslashes($ra[$i]['name']).' '.stripslashes($ra[$i]['team']).' - '.$ra[$i]['position'].'','label'=>''.stripslashes($ra[$i]['name']).' '.stripslashes($ra[$i]['team']).' - '.$ra[$i]['position'].'','type'=>$ra[$i]['type']);
					}
				}
			}
		} else {
			echo json_encode(array());die();
		}

		echo json_encode($players);die();
	}

	function ajax_load_trades() {
		global $wpdb;
	
		$dtc_mfl = new dtc_mfl;	
		$rules_array = $_POST['rules'] ?? '';		
		$player_id = $_POST['player_id'] ?? '';
		$mfl_id = $_POST['mfl_id'] ?? '';
		$years = $_POST['stat_years'] ?? '';
		$type = sanitize_text_field($_POST['type'] ) ?? '';
		$playertype = '';

		if ( $type == 'devy' ) {
			$playertype = '_devy';	
		}

		if ( $type == 'idp' ) {
			$playertype = '_idp';	
		}

		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players".$playertype." where id =  %d", $player_id), ARRAY_A);		
		$mfl = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_mfl where mfl_id = %d", $r[0]['mfl_id']), ARRAY_A);
			
		$players = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_players ", ARRAY_A);
		$plarray = array();
		$alltrades = '';

		for ($i = 0; $i < count($players); $i++) {
			$plarray[$players[$i]['mfl_id']] =$players[$i];
		}
				
		if ( $rules_array['rbppc'] == 1 ) {
			$rbppcand = 'AND rbppc >0 ';
		} else {
			$rbppcand = '';
		}
		
		if ( $rules_array['tepre'] == 1 ) {
			$tepreand = 'AND tepre >0 ';
		} else {
			$tepreand = '';
		}
		
		$query = "SELECT *," . $wpdb->prefix . "dtc_mfl_trades.id as tid  FROM `" . $wpdb->prefix . "dtc_mfl_trades` LEFT  JOIN " . $wpdb->prefix . "dtc_mfl_leagues on " . $wpdb->prefix . "dtc_mfl_trades.league_id = " . $wpdb->prefix . "dtc_mfl_leagues.league_id 
				WHERE
				( franchise2_gave_up LIKE '%".$r[0]['mfl_id']."%' OR franchise1_gave_up LIKE '%".$r[0]['mfl_id']."%' )
				AND
				(
				league_format = '".$rules_array['team_format']."' AND
				league_type = '".$rules_array['team_type']."' 
				)
				ORDER by timestamp DESC LIMIT 50";
		/*
		league_size = '".$rules_array['team_size']."' AND 
			".$rbppcand." 
			".$tepreand."
		*/
				
		$trades = $wpdb->get_results($query, ARRAY_A);

		// Function to filter out duplicate items based on name and otherKey
		$trades = array_filter($trades, function($item, $index) use ($trades) {
			$search = array_slice($trades, 0, $index); // Items before the current one
			foreach ($search as $existingItem) {

				if ($existingItem['id'] === $item['id'] && 
					$existingItem['franchise'] === $item['franchise'] && 
					$existingItem['franchise2'] === $item['franchise2'] && 
					$existingItem['franchise1_gave_up'] === $item['franchise1_gave_up'] && 
					$existingItem['franchise2_gave_up'] === $item['franchise2_gave_up'] && 
					$existingItem['timestamp'] === $item['timestamp'] && 
					// $existingItem['league_type'] === $item['league_type'] && 
					// $existingItem['league_format'] === $item['league_format'] && 
					$existingItem['league_id'] === $item['league_id']) {
					return false; // Duplicate found, discard this item
				}
			}
			return true; // No duplicate found, keep this item
		}, ARRAY_FILTER_USE_BOTH);

		$alltrades .= '
		<style type="text/css">
			.dtc-player-card-wrapper{font-family: "Trajan Pro", Arial, Helvetica, sans-serif;}
			.dtc-player-card-wrapper .dtc-calc-item{border-bottom: 1px solid #b1b2b2;clear: both; min-height: 100px;padding-top: 7px;}
			.dtc-player-card-wrapper .dtc-calc-item .dtc-calc-item-player-info{text-align: left;padding-top:10px;}
			.dtc-player-card-wrapper .dtc-calc-item .dtc-calc-item-player-score {padding-top:10px;padding-right:10px;}
			.dtc-player-card-wrapper .dtc-player-picker-left .dtc-calc-item{border-right:1px solid #000;border-left:1px solid #000;}
			.dtc-player-card-wrapper .dtc-player-picker-right .dtc-calc-item{border-right:1px solid #000; border-left:1px solid #000}
			.dtc-player-card-wrapper .dtc-trade-calc-item { margin-top: 20px !important; width: 100%; float: none;}
			.dtc-stats-settings-card{margin-bottom:40px;}
			.dtc-player-card-wrapper .dtc-trade-calc-item .dtc-player-input-total h3 {font-size:20px  !important}
			.dtc-player-card-wrapper .dtc-trade-calc-item h3 {font-size:20px  !important;}
			.dtc-player-card-wrapper .dtc-trade-calc-item {padding:10px 5px;}
			.dtc-player-card-wrapper  .dtc-player-input-total{border-right:1px solid #000;border-bottom:1px solid #000;}
			.dtc-player-card-wrapper  .dtc-player-picker-left .dtc-player-input-total{border-left:1px solid #000;}
			.dtc-player-card-wrapper .dtc-right-total, .dtc-player-card-wrapper .dtc-left-total {}

			.dtc-calc-sub-header{padding:5px;color:#FFF;background-color:#242526;;border-left:1px solid #000;border-right:1px solid #000}
			.dtc-calc-sub-header a, .dtc-player-name-mobile{color:#FFF !important}
			.dtc-calc-sub-header .dtc-calc-subheader-left{float:left; text-align:left;width:50%}
			.dtc-calc-sub-header .dtc-calc-subheader-right{float:left;text-align:right;width:50%}

			@media (max-width: 768px) {
				.dtc-player-card-wrapper .dtc-calc-item{ min-height: 135px;}
				.dtc-player-name-mobile {}
			}
		</style>
		';
				
		if ( $trades ) {		
			$trades = array_values($trades);
				
			for ($i = 0; $i < count($trades); $i++) {
				$player1 = array_filter(explode(",",$trades[$i]['franchise1_gave_up']));
				$player2 = array_filter(explode(",",$trades[$i]['franchise2_gave_up']));
				
				#player 1 gave up
				$player1_gave_up = '';
				$left_total = 0;
				$left_count = 0;
					
				if ( count($player1) > 0 ) {
					foreach($player1 as $player_id) {
						if (strpos($player_id, 'DP') !== false || strpos($player_id, 'FP') !== false) {
							$pick_info = $this->fix_trade_picks($player_id);
							$player_total = dtc_get_player_total($player_id,$rules_array,false, 'draft_picks',$pick_info );
							
							if ( $player_total == '' ) {
								$player_total = 0;	
							}

							$player1_gave_up .=$this-> pick_template_display($pick_info,$player_total	);					
							$left_total +=$player_total;
							$left_count ++;
						} else {
							$player_total = dtc_get_player_total($player_id,$rules_array,true);
							
							if ( $player_total == '' ) {
								$player_total = 0;
							}

							$player1_gave_up .=$this->player_template_display($player_id,dtc_get_player_total($player_id,$rules_array,true));
							$left_count ++;
							$left_total += $player_total;
						}
					}
				}
				
				#player 2 gave up
				$player2_gave_up = '';
				$right_total = 0;
				$right_count = 0;

				if ( count($player2)>0 ) {
					foreach ( $player2 as $player_id ) {
						if (strpos($player_id, 'DP') !== false || strpos($player_id, 'FP') !== false) {
							$pick_info = $this->fix_trade_picks($player_id);	
							$player_total = dtc_get_player_total($player_id,$rules_array,false, 'draft_picks',$pick_info );
							
							if ( $player_total == '' ) {
								$player_total = 0;
							}
								
							$player2_gave_up .=$this-> pick_template_display($pick_info,$player_total	);	
							$right_count++;
							$right_total +=$player_total;
						} else {
							if ( $player_id ) {	
								$player_total = dtc_get_player_total($player_id,$rules_array,true);
								
								if ( $player_total == '' ) {
									$player_total = 0;
								}

								$player2_gave_up .=$this->player_template_display($player_id,$player_total);
								$right_total +=$player_total;
								$right_count++;
							}
						}
					}
				}
				
				## <h4><a target="_blank" href="https://www58.myfantasyleague.com/'.date("Y").'/home/'.$trades[$i]['league_id'].'#0">'.$trades[$i]['league_name'].'</a></h4>
				## <div style="text-align:center"><em>'.date("F j, Y",$trades[$i]['timestamp']).'</em></div>
			
				#end gave ups
				if ( $trades[$i]['franchise1_gave_up'] != '' && $trades[$i]['franchise2_gave_up']  != '' ) {
					$extraleft = 0;
					$extraright = 0;
					$extra_rightd  = '';
					$extra_leftd = '';
				
					if ( $left_count > $right_count ) {
						$extraleft = $left_count - $right_count;	
				
						if ( $extraleft > 0 ) {	
							for ( $j = 1; $j <= $extraleft; $j++ ) {
								$extra_rightd .= '<div class="dtc-calc-item dtc-player-template"></div>';	
							}
						}
					}
				
					if ( $right_count > $left_count ) {
						$extraright = $right_count - $left_count;
						
						if( $extraright > 0 ) {
							for ($k = 1; $k <= $extraright; $k++) {
								$extra_leftd .= '<div class="dtc-calc-item dtc-player-template"></div>';	
							}
						}
					}
				
					if ( $right_count == $left_count ) {
						$extra_rightd = '';
						$extra_leftd = '';
					}

					$alltrades .= '
					<div style="margin-bottom:20px">
        
						<div class="dtc-calc-header">
							<div class="dtc-calc-header-left"><h3>Team One </h3></div>
							<div class="dtc-calc-header-right"><h3>Team Two </h3></div>
							<div style="clear:both"></div>
						</div>
						<div class="dtc-calc-sub-header">
							<div class="dtc-calc-subheader-left"><a target="_blank" href="https://www58.myfantasyleague.com/'.date("Y").'/home/'.$trades[$i]['league_id'].'#0">'.$trades[$i]['league_name'].'</a></div>
							<div class="dtc-calc-subheader-right"><em>'.date("F j, Y",$trades[$i]['timestamp']).'</em></div>
							<div style="clear:both"></div>
						</div>
        	
						<div class="dtc-calc-body">
							<div class="dtc-player-picker dtc-player-picker-left dtc-total-column" data-id="dtc-left-total">
								'.$player1_gave_up.'
								'.$extra_leftd.'
								<div class="dtc-player-input-total left-bottom-round">
									<h3>Total</h3>
									<div class="dtc-left-total dtc-column-total" style="padding-left:2px">'.dtc_format_total($left_total).'</div>
									<span>VAL</span>
								</div>
							</div>
			  				
							<div class="dtc-player-picker dtc-player-picker-right dtc-total-column" data-id="dtc-right-total">
								'.$player2_gave_up.'
								'.	$extra_rightd.'
								<div class="dtc-player-input-total right-bottom-round">
									<h3>Total</h3>
									<div class="dtc-right-total dtc-column-total"  style="padding-left:2px">'.dtc_format_total($right_total).'</div>
									<span>VAL</span>
								</div>
				   			</div>
						</div>
						
						<div style="clear:both"></div>
					</div>
					<div style="clear:both"></div>
					';
				}
			}
		} else {
			$alltrades .= '<h2 style="margin:50px;text-align:center">No trades available for '.$r[0]['name'].' with the selected settings.</h2>';	
		}
		
		$data['trades'] = $alltrades ;
		#end trades
				
		unset($player_total);
		#trade suggestions
		
		if ( $r[0]['type'] == 'idp' ) {
			$player_total = $r[0]['average'];	
		} else {
			$player_total = dtc_get_player_total($r[0]['mfl_id'],$rules_array,true,$r[0]['type']);	
		}
			
		$rules_array['position'] = 'all';
		$trade_suggestions = array();
		#echo dtc_get_top_json($rules_array);
		$rules_array['count'] = 1000;
		$url = dtc_get_top_json($rules_array);
		#echo $url;
				
		$top_list = wp_remote_get($url);
		$top = is_array($top_list) ? json_decode($top_list['body']) : [];
		$top_picks = dtc_get_top_picks_list($rules_array);
		#print_r($top_picks );
		$position_id = count($top) - 4;
					
		foreach( $top as $key=>$value ) {
			if ( $value->modified_average == $player_total ) {
				$position_id =$key;	
			}
		}

		$trade_suggestions[] = isset( $top[$position_id -4] ) ? (array)$top[$position_id -4] : [];
		$trade_suggestions[] = isset( $top[$position_id -3] ) ? (array)$top[$position_id -3] : [];
		$trade_suggestions[] = isset( $top[$position_id -2] ) ? (array)$top[$position_id -2] : [];
		$trade_suggestions[] = isset( $top[$position_id -1] ) ? (array)$top[$position_id -1] : [];
		$trade_suggestions[] = isset( $top[$position_id +1] ) ? (array)$top[$position_id +1] : [];
		$trade_suggestions[] = isset( $top[$position_id +2] ) ? (array)$top[$position_id +2] : [];
		# print_r($rules_array);
		#echo $position_id;
		#print_r($top);
		#$trade_suggestions[] = (array)$top[$position_id +2];	
				
		$data['trade_suggestions_arrays'] = $trade_suggestions;
		usort( $trade_suggestions, function($a, $b) {
			$a['average'] = isset( $a['average'] ) ? $a['average'] : 0;
			$b['average'] = isset( $b['average'] ) ? $b['average'] : 0;
			if ($a['average'] === $b['average']) return 0;
			return ($a['average'] < $b['average']) ? 1 : -1;
		});
					
		$player_sug = '';
		
		for ($x = 0; $x <= 2; $x++) {
			$trade_suggestions[$x]['mfl_id'] = ! empty( $trade_suggestions[$x]['mfl_id'] ) ? $trade_suggestions[$x]['mfl_id'] : '';
			$player_sug .= $this->player_template_display($trade_suggestions[$x]['mfl_id'], dtc_get_player_total($trade_suggestions[$x]['mfl_id'],$rules_array,true,''));	
		}
			
		foreach( $top_picks as $key=>$pick ) {
			if ( $pick['average'] > 0 ) {
				$pick_vales[$key] = $pick['average'];	
			}
		}

		#echo $player_total;
		#print_r($pick_vales);
		$closest = 	dtc_get_closest_number($player_total ,$pick_vales); 
		$closest_pick_key = '';
		$pick_suggestions = array();
		$pick_sug = '';

		foreach($top_picks as $key=>$pick) {
			if ( $closest == $pick['average'] ) {
				$closest_pick_key = $key;
			}
		}	
			
		if ( !isset($top_picks[$closest_pick_key -2]) ) {
			$pick_suggestions[] = $top_picks[0];	
		} else {
			$pick_suggestions[] = $top_picks[$closest_pick_key -2];
		}

		$pick_suggestions[] = $top_picks[$closest_pick_key -3];
		$pick_suggestions[] = $top_picks[$closest_pick_key -4];	
		
		if ( !isset($top_picks[$closest_pick_key -1]) ) {
			$pick_suggestions[] = $top_picks[1];	
		} else {
			$pick_suggestions[] = $top_picks[$closest_pick_key -1];	
		}
				
		$pick_suggestions[] = $top_picks[$closest_pick_key +1];
		
		usort($pick_suggestions, function($a, $b) {
			if ($a['average'] === $b['average']) return 0;
			return ($a['average'] < $b['average']) ? 1 : -1;
		});
									
		#print_r($pick_vales);
		#echo $closest;
		#echo $player_total;
		#print_r($top_picks);
		$player2_gave_up = ! empty( $player2_gave_up ) ? $player2_gave_up : '';	
		
		for ($x = 0; $x <= 2; $x++) {
			#print_r($suggestion);
			if ( $pick_suggestions[$x]['pick'] != '' ) {
				$pick_info =$pick_suggestions[$x]['pick'];	
				$player_total = dtc_get_player_total($player_id,$rules_array,false, 'draft_picks',$pick_suggestions[$x]['pick'] );
				
				if ($player_total == '') {
					$player_total = 0;
				}

				$player2_gave_up .=$this-> pick_template_display($pick_info,$player_total);	
				$pick_sug .=$this-> pick_template_display($pick_info,$player_total	);
			}
		}
			
		$trade_sug = '
		<h2 style="color:#FFF; font-size: 18px; padding: 20px 0px; padding-bottom: 0px;">Value Comps For '.$r[0]['name'].'</h2>
		<div class="dtc-calculator dtc-trade-calc-item">
            <div class="dtc-calc-header">
                <div class="dtc-calc-header-left"><h3>Players</h3></div>
                <div class="dtc-calc-header-right"><h3>Rookie Picks</h3></div>
                <div style="clear:both"></div>
            </div>
        	
            <div class="dtc-calc-body">
				<div class="dtc-player-picker dtc-player-picker-left dtc-total-column" data-id="dtc-left-total">'.$player_sug.'</div>
			</div>
			
			<div class="dtc-calc-body">
				<div class="dtc-player-picker dtc-player-picker-right dtc-total-column" data-id="dtc-left-total">'.$pick_sug.'</div>
			</div>
			 
		</div>
		<div style="clear:both"></div>
		';
			
		$data['trade_suggestion'] = $trade_sug;
		#end trade suggestions
		
		echo json_encode($data);
		die();
	}

	function ajax_load_stats() {
		global $wpdb;

		$rules_array = $_POST['rules'] ?? '';		
		$player_id = $_POST['player_id'] ?? '';
		$mfl_id = $_POST['mfl_id'] ?? '';
		$years = $_POST['stat_years'] ?? '';
		$type = ! empty( $_POST['type']) ? sanitize_text_field( $_POST['type'] ) : '';
		$college = '';
		$birthdate = '';
		$draft = '';

		if ( $years == '' ) {
			$years = '12';	
		}

		$stats_array = array();
		$data = array();
		$stats = array();

		$playertype = '';
	
		if ( $type == 'devy' ) {
			$playertype = '_devy';	
		}

		if ( $type == 'idp' ) {
			$playertype = '_idp';	
		}

    	if ( $playertype == '' ) {
		
		}

		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players".$playertype." where id =  %d", $player_id), ARRAY_A);
	
		$r_mfl_id = $r[0]['mfl_id'] ?? '';		
		$mfl = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_mfl where mfl_id = %d", $r_mfl_id), ARRAY_A);
			
		$data['player'] = $r[0] ?? '';
	
		if ( $type == '' ) {
			$stype = 'list';	
		} else {
			$stype = $type;	
		}

		$all_stats = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_history where pid =  %d AND type = %s", $player_id,$stype), ARRAY_A);
		#echo $wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_history where pid =  %d AND type = %s", $player_id,$stype);exit;

		if ( $all_stats ) {
			foreach( $all_stats as $stat ) {
				$stats_array[$stat['year']] = $stat;
			}
		}	
	
		$data['settings_card'] = dtc_settings_display($rules_array);		
		$labels = array();
		$subtract = 0;

		for ($i = 1; $i <=$years; $i++) {
			$labels[] =  date("M y", strtotime("-".$subtract." months"));
			$value = $stats_array[date("Y", strtotime("-".$subtract." months"))]['v'.date("n", strtotime("-".$subtract." months"))] ?? '';
			
			if ( $value != 0 && $value != '' ) {
				$value = dtc_get_player_total($player_id,$rules_array,false,$type,'',$value);
			}

			$stats['value'][] =$value;
			$stats['rank'][] = $stats_array[date("Y", strtotime("-".$subtract." months"))][date("n", strtotime("-".$subtract." months"))] ?? '';
			$subtract++;
		}
	
		#$data['stats'] =$stats;
		#$data['stats_array'] =$stats_array;
		$data['stats_data']['labels'] = array_reverse($labels);
		$data['stats_data']['value'] = array_reverse($stats['value']);
		$data['stats_data']['rank'] = array_reverse($stats['rank']);
		$stats_exist = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_history where pid = %d and year > %d and type = %s", $player_id,date("Y"),$type), ARRAY_A);			
			
		$r[0]['birthdate'] = $r[0]['birthdate'] ?? '0000-00-00';
		
		if ( $r[0]['birthdate'] != '0000-00-00' ) {
			$birthdate = '<span>'.dtc_get_birthdate($r[0]['birthdate']).'Y</span>';	
		}

		$weight = '';
		$height = '';
		
		if ( $mfl ) {
			if ( $mfl[0]['weight'] != '' ) {
				$weight = '<span>'.$mfl[0]['weight'].' lbs</span>';
			}

			if ( $mfl[0]['height'] != '' ) {
				$height = '<span>'.dtc_readable_height($mfl[0]['height']).' </span>';
			}
		}

		$jersey = '';
		
		if ( ! empty( $mfl[0]['jersey'] ) ) {
			$jersey = ' #'.$mfl[0]['jersey'].'';	
		}

		if ( ! empty( $mfl[0]['college'] ) ) {
			#$college = '<span>'.$mfl[0]['college'].'</span>';	
		}

		if ( ! empty( $mfl[0]['draft_year'] ) && ! empty( $mfl[0]['draft_round'] ) ) {
			$draft = '<br><span>Drafted in '.$mfl[0]['draft_year'].' round '.$mfl[0]['draft_round'].' </span>';	
		}
		
		$r[0]['id'] = $r[0]['id'] ?? '';
		$r[0]['type'] = $r[0]['type'] ?? '';
		$r[0]['name'] = $r[0]['name'] ?? '';
		$r[0]['position'] = $r[0]['position'] ?? '';
		$r[0]['team'] = $r[0]['team'] ?? '';
		
		$player_total = dtc_get_player_total($player_id, $rules_array, false, $type);	
		$data['player_card'] = '
		<div class="dtc-player-card-left">
			'.dtc_build_player_image($r[0]['id'],$r[0]['type'],180).'
		</div>
		<div class="dtc-player-card-right">
			<h2>'.$player_total .'</h2>
			<p>VAL</p>
			<div style="margin-top:10px;text-align:right">'.$this->maybe_show_badge($r[0],95).'</div>
		</div> 
		<div style="clear:both"></div>
									
		<div class="dtc-calc-item-player-info-stats">
			<h2>'.stripslashes($r[0]['name']).'</h2>	
			<span>'.$r[0]['position'].'</span>
			<span>'.$r[0]['team'].'</span>
			'.$birthdate.'
			'.$weight.'
			'.$height.' 	'.$college.'				'.$draft.'
			
			<div style="Font-size:12px">
			</div>
		</div>
		</div>
		'; // extra div?
				
		echo json_encode($data);	
			
		die();
	}

	function fix_trade_picks( $pick ) {
		$picka = explode("_",$pick);
		
		if (strpos($pick, 'DP') !== false) {
			$year = date("Y");
			$round= $picka[1] + 1;
			$pick = $picka[2] + 1;
			$pick = str_pad($pick, 2, '0', STR_PAD_LEFT);
			
			return ''.$year.' '.$round .'.'.$pick .'';
		}
		
		if (strpos($pick, 'FP') !== false) {
			$round= $picka[3] + 1;
			return ''.$picka[2].' '.dtc_ordinal($round) .' (Mid)';
		}
	}

	function dtc_restrict_pages() {
		global $current_user, $post;
		
		$restrict_pages = array(26200);
		
		if ( ! empty( $post->ID ) && in_array($post->ID,$restrict_pages ) && !is_admin()) {
			$redirect = false;
			$has_access = rcp_user_has_access( $current_user->ID, 1);
		
			$member = new RCP_Member( $current_user->ID );
			$status = $member->get_status();
			$expiration_date = strtotime($member->get_expiration_date(false));
			
			if ( $has_access == false or !is_user_logged_in() ) {
				$redirect = true;
			}

			if ( $status != 'active' ) {
				$redirect = true;
			}

			if ( $has_access == true ) {
				$redirect =  false;	
			}

			if ( current_user_can('manage_options') ) {
				$redirect = false;	
			}

			if ( current_user_can('dtc_admin_idp') ) {
				$redirect = false;	
			}
			
			if ( $expiration_date > time() ) {
				$redirect = false;
				$has_access = true;
			}

			if ( $has_access == false && is_user_logged_in() ) {
				wp_redirect('/pricing/');	
				exit;		
			}

			if ( $redirect == true ) {
				wp_redirect('/login/?redirect=/rookierundown/');	
				exit;	
			}
		}
	}

	function wp() {
		global $post, $current_user;
			
		if ( ! empty( $post->ID ) && ( $post->ID == 11916 || $post->ID == 31086 || $post->ID == 33408) ) {	
			#opcache_reset();
			header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
			header("Cache-Control: post-check=0, pre-check=0", false);
			header("Pragma: no-cache");
		}
		
		$read_only = false;
		if ( function_exists('rcp_user_has_access') ) {
			$has_access = rcp_user_has_access( $current_user->ID, 1);
		}

		$member = new RCP_Member( $current_user->ID );
		$status = $member->get_status();
		$expiration_date = strtotime($member->get_expiration_date(false));
		
		if ( $has_access == false or !is_user_logged_in() ) {
			$read_only = true;
		}
	
		if ( $status != 'active' ) {
			$read_only =  true;	
		}
		
		if ( $has_access == true  ||$has_access == 1 ) {
			$read_only =  false;	
		}
		
		if ( current_user_can('manage_options') ) {
			$read_only = false;		
		}

		if ( current_user_can('dtc_admin_idp') ) {
			$read_only = false;	
		}
		
		if ( $expiration_date > time() ) {
			$read_only = false;
		}
		
		if ( ! empty( $post->ID ) && $post->ID ==  11916 ) {
			if ( $read_only == true ) {
				wp_redirect('/pricing/');
				exit;	
			}
		}
	}

	function short_url() {
		$url = $_POST['url'] ?? '';
		echo 1;
		echo dtc_short_url($url);	
		exit;
	}

	function standalone() {
		
	}

	function ajax_save_user_setting() {
		global $current_user;
		
		$_POST['setting'] = $_POST['setting'] ?? '';
		$_POST['value'] = $_POST['value'] ?? '';

		update_user_meta($current_user->ID, '_dtc_setting_' . sanitize_text_field( $_POST['setting'] ) . '', $_POST['value']);
		exit;	
	}

	function get_player($name, $position) {
		
	}

	function ajax_get_dropdown() {
		$mode = $_POST['mode'] ?? '';		
		dtc_calculator::get_dropdown();
		die();	
	}

	function get_template($id, $type) {
		global $wpdb;
		
		if ( $type == 'devy' ) {
			$this->player_template($id,'devy');	
		} elseif( $type == 'idp' ) {
			$this->player_template($id,'idp');	
		} elseif( $type == 'pick' ) {
			$this->pick_template($id);
		} elseif( $type=='player' ) {
			$this->player_template($id);		
		} elseif( $type=='mplayer' ) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players where mfl_id =  %d", $id), ARRAY_A);	
			
			if ( $r != false ) {	
				$this->player_template($r[0]['id']);		
			} else {
				if ( $this->read_only  == false ) {
					echo '<a href="#" class="dtc-add-player">Add Player / Pick </a>';		
				}
			}
		} elseif( $type=='mpick' ) {
			$pick_num = explode("_",$id);
			$pick = ''.$pick_num[0].' ';

			if ( isset($pick_num[2]) ) {
				if ( is_numeric($pick_num[2]) ) {
					$pick .= ''.$pick_num[1].'.'.$pick_num[2].'';	
				} else {
					$pick .= ''.$pick_num[1].' ('.$pick_num[2].')';	
					// Incase we have double brackets caused by old inputs
					$pick = str_replace("))",")",str_replace("((","(",$pick));
				}
			} else {
				$future = isset($pick_num[1]) ? $pick_num[1] : '';
				$pick .= '('.$future.')';
			}

			$query = $wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_draft_picks  where pick  =  %s", $pick);
	
			$r = $wpdb->get_results($query, ARRAY_A);	
		
			if ( $r != false ) {	
				$this->pick_template($r[0]['id']);		
			} else {
				if ( $this->read_only  == false ) {
					echo '<a href="#" class="dtc-add-player">Add Player / Pick </a>';		
				}		
			}
		}
		
	}

	function player_template($id, $type="list", $mfl = false, $return = false) {
		global $wpdb;	
		
		if ( $type == "list" ) {
			$db = "".$wpdb->prefix . "dtc_players";
		} else {
			$db = "".$wpdb->prefix . "dtc_players_".esc_attr($type)."";
		}
		
		if ( $mfl == true ) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$db." where mfl_id = %d", $id), ARRAY_A);
		} else {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$db." where id = %d", $id), ARRAY_A);
		}
		
		if ( $r[0]['birthdate'] != '0000-00-00' ) {
			$birthdate = '<span>'.dtc_get_birthdate($r[0]['birthdate']).'Y</span>';	
		}

		$html  = '';
		$html .= '
		<div class="dtc-calc-item dtc-player-template" data-id="'.$id.'">
		';

		$html .= '
			<h4 class="dtc-player-name-mobile"><a href="#" class="dtc-player-info " data-id="'.$r[0]['id'].'" data-type="'.$r[0]['type'].'">'.stripslashes($r[0]['name']).'</a></h4>
		';
		
		$html .= '
			<div class="dtc-calc-item-image" >'.dtc_build_player_image($r[0]['id'],$type,80).'
				<div class="dtc-calc-item-player-info-stats-mobile">
					<span>'.$r[0]['position'].'</span>
					<span>'.$r[0]['team'].'</span>
					'.$birthdate.'
					
					<div class="dtc-calc-item-player-info-actions-mobile normal-mode player-template">
						<span><a href="#" class="dtc-calc-item-edit"><i class="fa fa-pencil" aria-hidden="true"></i></a></span>
						<!-- <span><a href="'.dtc_get_url($r[0]['id'],$type).'" data-lity><i class="fa fa-info-circle" aria-hidden="true"></i></a></span>-->
						<span><a href="#" class="dtc-remove-item"><i class="fa fa-times-circle" aria-hidden="true"></i></a></span>
					</div>	
				</div>
			</div>
		';

		$html .= '
			<div class="dtc-calc-item-player-info">'.$this->maybe_show_badge($r[0]).'
				<div class="dtc-calc-item-inner" data-pp-linker-content>
					<h4 class="dtc-player-name"><a href="#" class="dtc-player-info" data-id="'.$r[0]['id'].'" data-type="'.$r[0]['type'].'">'.stripslashes($r[0]['name']).'</a></h4>
			
					<div class="dtc-calc-item-player-score-mobile">
						<div class="dtc-calc-item-inner">
							'.number_format($r[0]['average'],1).'
						</div>				
						<span class="dtc-val">VAL</span>
					</div>

					<div class="dtc-calc-item-player-info-stats">
						<span>'.$r[0]['position'].'</span>
						<span>'.$r[0]['team'].'</span>
						'.$birthdate.'
					</div>
		';
					
		$html .= '
					<div class="dtc-calc-item-player-info-actions normal-mode player-template">
		'; 
					
		if ( $this->read_only == false ) {
			$html .= '
						<span><a href="#" class="dtc-calc-item-edit"><i class="fa fa-pencil" aria-hidden="true"></i></a></span>
			';
		}
		
		$html .= '
						<!--<span><a href="'.dtc_get_url($r[0]['id'],$type).'" data-lity><i class="fa fa-info-circle" aria-hidden="true"></i> </a></span> -->
		';
					
		if ( $this->read_only == false ) {	
			$html .= '
						<span style=""><a href="#" class="dtc-remove-item"><i class="fa fa-times-circle" aria-hidden="true"></i></a></span>
			';
		}
					
		$html .='
					</div>	
		';

		$html .='
				</div>
			</div>
		';
	
		$html .= '
			<div class="dtc-calc-item-player-score">	
				<div class="dtc-calc-item-inner">
				'.number_format($r[0]['average'],1).'
				</div>		
				<span class="dtc-val">VAL</span>		
			</div>
		';
		
		$html .= '
			<input type="hidden" data-player="'.stripslashes($r[0]['name']).'" class="dtc-calc-num"  data-mfl-id="'.$r[0]['mfl_id'].'"  data-tier="'.$r[0]['tier'].'"  value="'.$r[0]['average'].'" data-position="'.$r[0]['position'].'" data-qbtier="'.$r[0]['qbtier'].'" data-nonppr="'.$r[0]['nonppr'].'" data-rbppc="'.$r[0]['rbppc'].'">
			<div style="clear:both"></div>
		</div>
		';
		
		if ( $return == true ) {
			return $html;	
		} else {
			echo $html;	
		}

	}
	
	function player_template_display( $id, $score ) {
		global $wpdb;	
		$mfl = false;
		$birthdate = '';

		if ( $id == 0 ) {
			return false;	
		}

		$html  = '';
		$db = "".$wpdb->prefix . "dtc_players";

		$query = $wpdb->prepare("SELECT * FROM (SELECT * FROM " . $wpdb->prefix . "dtc_players UNION SELECT ALL * FROM " . $wpdb->prefix . "dtc_players_devy UNION SELECT ALL * FROM " . $wpdb->prefix . "dtc_players_idp) as t where mfl_id = %d", $id);
	
		$r = $wpdb->get_results($query, ARRAY_A);
		$type = $r[0]['type'] ?? '';
		
		if ( $r == false ) {
			$mfl = true;	
			$query = $wpdb->prepare("SELECT * FROM " . $wpdb->prefix . "dtc_players_mfl WHERE mfl_id = %d", $id);
	
			$r = $wpdb->get_results($query, ARRAY_A);	
		
			$r[0]['birthdate'] = ! empty( $r[0]['birthdate'] ) ? $r[0]['birthdate'] : '0000-00-00';

			$r[0]['birthdate'] = date("Y-m-d",intval( $r[0]['birthdate'] ) );
		}
		
		if ( $r[0]['birthdate'] != '0000-00-00' ) {
			$birthdate = '<span>'.dtc_get_birthdate($r[0]['birthdate']).'Y</span>';	
		}
		
		$r[0]['name'] = ! empty( $r[0]['name'] ) ? $r[0]['name'] : '';
		$r[0]['id'] = ! empty( $r[0]['id'] ) ? $r[0]['id'] : '';
		$r[0]['position'] = ! empty( $r[0]['position'] ) ? $r[0]['position'] : '';
		$r[0]['team'] = ! empty( $r[0]['team'] ) ? $r[0]['team'] : '';

		$html .= '
		<div class="dtc-calc-item dtc-player-template" data-id="'.$id.'">
		';
		$html .= '
			<h4 class="dtc-player-name-mobile">'.stripslashes($r[0]['name']).'</h4>
		';
		$html .= '
			<div class="dtc-calc-item-image" >'.dtc_build_player_image($r[0]['id'],$type,80).'
				<div class="dtc-calc-item-player-info-stats-mobile">
					<span>'.$r[0]['position'].'</span>
					<span>'.$r[0]['team'].'</span>
					'.$birthdate.'
				</div>
			</div>
		';

		$html .= '
			<div class="dtc-calc-item-player-info">
				<div class="dtc-calc-item-inner" data-pp-linker-content>
					<h4 class="dtc-player-name">'.stripslashes($r[0]['name']).'</h4>
					<div class="dtc-calc-item-player-score-mobile">
						<div class="dtc-calc-item-inner">
							'.$score.'
						</div>
						<span class="dtc-val">VAL</span>
					</div>

					<div class="dtc-calc-item-player-info-stats">
						<span>'.$r[0]['position'].'</span>
						<span>'.$r[0]['team'].'</span>
						'.$birthdate.'
					</div>
		';
		
		$html .='
				</div>
			</div>
		';
	
		$html .= '
			<div class="dtc-calc-item-player-score">	
				<div class="dtc-calc-item-inner">
					'.$score.'
				</div>		
				<span class="dtc-val">VAL</span>		
			</div>
		';
		
		$html .= '
			<div style="clear:both"></div>
		</div>
		';
		
		return $html;	
	}

	function show_dtc2020() {
		$settings = get_option('dtc_settings');
		
		if ( $settings['badge_display']== 0 ) {
			return false;
		}

		if ( $settings['badge_display']== 1 && !current_user_can('manage_options') ) {
			return false;
		}

		return true;
	}

	function maybe_show_badge_toggle() {
		$settings = get_option('dtc_settings');
		
		if ( $settings['badge_display']== 0 ) {
			return '';	
		}

		if ( $settings['badge_display']== 1 && !current_user_can('manage_options') ) {
			return '';	
		}

		echo '   
		<h3>Settings</h3>
		
		<div class="dtc-filter-buttons dtc-clear-filter-button">
        	<a href="#" class="dtc-under-calc-buttons left-top-round left-bottom-round right-top-round right-bottom-round dtc-showbadges" data-id="badges">Disable Badges</a>
			<a href="#dtc-mfl-modal" class="dtc-under-calc-buttons left-top-round left-bottom-round right-top-round right-bottom-round">League Import</a>
        </div>
        ';
	}
	
	function maybe_show_badge($r, $width = false ) {
		global $wpdb;

		$settings = get_option('dtc_settings');
		
		if ( $width != false ) {
			$bwidth = $width;	
		} else {
			$bwidth = $settings['badge_width'];	
		}

		if ( $settings['badge_display']== 0 ) {
			return '';	
		}

		if ( $settings['badge_display']== 1 && !current_user_can('manage_options') ) {
			return '';	
		}
		
		$trades = get_transient('dtc_get_latest_monthly');
		$is_top = false;	
		
		if ( ! empty( $trades) && ! empty ( $trades['Last Month'] ) ) {
			foreach($trades['Last Month'] as $top_month){
				if ( isset( $top_month['mfl_id'], $r['mfl_id'] ) && $top_month['mfl_id'] == $r['mfl_id'] ) {
					$is_top = true;
				}
			}
		}

		#$is_top = false;
		if ( empty( $r['badge_id'] ) && $is_top == false ) {
			return '';	
		}
		
		$selected = unserialize($r['badge_id']);
		
		if ( $is_top == true ) {
			$selected[] = 32;	
		}
		
		$sel_c = implode(",", $selected);
		
		if ( count($selected) == 0 ) {
			return '';	
		}

		$query ="SELECT * FROM  ".$wpdb->prefix . "dtc_badges where id IN(".$sel_c.")";
	
		$badge = $wpdb->get_results($query, ARRAY_A);
		
		if ($badge == false) {
			return '';	
		}
		
		$badges_html = '<div class="dtc-badge-logo "><div class="cycle-slideshow"  data-cycle-fx="scrollHorz">';
		
		foreach( $badge as $b ) {
			$badges_html .= '<img src="'.$b['image'].'" style="width:'.$bwidth .'px;">';	
		}

		$badges_html .='</div></div>';
	
		return $badges_html;
	}
	
	function pick_template_display( $pick, $value ) {
		global $wpdb;	
		
		$html = '';
		
		$html .= '
		<div class="dtc-calc-item dtc-pick-template dtc-pick-template-rookie">
			<h4 class="dtc-player-name-mobile">Rookie Pick</h4>
			
			<div class="dtc-calc-item-image dtc-calc-item-image-pick">
				<div class="dtc-image-item-wrapper">
					<img src="'.DTC_URL.'/asetts/headshots/RookiePick.png'.'">
				</div>
			<div>

			<div class="dtc-calc-item-player-info-stats-mobile dtc-calc-item-player-info-stats-pics">
				'.$pick.'
			</div>
	
		</div>
		</div>'; // extra div?
			
		$html .= '
		<div class="dtc-calc-item-player-info">
			<div class="dtc-calc-item-inner">
				<h4 class="dtc-player-name">Rookie Pick</h4>
				
				<div class="dtc-calc-item-player-info-stats dtc-calc-item-player-info-stats-pics">
					<span>'.$pick.'</span>
				</div>
					
				<div class="dtc-calc-item-player-score-mobile">
					<div class="dtc-calc-item-inner">
						'.$value.'
					</div>
					<span class="dtc-val">VAL</span>
				</div>
		';
				
		$html .= '
				</div>
			</div>';
			
		$html .='	
			<div class="dtc-calc-item-player-score">
				<div class="dtc-calc-item-inner">
				'.$value.'
				</div>
				<span class="dtc-val">VAL</span>
			</div>
		';
			
		$html .='
			<div style="clear:both"></div>
		</div>
		';
		
		return $html;
	}

	function pick_template( $id, $by_name = false, $return = false ) {
		global $wpdb;	
		
		$_POST['team_size'] = $_POST['team_size'] ?? '';
		$_POST['team_type'] = sanitize_text_field( $_POST['team_type'] ) ?? '';


		$team_size = dtc_number_to_name($_POST['team_size']);
		
		if ($_POST['team_type']=='2qb'|| $_POST['team_type']=='sf') {
			$value_key = $team_size.'sf';
		} else {
			$value_key = $team_size;
		}
		
		if ($by_name == false) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_draft_picks where id = %d", $id), ARRAY_A);
		} else {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_draft_picks where pick = %s", $id), ARRAY_A);	
		}
		
		$html = '';
		$html .= '
		<div class="dtc-calc-item dtc-pick-template">
			<h4 class="dtc-player-name-mobile"><a href="#">Rookie Pick</a></h4>
			
			<div class="dtc-calc-item-image"><div class="dtc-image-item-wrapper" style="border-radius:50%;overflow: hidden;border:2px solid #e0b82f;margin:3px;"><img src="'.DTC_URL.'/asetts/headshots/RookiePick.png'.'"></div>
			<div>
				<div class="dtc-calc-item-player-info-stats-mobile dtc-calc-item-player-info-stats-pics">
					<span>'.$r[0]['pick'].'</span>
				</div>
				
				<div class="dtc-calc-item-player-info-actions-mobile normal-mode pick-template">
					<span><a href="#" class="dtc-calc-item-edit"><i class="fa fa-pencil" aria-hidden="true"></i></a></span>
					<span><a href="#" class="dtc-remove-item"><i class="fa fa-times-circle" aria-hidden="true"></i></a></span>
				</div>	
			
			</div>
		</div>
		';
			
		$html .='
		<div class="dtc-calc-item-player-info">
			<div class="dtc-calc-item-inner">
				<h4 class="dtc-player-name"><a href="#">Rookie Pick</a></h4>
				
				<div class="dtc-calc-item-player-info-stats dtc-calc-item-player-info-stats-pics">
					<span>'.$r[0]['pick'].'</span>
				</div>
				
				<div class="dtc-calc-item-player-score-mobile">
					<div class="dtc-calc-item-inner">
					'.number_format($r[0][$value_key],1).'
					</div>
					<span class="dtc-val">VAL</span>
				</div>
					
				<div class="dtc-calc-item-player-info-actions normal-mode pick-template">
		';
		
		if ($this->read_only == false) {
			$html .='
					<span><a href="#" class="dtc-calc-item-edit"><i class="fa fa-pencil" aria-hidden="true"></i></a></span>
					<span><a href="#" class="dtc-remove-item"><i class="fa fa-times-circle" aria-hidden="true"></i></a></span>
			';
		}
				
		$html .= '
				</div>	
			</div>
		</div>
		';
			
		$html .= '	
		<div class="dtc-calc-item-player-score">
			<div class="dtc-calc-item-inner">
				'.number_format($r[0][$value_key],1).'
			</div>
			<span class="dtc-val">VAL</span>
		</div>
		';
			
		$pick_explode = explode(" ", $r[0]['pick']);
		$pick_num = explode(".", $pick_explode[1]);
		
		if ( isset($pick_explode[2]) ) {
			$pick = $pick_explode[2];	
		} else {
		 	$pick = $pick_num[1];
		}
			
		$html .= '
		<input data-pick-type="rookie"  type="hidden" data-player="'.$r[0]['pick'].'" data-year="'.$pick_explode[0].'" data-round="'.$pick_num[0].'" data-pick="'.$pick.'" class="dtc-calc-num" value="'.$r[0][$value_key].'" data-tier="0" data-position="pick" data-ten="'.$r[0]['ten'].'" data-twelve="'.$r[0]['twelve'].'" data-fourteen="'.$r[0]['fourteen'].'" data-sixteen="'.$r[0]['sixteen'].'" data-tensf="'.$r[0]['tensf'].'" data-twelvesf="'.$r[0]['twelvesf'].'" data-fourteensf="'.$r[0]['fourteensf'].'" data-sixteensf="'.$r[0]['sixteensf'].'">
		<div style="clear:both"></div>
		</div>'; // extra div?
		
		if ( $return == true ) {
			return $html;
		} else {
			echo $html;	
		}
	}

	function ajax_get_player_input() {
		$type = sanitize_text_field( $_POST['type'] ) ?? '';

		$_POST['id'] = $_POST['id'] ?? '';
		$_POST['type_player'] = sanitize_text_field( $_POST['type_player'] ) ?? '';

		if ($type == 'player') {
			dtc_calculator::player_template($_POST['id'],$_POST['type_player']);	
		}

		if ($type == 'pick') {
			dtc_calculator::pick_template($_POST['id']);	
		}
		
		do_action('dtc_calculator/ajax_get_player_input',$_POST);
		
		die();
	}

	function get_dropdown() {
		global $wpdb;
	
		$types = array();
		$types = ! empty( $_POST['list_types'] ) ? $_POST['list_types'] : $types;
		$_POST['size'] = $_POST['size'] ?? '';
		$options = '';
		
		if ( empty( $_POST['custom_class'] ) ) {
			$class = 'dtc-choose-player dtc-dropdown-select';	
		} else {
			$class = sanitize_text_field( $_POST['custom_class'] );	
		}

		echo '<div class="dtc-player-select2"><select class="'.$class .'" placeholder="Select A Player"><option value="">Select A Player</option>';	
		
		if ( empty( $_POST['picks'] ) ) {
			$r = get_transient('dtc_picks_dropdown_query');
			
			if ($r == false) { 			
				$r = $wpdb->get_results("SELECT * FROM ".$wpdb->prefix . "dtc_draft_picks WHERE dropdown_active = 0 ORDER BY pick ASC", ARRAY_A);				
				set_transient( 'dtc_picks_dropdown_query', $r, 5 * HOUR_IN_SECONDS );	
			}
			
			if ($r) {
				for ($i = 0; $i < count($r); $i++) {
					if(($_POST['size'] == '10' && $r[$i]['ten'] != 0) or ($_POST['size'] == '12' && $r[$i]['twelve'] != 0) or ($_POST['size'] == '14' && $r[$i]['fourteen'] != 0) or ($_POST['size'] == '16' && $r[$i]['sixteen'] != 0)){
						if(is_numeric($r[$i]['pick'])){
							$pick = number_format($r[$i]['pick'],2);
						} else {
							$pick = $r[$i]['pick'];
						}
						$r[$i]['nonppr'] = $r[$i]['nonppr'] ?? '';
						$options .= '<option value="'.$r[$i]['id'].'" data-id="pick" data-nonppr="'.$r[$i]['nonppr'].'" data-ten="'.$r[$i]['ten'].'" data-twelve="'.$r[$i]['twelve'].'" data-fourteen="'.$r[$i]['fourteen'].'" data-sixteen="'.$r[$i]['sixteen'].'" data-tensf="'.$r[$i]['tensf'].'" data-twelvesf="'.$r[$i]['twelvesf'].'" data-fourteensf="'.$r[$i]['fourteensf'].'" data-sixteensf="'.$r[$i]['sixteensf'].'" data-type="">'.$pick.'</option>';	
					}
				}
			}

			unset($r);
		}
		
		// $r = get_transient('dtc_players_dropdown_querys_'.implode('_',$types).'');
		if( empty( $r ) ){
			$unions = '';
			if(in_array("devy",$types)){
				$unions = " UNION SELECT ALL * FROM ".$wpdb->prefix . "dtc_players_devy ";	
			}
			$unions = apply_filters('dtc_calculator/get_dropdown/unions',$unions,$types);
			$query = "SELECT * FROM ".$wpdb->prefix . "dtc_players ".$unions." ORDER BY name";
			$r = $wpdb->get_results($query, ARRAY_A);
			// set_transient( 'dtc_players_dropdown_querys_'.implode('_',$types).'', $r,  HOUR_IN_SECONDS );			
		}

		if($r){
			for ($i = 0; $i < count($r); $i++) {
				if ( $r[$i]['position'] === 'K' || $r[$i]['position'] === 'DEF' ) {
					continue;
				}
				
				if($r[$i]['type'] != "list"){
					$display = 'style="display:none"';	
				} else {
					$display = '';	
				}
				if(in_array($r[$i]['type'],$types)){
					$options .= '<option value="'.$r[$i]['id'].'" data-id="player"  data-nonppr="'.$r[$i]['nonppr'].'" data-type="'.$r[$i]['type'].'"  '.$display.'>'.stripslashes($r[$i]['name']).' - '.$r[$i]['position'].'</option>';	
				}
			}
		}
			
		echo apply_filters('dtc_calculator/dropdown/options',$options,$_POST,$types);
		echo '</select></div>';
		$this->js();	
	}

	function js() {
		$settings = get_option('dtc_settings');
		?>	
    	
    	<script type="text/javascript">
			jQuery(document).ready(function($) {
				$(".dtc-dropdown-select").select2({
					placeholder: "Select a player",
					allowClear: true,
					width: "100%" 
				});
		
				$(".dtc-dropdown-select").select2('open');
				
				$(".dtc-dropdown-select").on("select2:select", function(sel) { 
		 			var type = $(this).find(":selected").data("id");
		  			var type_player = $(this).find(":selected").data("type");
		  			console.log(type_player );
		  			var value = $(this).find(":selected").val();
		   			var value_text = $(this).find(":selected").text();
		   
		   			if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Selected Player: '+ value_text, 'Selected Player: '+ value_text);
					console.log('Selected Player: '+ value_text);
					var team_size = $(".dtc-team-size-val").val();
					var team_type = $(".dtc-team-type-val").val();
		
					var parent = $(this).parent();
					console.log(type);
					console.log(value);
						
					$.post(dtc.ajaxurl, {
						"action":"dtc_ajax_get_player_input",
						"id": value, 
						"type": type,
						"team_size":team_size,
						"team_type":team_type,
						"type_player":type_player,
						'settings':dtc_get_rules()
					}, function(response) {
						parent.html(response);
						recalculatePlayers();
						$('.cycle-slideshow').cycle();
						
						if($(".dtc-showbadges").attr('data-enabled') == 1){
							$(".dtc-badge-logo").hide();
						}else{
							$(".dtc-badge-logo").show();	
						}
					});	
				});
			});
		</script>
    	
		<?php do_action('calculator_inline_js'); ?>
    	<?php	
	}
    
    function get_featured_poll() {
     	$settings = get_option('dtc_settings');
        
		if(($settings['polls_display'] == 1 or $settings['polls_display'] == 2) && $settings['featured_poll'] != '' ){ 
      		if($settings['polls_display'] == 2 or ($settings['polls_display'] == 1 && current_user_can('manage_options')) ){
       			echo  do_shortcode('<div class="featured-poll-wrapper">[gravityform id="'.$settings['featured_poll'] .'" title="false" description="false" ajax="true"]</div>');
     			?>
				<script type="text/javascript">
					jQuery(document).ready(function($) {
						var strings, resultsButton, backButtonMarkup;
						console.log('poll loaded');
						strings = gpoll_strings;
						resultsButton = '<a href="/polls/" class="">View All Polls</a>';
						
						$(".gform_wrapper.gpoll_enabled form, .gform_wrapper.gpoll_enabled_wrapper form").each(function(){
							if (jQuery(this).hasClass("gpoll_show_results_link"))
								$(".gpoll_show_results_link_wrapper").parent().append(resultsButton);
							$(this).find(".gform_button").parent().append(resultsButton);
						});
					});
				</script>
				<?php
 			} 
		}
        
    }

	function shortcode($atts) {
		$settings = get_option('dtc_settings');
		
		global $wpdb, $current_user;
		$total_spaces = 4;
		$has_access =  dtc_is_user();
		
		if ($this->read_only == true) {
			echo '<div class="dtc-register-now"><h1><a href="/pricing" style="color:#FFF !important;font-size:25px;">Like this Calculator? Click here to sign up for full access to players and settings. </a> <a style="color:#FFF !important;" href="/login">Click here to login</a></h1></div>';	
		}
		
		$leauge_format_alt = '';

		if ( ! empty( $_REQUEST['left-side'] ) ) {
			$left_side = sanitize_text_field($_REQUEST['left-side']);	
		} else {
			$left_side = get_user_meta($current_user->ID, '_dtc_setting_left_side',true);	
			
			if ($left_side == '') {
				$left_side = '';
			}
		}
			
		$leauge_format_alt_arr = explode(',',$leauge_format_alt);
		
		if ($left_side != '') {
			$left_side_arr = explode(',',$left_side);
		} else {
			$left_side_arr = array();	
		}

		array_filter($left_side_arr);

		if ( ! empty( $_REQUEST['right-side'] ) ) {
			$right_side = sanitize_text_field($_REQUEST['right-side']);	
		} else {
			$right_side = get_user_meta($current_user->ID, '_dtc_setting_right_side',true);	
			
			if ($right_side == '') {
				$right_side = '';
			}
		}
			
		$leauge_format_alt_arr = explode(',',$leauge_format_alt);
		
		if ($right_side != '') {
			$right_side_arr = explode(',',$right_side);
		} else {
			$right_side_arr = array();	
		}
			
		array_filter($right_side_arr);
		?>
        
        <!-- <a href="#" class="export-png">Save PNG</a>--><div id="img-out"></div>
        <div class="dtc-calculator-wrapper-remove">
			<?php
				$rotogpt_subscription = null;
				$current_membership = Membership::getCurrentUserMembership();
				if ($current_membership) {
					$rotogpt_subscription = Membership::getRotoGptSubscription($current_membership);
				}
			?>
        	<div class="dtc-calculator-wrapper <?php echo $rotogpt_subscription ? 'with-rotogpt' : 'no-rotogpt' ?>">
			
				<?php include plugin_dir_path(__FILE__) . 'league-integration/league-integration.php'; ?>
        
				<div id="main_calculator" class="dtc-calculator">
					<div class="dtc-calc-header">
						<div class="dtc-calc-header-left"><h3>Give</h3></div>
						<div class="dtc-calc-header-right"><h3>Receive</h3></div>
						<div style="clear:both"></div>
					</div>
				
					<div class="dtc-calc-body">
				
						<div class="dtc-player-picker dtc-player-picker-left dtc-total-column" data-id="dtc-left-total">
							<?php
								$this->output_rows($left_side_arr);
							?>
	
							<div class="dtc-player-input-total left-bottom-round">
								<h3>Total</h3>
								<div class="dtc-left-total dtc-column-total">0.0</div>
								<span>VAL</span>
							</div>
						</div>
						
						<div class="dtc-player-picker dtc-player-picker-right dtc-total-column" data-id="dtc-right-total">
							<?php
								$this->output_rows($right_side_arr);
							?>

							<div class="dtc-player-input-total right-bottom-round">
								<h3>Total</h3>
								<div class="dtc-right-total dtc-column-total">0.0</div>
								<span>VAL</span>
							</div>
						</div>
						<div style="clear:both"></div>
					</div>
				</div>

				<?php
				if ($rotogpt_subscription) {
					$access_token = Membership::rotoGptSignin($current_membership, $rotogpt_subscription);
					?>
				
					<div id="rotogpt_widget" class="rotogpt_widget" sessionid="g45" settings=""></div>
					<script type="text/javascript">
						// For debugging purpose
						console.log('rotogpt widget loaded');
						console.log('current user: ' + "<?php echo $current_user->ID; ?>");
						console.log('rotogpt_subscription: ' + "<?php echo $rotogpt_subscription; ?>");
						console.log('current_membership: ' + "<?php echo $current_membership->get_id(); ?>");
						console.log('current_membership -> user_id: ' + "<?php echo (string) $current_membership->get_customer()->get_id(); ?>");
						console.log('current_membership -> sign_up_date: ' + "<?php echo date('d-m-Y H:i:s', strtotime($current_membership->get_activated_date())); ?>");

						console.log('access token: ' + "<?php echo $access_token; ?>");
						window.rotoGPT_access_token = "<?php echo $access_token; ?>";
						jQuery(document).ready(function($) {
							var intervalId = window.setInterval(function(){
								window.rotoGPT_LeagueSettings = dtc_get_rules_forRotoGPT();
							}, 500);
						});
					</script>
					<script src="<?php echo DTC_IS_PRODUCTION ? 'https://storage.googleapis.com/chat-widget-roto-gpt-prod-zyx/chat-widget/rotogpt-widget.min.js' : 'https://storage.googleapis.com/chat-widget-roto-gpt-dev-zyx/chat-widget/rotogpt-widget.min.js' ?>"></script>
					<?php
					}
					?>
				<div style="clear:both"></div>
			</div>
        </div>
		<div style="clear:both"></div>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
				$(".dtc-recalculate-calculator").trigger( "click" );
			});
		</script>
    
     	<div class="remodal-grey" style="display:none">
        	<div style="background-color:#343536 !important" data-remodal-id="dtc-idp-modal" >
           		<button data-remodal-action="close" class="remodal-close"></button>
               	<div style="padding:20px">
          			<h2 style="color:#FFF;margin-bottom:20px">We're excited to introduce the addition of IDP into the Dynasty Trade Calculator!<br />Some information about the new IDP function</h2>
					<div style="
						color: #FFF;
						text-align: left;
						max-width: 750px;
						margin: 0px auto;
						margin-top: 40px;
					">
						<p>&bull; The IDP values are powered by our partners; <img style="width:40px" src="<?php echo DTC_URL; ?>/asetts/images/idpguys.png" />. For more IDP content, visit <a href="http://idpguys.org" target="_blank">idpguys.org</a></p>
						<p>&bull; Our IDP values are based on basic league rosters & scoring settings: 6 total IDPs (2 LB, 2 DT, 2DB)</p>
						<p>&bull; We are currently working to develop additional IDP calculator settings for more accuracy in your trade evaluations.</p>
					</div>

					<div class="modal-buttons" style="margin-bottom:50px;margin-top:50px;">
						<a href="#" class="dtc-yellow-button" data-remodal-action="close" class="remodal-close">Close Window</a>
					</div>
        		</div>
    		</div> 
         
        	<div class="remodal remodal-black" data-remodal-id="dtc-player-modal"  data-remodal-options="hashTracking: false">
     			<button data-remodal-action="close" class="remodal-close"></button>
     			
				<div class="dtc-player-card-wrapper">
					<div class="dtc-player-card-tabs">
						<a href="#" data-id="dtc-player-information" class="active"  data-dtc-id="" data-mfl-id="">Player Info</a> 
						<a href="#"  data-id="dtc-player-recent-trades" data-dtc-id="" data-mfl-id="">Trade Wire</a> 
						<a href="#" data-id="dtc-player-recent-suggestions"  data-dtc-id="" data-mfl-id="">Value Comps</a>
					</div>
     
     				<div class="dtc-player-information dtc-player-profile-tab" style="display:block">
      					<div class="dtc-stats-player-card">
         				</div>     
         				
						<div class="dtc-stats-settings-card"></div> 
          				
						<div style="clear:both"></div>
						<div style="text-align:right;margin:10px 0px">
							<button data-remodal-action="close" class="remodal-close"></button>
						
							<select class="stat-years" data-player-id="0" data-type="">
								<option value="12">Last 12 Months</option>
								<option value="24">Last 24 Months</option>
							</select>
						</div>

						<div class="ranking-chart-item">
						
						</div>
					</div>

					<div class="dtc-player-recent-trades dtc-player-profile-tab">
						<div class="dtc-stats-player-card">
							
						</div>   
     					
						<div class="dtc-stats-settings-card"></div>
     					<div class="dtc-stats-recent-trade-content">
       						<div style="text-align:center;padding:100px"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div> 
        				</div>
    				</div>

					<div class="dtc-player-recent-suggestions dtc-player-profile-tab">
						<div class="dtc-stats-player-card">
         			</div>

         			<div class="dtc-stats-settings-card"></div>
         			<div class="dtc-stats-trade-suggestion-content">
        				<div style="text-align:center;padding:100px"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div>
        			</div>   
				</div>
			</div>
  		</div>
    	</div>
		<!-- extra div?  -->
    
		<script type="text/javascript">
			jQuery(document).ready(function($) {

			<?php
			if ($this->show_dtc2020() == true) {
				?>
			<?php }?>
			});
		</script>
    	<?php
	
		do_action('dtc_calculator_bottom');
	}
	
	function output_rows($left_side_arr) {
		$total_left_side = 1;
		
		if (count($left_side_arr)>0) {
			foreach($left_side_arr as $player){
				$player_ar = explode("-",$player);
				
				if ( in_array($player_ar[0], array('player','pick','devy','mplayer','mpick'))) {
					echo ' 
						<div class="dtc-player-input dtc-player-input-items">
					';			
					
					$this->get_template($player_ar[1],$player_ar[0]);
					$total_left_side -= 1;
					echo '</div>';		
				}
			}
			
			#echo '	recalculatePlayers();';	
		}
							
		if( $total_left_side <=0){
			echo '<div class="dtc-player-input dtc-player-input-items">';
			if($this->read_only == false){
				echo '<a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a>';	
			}
			echo'</div>';	
		} else {
			for ($x = 0; $x <= $total_left_side; $x++) {
				$num = $x +1;
				$tot = $total_left_side + 1;
					
				if ($num == $tot) {
					$last = 'dtc-add-another';	
				} else {
					$last = '';	
				}
				
				echo '<div class="dtc-player-input dtc-player-input-items">';
				if ($this->read_only == false) {
					echo '<a href="#" class="dtc-add-player '.$last.'">Add Player / Pick </a>';
				}
								
				echo'</div>';
			} 
		}
	}
	
}