# A set of files you probably don't want in your WordPress.org distribution
.deploy
.distignore
.DS_Store
.editorconfig
.git
.github
.gitignore
.gitmodules
*.sql
*.tar.gz
*.zip
bin
composer.json
composer.lock
Gruntfile.js
node_modules
package.json
phpcs.xml.dist
phpcs.ruleset.xml
phpstan.neon.dist
phpunit.xml
phpunit.xml.dist
README.md
tests
Thumbs.db
wp-cli.local.yml
.DS_Store

# Composer vendor directory - exclude all except essential autoload files
vendor/*
!vendor/autoload.php
!vendor/composer/
vendor/composer/*
!vendor/composer/autoload_*.php
!vendor/composer/ClassLoader.php
!vendor/composer/InstalledVersions.php
!vendor/composer/installed.php
!vendor/composer/installed.json
!vendor/composer/LICENSE

# Test-related files and directories (security)
tests/MembershipTest.php
tests/MembershipMockTest.php
tests/MembershipApiTest.php
tests/MembershipEdgeCasesTest.php
.phpunit.result.cache

# Membership-related documentation (security)
docs/membership-tests-summary.md

# Example files (remove when not needed)
src/Example/