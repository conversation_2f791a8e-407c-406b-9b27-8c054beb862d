if ('serviceWorker' in navigator) {
  window.addEventListener('load', function() {
    navigator.serviceWorker.register('/sw.js').then(function(registration) {
      // Registration was successful
      ////console.log('ServiceWorker registration successful with scope: ', registration.scope);
    }, function(err) {
      // registration failed :(
     // //console.log('ServiceWorker registration failed: ', err);
    });
  });
}

// Add event listener to listen for messages from the child
window.addEventListener('message', function(event) {
		// Check if the message is from a trusted source (optional)

		// Check if the message contains the action to scroll the mainArea into view
		if (event.data && event.data.action === 'scrollIntoView') {
			// Get a reference to the mainArea div in the parent document
			var mainArea = document.getElementById(event.data.targetId);
			
			// Scroll the parent document to make the mainArea div visible
			if (mainArea) {
				mainArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
			} else {
				console.error('Main area element not found in the parent document.');
			}
		}
	}, false);

function hasTouch() {
    return 'ontouchstart' in document.documentElement
           || navigator.maxTouchPoints > 0
           || navigator.msMaxTouchPoints > 0;
}

if (hasTouch()) { // remove all :hover stylesheets
    try { // prevent exception on browsers not supporting DOM styleSheets properly
        for (var si in document.styleSheets) {
            var styleSheet = document.styleSheets[si];
            if (!styleSheet.rules) continue;

            for (var ri = styleSheet.rules.length - 1; ri >= 0; ri--) {
                if (!styleSheet.rules[ri].selectorText) continue;

                if (styleSheet.rules[ri].selectorText.match(':hover')) {
                    styleSheet.deleteRule(ri);
                }
            }
        }
    } catch (ex) {}
}

function dtc_top_subscribe(){
	var inst = jQuery('[data-remodal-id=dtc-login]').remodal();
		inst.open();
}

function dtc_log(logdata){
	console.log(logdata);
}

function dtc_clear_calculator(){
	var calc_template_left_go = '<div class="dtc-player-input"><a href="#" class="dtc-add-player ">Add Player / Pick </a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick </a></div>';
	var calc_template_right_go = '<div class="dtc-player-input "><a href="#" class="dtc-add-player ">Add Player / Pick </a></div><div class="dtc-player-input "><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick </a></div>';
	
	jQuery(".dtc-player-input").remove(); 
	jQuery(".dtc-player-input").remove();
	jQuery(".dtc-column-total").text('0.0');
	jQuery("#main_calculator .dtc-player-picker-left .dtc-player-input-total").before(calc_template_left_go);
	jQuery("#main_calculator .dtc-player-picker-right .dtc-player-input-total").before(calc_template_right_go);	
}

function dtc_ga_track(cat,action){
			 if (window.__gaTracker) __gaTracker('send', 'event',cat, action,action);
}

function dtc_get_rules(){
	var team_size = 0;
	var  team_type = 0;
	var team_format = 0;
	var tepre = 0;
	var rbppc = 0;
	var devy = 0;
	var idp = 0;
	var offense = 1;
	var mode ="normal";
	var startup_rookie = "no";

	jQuery( ".dtc-leauge-size" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_size = jQuery(this).attr('data-id');	
		}
	});
		
	jQuery( ".dtc-leauge-type" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_type = jQuery(this).attr('data-id');	
		}
	});
		
	jQuery( ".dtc-leauge-format" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_format = jQuery(this).attr('data-id');	
		}
	});
		
	jQuery( ".dtc-calculator-mode" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			mode = jQuery(this).attr('data-id');	
		}
	});

	if (mode == "startup") {
		startup_rookie = jQuery(".dtc-under-calc-button-startup").attr('data-rookie-draft');
	}
		
	if(jQuery('.dtc-te-premium-actions').attr('data-enabled') == 1){
		tepre = 1;	
	} else {
		tepre = 0;	
	}
		
	if(jQuery('.dtc-rb-ppc-premium-actions').attr('data-enabled') == 1){
		rbppc = 1;	
	} else {
		rbppc = 0;	
	}
	
	if(jQuery('.dtc-devy-actions').attr('data-enabled') == 1){
		devy = 1;	
	} else {
		devy= 0;	
	}

	if(jQuery('.dtc-idp-actions').attr('data-enabled') == 1){
		idp = 1;	
	} else {
		idp= 0;	
	}

	if(jQuery('.dtc-offense-actions').attr('data-enabled') == 1){
		offense = 1;	
	} else {
		offense= 0;	
	}
	
	return {'team_size':team_size,'team_type':team_type,'team_format':team_format,'tepre':tepre,'rbppc':rbppc,'devy':devy,'offense':offense,'idp':idp,'mode':mode,'startup_rookie':startup_rookie}	
}

function dtc_get_rules_forRotoGPT(){
	var team_size = 0;
	var  team_type = 0;
	var team_format = 0;
	var tepre = 0;
	var rbppc = 0;
	var devy = 0;
	var idp = 0;
	var offense = 1;
	var mode ="normal";
	var startup_rookie = "no";

	jQuery( ".dtc-leauge-size" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_size = jQuery(this).attr('data-id');	
		}
	});
			
	jQuery( ".dtc-leauge-type" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_type = jQuery(this).attr('data-id');	
		}
	});
			
	jQuery( ".dtc-leauge-format" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			team_format = jQuery(this).attr('data-id');	
		}
	});
			
	jQuery( ".dtc-calculator-mode" ).each(function( index ) {
		if(jQuery(this).hasClass('filter-active')){
			mode = jQuery(this).attr('data-id');	
		}
	});

	if (mode == "startup") {
		startup_rookie = jQuery(".dtc-under-calc-button-startup").attr('data-rookie-draft');
	}
			
	if(jQuery('.dtc-te-premium-actions').attr('data-enabled') == 1){
		tepre = 1;	
	}else{
		tepre = 0;	
	}
			
	if(jQuery('.dtc-rb-ppc-premium-actions').attr('data-enabled') == 1){
		rbppc = 1;	
	}else{
		rbppc = 0;	
	}

	if(jQuery('.dtc-devy-actions').attr('data-enabled') == 1){
		devy = 1;	
	} else {
		devy= 0;	
	}

	if(jQuery('.dtc-idp-actions').attr('data-enabled') == 1){
		idp = 1;	
	} else {
		idp= 0;	
	}

	if(jQuery('.dtc-offense-actions').attr('data-enabled') == 1){
		offense = 1;	
	} else {
		offense= 0;	
	}
	
	return JSON.stringify({'team_format':team_format,'team_type':team_type,'team_size':team_size,'te_premium':tepre,'rb_premium':rbppc,'devy':devy,'offense':offense,'idp':idp,'mode':mode,'startup_rookie':startup_rookie});	
}
	
function dtc_round_num(num){
	var decimal_places = 1;
	var decimal_factor = decimal_places === 0 ? 1 : Math.pow(10, decimal_places);
		
	return num.toFixed(decimal_places);	
}

function dtc_round(from,to,location,run_trigger){
	// how many decimal places allows
	var decimal_places = 1;
	var decimal_factor = decimal_places === 0 ? 1 : Math.pow(10, decimal_places);
	
	if(run_trigger == false){
		
		var floored_number = Math.floor(to) / decimal_factor;
		if (decimal_places > 0) {
				// force decimal places even if they are 0
				floored_number = floored_number.toFixed(decimal_places);
			}
	
		location.text(floored_number);
	} else {
		location.prop('number', from).animateNumber({
			number: to * decimal_factor,
			numberStep: function(now, tween) {
				var floored_number = Math.floor(now) / decimal_factor,
				target = jQuery(tween.elem);

				if (decimal_places > 0) {
					// force decimal places even if they are 0
					floored_number = floored_number.toFixed(decimal_places);
				}

				target.text(floored_number);
			}
		});
	}
}

// This contains fuzzy logic for when the league has an odd league size
function dtc_team_size_to_column(size) {
  const n = parseInt(size, 10);

  if (isNaN(n)) {
    return 'twelve';
  }
  if (n <= 10)  return 'ten';
  if (n <= 12)  return 'twelve';
  if (n <= 14)  return 'fourteen';
  return 'sixteen';
}
	
function calculateColumn(column){
}
	
function _dtc_save_setting(type,value){
	jQuery.post(dtc.ajaxurl,
		{'action':'dtc_ajax_user_save_setting', 'setting':type,'value':value},
		function(response) {
		
		});
}

function dtc_re_add_totals(){
}

var intervalId = window.setInterval(function(){
	const left_dtc_trade = [];
	const right_dtc_trade = [];

	jQuery("#main_calculator .dtc-player-picker-left .dtc-calc-item").each(function() {
		var name = jQuery(this).find(".dtc-player-name a").text(); 
		var value = jQuery(this).find(".dtc-calc-item-player-score-mobile .dtc-calc-item-inner").text().replace('\n', '').replace('\t', '');
		var header = jQuery(this).find('.dtc-player-name-mobile').text();

		if (!name && header) {
			name = header;
		}

		if (name == "Rookie Pick") {
			var pickInfo = jQuery(this).find(".dtc-calc-item-player-info-stats span").text();

			left_dtc_trade.push({ type: "Pick", name: pickInfo, position: null, team: null, age: null, dtc_points: parseFloat(value) });
		} else if (name == "Startup Pick") {
			var pickInfo = jQuery(this).find(".dtc-calc-item-player-info-stats span").text();

			left_dtc_trade.push({ type: "StartupPick", name: pickInfo, position: null, team: null, age: null, dtc_points: parseFloat(value) });
		} else {
			var position = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(1)").text();
			var team = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(2)").text();
			var age = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(3)").text();

			var dtc_id = jQuery(this).data('id')
			left_dtc_trade.push({ type: "Player", name: name, position: position, team: team, age: age, dtc_points: parseFloat(value), dtc_id: dtc_id });
		}	
	});

	jQuery("#main_calculator .dtc-player-picker-right .dtc-calc-item").each(function() {
		var name = jQuery(this).find(".dtc-player-name a").text(); 
		var value = jQuery(this).find(".dtc-calc-item-player-score-mobile .dtc-calc-item-inner").text().replace('\n', '').replace('\t', '');
		var header = jQuery(this).find('.dtc-player-name-mobile').text();

		if (!name && header) {
			name = header;
		}

		if (name == "Rookie Pick") {
			var pickInfo = jQuery(this).find(".dtc-calc-item-player-info-stats span").text();

			right_dtc_trade.push({ type: "Pick", name: pickInfo, position: null, team: null, age: null, dtc_points: parseFloat(value) });
		} else if (name == "Startup Pick") {
			var pickInfo = jQuery(this).find(".dtc-calc-item-player-info-stats span").text();

			right_dtc_trade.push({ type: "StartupPick", name: pickInfo, position: null, team: null, age: null, dtc_points: parseFloat(value) });
		} else {
			var position = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(1)").text();
			var team = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(2)").text();
			var age = jQuery(this).find(".dtc-calc-item-player-info-stats span:nth-child(3)").text();

			var dtc_id = jQuery(this).data('id')
			right_dtc_trade.push({ type: "Player", name: name, position: position, team: team, age: age, dtc_points: parseFloat(value), dtc_id: dtc_id });
		}	
	});

	if (left_dtc_trade.length > 0 && right_dtc_trade.length > 0) {
		var give = Object.assign({}, ...left_dtc_trade.map((x, index) => ({[index]: x})))
		var receive = Object.assign({}, ...right_dtc_trade.map((x, index) => ({[index]: x})))
		window.rotoGPT_Trade = JSON.stringify({'give': give, 'receive': receive});
	} else {
		window.rotoGPT_Trade = undefined;
	}

	const rotoGPTTradeChangeEvent = new Event('rotoGPT_Trade_changed');
	window.dispatchEvent(rotoGPTTradeChangeEvent);

}, 500);
	

function recalculatePlayers(run_trigger){
	if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Recalculate Players', 'Recalculate Players');
	var team_size = jQuery(".dtc-team-size-val").val();
	var team_type = jQuery(".dtc-team-type-val").val();
	var team_format = jQuery(".dtc-team-format-val").val();
	
	jQuery("#main_calculator .dtc-total-column").each(function(){
		var left_total = 0;
		var total_val = 0;
		var total_column = jQuery(this).data('id');
		if(jQuery(this).hasClass('dtc-player-picker-left')){
			var current_column = 'left';	
		} else {
			var current_column = 'right';	
		}
		
		// console.log('Processing column: ' + current_column);
		var i= 0;
		jQuery(".dtc-calc-num",this).each(function() {
			var tier = jQuery(this).data('tier');
			var sub_total_field = jQuery(this);
			var parent = jQuery(this).closest('.dtc-calc-item');
			var total_val = 0;

			if(jQuery(this).data("position") == "pick"){
				if(team_format == "2qb" || team_format == "sf"){
					var val = jQuery(this).data( dtc_team_size_to_column(team_size)+ "sf");
				} else {
					var val = jQuery(this).data( dtc_team_size_to_column(team_size));	
				}
				
				if(jQuery(".dtc-devy-actions").attr("data-enabled") == 1){
					if(dtc.settings && dtc.settings.devy_pick_percentage != ''){
						//console.log('Pick Discount = ' + dtc.settings.devy_pick_percentage);
						val = val - (val * (dtc.settings.devy_pick_percentage / 100));
					}
				}
			} else {
				var val = jQuery(this).val();
			}

			total_val = parseFloat(val);
			
			if(run_trigger != false){
				jQuery( document ).trigger( "dtc_recalculate" );
			}
				
			if(team_type =="nonppr" && sub_total_field.data("nonppr") == "1"){
				total_val = (total_val - (total_val) * parseFloat(dtc.nonppr) );	
				//parent.closest('.dtc-calc-item-player-score-mobile').html('<div class="dtc-calc-item-inner">'>+total_val+'<span>VAL</span></div>');	
				//parent.closest('.dtc-calc-item-player-score').html('<div class="dtc-calc-item-inner">'>+total_val+'<span>VAL</span></div>');	
			}
				
			if(team_type =="half_ppr" && sub_total_field.data("nonppr") == "1"){
				var non_ppr_value =  (total_val - (total_val) * (parseFloat(dtc.nonppr) / 2) );
				//console.log('Half Value: '+non_ppr_value);
				
				total_val = parseInt(non_ppr_value);
			}
			
			if((team_format == "2qb" || team_format == "sf") && sub_total_field.data("position") == "QB" ){
				if(team_format == "2qb"){
					var increase_sf = dtc.settings ? dtc.settings.sfqb : 1;	
				} else {
					var increase_sf = dtc.settings ? dtc.settings.sf : 1;	
				}
				
				total_val = ((total_val) * parseFloat(increase_sf));
			}
				
			//Tiers
			if(tier >0 && team_size==10 && dtc.settings){
				//console.log(dtc.settings.tier[tier]);
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.tier[tier]) );		
			}
				
			if((team_format == "2qb" || team_format == "sf") && sub_total_field.data("position") == "QB" &&  parseFloat(sub_total_field.data("qbtier"))>0 && dtc.settings){
				var qbtier_increase = parseFloat(dtc.settings.qbtier[parseFloat(sub_total_field.data("qbtier"))]);
				total_val = total_val + qbtier_increase ;
			}
				
			dtc_log('Pre Boost:' + total_val);
			if(team_format == "sf" && team_size == '14' && sub_total_field.data("position") == "QB" && dtc.settings){
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.boost_14_sf) );			
			}
				
			if(team_format == "sf" && team_size == '16' && sub_total_field.data("position") == "QB" && dtc.settings){
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.boost_16_sf) );			
			}

			if(team_format == "2qb" && team_size == '14' && sub_total_field.data("position") == "QB" && dtc.settings){
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.boost_14_2qb) );			
			}
				
			if(team_format == "2qb" && team_size == '16' && sub_total_field.data("position") == "QB" && dtc.settings){
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.boost_16_2qb) );			
			}
				
			if(jQuery('.dtc-te-premium-actions').attr('data-enabled') == 1  && sub_total_field.data("position") == "TE" && dtc.settings){
				//console.log('boosting te');
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.te_premium) );			
			}
				
			if(jQuery('.dtc-rb-ppc-premium-actions').attr('data-enabled') == 1  && sub_total_field.data("position") == "RB" && sub_total_field.data("rbppc") == 1 && dtc.settings){
				//console.log('boosting rb ppc');
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.rbppc) );			
			}
				
			if(team_size == '16' && (team_format != "2qb" && team_format != "sf" ) && sub_total_field.data("position") == "QB" && dtc.settings){
				//console.log('boosting QB ppc');
				total_val = (total_val + (total_val) * parseFloat(dtc.settings.qb_boost) );			
			}
				
				
			total_val =total_val;
			dtc_log('Post Boost:' + total_val);
			//parent.find('.dtc-calc-item-player-score-mobile').html('<div class="dtc-calc-item-inner">0<span>VAL</span></div>');	
			//parent.find('.dtc-calc-item-player-score').html('<div class="dtc-calc-item-inner">0<span>VAL</span></div>');	
			var calc_total_mobile= parent.find('.dtc-calc-item-player-score-mobile .dtc-calc-item-inner');
			var calc_total_desktop= parent.find('.dtc-calc-item-player-score .dtc-calc-item-inner');
			//console.log( total_val.toFixed(1));
			
			dtc_round(calc_total_mobile.text(),total_val,calc_total_mobile,run_trigger);
			dtc_round(calc_total_mobile.text(),total_val,calc_total_desktop,run_trigger);
				
				
			sub_total_field.attr('data-calculated',total_val);
			left_total +=total_val ;
			total_val = "";
			sub_total_field = "";
			i++;
		});	
	
		dtc_round(jQuery("." + total_column).text(), left_total,jQuery("." + total_column),run_trigger);
	});//end loop total columns
		
	var left_totals = {}
	
	jQuery("#main_calculator .dtc-player-picker-left .dtc-calc-num").each(function(i,v) {
		if(dtc.settings && parseFloat(jQuery(this).val())>parseFloat(dtc.settings.boost_14_dif)){
			//console.log(jQuery(this).data("player"));
			left_totals[i] = jQuery(this).val();
		}
	});

	var right_totals = {}
	jQuery("#main_calculator .dtc-player-picker-right .dtc-calc-num").each(function(i,v) {
		if(dtc.settings && parseFloat(jQuery(this).val())>parseFloat(dtc.settings.boost_14_dif)){
			//console.log(jQuery(this).data("player"));
			right_totals[i] = jQuery(this).val();
		}		
		
	});

	//console.log('Left Total: ' + Object.keys(left_totals).length);
	//console.log('Right Total: ' + Object.keys(right_totals).length);
	
	if(Object.keys(left_totals).length != '0' && Object.keys(right_totals).length != '0'){
		//console.log('Continue boosts');
	
		if(Object.keys(left_totals).length >Object.keys(right_totals).length){
			var boost_14 = '#main_calculator .dtc-player-picker-left';	
		} else if(Object.keys(right_totals).length >Object.keys(left_totals).length){
			var boost_14 = '#main_calculator .dtc-player-picker-right';		
		} else {
			var boost_14 = false;	
		}
	
		//console.log(boost_14);
		if(boost_14 != false && team_size == '14'){
			var total_boost = 0;
			jQuery(boost_14 + " .dtc-calc-num").each(function(i,v) {
				var	total_val = parseFloat(jQuery(this).attr('data-calculated'));
				var	original_val = parseFloat(jQuery(this).attr('data-calculated'));
				var sub_total_field = jQuery(this);						
				var parent = jQuery(this).closest('.dtc-calc-item');
					
				if(dtc.settings && total_val>parseFloat(dtc.settings.boost_14_dif)){
					//console.log(total_val);	
					total_val = (total_val + (total_val * parseFloat(dtc.settings.boost_14) ));	
					//console.log(total_val);
				} else {
					total_val = total_val;								
				}
				
				total_boost += total_val;
					
				var calc_total_mobile= parent.find('.dtc-calc-item-player-score-mobile .dtc-calc-item-inner');
				var calc_total_desktop= parent.find('.dtc-calc-item-player-score .dtc-calc-item-inner');	
				
				dtc_round(parseFloat(original_val),total_val,calc_total_mobile,run_trigger);
				dtc_round(parseFloat(original_val),total_val,calc_total_desktop,run_trigger);		
			});

			dtc_round(jQuery(boost_14 + " .dtc-column-total").text(),total_boost,jQuery(boost_14 + " .dtc-column-total"),run_trigger);			
		}

		//16 boosts
		//start boosts
		var left_totals = {}
		jQuery("#main_calculator .dtc-player-picker-left .dtc-calc-num").each(function(i,v) {
			if(dtc.settings && parseFloat(jQuery(this).val())>parseFloat(dtc.settings.boost_16_dif)){
				//console.log(jQuery(this).data("player"));
				left_totals[i] = jQuery(this).val();
			}
		});

		var right_totals = {}
		jQuery("#main_calculator .dtc-player-picker-right .dtc-calc-num").each(function(i,v) {
			if(dtc.settings && parseFloat(jQuery(this).val())>parseFloat(dtc.settings.boost_16_dif)){
				//console.log(jQuery(this).data("player"));
				right_totals[i] = jQuery(this).val();
			}		
		});
		
		//console.log(left_totals.length);
		//console.log(right_totals.length);
		
		if(Object.keys(left_totals).length >Object.keys(right_totals).length){
			var boost_16 = '#main_calculator .dtc-player-picker-left';	
		} else if(Object.keys(right_totals).length >Object.keys(left_totals).length){
			var boost_16 = '#main_calculator .dtc-player-picker-right';		
		} else {
			var boost_16 = false;	
		}
		
		//console.log(boost_16);
		if(boost_16 != false && team_size == '16'){
			var total_boost = 0;
			jQuery(boost_16 + " .dtc-calc-num").each(function(i,v) {
				var	total_val = parseFloat(jQuery(this).attr('data-calculated'));
				var	original_val = parseFloat(jQuery(this).attr('data-calculated'));
				var sub_total_field = jQuery(this);						
				var parent = jQuery(this).closest('.dtc-calc-item');
					
				if(dtc.settings && total_val>parseFloat(dtc.settings.boost_16_dif)){
					//console.log(total_val);	
					total_val = (total_val + (total_val * parseFloat(dtc.settings.boost_16) ));	
					//console.log(total_val);
				} else {
					total_val = total_val;								
				}

				total_boost += total_val;
					
				var calc_total_mobile= parent.find('.dtc-calc-item-player-score-mobile .dtc-calc-item-inner');
				var calc_total_desktop= parent.find('.dtc-calc-item-player-score .dtc-calc-item-inner');			
				dtc_round(parseFloat(original_val),total_val,calc_total_mobile,run_trigger);
				dtc_round(parseFloat(original_val),total_val,calc_total_desktop,run_trigger);	
			});

			dtc_round(jQuery(boost_16 + " .dtc-column-total").text(),total_boost,jQuery(boost_16 + " .dtc-column-total"));	
		}
	}
	
	var total_left_count = jQuery(".dtc-player-picker-left .dtc-calc-num").length;
	var total_right_count = jQuery(".dtc-player-picker-right .dtc-calc-num").length;
	// console.log('start diffs');
				
	if(total_left_count > total_right_count){
		var difference = total_left_count - total_right_count;
		var difference_side = 'left';
		// console.log('left dif:'+ difference);	
	} else {
		var difference = total_right_count - total_left_count;	
		// console.log('right dif:'+ difference);	
		var difference_side = 'right';
	}

	if (dtc.settings !==  undefined) {
		// console.log('Difference Setting:' + dtc.settings.difference[difference]);
		if( dtc.settings && difference >0 && dtc.settings.difference[difference] > 0 && jQuery('.dtc-unbalanced-trade').attr('data-enabled') == 1){
			// console.log('found diff discounts');
						
			var total_boost = 0;		
			jQuery("#main_calculator .dtc-player-picker-"+difference_side+" .dtc-calc-num").each(function(i,v) {
				var	total_val = parseFloat(jQuery(this).attr('data-calculated'));
				var	original_val = parseFloat(jQuery(this).attr('data-calculated'));
				var sub_total_field = jQuery(this);						
				var parent = jQuery(this).closest('.dtc-calc-item');
				var calc_total_mobile= parent.find('.dtc-calc-item-player-score-mobile .dtc-calc-item-inner');
				var calc_total_desktop= parent.find('.dtc-calc-item-player-score .dtc-calc-item-inner');	
				console.log(total_val);
		
				let dtc_settings_difference = dtc.settings ? dtc.settings.difference[difference] : 1;
				total_val = (total_val - (total_val * parseFloat( dtc.settings.difference[difference])));	
				console.log(total_val);
				total_boost += total_val;
				dtc_round(parseFloat(original_val),total_val,calc_total_mobile,run_trigger);
				dtc_round(parseFloat(original_val),total_val,calc_total_desktop,run_trigger);	
			
			});
						
			dtc_round(jQuery("#main_calculator .dtc-player-picker-"+difference_side+" .dtc-column-total").text(),total_boost,jQuery("#main_calculator .dtc-player-picker-"+difference_side+" .dtc-column-total"));	
		}
	}

	jQuery(document).on('click', '.dtc-iframe', function (event) {
		jQuery('#modal-iframe').iziModal('open')
			return false;
	});

	jQuery(".dtc-dropdown-select").each(function() {
	
		var parent=	jQuery(this).closest('.dtc-calc-item');
		var list_types = [];
		
		if(jQuery(".dtc-offense-actions").attr("data-enabled") == 1){
			list_types.push("list");
		}
		
		if(jQuery(".dtc-devy-actions").attr("data-enabled") == 1){
			list_types.push("devy");
		}

		if(jQuery(".dtc-idp-actions").attr("data-enabled") == 1){
			list_types.push("idp");
		}

		jQuery.post(dtc.ajaxurl,
					{'action':'dtc_ajax_get_dropdown', 'type':team_type,'size':team_size,'list_types':list_types,'settings':dtc_get_rules()},
					function(response) {
						parent.html(response);
					});
	});
}
		
jQuery(document).on('select2:open', () => {
	document.querySelector('.select2-search__field').focus();
});

jQuery(document).on('select2:close', (e) => {
	let selectElement = jQuery(e.target);
	if ( ! selectElement.val() ) {
		let pickerMarkup = '<div class="dtc-player-input dtc-player-input-items"><a href="#" class="dtc-add-player ">Add Player / Pick </a></div>';
		selectElement.closest('.dtc-player-input').before(pickerMarkup);
		selectElement.closest('.dtc-player-input').remove();
	}
});

jQuery(document).ready(function($) {
	window.addEventListener('beforeinstallprompt', function(e) {
		// beforeinstallprompt Event fired

		// e.userChoice will return a Promise.
		e.userChoice.then(function(choiceResult) {
			//console.log(choiceResult.outcome);

			if(choiceResult.outcome == 'dismissed') {
			//console.log('User cancelled home screen install');
			}
			else {
			//console.log('User added to home screen');
			}
		});
	});
	
	$( document ).on( "click", ".dtc-leauge-format-alt", function() {
		removeEmptySearches(".dtc-player-picker-left");
		removeEmptySearches(".dtc-player-picker-right");
	});
	
	$( document ).on( "click", ".dtc-leauge-size", function() {
		var settings = dtc_get_rules();
		
		if(settings.mode == 'startup'){
			var r = confirm("Changing the league size will reset the calculator!");
			
			if (r == true) {
				dtc_clear_calculator();
			} else {
				return false;
			}
		}

		if(dtc.r == 1){			
			return false;	
		}

		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Clicked Leauge Size: ' + $(this).text(), 'Clicked Leauge Size: ' + $(this).text());

		$(".dtc-leauge-size").removeClass('filter-active');
		$(this).addClass('filter-active');
		$('.dtc-team-size-val').val($(this).data('id'));
		recalculatePlayers();
		_dtc_save_setting('ls',$(this).data('id'));
		false;
	});
		
	$( document ).on( "click", ".dtc-leauge-type", function() {
		if(dtc.r == 1){			
			return false;	
		}

		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Clicked Leauge Type: ' + $(this).text(), 'Clicked Leauge Type: ' + $(this).text());
		
		$(".dtc-leauge-type").removeClass('filter-active');
		$(this).addClass('filter-active');
		$('.dtc-team-type-val').val($(this).data('id'));
		recalculatePlayers();
		_dtc_save_setting('lt',$(this).data('id'));
		return false;
	});
		
	$( document ).on( "click", ".dtc-leauge-format", function() {
		if(dtc.r == 1){			
			return false;	
		}
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Clicked Leauge Format: ' + $(this).text(), 'Clicked Leauge Format: ' + $(this).text());
		
		$(".dtc-leauge-format").removeClass('filter-active');
		$(this).addClass('filter-active');
		$('.dtc-team-format-val').val($(this).data('id'));
		recalculatePlayers();
		_dtc_save_setting('lf',$(this).data('id'));
		return false;
	});
		
	$( document ).on( "click", ".dtc-calc-item-edit", function() {
		if(dtc.r == 1){			
			return false;	
		}

		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Edit Calculator Item', 'Edit Calculator Item');
	
		var parent = $(this).closest('.dtc-player-input');
		//console.log(parent);

		var team_size = $(".dtc-team-size-val").val();
		var team_type = $(".dtc-team-type-val").val();
		var list_types = [];

		if($(".dtc-offense-actions").attr("data-enabled") == 1){
			list_types.push("list");
		}
	
		if($(".dtc-devy-actions").attr("data-enabled") == 1){
			list_types.push("devy");
		}

		if(jQuery(".dtc-idp-actions").attr("data-enabled") == 1){
			list_types.push("idp");
		}
	
		parent.html('<div class="dtc-loader"><img src="'+ dtc.plugin_url+ '/asetts/images/loading_front.gif"></div>');
	  
		$.post(dtc.ajaxurl,
				{'action':'dtc_ajax_get_dropdown', 'type':team_type,'size':team_size,'list_types':list_types,'settings':dtc_get_rules()},
				function(response) {
					parent.html(response,function(){
						recalculatePlayers();
					});
				});

		return false;	
	});
		
	$(document).on("click",".dtc-showbadges",function(){
		var enabled = $(".dtc-showbadges").attr('data-enabled');
		
		if(enabled == '1'){
			$(this).attr('data-enabled','0');
			$(this).removeClass('filter-active');
			$(".dtc-badge-logo").show();
		} else {
			$(this).attr('data-enabled','1');
			$(this).addClass('filter-active');
			$(".dtc-badge-logo").hide();	
		}
			
		return false;
	});
		
	$(document).on("click",".dtc-unbalanced-trade",function(){
		var enabled = $(".dtc-unbalanced-trade").attr('data-enabled');
		
		if(enabled == '1'){
			$(this).attr('data-enabled','0');
			$(this).removeClass('filter-active');
		} else {
			$(this).attr('data-enabled','1');
			$(this).addClass('filter-active');
		}
			recalculatePlayers();
			return false;
	});

	function getPlayerInputCount(picker) {
		return picker.find('.dtc-player-input').length;
	}
	
	function addPlayerInput(picker) {
		var newPlayerInput = $('<div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div>');
		newPlayerInput.insertBefore(picker.find('.dtc-player-input-total'));
	}
	
	function equalizePlayerInputs() {
		var leftPicker = $('.dtc-player-picker-left');
		var rightPicker = $('.dtc-player-picker-right');
		
		var leftCount = getPlayerInputCount(leftPicker);
		var rightCount = getPlayerInputCount(rightPicker);
		
		while (leftCount < rightCount) {
			addPlayerInput(leftPicker);
			leftCount++;
		}
		
		while (rightCount < leftCount) {
			addPlayerInput(rightPicker);
			rightCount++;
		}
	}
		
	$( document ).on( "click", ".dtc-add-player", function() {
		if (window.IntegrationLeagueName) {
			var inst = $('[data-remodal-id=dtc-mfl-modal]').remodal();
			inst.open();			
			return false;
		}

		if(dtc.r == 1){			
			return false;	
		}
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Add Player to Calculator', 'Add Player to Calculator');
		var current = $(this);
		var parent = $(this).parent();
	
		var team_size = $(".dtc-team-size-val").val();
		var team_type = $(".dtc-team-type-val").val();
		var list_types = [];
		if ($(".dtc-offense-actions").attr("data-enabled") == 1) {
			list_types.push("list");
		}
	
		if ($(".dtc-devy-actions").attr("data-enabled") == 1) {
			list_types.push("devy");
		}
		if (jQuery(".dtc-idp-actions").attr("data-enabled") == 1) {
			list_types.push("idp");
		}
	
		/*
		if (current.hasClass('dtc-add-another')) {
			$(".dtc-player-picker .dtc-add-another").each(function(i, v) {
				var last_current = $(this);
				var last_parent = $(this).parent();
	
				last_parent.after('<div class="dtc-player-input "><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div>');
				last_current.removeClass('dtc-add-another');
			});
		}
		*/

		if (current.hasClass('dtc-add-player')) {
			// We want the parent of the parent
			var allAddAnother = current.parent().parent().find('.dtc-add-player')

			if (allAddAnother.length === 1) {
				var last_parent = current.parent();
				last_parent.after('<div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div>');
			}
			current.removeClass('dtc-add-another');

			equalizePlayerInputs();
		}
		
		parent.html('<di class="dtc-loader"><img src="' + dtc.plugin_url + '/asetts/images/loading_front.gif"></div>');
		
		$.post(dtc.ajaxurl, {
			'action': 'dtc_ajax_get_dropdown',
			'type': team_type,
			'size': team_size,
			'list_types': list_types,
			'settings': dtc_get_rules()
		}, function(response) {
			parent.html(response);
	
		});
	
		return false;
	});	
		
	$( document ).on( "click", ".load-clipcast", function() {
		console.log($(this).attr('data-clipcast'));
		//window.ClipcastEmbedPlayer.searchClips($(this).attr('data-clipcast'), {exclusivePodcastId: ["TRADECALCDUDES"]});
		window.ClipcastEmbedPlayer.searchClips($(this).attr('data-clipcast'),{preferredPodcastId: "DTC"});
		return false;
	});
	
	$( document ).on( "click", ".dtc-clear-calculator", function() {
		if(dtc.r == 1){			
			return false;	
		}

		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Clear Calculator', 'Clear Calculator');
		$('#main_calculator .dtc-player-picker-left').html('<div class="dtc-player-input"><a href="#" class="dtc-add-player">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player ">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div><div class="dtc-player-input-total left-bottom-round"><h3>Total</h3><div class="dtc-left-total dtc-column-total">0.0</div><span>VAL</span></div>');
		$('#main_calculator .dtc-player-picker-right').html('<div class="dtc-player-input"><a href="#" class="dtc-add-player">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player ">Add Player / Pick</a></div><div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div><div class="dtc-player-input-total right-bottom-round"><h3>Total</h3><div class="dtc-right-total dtc-column-total">0.0</div><span>VAL</span></div>');
		return false;
	});
		
	$( document ).on( "click", ".dtc-player-info", function() {
		var player_id = $(this).attr('data-id');
		var rules = '';
		var type = $(this).attr('data-type');
		
		// Add check here
		console.log('testing:', window.dtcIsReadOnly);
		dtc_get_player_modal(player_id,rules,type);
	});
	
	$( document ).on("change", function() {
		var player_id = $(".stat-years").attr("data-player-id");

   		$.post(dtc.ajaxurl,
				{"action":"dtc_ajax_get_player_stats","player_id":player_id, "stat_years": $(".stat-years").val(), 'rules': dtc_get_rules()},
				function(response) {
   					var obj  = $.parseJSON(response);
   					dtc_reload_stats(player_id,obj.stats_data);
   					$('.cycle-slideshow').cycle();
   				});
 	});
		
	$( document ).on( "click", ".dtc-recalculate-calculator", function() {
		recalculatePlayers();
		return false;
	});

	$( document ).on( "click", ".dtc-offense-actions", function() {
		var current = $(this);
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used Offense Button', 'Used Offsense Button');
		
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}
		
		var value_str = '';
				
		if($('.dtc-te-premium-actions').hasClass('filter-active')){
			value_str += 'te-premium,';
		}

		if($('.dtc-rb-ppc-premium-actions').hasClass('filter-active')){
			value_str += 'rb-ppc-premium,';
		}
		
		if($('.dtc-offense-actions').hasClass('filter-active')){
			value_str += 'offense,';
		}

		if($('.dtc-idp-actions').hasClass('filter-active')){
			value_str += 'idp,';
		}

		if($('.dtc-devy-actions').hasClass('filter-active')){
			value_str += 'devy,';
		}

		_dtc_save_setting('lfa',value_str);
		recalculatePlayers();
		
		return false;	
	});
			
	$( document ).on( "click", ".dtc-idp-actions", function() {
		var current = $(this);
				
		if(!current.hasClass('filter-active') && dtc.user.ignore_idp_modal != 1 ){
			var inst = jQuery('[data-remodal-id=dtc-idp-modal]').remodal();
			
			jQuery.post(dtc.ajaxurl, {"action":"dtc_ignore_modal","modal":"idp_modal"},
				function(response) {
					inst.open();
				});
		}
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used IDP Button', 'Used IDP');
		
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}

		var value_str = '';
				
		if($('.dtc-te-premium-actions').hasClass('filter-active')){
			value_str += 'te-premium,';
		}

		if($('.dtc-rb-ppc-premium-actions').hasClass('filter-active')){
			value_str += 'rb-ppc-premium,';
		}

		if($('.dtc-offense-actions').hasClass('filter-active')){
			value_str += 'offense,';
		}

		if($('.dtc-idp-actions').hasClass('filter-active')){
			value_str += 'idp,';
		}

		if($('.dtc-devy-actions').hasClass('filter-active')){
			value_str += 'devy,';
		}

		_dtc_save_setting('lfa',value_str);
		recalculatePlayers();
		
		return false;	
	});
	
	$( document ).on( "click", ".dtc-devy-actions", function() {
		if(dtc.r == 1){			
			return false;	
		}

		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used Devy Button', 'Used Devy');
		
		var current = $(this);
				
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}

		var value_str = '';
				
		if($('.dtc-te-premium-actions').hasClass('filter-active')){
			value_str += 'te-premium,';
		}

		if($('.dtc-rb-ppc-premium-actions').hasClass('filter-active')){
			value_str += 'rb-ppc-premium,';
		}

		if($('.dtc-offense-actions').hasClass('filter-active')){
			value_str += 'offense,';
		}

		if($('.dtc-idp-actions').hasClass('filter-active')){
			value_str += 'idp,';
		}

		if($('.dtc-devy-actions').hasClass('filter-active')){
			value_str += 'devy,';
		}
		_dtc_save_setting('lfa',value_str);
		
		recalculatePlayers();
		
		return false;
	});
			
	$( document ).on( "click", ".dtc-te-premium-actions", function() {
		if(dtc.r == 1){			
			return false;	
		}

		var current = $(this);
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used Premium Button: ' + current.text() , 'Used Premium Button: ' + current.text());
		
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}

		var value_str = '';
				
		if($('.dtc-te-premium-actions').hasClass('filter-active')){
			value_str += 'te-premium,';
		}
		
		if($('.dtc-rb-ppc-premium-actions').hasClass('filter-active')){
			value_str += 'rb-ppc-premium,';
		}
		
		if($('.dtc-idp-actions').hasClass('filter-active')){
			value_str += 'idp,';
		}

		if($('.dtc-offense-actions').hasClass('filter-active')){
			value_str += 'offense,';
		}

		if($('.dtc-devy-actions').hasClass('filter-active')){
			value_str += 'devy,';
		}
		
		_dtc_save_setting('lfa',value_str);
		recalculatePlayers();
		
		return false;
	});
			
	$( document ).on( "click", ".dtc-rb-ppc-premium-actions", function() {
		console.log('rbppc');
		if(dtc.r == 1){			
			return false;	
		}
		
		console.log('rbppc');
		var current = $(this);
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used RB PPC Button: ' + current.text(), 'Used RB PPC Button: ' + current.text());
		
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}
		
		var value_str = '';
				
		if($('.dtc-te-premium-actions').hasClass('filter-active')){
			value_str += 'te-premium,';
		}

		if($('.dtc-rb-ppc-premium-actions').hasClass('filter-active')){
			value_str += 'rb-ppc-premium,';
		}

		if($('.dtc-idp-actions').hasClass('filter-active')){
			value_str += 'idp,';
		}
		
		if($('.dtc-offense-actions').hasClass('filter-active')){
			value_str += 'offense,';
		}

		if($('.dtc-devy-actions').hasClass('filter-active')){
			value_str += 'devy,';
		}

		_dtc_save_setting('lfa',value_str);
		recalculatePlayers();
		
		return false;
		
	});
			
	
	$( document ).on( "click", ".dtc-qb-boost-premium-actions", function() {
		if(dtc.r == 1){			
			return false;	
		}

		var current = $(this);
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Used Boost: ' + current.text(), 'Used Boost: ' + current.text());
		
		if(current.attr("data-enabled") == 1){
			$(this).attr("data-enabled","0");
			$(this).removeClass("filter-active");	
		} else {
			$(this).addClass("filter-active");	
			$(this).attr("data-enabled","1");
		}
		
		recalculatePlayers();
		
		return false;
	});
			
	$( document ).on( "click", ".dtc-remove-item", function() {
		if(dtc.r == 1){			
			return false;	
		}
		
		if (window.__gaTracker) __gaTracker('send', 'event', 'Calculator', 'Remove Player', 'Remove Player');
		
		var parent = $(this).closest('.dtc-player-input');
		parent.html('<a href="#" class="dtc-add-player">Add Player / Pick</a>');
		recalculatePlayers();
		
		return false;
	});

	$( document ).on( "click", ".dtc-clear-calculator", function() {
		dtc_clear_calculator();
		
		return false;
	});
				
	$( document ).on( "click", ".dtc-player-card-tabs a", function() {
		$(".dtc-player-card-tabs a").removeClass('active');
		$(this).addClass('active');
		$(".dtc-player-profile-tab").hide();
		$("."+$(this).attr('data-id')).show();
	
		return false;
					
	});
				
	$( document ).on( "click", ".ga-track", function() {
		dtc_ga_track($(this).attr('data-category'),$(this).attr('data-action'));
	});

	$( document ).on( "click", ".dtc-copy-url-click", function() {
		var main_url = dtc.calculator_url;
				
		var ls = $(".dtc-team-size-val").val();
		var lt = $(".dtc-team-type-val").val();
		var lf = $(".dtc-team-format-val").val();
		main_url += '?dtc_share=1';
				 
		if(ls != ''){	
			main_url += '&ls=' + ls;
		}

		if(lt != ''){	
			main_url += '&lt=' + lt;
		}

		if(lf != ''){	
			main_url += '&lf=' + lf;
		}

		var lfa = '';
		if($(".dtc-te-premium-actions").hasClass("filter-active")){
			lfa +="te-premium,";	
		}

		if($(".dtc-rb-ppc-premium-actions").hasClass("filter-active")){
			lfa +="rb-ppc-premium,";	
		}

		if($(".dtc-devy-actions").hasClass("filter-active")){
			lfa +="devy,";	
		}
				
		if(lfa != ''){	
			main_url += '&lfa=' + lfa;
		}
				 
		var left_side = '';
		
		$(".dtc-player-picker-left .dtc-calc-num").each(function( index ) {
			var input = $(this);
					
			if(input.attr('data-position') == 'pick'){
				left_side += 'mpick-'+  input.attr('data-year')+ '_'+ input.attr('data-round') + '_'+ input.attr('data-pick').replace('(', '').replace(')', '')+',';		
			} else {
				left_side += 'mplayer-'+  input.attr('data-mfl-id')+',';	
			}
					
			//console.log(input);
		});
				 
		if(left_side != ''){	
			main_url += '&left-side=' + left_side;
		}
				 
		var right_side = '';
		$(".dtc-player-picker-right .dtc-calc-num").each(function( index ) {
			var input = $(this);
					
			if(input.attr('data-position') == 'pick'){
				right_side += 'mpick-'+  input.attr('data-year')+ '_'+ input.attr('data-round') + '_'+ input.attr('data-pick').replace('(', '').replace(')', '')+',';		
			} else {
				right_side += 'mplayer-'+  input.attr('data-mfl-id')+',';	
			}
					
			//console.log(input);
		});
				 
		if(right_side != ''){	
			main_url += '&right-side=' + right_side;
		}
				 
		$("#dtc-share-url").val(main_url);
				 
		var copyText = document.getElementById("dtc-share-url");
		copyText.select();
		document.execCommand("copy");
 		alert("Copied URL");
		
		return false;
	});
});


var dtcGlobalFunctions = {
	recalculate: function() {
		recalculatePlayers();
	}
};

function removeEmptySearches(parentClass) {
    jQuery(parentClass).each(function() {
        var selectElement = jQuery(this).find(".dtc-choose-player");
        if (!selectElement.val()) {
			// Get the immediate parent of selectElement
			var immediateParent = selectElement.parent();

			// Get the main parent area
			var mainParent = immediateParent.parent();
			mainParent.removeAttr("data-select2-id");
			mainParent.empty();

			// Append new link to the immediate parent
			mainParent.append('<a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick </a>');
        }
    });
}

function dtc_get_player_modal(player_id,rules,type){
		jQuery(".dtc-stats-recent-trade-content").empty();
		jQuery(".dtc-stats-trade-suggestion-content").empty();
		
		jQuery.post(dtc.ajaxurl,
			{"action":"dtc_ajax_get_player_stats","player_id":player_id, "stat_years": jQuery(".stat-years").val(), 'rules': dtc_get_rules(),'type':type},
			function(response) {
				var obj  = jQuery.parseJSON(response);
				console.log(obj);
				jQuery(".dtc-stats-player-card").html(obj.player_card);
				jQuery(".dtc-stats-settings-card").html(obj.settings_card);
				jQuery(".stat-years").attr("data-player-id",player_id);
				jQuery(".stat-years").attr("data-type",type);
				jQuery(".dtc-player-card-tabs a").attr('data-dtc-id',obj.player.id);
				jQuery(".dtc-player-card-tabs a").attr('data-mfl-id',obj.player.mfl_id);
				var inst = jQuery('[data-remodal-id=dtc-player-modal]').remodal();
		
				dtc_reload_stats(player_id,obj.stats_data);
		
				jQuery('.cycle-slideshow').cycle();
				inst.open();
	
				jQuery.post(dtc.ajaxurl,
							{"action":"dtc_ajax_get_player_trades_tabs","player_id":player_id, 'rules': dtc_get_rules(),'type':type},
							function(response) {
								var objt  = jQuery.parseJSON(response);
								console.log(objt);
								jQuery(".dtc-stats-recent-trade-content").html(objt.trades)
								jQuery(".dtc-stats-trade-suggestion-content").html(objt.trade_suggestion)
							});
	
			});
}

function dtc_reload_stats(player_id,stats){
	jQuery("#history_chart").empty();
	jQuery(".ranking-chart-item").html('<canvas id="history_chart"  width="100%" height="350"></canvas>');	
					
	var labels = stats.labels;
	
	var chart_options = {
    	title: {
      		display: false,
		},
		responsive:true,
	 	legend: {
        	labels: {
                fontColor: "white",
			}
        },
		maintainAspectRatio: false,
		scales: {
			yAxes: [{
				gridLines: {
					drawBorder: false,
					color: 'rgb(32,33,38)',
				},
				ticks: {
                    fontColor: "white",
				}
			}],
            xAxes: [{
                ticks: {
                    fontColor: "white",
                }
            }]
		}
	};	
	
	var chx = jQuery('#history_chart');
	
	new Chart(chx, {
  		type: 'line',
		data: {
		    labels: labels,
	 		tooltips: {enabled: false},
    		hover: {mode: null},
			datasets: [
				{ 
					data: stats.value,
					label: "Historical Value",
					borderColor: 'rgb(224,184,47)',
					backgroundColor: 'rgb(0, 0, 0)',
					fill: false
				},
				{ 
					data:stats.rank,
					label: "Positional Rank",
					borderColor: 'rgb(255,255,255)',
					backgroundColor: 'rgb(0, 0, 0)',
					fill: false
      			}
    		]
  		},
  		options: chart_options
	});
}


jQuery(document).ready(function () {
	function toggleOverlayVisibility() {
        if (!jQuery('.dtc-mfl-nav').length || !jQuery('.dtc-mfl-nav').is(':visible')) {
            jQuery('#dtc-integration-overlay').show();
        } else {
            jQuery('#dtc-integration-overlay').hide();
        }
    }

    setInterval(toggleOverlayVisibility, 500);

	jQuery('.dtc-settings-toggle').click(function() {
		const leagueInfoArea = document.getElementById('league_info_area');
		const leagueSelectorArea = document.getElementById('league_selector_area');
        const leagueSelector = document.getElementById('league_selector');
		
		let toggle = jQuery('.dtc-settings-toggle');
		let settings = jQuery('.dtc-filters-wrapper');
		if (toggle.hasClass('dtc-open')) {
			toggle.removeClass('dtc-open');
			toggle.addClass('dtc-closed');
			settings.slideUp();
			leagueInfoArea.appendChild(leagueSelector);
			return;
		}
		if (toggle.hasClass('dtc-closed')) {
			toggle.removeClass('dtc-closed');
			toggle.addClass('dtc-open');
			settings.slideDown();
			window.scrollTo({'top': 0, 'behavior': 'smooth'});
			toggle.removeClass('dtc-sticky');
			leagueSelectorArea.appendChild(leagueSelector);
			return;
		}
	});
});

jQuery(document).ready(function () {
	// Monitor for changes in the DOM using MutationObserver
	var mflobserver = new MutationObserver(function(mutations) {
		mutations.forEach(function(mutation) {
			// Check if the added nodes contain an element with the specified name
			if (window.IntegrationLeagueId !== undefined && window.IntegrationName == 'mfl') {
				if (jQuery(mutation.addedNodes).find('select[name="mfl_league_id"]').length > 0) {
				var leagueId = window.rotoGPT_LeagueId;
				jQuery('select[name="mfl_league_id"]').val(leagueId);
				jQuery('select[name="mfl_league_id"]').trigger('change');
			}
			}
		});
	});

	// Start observing the document body and its subtree for changes
	mflobserver.observe(document.body, {
		childList: true, // Listen for changes in the children of the body
		subtree: true // Include all descendants of the body
	});

	// Monitor for changes in the DOM using MutationObserver
	var sleeperobserver = new MutationObserver(function(mutations) {
		mutations.forEach(function(mutation) {
			// Check if the added nodes contain an element with the specified name
			if (window.IntegrationLeagueId !== undefined && window.IntegrationName == 'sleeper') {
				if (jQuery(mutation.addedNodes).find('select[name="sleeper_api_league_id"]').length > 0) {
				var leagueId = window.rotoGPT_LeagueId;
				jQuery('select[name="sleeper_api_league_id"]').val(leagueId);
				jQuery('select[name="sleeper_api_league_id"]').trigger('change');
			}
			}
		});
	});
	
	// Start observing the document body and its subtree for changes
	sleeperobserver.observe(document.body, {
		childList: true, // Listen for changes in the children of the body
		subtree: true // Include all descendants of the body
	});

	// Monitor for changes in the DOM using MutationObserver
	var fleaobserver = new MutationObserver(function(mutations) {
		mutations.forEach(function(mutation) {
			// Check if the added nodes contain an element with the specified name
			if (window.IntegrationLeagueId !== undefined  && window.IntegrationName == 'fleaflicker') {
				if (jQuery(mutation.addedNodes).find('select[name="fleaflicker_league_id"]').length > 0) {
				var leagueId = window.rotoGPT_LeagueId;
				jQuery('select[name="fleaflicker_league_id"]').val(leagueId);
				jQuery('select[name="fleaflicker_league_id"]').trigger('change');
			}
			}
		});
	});
	
	// Start observing the document body and its subtree for changes
	fleaobserver.observe(document.body, {
		childList: true, // Listen for changes in the children of the body
		subtree: true // Include all descendants of the body
	});
});