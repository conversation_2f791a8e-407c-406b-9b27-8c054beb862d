import{V as a,a as B,m as v,n as f,h as M,i as Y,b as A,e as G,p as j}from"./chunks/vendor-0853f02f.min.js";import{R as K,D as I}from"./chunks/date-intervals-018a2dda.min.js";import{R as q,a as J,b as Q,c as X}from"./chunks/report-table-helper-39dc7ff9.min.js";import{S as e1}from"./chunks/SettingsInfoTooltip-4f19e286.min.js";import{W as t1}from"./chunks/WidgetReportError-b3fe6132.min.js";import{N as o1,M as z,s as _}from"./chunks/index-468bfca9.min.js";const s1=(s,e,o,i,r)=>new Promise(n=>{let d=new FormData;o=JSON.stringify(o),d.append("action","monsterinsights_save_widget_state"),d.append("nonce",a.prototype.$mi.nonce),d.append("width",e),d.append("reports",o),d.append("interval",i),d.append("notice",r),d.append("compact",s.state.compact),B.post(a.prototype.$mi.ajax,d).then(c=>{a.prototype.$swal.close(),n(c.data)}).catch(function(c){console.log(c)})}),i1=()=>new Promise(()=>{let s=new FormData;s.append("action","monsterinsights_mark_notice_closed"),s.append("nonce",a.prototype.$mi.nonce),B.post(a.prototype.$mi.ajax,s)}),N={saveWidgetState:s1,markNoticeClosed:i1},r1=s=>new Promise(e=>{if(a.prototype.$mi.widget_state&&a.prototype.$mi.widget_state.reports){for(let o in a.prototype.$mi.widget_state.reports)if(a.prototype.$mi.widget_state.reports.hasOwnProperty(o))for(let i in a.prototype.$mi.widget_state.reports[o])a.prototype.$mi.widget_state.reports[o].hasOwnProperty(i)&&(s.state.reports[i].enabled=a.prototype.$mi.widget_state.reports[o][i])}s.state.width=a.prototype.$mi.widget_state.width,s.state.interval=a.prototype.$mi.widget_state.interval,s.state.notice30day=a.prototype.$mi.widget_state.notice30day,s.state.compact=typeof a.prototype.$mi.widget_state.compact<"u"?a.prototype.$mi.widget_state.compact:!1,e(!0)}),n1=s=>{let e={overview:{},publisher:{},ecommerce:{}},o=s.rootGetters.hasOwnProperty("$_reports/date")?s.rootGetters["$_reports/date"].interval:"";for(let i in s.state.reports)if(s.state.reports.hasOwnProperty(i)&&s.state.reports[i].hasOwnProperty("type")){let r=s.state.reports[i].type;e[r][i]=s.state.reports[i].enabled}N.saveWidgetState(s,s.state.width,e,o)},a1=()=>{N.markNoticeClosed()},l1={processDefaults:r1,saveWidgetState:n1,markNoticeClosed:a1},C1=s=>{const e="hide_for_v4",o={};for(let i in s.reports)s.reports[i][e]||(o[i]=s.reports[i]);return o},c1=s=>s.width,d1=s=>s.width!=="regular"?!1:s.compact,p1=s=>s.loaded,g1=s=>s.error,h1=s=>s.notice30day,f1={reports:C1,width:c1,loaded:p1,error:g1,notice30day:h1,compact:d1},u1=(s,e)=>{s.reports[e]&&a.set(s.reports[e],"enabled",!0)},w1=(s,e)=>{s.reports[e]&&a.set(s.reports[e],"enabled",!1)},m1=(s,e)=>{s.loaded=e},_1=(s,e)=>{s.width=e},v1=(s,e)=>{s.compact=e},y1=(s,e)=>{if(typeof e.title>"u"&&typeof e.content>"u"&&typeof e.footer>"u"){a.set(s.error,e.report,!1);return}a.set(s.error,e.report,e)},L1={ENABLE_REPORT:u1,DISABLE_REPORT:w1,UPDATE_LOADED:m1,UPDATE_WIDTH:_1,SET_ERROR:y1,UPDATE_COMPACT:v1},{__:l}=wp.i18n,b1={width:"regular",interval:30,loaded:!1,compact:!0,reports:{overview:{type:"overview",name:l("Overview Report","google-analytics-for-wordpress"),enabled:!0,component:"WidgetReportOverview"},toppages:{type:"overview",name:l("Top Posts/Pages","google-analytics-for-wordpress"),tooltip:l("This list shows your website's most viewed posts and pages based on pageviews.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportTopPosts"},newvsreturn:{type:"overview",name:l("New vs. Returning Visitors","google-analytics-for-wordpress"),tooltip:l("This graph shows what percent of your user sessions come from new versus repeat visitors.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportNewVsReturning"},devices:{type:"overview",name:l("Device Breakdown","google-analytics-for-wordpress"),tooltip:l("This graph shows the percentage of sessions on your site from different types of devices: traditional desktops/laptops, tablets, and mobile phones.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportDevices"},landingpages:{type:"publisher",name:l("Top Landing Pages","google-analytics-for-wordpress"),tooltip:l("This list shows the top pages users first land on when visiting your website.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportLandingPages"},exitpages:{type:"publisher",name:l("Top Exit Pages","google-analytics-for-wordpress"),tooltip:l("This list shows the top pages users exit your website from.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportExitPages",hide_for_v4:!0},outboundlinks:{type:"publisher",name:l("Top Outbound Links","google-analytics-for-wordpress"),tooltip:l("This list shows the top links clicked on your website that go to another website.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportOutboundLinks"},affiliatelinks:{type:"publisher",name:l("Top Affiliate Links","google-analytics-for-wordpress"),tooltip:l("This list shows the top affiliate links your visitors clicked the most.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportAffiliateLinks"},downloadlinks:{type:"publisher",name:l("Top Download Links","google-analytics-for-wordpress"),tooltip:l("This list shows the download links your visitors clicked the most.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportDownloadLinks"},infobox:{type:"ecommerce",name:l("Overview","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportEcommerceOverview"},products:{type:"ecommerce",name:l("Top Products","google-analytics-for-wordpress"),tooltip:l("This list shows the top selling products on your website.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportTopProducts"},conversions:{type:"ecommerce",name:l("Top Conversion Sources","google-analytics-for-wordpress"),tooltip:l("This list shows the top referral websites in terms of product revenue.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportTopConversions"},addremove:{type:"ecommerce",name:l("Total Add/Remove","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportAddRemove"},days:{type:"ecommerce",name:l("Time to Purchase","google-analytics-for-wordpress"),tooltip:l("This list shows how many days from first visit it took users to purchase products from your site.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportDays",hide_for_v4:!0},sessions:{type:"ecommerce",name:l("Sessions to Purchase","google-analytics-for-wordpress"),tooltip:l("This list shows the number of sessions it took users before they purchased a product from your website.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportSessions",hide_for_v4:!0},newcustomers:{type:"ecommerce",name:l("New Customers","google-analytics-for-wordpress"),tooltip:l("The percentage of first time purchasers.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportNewCustomers",hide_for_ua:!0},abandonedcheckouts:{type:"ecommerce",name:l("Abandoned Checkouts","google-analytics-for-wordpress"),tooltip:l("The percentage of checkouts that do not result in a transaction.","google-analytics-for-wordpress"),enabled:!1,component:"WidgetReportAbandonedCheckouts",hide_for_ua:!0}},error:{},notice30day:!1},x1={namespaced:!0,state:b1,actions:l1,getters:f1,mutations:L1},{__:w}=wp.i18n,M1={name:"WidgetTips",data(){return{tips:[{text:w("Forms Tracking help you see who’s viewing your forms, so you can increase conversions.","google-analytics-for-wordpress"),utm:"forms",level:"pro"},{text:w("Custom Dimensions show you popular categories, best time to publish, focus keywords, etc.","google-analytics-for-wordpress"),utm:"custom-dimensions",level:"pro"},{text:w("Make Google Analytics GDPR compliant with our EU Compliance addon.","google-analytics-for-wordpress"),utm:"gdpr",level:"plus"},{text:w("Get real-time Google Analytics report right inside your WordPress dashboard.","google-analytics-for-wordpress"),utm:"real-time",level:"plus"},{text:w("See all your important store metrics in one place with Enhanced Ecommerce Tracking.","google-analytics-for-wordpress"),utm:"ecommerce",level:"pro"},{text:w("Unlock search console report to see your top performing keywords in Google.","google-analytics-for-wordpress"),utm:"search-console",level:"plus"},{text:w("Get Page Insights to see important metrics for individual posts / pages in WordPress.","google-analytics-for-wordpress"),utm:"page-insights",level:"plus"},{text:w("Publishers Report shows your top performing pages, audience demographics, and more.","google-analytics-for-wordpress"),utm:"publishers",level:"plus"},{text:w("Get Scroll-Depth tracking to see how far users scroll on your pages before leaving.","google-analytics-for-wordpress"),utm:"scroll",level:"plus"}],text_upgrade_link:w("Upgrade to Pro »","google-analytics-for-wordpress"),text_pro_tip:w("Pro Tip:","google-analytics-for-wordpress")}},computed:{...v({license:"$_license/license",license_network:"$_license/license_network"}),licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type},tip(){return this.getTip()}},methods:{getRandomTip(){return this.tips[Math.floor(Math.random()*this.tips.length)]},getPlusTip(){let s=this.getRandomTip();return s.level!=="pro"?this.getPlusTip():s},getTip(){return this.licenseLevel===""?this.getRandomTip():this.licenseLevel==="plus"||this.licenseLevel==="basic"?this.getPlusTip():!1},upgradeUrl(s,e){return this.$getUpgradeUrl(s,e)}}};var H1=function(){var e=this,o=e._self._c;return e.tip?o("div",{staticClass:"monsterinsights-tips"},[o("span",{staticClass:"monstericon-star"}),o("div",{staticClass:"monsterinsights-tip-text"},[o("strong",{domProps:{textContent:e._s(e.text_pro_tip)}}),e._v(" "+e._s(e.tip.text)+" "),o("a",{attrs:{href:e.upgradeUrl("pro-tips",e.tip.utm),target:"_blank",rel:"noopener"},domProps:{textContent:e._s(e.text_upgrade_link)}})])]):e._e()},V1=[],k1=f(M1,H1,V1,!1,null,null,null,null);const $1=k1.exports,{__:p,sprintf:Z}=wp.i18n,R1={name:"WidgetReportOverview",components:{WidgetTips:$1,ReportInfobox:q},computed:{...v({overview:"$_reports/overview",widget_width:"$_widget/width"}),infoboxRange(){return this.overview.infobox&&this.overview.infobox.range?this.overview.infobox.range:0},infoboxSessionsData(){return this.infoboxData("sessions")},infoboxPageviewsData(){return this.infoboxData("pageviews")},infoboxDurationData(){return this.infoboxData("duration")},infoboxBounceData(){return this.infoboxData("bounce_rate",!0)},infoboxTotalUsersData(){return this.infoboxData("totalusers")},infoboxNewUsersData(){return this.infoboxData("new_users")}},data(){return{chartKey:0,current_tab:"sessions",text_sessions:p("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:Z(p("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:p("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:Z(p("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),text_infobox_tooltip_sessions:p("A session is the browsing session of a single user to your site.","google-analytics-for-wordpress"),text_infobox_tooltip_pageviews:p("A pageview is defined as a view of a page on your site that is being tracked by the Analytics tracking code. Each refresh of a page is also a new pageview.","google-analytics-for-wordpress"),text_infobox_tooltip_average:p("Average session duration is calculated by dividing the total time spent by all users on your site (in seconds) by the number of sessions.","google-analytics-for-wordpress"),text_infobox_tooltip_bounce:p("Bounce Rate represents the percentage of sessions that don't meet the criteria for an engaged session. A session counts as engaged if the visitor completes an event (like a click or form submission), stays on your site for 10 seconds or longer, or views more than one page.","google-analytics-for-wordpress"),text_infobox_tooltip_totalusers:p("The number of distinct tracked users","google-analytics-for-wordpress"),text_infobox_tooltip_newusers:p("Users who interacted with your site for the first time.","google-analytics-for-wordpress"),text_duration:p("Avg. Session Duration","google-analytics-for-wordpress"),text_bounce:p("Bounce Rate","google-analytics-for-wordpress"),text_total_users:p("Total Users","google-analytics-for-wordpress"),text_new_users:p("New Users","google-analytics-for-wordpress")}},methods:{infoboxData(s,e=!1){let o={};return this.overview.infobox&&this.overview.infobox[s]&&(o.change=this.overview.infobox[s].prev,o.value=this.overview.infobox[s].value.toString(),this.overview.infobox[s].prev===0?o.direction="":this.overview.infobox[s].prev>0?(o.direction="up",o.color="green"):(o.direction="down",o.color="red")),e&&(o.direction==="down"?o.color="green":o.color="red"),o},forceRerender(){this.chartKey+=1}}};var Z1=function(){var e=this,o=e._self._c;return o("div",[o("div",{staticClass:"monsterinsights-report-row monsterinsights-report-infobox-row"},[o("report-infobox",{attrs:{title:e.text_sessions,value:e.$formatNumber(e.infoboxSessionsData.value),change:e.infoboxSessionsData.change,color:e.infoboxSessionsData.color,direction:e.infoboxSessionsData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_sessions}}),o("report-infobox",{attrs:{title:e.text_pageviews,value:e.$formatNumber(e.infoboxPageviewsData.value),change:e.infoboxPageviewsData.change,color:e.infoboxPageviewsData.color,direction:e.infoboxPageviewsData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_pageviews}}),o("report-infobox",{attrs:{title:e.text_duration,value:e.infoboxDurationData.value,change:e.infoboxDurationData.change,color:e.infoboxDurationData.color,direction:e.infoboxDurationData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_average}}),o("report-infobox",{attrs:{title:e.text_total_users,value:e.$formatNumber(e.infoboxTotalUsersData.value),change:e.infoboxTotalUsersData.change,color:e.infoboxTotalUsersData.color,direction:e.infoboxTotalUsersData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_totalusers}}),o("report-infobox",{attrs:{title:e.text_bounce,value:e.infoboxBounceData.value,change:e.infoboxBounceData.change,color:e.infoboxBounceData.color,direction:e.infoboxBounceData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_bounce}}),o("report-infobox",{attrs:{title:e.text_new_users,value:e.$formatNumber(e.infoboxNewUsersData.value),change:e.infoboxNewUsersData.change,color:e.infoboxNewUsersData.color,direction:e.infoboxNewUsersData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_newusers}})],1),o("WidgetTips")],1)},P1=[],T1=f(R1,Z1,P1,!1,null,null,null,null);const D1=T1.exports,{__:P}=wp.i18n,S1={name:"WidgetReportsLink",props:{see_all:String,go_to:String},data(){return{default_see_all:P("See All Reports","google-analytics-for-wordpress"),default_go_to:P("Go to the Analytics Dashboard","google-analytics-for-wordpress")}},computed:{text_see_all(){return this.see_all?this.see_all:this.default_see_all},text_go_to(){return this.go_to?this.go_to:this.default_go_to}}};var E1=function(){var e=this,o=e._self._c;return o("div",{staticClass:"monsterinsights-reports-link"},[o("span",{domProps:{innerHTML:e._s(e.text_see_all)}}),e._v(" "),o("a",{staticClass:"monsterinsights-button monsterinsights-button-small",attrs:{href:e.$mi.reports_url},domProps:{innerHTML:e._s(e.text_go_to)}})])},W1=[],F1=f(S1,E1,W1,!1,null,null,null,null);const B1=F1.exports;let k=!1,$=!1,T=!1;const A1={name:"WidgetAccordion",components:{WidgetReportsLink:B1,ReportUpsellOverlay:J,WidgetReportError:t1,SettingsInfoTooltip:e1,WidgetReportOverview:D1},props:{mobileWidth:{default:782,type:Number}},data(){return{activeReport:"overview",reportsWithUpsell:{},isMobile:!1}},created(){if(this.$mi.widget_state&&this.$mi.widget_state.interval){const s=this.$mi.widget_state.interval,e=this.$mi_intervals(),o=e[s]||e.last30days,i=o.start||M().subtract(1,"days"),r=o.end||M.subtract(30,"days");this.$store.commit("$_reports/UPDATE_INTERVAL",this.$mi.widget_state.interval),this.$store.commit("$_reports/UPDATE_DATE",{start:i.format("YYYY-MM-DD"),end:r.format("YYYY-MM-DD")})}},computed:{...v({widget_reports:"$_widget/reports",widget_width:"$_widget/width",loaded:"$_widget/loaded",error:"$_widget/error"}),widgetFullWidth(){return this.widget_width!=="regular"},widgetReports(){let s={},e={};k=!1;for(let o in this.widget_reports)if(this.widget_reports.hasOwnProperty(o)&&this.widget_reports[o].enabled){if(this.widgetFullWidth){if(typeof this.reportsWithUpsell[o]<"u")if(k===!1)k=!0;else continue;if(this.error[this.widget_reports[o].type]){let i=this.widget_reports[o].type;if(e[i]=e[i]?e[i]+1:1,e[i]>1)continue}}s[o]=this.widget_reports[o]}return s}},methods:{maybeHideUpsell(s){this.$set(this.reportsWithUpsell,s,1),this.widgetFullWidth&&!T&&(T=!0,this.$forceUpdate())},toggle(s,e){const o=this.widget_reports[e].type,i=this;i.$store.commit("$_widget/UPDATE_LOADED",!1),i.$store.commit("$_widget/SET_ERROR",{report:o}),this.$store.dispatch("$_reports/getReportData",o).then(function(){i.$store.commit("$_widget/UPDATE_LOADED",!0)}),this.activeReport=e===this.activeReport?"":e,this.activeReport!==""&&this.scrollIntoView(s.target)},toggleClass(s){let e="monsterinsights-widget-toggle";return this.activeReport===s&&(e+=" monsterinsights-widget-toggle-active"),e},showReport(s,e){return this.widgetFullWidth&&e.enabled&&!this.isMobile?!0:this.activeReport===s},reportClass(s){return"monsterinsights-widget-report-element monsterinsights-widget-report-"+s},scrollIntoView(s){this.$nextTick(()=>{let e=s.getBoundingClientRect();window.scrollTo({top:e.top-50+pageYOffset,left:0,behavior:"smooth"})})},showReportTitle(s){return this.widgetFullWidth?!0:s},handleResize(){$||($=!0,window.requestAnimationFrame?window.requestAnimationFrame(this.resizeCallback):setTimeout(this.resizeCallback,66))},resizeCallback(){this.isMobile=window.innerWidth<this.mobileWidth,$=!1}},mounted(){const s=this;this.$store.dispatch("$_reports/getReportData","overview").then(function(){s.$store.commit("$_widget/UPDATE_LOADED",!0),s.$forceUpdate()}),window.addEventListener("resize",this.handleResize),this.handleResize()},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)}};var I1=function(){var e=this,o=e._self._c;return o("div",{staticClass:"monsterinsights-widget-accordion monsterinsights-widget-accordion-lite"},[e._l(e.widgetReports,function(i,r){return o("div",{key:r,class:e.reportClass(r)},[e.showReportTitle(i.enabled)?o("div",{class:e.toggleClass(r),attrs:{tabindex:"0"},on:{click:function(n){return n.preventDefault(),e.toggle(n,r)},keyup:[function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"space",32,n.key,[" ","Spacebar"])?null:e.toggle(n,r)},function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"enter",13,n.key,"Enter")?null:e.toggle(n,r)}]}},[o("h2",{staticClass:"monsterinsights-widget-report-title"},[o("span",{domProps:{textContent:e._s(i.name)}}),i.tooltip?o("settings-info-tooltip",{attrs:{content:i.tooltip}}):e._e()],1)]):e._e(),e.showReport(r,i)?o("div",{staticClass:"monsterinsights-widget-content"},[e.error[i.type]?o("widget-report-error",{attrs:{error:e.error[i.type]}}):r==="overview"&&e.loaded?o("WidgetReportOverview"):e.loaded?o("report-upsell-overlay",{attrs:{report:i.type,"no-image":"","show-sample-button":!1}}):o("div",{staticClass:"monsterinsights-widget-loading"})],1):e._e()])}),e.widgetFullWidth?e._e():o("widget-reports-link")],2)},z1=[],N1=f(A1,I1,z1,!1,null,null,null,null);const O1=N1.exports,{__:b}=wp.i18n,U1={name:"WidgetSettingsHide",data(){return{text_hide_widget:b("Hide dashboard widget","google-analytics-for-wordpress")}},computed:{...v({widget_width:"$_widget/width"}),fullWidth(){return this.widget_width!=="regular"}},methods:{hideWidget(){const s=this;this.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:b("Are you sure you want to hide the MonsterInsights Dashboard Widget? ","google-analytics-for-wordpress"),showCancelButton:!0,confirmButtonText:b("Yes, hide it!","google-analytics-for-wordpress"),cancelButtonText:b("No, cancel!","google-analytics-for-wordpress"),reverseButtons:!0}).then(function(e){if(e.value){s.$swal({icon:"success",title:b("MonsterInsights Widget Hidden","google-analytics-for-wordpress"),html:b('You can re-enable the MonsterInsights widget at any time using the "Screen Options" menu on the top right of this page',"google-analytics-for-wordpress")});const o=document.getElementById("monsterinsights_reports_widget-hide");o&&o.click()}})}}};var Y1=function(){var e=this,o=e._self._c;return o("button",{staticClass:"monsterinsights-hide-button",domProps:{textContent:e._s(e.text_hide_widget)},on:{click:function(i){return i.preventDefault(),e.hideWidget.apply(null,arguments)}}})},G1=[],j1=f(U1,Y1,G1,!1,null,null,null,null);const K1=j1.exports,q1={name:"WidgetSettingsCompact"};var J1=function(){var e=this,o=e._self._c;return o("div")},Q1=[],X1=f(q1,J1,Q1,!1,null,null,null,null);const e2=X1.exports,{__:V}=wp.i18n;a.directive("click-outside",{bind:function(s,e,o){s.clickOutsideEvent=function(i){s===i.target||s.contains(i.target)||o.context[e.expression](i)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}});const t2={name:"WidgetSettingsReports",components:{WidgetSettingsCompact:e2,WidgetSettingsHide:K1},data(){return{dropdownVisible:!1,text_settings_overview:V("Show Overview Reports","google-analytics-for-wordpress"),text_settings_publisher:V("Show Publishers Reports","google-analytics-for-wordpress"),text_settings_ecommerce:V("Show eCommerce Reports","google-analytics-for-wordpress"),text_settings_dropdown:V("Settings Menu","google-analytics-for-wordpress"),tooltip_data:{content:V("Available in PRO version","google-analytics-for-wordpress"),autoHide:!1,trigger:"hover focus click"}}},computed:{...v({widget_reports:"$_widget/reports"}),reportSettings(){let s={};for(let e in this.widget_reports)this.widget_reports.hasOwnProperty(e)&&e!=="overview"&&(s[e]=this.widget_reports[e]);return s}},methods:{toggleReport(s,e){this.widget_reports[e].enabled?this.$store.commit("$_widget/DISABLE_REPORT",e):(this.$store.commit("$_widget/ENABLE_REPORT",e),this.fullWidth&&this.getReportData(e)),this.saveState()},getReportSettings(s){let e={};for(let o in this.reportSettings)this.reportSettings.hasOwnProperty(o)&&o!=="overview"&&s===this.reportSettings[o].type&&(e[o]=this.reportSettings[o]);return e},toggleDropdown(){this.dropdownVisible=!this.dropdownVisible},hideDropdown(){this.dropdownVisible=!1},saveState(){this.$store.dispatch("$_widget/saveWidgetState")}}};var o2=function(){var e=this,o=e._self._c;return o("div",{staticClass:"monsterinsights-widget-dropdown"},[o("button",{staticClass:"monsterinsights-widget-cog",attrs:{"aria-label":e.text_settings_dropdown,"aria-pressed":e.dropdownVisible,type:"button"},on:{click:function(i){return i.stopPropagation(),e.toggleDropdown.apply(null,arguments)}}},[o("i",{staticClass:"monstericon-cog"})]),e.dropdownVisible?o("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.hideDropdown,expression:"hideDropdown"}],staticClass:"monsterinsights-widget-dropdown-content"},[o("widget-settings-compact"),o("span",{domProps:{textContent:e._s(e.text_settings_overview)}}),e._l(e.getReportSettings("overview"),function(i,r){return o("div",{key:r,staticClass:"monsterinsights-widget-setting"},[o("label",{class:i.enabled?"monsterinsights-checked":"",attrs:{tabindex:"0"},on:{click:function(n){return n.preventDefault(),e.toggleReport(n,r)},keyup:[function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"enter",13,n.key,"Enter")?null:e.toggleReport(n,r)},function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"space",32,n.key,[" ","Spacebar"])?null:e.toggleReport(n,r)}]}},[o("input",{attrs:{type:"checkbox"},domProps:{checked:i.enabled}}),e._v(" "+e._s(i.name)+" ")])])}),o("span",{domProps:{textContent:e._s(e.text_settings_publisher)}}),e._l(e.getReportSettings("publisher"),function(i,r){return o("div",{key:r,staticClass:"monsterinsights-widget-setting"},[o("label",{directives:[{name:"tooltip",rawName:"v-tooltip.left",value:e.tooltip_data,expression:"tooltip_data",modifiers:{left:!0}}],staticClass:"monsterinsights-faded",attrs:{tabindex:"0"}},[o("input",{attrs:{type:"checkbox"},domProps:{checked:i.enabled}}),e._v(" "+e._s(i.name)+" ")])])}),o("span",{domProps:{textContent:e._s(e.text_settings_ecommerce)}}),e._l(e.getReportSettings("ecommerce"),function(i,r){return o("div",{key:r,staticClass:"monsterinsights-widget-setting"},[o("label",{directives:[{name:"tooltip",rawName:"v-tooltip.left",value:e.tooltip_data,expression:"tooltip_data",modifiers:{left:!0}}],staticClass:"monsterinsights-faded",attrs:{tabindex:"0"}},[o("input",{attrs:{type:"checkbox"},domProps:{checked:i.enabled}}),e._v(" "+e._s(i.name)+" ")])])}),o("widget-settings-hide")],2):e._e()])},s2=[],i2=f(t2,o2,s2,!1,null,null,null,null);const r2=i2.exports,{__:D}=wp.i18n,n2={name:"WidgetSettingsWidth",data(){return{normal_sortables:"",widget_element:"",welcome_panel:""}},computed:{...v({widget_width:"$_widget/width",widget_reports:"$_widget/reports"}),fullWidth:{set(s){let e="regular";s&&(e="full"),this.$store.commit("$_widget/UPDATE_WIDTH",e),this.saveState()},get(){return this.widget_width!=="regular"}},tooltip_data(){return{content:this.fullWidth?D("Show in widget mode","google-analytics-for-wordpress"):D("Show in full-width mode","google-analytics-for-wordpress"),autoHide:!1,trigger:"hover focus click"}}},methods:{toggleFullWidth(s){if(s!==!0&&(this.fullWidth=!this.fullWidth),this.fullWidth)this.widget_element.classList.add("monsterinsights-widget-full-width"),this.widget_element.classList.remove("monsterinsights-widget-regular-width"),this.welcome_panel.parentNode.insertBefore(this.widget_element,this.welcome_panel),this.getActiveReportsData();else{if(this.widget_element.classList.add("monsterinsights-widget-regular-width"),s===!0)return;this.widget_element.classList.remove("monsterinsights-widget-full-width"),this.normal_sortables.insertBefore(this.widget_element,this.normal_sortables.firstChild),this.normal_sortables.classList.remove("empty-container")}},getActiveReportsData(){const s=this;let e={};for(let r in this.widget_reports)this.widget_reports.hasOwnProperty(r)&&this.widget_reports[r].enabled&&(e[this.widget_reports[r].type]=1);let o=Object.keys(e).length,i=0;for(let r in e)e.hasOwnProperty(r)&&(i++,s.$store.commit("$_widget/UPDATE_LOADED",!1),this.$store.dispatch("$_reports/getReportData",r).then(function(){i===o&&s.$store.commit("$_widget/UPDATE_LOADED",!0)}))},saveState(){this.$store.dispatch("$_widget/saveWidgetState")}},mounted(){this.widget_element=document.getElementById("monsterinsights_reports_widget"),this.normal_sortables=document.getElementById("normal-sortables"),this.welcome_panel=document.getElementById("dashboard-widgets-wrap")}};var a2=function(){var e=this,o=e._self._c;return o("button",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.tooltip_data,expression:"tooltip_data"}],staticClass:"monsterinsights-width-button",attrs:{"aria-label":e.tooltip_data.content},on:{click:e.toggleFullWidth}},[e.fullWidth?o("i",{staticClass:"monstericon-compress"}):o("i",{staticClass:"monstericon-expand"})])},l2=[],C2=f(n2,a2,l2,!1,null,null,null,null);const c2=C2.exports,{sprintf:d2}=wp.i18n,p2={name:"WidgetSettings",components:{WidgetSettingsWidth:c2,WidgetSettingsReports:r2},computed:{selectedIntervalText(){const s=this.$mi.widget_state.interval,e=this.$mi_intervals();return(e[s]||e.last30days).text}},methods:{toggleFullWidth(){this.$refs.width.toggleFullWidth(!0)},sprintf:d2}};var g2=function(){var e=this,o=e._self._c;return o("div",{staticClass:"monsterinsights-widget-settings"},[o("div",{staticClass:"monsterinsights-widget-interval-static",domProps:{textContent:e._s(e.selectedIntervalText)}}),o("widget-settings-width",{ref:"width"}),o("widget-settings-reports")],1)},h2=[],f2=f(p2,g2,h2,!1,null,null,null,null);const u2=f2.exports,{__:x,sprintf:w2}=wp.i18n,m2={name:"WidgetFooter",data(){return{display:!1,text_learn_more:x("Learn More","google-analytics-for-wordpress"),learn_url:null,action_text:null,action_url:null,plugin_name:null}},created(){this.display=!0,this.action_url=this.$mi.userfeedback_url,this.plugin_name="UserFeedback";let s="milink";if(this.learn_url="https://userfeedback.com/?utm_source=monsterinsights&utm_medium=referral&utm_campaign="+s,!this.$mi.userfeedback_installed){this.action_text=x("Install","google-analytics-for-wordpress");return}if(!this.$mi.userfeedback_enabled){this.action_text=x("Activate","google-analytics-for-wordpress");return}if(this.plugin_name="WPForms",this.action_url=this.$mi.wpforms_url,this.learn_url="https://wpforms.com/",!this.$mi.wpforms_installed){this.action_text=x("Install","google-analytics-for-wordpress");return}if(!this.$mi.wpforms_enabled){this.action_text=x("Activate","google-analytics-for-wordpress");return}this.display=!1},computed:{showRecommended(){return this.display},getRecommendedText(){return w2('%1$s <span class="monsterinsights-dark">%2$s</span>',x("Recommended Plugin:","google-analytics-for-wordpress"),this.plugin_name)}}};var _2=function(){var e=this,o=e._self._c;return e.showRecommended?o("div",{staticClass:"monsterinsights-widget-footer"},[o("span",{domProps:{innerHTML:e._s(e.getRecommendedText)}}),o("span",[e._v(" - ")]),o("a",{attrs:{href:e.action_url},domProps:{textContent:e._s(e.action_text)}}),o("a",{attrs:{href:e.learn_url,target:"_blank"},domProps:{textContent:e._s(e.text_learn_more)}})]):e._e()},v2=[],y2=f(m2,_2,v2,!1,null,null,null,null);const L2=y2.exports,{__:b2}=wp.i18n,x2={name:"ModuleDashboardWidget",components:{ReportReAuth:Q,WidgetFooter:L2,WidgetSettings:u2,WidgetAccordion:O1},data(){return{text_overview_report:b2("Overview Report","google-analytics-for-wordpress")}},computed:{...v({blocked:"$_app/blocked",blur:"$_reports/blur",widget_width:"$_widget/width",compact:"$_widget/compact",reauth:"$_reports/reauth"}),route(){return this.$route.name},mainClass(){let s="monsterinsights-dashboard-widget-page";return this.blur&&(s+=" monsterinsights-blur"),this.compact&&(s+=" monsterinsights-dashboard-widget-compact"),s},fullWidth(){return this.widget_width!=="regular"}},created(){const s="$_reports";s in this.$store._modules.root._children||this.$store.registerModule(s,K);const e="$_widget";e in this.$store._modules.root._children||this.$store.registerModule(e,x1)},mounted(){this.$store.dispatch("$_widget/processDefaults").then(()=>{this.$nextTick(()=>{this.$refs.settings.toggleFullWidth(!0)})})}};var M2=function(){var e=this,o=e._self._c;return o("div",{class:e.mainClass},[o("widget-settings",{ref:"settings"}),e.reauth?o("report-re-auth"):o("widget-accordion"),e.fullWidth?o("div",{staticClass:"monsterinsights-fullwidth-mascot"}):e._e(),e.fullWidth?o("div",{staticClass:"monsterinsights-fullwidth-report-title",domProps:{textContent:e._s(e.text_overview_report)}}):e._e(),e.fullWidth?e._e():o("widget-footer")],1)},H2=[],V2=f(x2,M2,H2,!1,null,null,null,null);const k2=V2.exports;const{__:R}=wp.i18n,$2={name:"WidgetReminder",data(){return{notifications:[],icons:{default:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#D3F8EA"/><path d="M21.8634 18.6429C21.8634 18.8571 21.7831 19.0268 21.6224 19.1518C21.5688 19.3482 21.542 19.6786 21.542 20.1429C21.542 20.6071 21.5688 20.9375 21.6224 21.1339C21.7831 21.2768 21.8634 21.4464 21.8634 21.6429V22.0714C21.8634 22.25 21.8009 22.4018 21.6759 22.5268C21.5509 22.6518 21.3992 22.7143 21.2206 22.7143H12.4349C11.7206 22.7143 11.1134 22.4643 10.6134 21.9643C10.1134 21.4643 9.86345 20.8571 9.86345 20.1429V11.5714C9.86345 10.8571 10.1134 10.25 10.6134 9.75C11.1134 9.25 11.7206 9 12.4349 9H21.2206C21.3992 9 21.5509 9.0625 21.6759 9.1875C21.8009 9.3125 21.8634 9.46429 21.8634 9.64286V18.6429ZM13.292 12.5893V13.125C13.292 13.2321 13.3456 13.2857 13.4527 13.2857H19.1313C19.2384 13.2857 19.292 13.2321 19.292 13.125V12.5893C19.292 12.4821 19.2384 12.4286 19.1313 12.4286H13.4527C13.3456 12.4286 13.292 12.4821 13.292 12.5893ZM13.292 14.3036V14.8393C13.292 14.9464 13.3456 15 13.4527 15H19.1313C19.2384 15 19.292 14.9464 19.292 14.8393V14.3036C19.292 14.1964 19.2384 14.1429 19.1313 14.1429H13.4527C13.3456 14.1429 13.292 14.1964 13.292 14.3036ZM20.0688 21C20.0152 20.4286 20.0152 19.8571 20.0688 19.2857H12.4349C12.2027 19.2857 11.9974 19.375 11.8188 19.5536C11.6581 19.7143 11.5777 19.9107 11.5777 20.1429C11.5777 20.375 11.6581 20.5804 11.8188 20.7589C11.9974 20.9196 12.2027 21 12.4349 21H20.0688Z" fill="#1EC185"/></svg>',star:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#D4E7F7"/><path d="M15.0867 9.48214C15.2474 9.16071 15.5063 9 15.8634 9C16.2206 9 16.4795 9.16071 16.6402 9.48214L18.3813 13.0179L22.292 13.6071C22.6492 13.6429 22.8813 13.8304 22.9884 14.1696C23.0956 14.5089 23.0242 14.8036 22.7742 15.0536L19.9349 17.8125L20.6045 21.7232C20.6581 22.0625 20.542 22.3304 20.2563 22.5268C19.9706 22.7411 19.6759 22.7679 19.3724 22.6071L15.8634 20.7857L12.3545 22.6071C12.0509 22.7857 11.7563 22.7679 11.4706 22.5536C11.1849 22.3393 11.0688 22.0625 11.1224 21.7232L11.792 17.8125L8.95274 15.0536C8.70274 14.8036 8.63131 14.5089 8.73845 14.1696C8.84559 13.8304 9.07774 13.6429 9.43488 13.6071L13.3456 13.0179L15.0867 9.48214Z" fill="#2679C1"/></svg>',warning:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#FAD1D1"/><path d="M17.3634 19.0714C17.792 19.4821 18.0063 19.9821 18.0063 20.5714C18.0063 21.1607 17.792 21.6607 17.3634 22.0714C16.9527 22.5 16.4527 22.7143 15.8634 22.7143C15.2742 22.7143 14.7652 22.5 14.3367 22.0714C13.9259 21.6607 13.7206 21.1607 13.7206 20.5714C13.7206 19.9821 13.9259 19.4821 14.3367 19.0714C14.7652 18.6429 15.2742 18.4286 15.8634 18.4286C16.4527 18.4286 16.9527 18.6429 17.3634 19.0714ZM13.9617 9.66964C13.9617 9.49107 14.0242 9.33929 14.1492 9.21429C14.2742 9.07143 14.4259 9 14.6045 9H17.1224C17.3009 9 17.4527 9.07143 17.5777 9.21429C17.7027 9.33929 17.7652 9.49107 17.7652 9.66964L17.3902 16.9554C17.3902 17.1339 17.3277 17.2857 17.2027 17.4107C17.0777 17.5179 16.9259 17.5714 16.7474 17.5714H14.9795C14.8009 17.5714 14.6492 17.5179 14.5242 17.4107C14.3992 17.2857 14.3367 17.1339 14.3367 16.9554L13.9617 9.66964Z" fill="#EB5757"/></svg>',lightning:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#E1DAF1"/><path d="M20.0331 13.2857C20.2831 13.2857 20.4706 13.3929 20.5956 13.6071C20.7206 13.8214 20.7206 14.0357 20.5956 14.25L15.8813 22.3929C15.7563 22.6071 15.5688 22.7143 15.3188 22.7143C15.1045 22.7143 14.9349 22.6339 14.8099 22.4732C14.6849 22.3125 14.6492 22.125 14.7027 21.9107L15.9349 16.7143H12.7474C12.6224 16.7143 12.5063 16.6786 12.3992 16.6071C12.292 16.5357 12.2117 16.4464 12.1581 16.3393C12.1045 16.2321 12.0867 16.1161 12.1045 15.9911L12.9617 9.5625C12.9795 9.45536 13.0152 9.35714 13.0688 9.26786C13.1402 9.17857 13.2206 9.11607 13.3099 9.08036C13.3992 9.02679 13.4974 9 13.6045 9H17.4617C17.6759 9 17.8456 9.08929 17.9706 9.26786C18.0956 9.42857 18.1313 9.60714 18.0777 9.80357L16.9527 13.2857H20.0331Z" fill="#6F4BBB"/></svg>'},text_expired_license:{title:R("Your MonsterInsights license key has expired.","google-analytics-for-wordpress"),content:R("Renew today to ensure Google Analytics continues to track properly.","google-analytics-for-wordpress"),btn:{text:R("Click here.","google-analytics-for-wordpress"),url:this.$getUrl("vue-admin-notification","expired-license","https://www.monsterinsights.com/my-account/")}},elem_id_expired_license:"expired-license-notice",showLoader:!1}},computed:{...v({license:"$_license/license",license_network:"$_license/license_network"})},mounted(){this.getNotifications(),this.checkExpiredLicenseNoticeStatus()},methods:{async getNotifications(){const s=await o1.fetchNotifications();return s?this.notifications=s.notifications:!1},hideNotice(s){let e=this;this.showLoader=!0,this.$store.dispatch("$_notifications/dismissNotification",s).then(()=>{e.getNotifications(),this.showLoader=!1})},isLicenseExpired(){return this.license!=="undefined"?this.license.is_expired:!1},isNetworkLicenseExpired(){return this.license_network!=="undefined"?this.license_network.is_expired:!1},showExpiredLicenseNotice(){const s=localStorage.getItem("hideExpiredLicenseNotice");return!(s&&parseInt(s)===1)},hideExpiredNotice(s){const e=M(M().tz(this.$mi.timezone).format("YYYY-MM-DD"));localStorage.setItem("hideExpiredLicenseNotice",1),localStorage.setItem("hideExpiredLicenseNoticeTime",e);let o=document.getElementById(s);o.style.display="none"},checkExpiredLicenseNoticeStatus(){const s=localStorage.getItem("hideExpiredLicenseNoticeTime");s&&M(M().tz(this.$mi.timezone).format("YYYY-MM-DD")).diff(s)>=1&&(localStorage.removeItem("hideExpiredLicenseNotice"),localStorage.removeItem("hideExpiredLicenseNoticeTime"))}}};var R2=function(){var e=this,o=e._self._c;return o("div",{staticClass:"monsterinsights-reminder-widget-container"},[e.isLicenseExpired()||e.isNetworkLicenseExpired()?o("transition",{attrs:{name:"monsterinsights-slide-up"}},[e.showExpiredLicenseNotice()?o("div",{attrs:{id:e.elem_id_expired_license}},[o("div",{staticClass:"monsterinsights-reminder-widget"},[o("div",{staticClass:"monsterinsights-tracking-notice"},[o("div",{staticClass:"monsterinsights-tracking-notice-icon"},[o("div",{domProps:{innerHTML:e._s(e.icons.warning)}})]),o("div",{staticClass:"monsterinsights-tracking-notice-text"},[o("h3",{domProps:{textContent:e._s(e.text_expired_license.title)}}),o("p",[o("span",{domProps:{innerHTML:e._s(e.text_expired_license.content)}}),o("br"),o("br"),o("span",[o("a",{staticClass:"monsterinsights-reminder-notice-links",attrs:{target:"_blank",href:e.text_expired_license.btn.url,title:e.text_expired_license.btn.text}},[e._v(" "+e._s(e.text_expired_license.btn.text)+" ")])])])]),o("div",{staticClass:"monsterinsights-tracking-notice-close",on:{click:function(i){return e.hideExpiredNotice(e.elem_id_expired_license)}}},[e._v(" × ")])])])]):e._e()]):e._e(),e.notifications?o("div",[e.showLoader?o("div",{staticClass:"monsterinsights-notice-closing-loader"}):e._e(),o("TransitionGroup",{attrs:{name:"monsterinsights-slide-up",tag:"div"}},e._l(e.notifications,function(i,r){return o("div",{key:r,staticClass:"monsterinsights-reminder-widget"},[o("div",{staticClass:"monsterinsights-tracking-notice"},[o("div",{staticClass:"monsterinsights-tracking-notice-icon"},[o("div",{domProps:{innerHTML:e._s(e.icons[i.icon])}})]),o("div",{staticClass:"monsterinsights-tracking-notice-text"},[o("h3",{domProps:{textContent:e._s(i.title)}}),o("p",[o("span",{domProps:{innerHTML:e._s(i.content)}}),o("br"),i.btns?o("span",e._l(i.btns,function(n,d){return o("a",{key:d,staticClass:"monsterinsights-reminder-notice-links",attrs:{target:n.is_external?"_blank":"_self",href:n.url},domProps:{textContent:e._s(n.text)},on:{click:function(c){return e.hideNotice(i.id)}}})}),0):e._e()])]),o("div",{staticClass:"monsterinsights-tracking-notice-close",on:{click:function(n){return e.hideNotice(i.id)}}},[e._v(" × ")])])])}),0)],1):e._e()],1)},Z2=[],P2=f($2,R2,Z2,!1,null,null,null,null);const T2=P2.exports,D2={"addon-ads":{viewBox:"0 0 512 512",content:'<path d="M497.941 225.941L286.059 14.059A48 48 0 0 0 252.118 0H48C21.49 0 0 21.49 0 48v204.118a47.998 47.998 0 0 0 14.059 33.941l211.882 211.882c18.745 18.745 49.137 18.746 67.882 0l204.118-204.118c18.745-18.745 18.745-49.137 0-67.882zM259.886 463.996L48 252.118V48h204.118L464 259.882 259.886 463.996zM192 144c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48z" fill="currentColor"/>'},"addon-ai-insights":{viewBox:"0 0 19 19",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M13.2226 1.65012C13.4579 0.575261 14.9892 0.568832 15.2348 1.6424L15.2451 1.69255L15.2682 1.79283C15.5511 2.99369 16.5231 3.9104 17.7394 4.12255C18.8605 4.31798 18.8605 5.92769 17.7394 6.12312C17.1413 6.22722 16.5873 6.50571 16.1469 6.92364C15.7066 7.34156 15.3995 7.88027 15.2644 8.47212L15.2335 8.60326C14.9892 9.67683 13.4592 9.6704 13.2226 8.59555L13.1982 8.4824C13.0684 7.88823 12.7645 7.3462 12.3253 6.92553C11.8861 6.50485 11.3314 6.2246 10.7322 6.12055C9.61364 5.9264 9.61364 4.31926 10.7322 4.12512C11.3293 4.0215 11.8821 3.74294 12.3207 3.32475C12.7592 2.90656 13.0638 2.36758 13.1956 1.77612L13.2136 1.69126L13.2226 1.65012ZM14.1574 10.8018C13.5447 10.7899 12.9592 10.547 12.5181 10.1217C12.4211 10.1856 12.3366 10.2667 12.2686 10.3608C11.6901 11.1271 11.0125 11.9101 10.2462 12.6751C9.66507 13.2563 9.07621 13.7847 8.49507 14.2565C7.91393 13.7847 7.32507 13.2563 6.74393 12.6751C6.18707 12.1194 5.65923 11.5353 5.1625 10.9253C5.63435 10.3428 6.16407 9.75526 6.74393 9.17412C7.46794 8.44757 8.2412 7.77182 9.05821 7.15169C9.14353 7.09057 9.21824 7.01586 9.27935 6.93055C9.03494 6.7077 8.83914 6.43681 8.70419 6.13484C8.56924 5.83286 8.49805 5.50629 8.49507 5.17555C7.23378 4.29226 5.98664 3.63783 4.87321 3.29712C3.66721 2.92812 2.25293 2.82912 1.32593 3.75483C0.725497 4.35655 0.562212 5.17426 0.608497 5.95212C0.654783 6.73126 0.915783 7.58755 1.3105 8.45669C1.71315 9.32151 2.19386 10.1478 2.74664 10.9253C2.19399 11.7019 1.71329 12.5273 1.3105 13.3913C0.915783 14.2604 0.654783 15.1167 0.608497 15.8958C0.562212 16.6737 0.724212 17.4914 1.32593 18.0931C1.92764 18.6935 2.74535 18.8568 3.52321 18.8105C4.30107 18.763 5.15864 18.5033 6.02778 18.1085C6.81207 17.7524 7.6465 17.2664 8.49635 16.6724C9.34493 17.2664 10.1781 17.7524 10.9636 18.1085C11.8315 18.5033 12.6891 18.7643 13.4682 18.8105C14.2461 18.8568 15.0625 18.6935 15.6642 18.0918C16.5912 17.1661 16.4922 15.7518 16.1232 14.5458C15.7709 13.3964 15.0856 12.1043 14.1574 10.8018ZM4.30878 5.14083C5.04935 5.36712 5.93264 5.79655 6.88921 6.41883C5.84483 7.30492 4.87473 8.27502 3.98864 9.3194C3.64043 8.78918 3.33188 8.23396 3.0655 7.65826C2.72735 6.91255 2.5615 6.2954 2.53321 5.8364C2.50621 5.37483 2.61935 5.18969 2.69007 5.11898C2.80578 5.00326 3.23264 4.81297 4.30878 5.14083ZM3.0655 14.191C3.30207 13.6703 3.61193 13.111 3.98864 12.5298C4.87516 13.5742 5.84569 14.5443 6.8905 15.4304C6.36072 15.779 5.80592 16.088 5.23064 16.3548C4.48493 16.693 3.86778 16.8588 3.40878 16.8871C2.94593 16.9141 2.76207 16.801 2.69135 16.7303C2.62064 16.6595 2.5075 16.4731 2.5345 16.0128C2.56278 15.5538 2.72735 14.9367 3.06678 14.191H3.0655ZM11.7608 16.3548C11.1857 16.0883 10.6313 15.7793 10.1022 15.4304C11.1457 14.5442 12.1149 13.5741 13.0002 12.5298C13.6212 13.4877 14.0506 14.371 14.2769 15.1115C14.6061 16.1864 14.4158 16.6145 14.3001 16.7303C14.2281 16.801 14.0429 16.9141 13.5826 16.8858C13.1224 16.8601 12.5065 16.693 11.7608 16.3548ZM7.20935 10.9253C7.20935 10.5843 7.34481 10.2572 7.58593 10.0161C7.82705 9.77501 8.15408 9.63955 8.49507 9.63955C8.83606 9.63955 9.16309 9.77501 9.40421 10.0161C9.64532 10.2572 9.78078 10.5843 9.78078 10.9253C9.78078 11.2663 9.64532 11.5933 9.40421 11.8344C9.16309 12.0755 8.83606 12.211 8.49507 12.211C8.15408 12.211 7.82705 12.0755 7.58593 11.8344C7.34481 11.5933 7.20935 11.2663 7.20935 10.9253Z" fill="currentColor" />'},"addon-amp":{viewBox:"0 0 320 512",content:'<path d="M192 416c0 17.7-14.3 32-32 32s-32-14.3-32-32 14.3-32 32-32 32 14.3 32 32zM320 48v416c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V48C0 21.5 21.5 0 48 0h224c26.5 0 48 21.5 48 48zm-48 410V54c0-3.3-2.7-6-6-6H54c-3.3 0-6 2.7-6 6v404c0 3.3 2.7 6 6 6h212c3.3 0 6-2.7 6-6z" fill="currentColor"/>'},"addon-dimensions":{viewBox:"0 0 576 512",content:'<path d="M128 288c-17.67 0-32 14.33-32 32s14.33 32 32 32 32-14.33 32-32-14.33-32-32-32zm154.65-97.08l16.24-48.71c1.16-3.45 3.18-6.35 4.92-9.43-4.73-2.76-9.94-4.78-15.81-4.78-17.67 0-32 14.33-32 32 0 15.78 11.63 28.29 26.65 30.92zM176 176c-17.67 0-32 14.33-32 32s14.33 32 32 32 32-14.33 32-32-14.33-32-32-32zM288 32C128.94 32 0 160.94 0 320c0 52.8 14.25 102.26 39.06 144.8 5.61 9.62 16.3 15.2 27.44 15.2h443c11.14 0 21.83-5.58 27.44-15.2C561.75 422.26 576 372.8 576 320c0-159.06-128.94-288-288-288zm212.27 400H75.73C57.56 397.63 48 359.12 48 320 48 187.66 155.66 80 288 80s240 107.66 240 240c0 39.12-9.56 77.63-27.73 112zM416 320c0 17.67 14.33 32 32 32s32-14.33 32-32-14.33-32-32-32-32 14.33-32 32zm-56.41-182.77c-12.72-4.23-26.16 2.62-30.38 15.17l-45.34 136.01C250.49 290.58 224 318.06 224 352c0 11.72 3.38 22.55 8.88 32h110.25c5.5-9.45 8.88-20.28 8.88-32 0-19.45-8.86-36.66-22.55-48.4l45.34-136.01c4.17-12.57-2.64-26.17-15.21-30.36zM432 208c0-15.8-11.66-28.33-26.72-30.93-.07.21-.07.43-.14.65l-19.5 58.49c4.37 2.24 9.11 3.8 14.36 3.8 17.67-.01 32-14.34 32-32.01z" fill="currentColor"/>'},"addon-ecommerce":{viewBox:"0 0 448 512",content:'<path d="M352 128C352 57.42 294.579 0 224 0 153.42 0 96 57.42 96 128H0v304c0 44.183 35.817 80 80 80h288c44.183 0 80-35.817 80-80V128h-96zM224 48c44.112 0 80 35.888 80 80H144c0-44.112 35.888-80 80-80zm176 384c0 17.645-14.355 32-32 32H80c-17.645 0-32-14.355-32-32V176h48v40c0 13.255 10.745 24 24 24s24-10.745 24-24v-40h160v40c0 13.255 10.745 24 24 24s24-10.745 24-24v-40h48v256z" fill="currentColor"/>'},"addon-eu-compliance":{viewBox:"0 0 576 512",content:'<path d="M528 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm0 400H303.2c.9-4.5.8 3.6.8-22.4 0-31.8-30.1-57.6-67.2-57.6-10.8 0-18.7 8-44.8 8-26.9 0-33.4-8-44.8-8-37.1 0-67.2 25.8-67.2 57.6 0 26-.2 17.9.8 22.4H48V144h480v288zm-168-80h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8zm0-64h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8zm0-64h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8zm-168 96c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64z" fill="currentColor"/>'},"addon-forms":{viewBox:"0 0 384 512",content:'<path d="M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM192 40c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm144 418c0 3.3-2.7 6-6 6H54c-3.3 0-6-2.7-6-6V118c0-3.3 2.7-6 6-6h42v36c0 6.6 5.4 12 12 12h168c6.6 0 12-5.4 12-12v-36h42c3.3 0 6 2.7 6 6z" fill="currentColor"/>'},"addon-media":{viewBox:"0 0 576 512",content:'<path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z" fill="currentColor"/>'},"addon-page-insights":{viewBox:"0 0 512 512",content:'<path d="M396.8 352h22.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-22.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-192 0h22.4c6.4 0 12.8-6.4 12.8-12.8V140.8c0-6.4-6.4-12.8-12.8-12.8h-22.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h22.4c6.4 0 12.8-6.4 12.8-12.8V204.8c0-6.4-6.4-12.8-12.8-12.8h-22.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zM496 400H48V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-16c0-8.84-7.16-16-16-16zm-387.2-48h22.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-22.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8z" fill="currentColor"/>'},"addon-performance":{viewBox:"0 0 576 512",content:'<path d="M128 288c-17.67 0-32 14.33-32 32s14.33 32 32 32 32-14.33 32-32-14.33-32-32-32zm154.65-97.08l16.24-48.71c1.16-3.45 3.18-6.35 4.92-9.43-4.73-2.76-9.94-4.78-15.81-4.78-17.67 0-32 14.33-32 32 0 15.78 11.63 28.29 26.65 30.92zM176 176c-17.67 0-32 14.33-32 32s14.33 32 32 32 32-14.33 32-32-14.33-32-32-32zM288 32C128.94 32 0 160.94 0 320c0 52.8 14.25 102.26 39.06 144.8 5.61 9.62 16.3 15.2 27.44 15.2h443c11.14 0 21.83-5.58 27.44-15.2C561.75 422.26 576 372.8 576 320c0-159.06-128.94-288-288-288zm212.27 400H75.73C57.56 397.63 48 359.12 48 320 48 187.66 155.66 80 288 80s240 107.66 240 240c0 39.12-9.56 77.63-27.73 112zM416 320c0 17.67 14.33 32 32 32s32-14.33 32-32-14.33-32-32-32-32 14.33-32 32zm-56.41-182.77c-12.72-4.23-26.16 2.62-30.38 15.17l-45.34 136.01C250.49 290.58 224 318.06 224 352c0 11.72 3.38 22.55 8.88 32h110.25c5.5-9.45 8.88-20.28 8.88-32 0-19.45-8.86-36.66-22.55-48.4l45.34-136.01c4.17-12.57-2.64-26.17-15.21-30.36zM432 208c0-15.8-11.66-28.33-26.72-30.93-.07.21-.07.43-.14.65l-19.5 58.49c4.37 2.24 9.11 3.8 14.36 3.8 17.67-.01 32-14.34 32-32.01z" fill="currentColor"/>'},"addon-user-journey":{viewBox:"0 0 50 43",content:'<path d="M47.9048 37.4762C48.8095 36.0476 49.1429 34.4286 48.9048 32.619C48.619 30.8095 47.9524 29.4286 46.9048 28.4762C46.1905 27.8095 42.8571 26.7619 36.8571 25.2857C38.2857 23.9524 39.381 22.1905 40.2381 20C41.0952 17.8571 41.5238 15.6667 41.5238 13.3809C41.5238 11.9048 41.4286 10.5714 41.2381 9.42857C41.0476 8.28571 40.7619 7.14286 40.2857 6C39.8095 4.90476 39.1905 4 38.4286 3.23809C37.6667 2.52381 36.619 1.95238 35.381 1.52381C34.1429 1.09524 32.6667 0.85714 31 0.85714C28.4286 0.85714 26.381 1.38095 24.7619 2.42857C26.7619 2.61905 28.4286 3.14285 29.7619 3.95238C30.5238 3.90476 30.9524 3.85714 31 3.85714C32.1905 3.85714 33.2381 4.04762 34.1429 4.38095C35.0476 4.71428 35.7619 5.14285 36.3333 5.71428C36.8571 6.28571 37.2857 6.95238 37.6191 7.80952C37.9524 8.66667 38.1905 9.52381 38.2857 10.4286C38.381 11.3333 38.4762 12.3333 38.4762 13.4762C38.4762 15.4762 38.0952 17.381 37.3333 19.2857C36.5714 21.1905 35.5714 22.619 34.3333 23.619V25.619L34.7619 26.9524C40.2857 28.4286 43.5238 29.3333 44.4286 29.7143C45 30 45.4762 30.5238 45.7619 31.3333C46.0476 32.1429 46.0476 32.9524 45.8571 33.7619C45.619 34.5714 45.1429 35.0952 44.4286 35.381H39.8571C40.0476 36.381 40.0476 37.3333 39.8571 38.2381C44.8095 38.0952 47.4762 37.8571 47.9048 37.4762ZM29.5238 17.0952C29.5238 15.5714 29.4286 14.1429 29.2381 12.9048C29.0476 11.6667 28.7619 10.4762 28.2857 9.28571C27.8095 8.14285 27.1429 7.14286 26.381 6.38095C25.619 5.61905 24.619 5.04762 23.381 4.57143C22.1429 4.09524 20.6667 3.85714 19 3.85714C17.5714 3.85714 16.3333 4.04762 15.2381 4.33333C14.0952 4.61904 13.1905 5.09524 12.4286 5.66667C11.6667 6.28571 11.0476 6.95238 10.5238 7.71428C9.95238 8.47619 9.57143 9.38095 9.28571 10.4286C9 11.4762 8.76191 12.5238 8.66667 13.5714C8.52381 14.619 8.47619 15.8095 8.47619 17.0952C8.47619 19.5238 8.90476 21.9048 9.7619 24.1905C10.619 26.4762 11.8095 28.3333 13.2857 29.7619C7.09524 31.3333 3.71429 32.381 3.09524 32.9524C2.04762 33.9048 1.38095 35.3333 1.09524 37.1429C0.809524 38.9524 1.14286 40.5714 2.09524 41.9524C3.14286 42.5714 8.80952 42.8571 19.0952 42.8571C29.6191 42.8571 35.2381 42.5714 35.9048 41.9524C36.8095 40.5714 37.1429 38.9524 36.9048 37.1429C36.619 35.3333 35.9524 33.9048 34.9048 32.9524C34.2381 32.381 30.8095 31.2857 24.7143 29.7619C26.1905 28.3333 27.381 26.4762 28.2381 24.1429C29.0952 21.8571 29.5238 19.4762 29.5238 17.0952ZM18.9524 6.85714C20.4762 6.85714 21.7143 7.14286 22.7619 7.66667C23.8095 8.19047 24.5714 8.95238 25.0952 9.95238C25.5714 10.9524 25.9524 12 26.1429 13.1429C26.3333 14.2857 26.4762 15.6667 26.4762 17.1905C26.4762 19.3333 26.0952 21.4286 25.3333 23.4286C24.5714 25.4286 23.5714 27 22.3333 28.0476V30.0952L22.7619 31.4762C28.2857 32.9048 31.5238 33.8571 32.4286 34.2381C33 34.5238 33.4762 35.0476 33.7619 35.8571C34.0476 36.6667 34.0476 37.4762 33.8571 38.2857C33.619 39.0952 33.1429 39.619 32.4286 39.8571L18.8095 39.8095L5.52381 39.8571C4.66667 39.5714 4.19048 39.0476 4.04762 38.1905C3.90476 37.381 4.04762 36.5714 4.47619 35.8095C4.85714 35.0476 5.38095 34.5238 6 34.2381C7.19048 33.7143 10.2857 32.7143 15.2857 31.2857L15.619 30.0952V28.0476C14.3333 27 13.3333 25.4286 12.5714 23.4286C11.8095 21.4286 11.4762 19.3333 11.4762 17.1905C11.4762 15.6667 11.5714 14.2857 11.7619 13.1429C11.9524 12 12.3333 10.9524 12.8571 9.95238C13.381 8.95238 14.1429 8.19047 15.1905 7.66667C16.1905 7.14286 17.4286 6.85714 18.9524 6.85714Z" fill="currentColor"/>'},"addon-ppc-tracking":{viewBox:"0 0 50 50",content:'<path class="st0" d="M3.2 28.1V5.9c.2-.4.5-.6 1-.5h2.6c0-.1.1-.1.1-.1V4.1c0-.6.3-.9.9-.9h5.5c.6 0 .9.3.9.9v1.3h7.2V4.2c0-.6.3-.9.9-.9h5.4c.6 0 .9.3.9.9v1.2h7.2V4.2c0-.7.3-1 1-1h5.3c.7 0 .9.2 1 .9v1.2h2.5c.9 0 1.1.2 1.1 1.1v21c0 1-.2 1.2-1.2 1.2H31.4v1.8c0 .8-.2 1-1 1.1H30c0 .6-.2 0-1.1 0h-1.8v13.8h18.7c.3 0 .6.2.7.5.1.3 0 .6-.3.8-.1 0-.1.1-.2.1H3v-.9c.2-.4.6-.5 1-.5h18.5V31.5H19c-.5 0-.8-.3-.8-.8v-2H4.1c-.2-.1-.6-.1-.9-.6zm1.5-1h40.6V6.9h-2.2v1.3c0 .6-.3.9-.9.9h-5.5c-.6 0-.9-.3-.9-.9V7h-7.3v1.2c0 .6-.3.9-.9.9h-5.4c-.6 0-.9-.3-.9-.9V7h-7.2V8.2c0 .7-.2.9-.9.9H7.8c-.7-.1-.9-.3-.9-1V6.9H4.7v20.2zm21 18.2V31.5h-1.4v13.8h1.4zM30 30.1v-1.4H19.9v1.4H30zM22.8 4.7v2.9h4.3V4.7h-4.3zM8.3 7.6h4.3V4.8H8.3v2.8zm33.4-2.9h-4.3v2.8h4.3V4.7z" id="VtINyh.tif"/><path class="st0" d="M28.1 13.3c.5 0 1 0 1.5.1 1.3.2 2.2 1.1 2.7 2.3.5 1.3.6 2.7 0 4.1-.6 1.6-1.8 2.5-3.5 2.5H27c-.7 0-1.2-.4-1.2-1.1v-6.8c0-.7.4-1 1.1-1.1h1.2zm-.1 2.1v4.7c.8.1 1.7.1 2.2-.7.6-1.1.6-2.2.1-3.3-.6-.9-1.5-.7-2.3-.7zM22.6 20.6H20c-.1 0-.3.1-.4.3-.1.2-.2.5-.3.7-.3.6-.8.9-1.4.7-.6-.2-.8-.8-.6-1.4.7-1.8 1.3-3.5 2-5.2.2-.5.4-1.1.6-1.6.2-.5.6-.8 1.1-.8.6 0 .9.3 1.1.8.9 2.3 1.7 4.6 2.6 6.9.2.6 0 1.2-.6 1.4-.6.2-1.1 0-1.4-.7.1-.4 0-.7-.1-1.1zm-2.4-1.7h1.9c-.3-.8-.6-1.5-.9-2.5-.4 1-.7 1.7-1 2.5z"/>'},"addon-exceptions":{viewBox:"0 0 17 15",content:'<path d="M6.44222 12.6174H10.3814C10.3026 13.1192 10.0617 13.5748 9.70113 13.9033C9.3406 14.2319 8.88382 14.4121 8.41179 14.4121C7.93975 14.4121 7.48297 14.2319 7.12244 13.9033C6.76191 13.5748 6.52092 13.1192 6.44222 12.6174ZM8.41179 0.412109C9.73623 0.412109 11.0064 0.979422 11.943 1.98925C12.8795 2.99907 13.4056 4.36868 13.4056 5.79679V8.66862L14.3498 10.9374C14.3927 11.0411 14.4109 11.1548 14.4028 11.2681C14.3946 11.3814 14.3603 11.4906 14.303 11.586C14.2457 11.6814 14.1673 11.7598 14.0747 11.8142C13.9822 11.8685 13.8785 11.8971 13.7732 11.8973H3.05306C2.94755 11.8973 2.84371 11.8688 2.75101 11.8144C2.65831 11.7601 2.5797 11.6816 2.52232 11.5861C2.46494 11.4906 2.43062 11.3812 2.42249 11.2678C2.41436 11.1543 2.43268 11.0405 2.47578 10.9366L3.41795 8.6679V5.78746L3.42128 5.60797C3.46682 4.21359 4.01254 2.89271 4.94346 1.92362C5.87438 0.954528 7.11781 0.412175 8.41179 0.412109ZM15.7361 4.89719C15.8626 4.89723 15.9844 4.94906 16.0769 5.0422C16.1693 5.13534 16.2255 5.26284 16.2342 5.39896C16.2428 5.53507 16.2032 5.66963 16.1233 5.77547C16.0435 5.8813 15.9293 5.9505 15.804 5.9691L15.7361 5.97413H14.4044C14.2779 5.97408 14.1561 5.92226 14.0636 5.82912C13.9712 5.73598 13.9149 5.60847 13.9063 5.47236C13.8977 5.33625 13.9373 5.20168 14.0172 5.09585C14.097 4.99002 14.2111 4.92081 14.3365 4.90222L14.4044 4.89719H15.7361ZM2.41918 4.89719C2.54571 4.89723 2.6675 4.94906 2.75995 5.0422C2.85241 5.13534 2.90862 5.26284 2.91725 5.39896C2.92587 5.53507 2.88626 5.66963 2.80641 5.77547C2.72657 5.8813 2.61244 5.9505 2.4871 5.9691L2.41918 5.97413H1.08749C0.960962 5.97408 0.839168 5.92226 0.746714 5.82912C0.654261 5.73598 0.598043 5.60847 0.589419 5.47236C0.580795 5.33625 0.620408 5.20168 0.700255 5.09585C0.780101 4.99002 0.894227 4.92081 1.01957 4.90222L1.08749 4.89719H2.41918ZM15.4697 0.804832C15.5416 0.908032 15.5767 1.03561 15.5686 1.16435C15.5605 1.29309 15.5099 1.41443 15.4258 1.50628L15.3699 1.55869L14.0382 2.63562C13.9374 2.71755 13.8115 2.75527 13.686 2.74119C13.5604 2.72712 13.4445 2.66229 13.3614 2.55976C13.2784 2.45723 13.2345 2.3246 13.2385 2.18855C13.2425 2.05251 13.2941 1.92314 13.383 1.82649L13.4389 1.77407L14.7706 0.697139C14.8766 0.611452 15.0097 0.57466 15.1409 0.594857C15.272 0.615053 15.3903 0.690584 15.4697 0.804832ZM2.05296 0.697139L3.38465 1.77407C3.43712 1.8165 3.48132 1.86966 3.51473 1.9305C3.54814 1.99135 3.57011 2.0587 3.57939 2.1287C3.58866 2.1987 3.58506 2.26999 3.56878 2.33849C3.55251 2.40699 3.52388 2.47136 3.48453 2.52793C3.44518 2.5845 3.39589 2.63216 3.33946 2.66819C3.28303 2.70421 3.22057 2.72791 3.15565 2.73791C3.09073 2.74791 3.02461 2.74402 2.96109 2.72647C2.89756 2.70892 2.83786 2.67805 2.78539 2.63562L1.4537 1.55869C1.40124 1.51626 1.35704 1.46311 1.32363 1.40226C1.29021 1.34141 1.26824 1.27407 1.25897 1.20406C1.24969 1.13406 1.2533 1.06278 1.26957 0.994275C1.28585 0.925775 1.31448 0.861402 1.35383 0.804832C1.39317 0.748262 1.44247 0.700602 1.4989 0.664575C1.55533 0.628547 1.61779 0.604857 1.68271 0.594857C1.74763 0.584856 1.81374 0.588742 1.87727 0.606291C1.9408 0.623841 2.0005 0.654711 2.05296 0.697139Z" fill="currentColor" />'},"addon-site-notes-important-events":{viewBox:"0 0 18 19",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M17.7059 2.00551C17.182 1.47571 16.6547 0.949325 16.1274 0.422943L15.9516 0.247424C15.6222 -0.0814546 15.3793 -0.0828641 15.0509 0.245545L15.049 0.247476C11.7801 3.51635 8.51121 6.78523 5.24656 10.0588C5.13521 10.1702 5.04125 10.3182 4.99004 10.4666C4.81253 10.9831 4.64049 11.5016 4.46846 12.0201C4.33358 12.4266 4.1987 12.8331 4.06119 13.2386C3.98226 13.4712 3.96675 13.6821 4.15609 13.8653C4.33792 14.042 4.54699 14.0204 4.76734 13.9466C5.09281 13.8371 5.4187 13.7289 5.74458 13.6206L5.74479 13.6206L5.74494 13.6205C6.33625 13.4241 6.92754 13.2277 7.51629 13.0239C7.67885 12.9675 7.84188 12.8665 7.96357 12.7453C11.2096 9.50723 14.4505 6.26402 17.6899 3.01987C17.7678 2.94203 17.8358 2.85436 17.9038 2.76669C17.9355 2.72578 17.9673 2.68486 18 2.64495V2.36352C17.9693 2.32565 17.9394 2.28684 17.9095 2.24801L17.9095 2.24793C17.8449 2.16395 17.7802 2.07988 17.7064 2.00504L17.7059 2.00551ZM16.6319 2.66609C13.5451 5.75473 10.4583 8.84337 7.36783 11.9282C7.27856 12.0175 7.15688 12.0889 7.03707 12.1312C6.66031 12.2644 6.28061 12.3892 5.90099 12.5141C5.77693 12.5549 5.65287 12.5957 5.52893 12.6367C5.48541 12.651 5.44125 12.663 5.38542 12.6781L5.38539 12.6781C5.35864 12.6853 5.32922 12.6932 5.29589 12.7025C5.34804 12.546 5.39938 12.3913 5.45022 12.2382L5.45022 12.2382C5.61143 11.7527 5.76755 11.2826 5.92828 10.8143C5.94538 10.7645 5.98956 10.7224 6.03193 10.682C6.04193 10.6725 6.05182 10.663 6.06124 10.6536C9.15928 7.55369 12.2578 4.45519 15.3568 1.35668C15.3881 1.3253 15.4217 1.29565 15.4493 1.27133C15.4597 1.26216 15.4693 1.25374 15.4775 1.24627C15.9036 1.671 16.3227 2.08914 16.7601 2.52561C16.752 2.53452 16.7423 2.54561 16.7314 2.55813L16.7314 2.55815C16.7042 2.58919 16.6694 2.62907 16.6319 2.66656V2.66609ZM15.9868 9.43065C15.9558 9.19292 15.765 9.02378 15.5245 9.01392C15.1961 9.00029 15.0001 9.22205 14.9997 9.61388C14.9994 11.1925 14.9995 12.7711 14.9996 14.3497L14.9997 16.7177V16.9953H1.01248V3.00859H1.33008C3.98648 3.00859 6.64241 3.00859 9.29881 3.00718C9.41485 3.00718 9.53513 3.00343 9.64648 2.9743C9.86307 2.91792 9.99415 2.72764 9.99321 2.50259C9.99227 2.27707 9.85978 2.09055 9.64319 2.03323C9.54687 2.00786 9.44257 2.00176 9.34203 2.00176C6.45166 2.00035 3.56081 2.00035 0.670442 2.00082C0.16303 2.00035 0 2.16572 0 2.67643V17.3294C0 17.8528 0.160211 18.013 0.685947 18.013H15.3107C15.8411 18.013 15.9957 17.8589 15.9957 17.3308V9.68812L15.9958 9.65017V9.65016C15.9961 9.57664 15.9964 9.50279 15.9868 9.43065ZM1.99535 15.1996C1.99628 14.9746 2.12878 14.7876 2.34537 14.7303C2.44215 14.7049 2.54598 14.6988 2.64652 14.6988H4.66631C4.67054 14.6983 4.67383 14.6979 4.67852 14.6979C5.41617 14.6976 6.15402 14.6977 6.89181 14.6978C7.26067 14.6978 7.62952 14.6979 7.99832 14.6979H11.3181C11.3199 14.6979 11.3215 14.698 11.3231 14.6982C11.3255 14.6985 11.3277 14.6988 11.3303 14.6988H13.3501C13.4507 14.6988 13.555 14.7049 13.6513 14.7303C13.8679 14.7876 14.0004 14.9741 14.0013 15.1996C14.0022 15.4247 13.8711 15.615 13.6546 15.6714C13.5432 15.7005 13.4229 15.7042 13.3069 15.7042C12.4254 15.7052 11.544 15.7052 10.6626 15.7052H10.6599H10.658H7.99832H5.33863H5.33675C4.89558 15.7049 4.45442 15.7048 4.01325 15.7047C3.57208 15.7046 3.13092 15.7045 2.68975 15.7042C2.57323 15.7042 2.45343 15.7005 2.34208 15.6714C2.12502 15.615 1.99441 15.4242 1.99535 15.1996Z" fill="#210F59" />'},"report-sessions":{viewBox:"0 0 17 16",content:'<rect width="17" height="16" fill="#210F59"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-190 -273.992)" fill="#210F59"/><rect width="158" height="3039" transform="translate(-190 -273.992)" fill="#210F59"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z"/></mask><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z" fill="white"/><path d="M-14.5 -16.7299V-17.7299H-15.5V-16.7299H-14.5ZM196.99 -16.7299H197.99V-17.7299H196.99V-16.7299ZM196.99 57.9115V58.9115H197.99V57.9115H196.99ZM-14.5 57.9115H-15.5V58.9115H-14.5V57.9115ZM-14.5 -15.7299H196.99V-17.7299H-14.5V-15.7299ZM195.99 -16.7299V57.9115H197.99V-16.7299H195.99ZM196.99 56.9115H-14.5V58.9115H196.99V56.9115ZM-13.5 57.9115V-16.7299H-15.5V57.9115H-13.5Z" fill="#210F59" mask="url(#path-1-inside-1_45_7568)"/><mask id="path-3-inside-2_45_7568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z" stroke="#210F59" stroke-width="2" mask="url(#path-3-inside-2_45_7568)"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-190 -273.992)"/></clipPath></defs>'},"report-sessions-active":{viewBox:"0 0 17 16",content:'<rect width="17" height="16" fill="#210F59"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-190 -273.992)" fill="#210F59"/><rect width="158" height="3039" transform="translate(-190 -273.992)" fill="#210F59"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z"/></mask><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z" fill="#f4f3f7"/><path d="M-14.5 -16.7299V-17.7299H-15.5V-16.7299H-14.5ZM196.99 -16.7299H197.99V-17.7299H196.99V-16.7299ZM196.99 57.9115V58.9115H197.99V57.9115H196.99ZM-14.5 57.9115H-15.5V58.9115H-14.5V57.9115ZM-14.5 -15.7299H196.99V-17.7299H-14.5V-15.7299ZM195.99 -16.7299V57.9115H197.99V-16.7299H195.99ZM196.99 56.9115H-14.5V58.9115H196.99V56.9115ZM-13.5 57.9115V-16.7299H-15.5V57.9115H-13.5Z" fill="#210F59" mask="url(#path-1-inside-1_45_7568)"/><mask id="path-3-inside-2_45_7568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z" stroke="#6527F5" stroke-width="2" mask="url(#path-3-inside-2_45_7568)"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-190 -273.992)"/></clipPath></defs>'},"report-pageviews":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6140)"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.79663 10.1912C4.88818 10.3263 4.98868 10.4564 5.09682 10.5796C3.64301 10.072 2.2268 9.2014 0.978091 8.02736C2.2268 6.85334 3.64301 5.98275 5.09681 5.4752C4.98868 5.59836 4.88818 5.72847 4.79663 5.86355C4.65615 6.072 4.53606 6.29178 4.43863 6.52063C4.23698 6.99871 4.13502 7.50625 4.13502 8.02738C4.13502 8.54852 4.23698 9.05606 4.43863 9.53414C4.53606 9.76299 4.65615 9.98277 4.79663 10.1912ZM15.0219 8.02744C13.7755 9.19935 12.3621 10.0689 10.911 10.5769C11.0183 10.4545 11.118 10.3253 11.2088 10.1912C11.3493 9.98277 11.4694 9.76299 11.5668 9.53414C11.7685 9.05606 11.8704 8.54852 11.8704 8.02738C11.8704 7.50625 11.7685 6.99871 11.5668 6.52063C11.4694 6.29178 11.3493 6.072 11.2088 5.86355C11.118 5.72949 11.0183 5.60033 10.9111 5.47799C12.3621 5.98595 13.7755 6.85552 15.0219 8.02744ZM11.0684 8.02738C11.0684 6.34307 9.70272 4.97148 8.02064 4.96181L7.99997 4.96179L7.9885 4.96179C6.30473 4.96949 4.93711 6.34185 4.93711 8.02738C4.93711 9.71767 6.31245 11.093 8.00273 11.093C9.69302 11.093 11.0684 9.71767 11.0684 8.02738ZM10.1434 4.41573C9.43564 4.24774 8.72122 4.16168 8.02424 4.15973L8.00273 4.15967L7.98596 4.1597C7.28573 4.16084 6.56777 4.24692 5.85652 4.41573C5.16319 4.58113 4.46759 4.82584 3.78785 5.14305C3.12624 5.4512 2.47595 5.83185 1.85512 6.27368C1.24336 6.70645 0.658781 7.20266 0.117255 7.74418C-0.039085 7.90053 -0.039085 8.15429 0.117255 8.31063L0.122762 8.31604C0.662767 8.85545 1.24548 9.3498 1.8552 9.78112C2.47603 10.2229 3.12632 10.6036 3.78793 10.9118C4.46767 11.229 5.16327 11.4737 5.8566 11.6391C6.5726 11.809 7.29539 11.8951 8.00005 11.8951C8.70471 11.8951 9.4275 11.809 10.1435 11.6391C10.8368 11.4737 11.5324 11.229 12.2122 10.9118C12.8738 10.6036 13.5241 10.2229 14.1449 9.78112C14.7567 9.34835 15.3412 8.85214 15.8828 8.31062C16.0391 8.15428 16.0391 7.90051 15.8828 7.74417L15.8773 7.73877C15.3373 7.19935 14.7545 6.70501 14.1448 6.27368C13.524 5.83185 12.8737 5.4512 12.2121 5.14305C11.5323 4.82584 10.8367 4.58113 10.1434 4.41573ZM9.40371 6.62483C9.58724 6.80609 9.72999 7.01908 9.82968 7.25472C9.93391 7.49943 9.98602 7.75999 9.98602 8.02736C9.98602 8.29472 9.93391 8.55529 9.82968 8.8C9.72999 9.03564 9.58724 9.24862 9.40371 9.42989C9.22245 9.61342 9.00946 9.75616 8.77382 9.85586C8.52911 9.96008 8.26855 10.0122 8.00118 10.0122C7.73382 10.0122 7.47325 9.96008 7.22855 9.85586C6.9929 9.75616 6.77992 9.61342 6.59865 9.42989C6.41512 9.24862 6.27238 9.03564 6.17268 8.8C6.06846 8.55529 6.01634 8.29472 6.01634 8.02736C6.01634 7.79851 6.05486 7.57647 6.1319 7.36348C6.15456 7.43599 6.19081 7.50396 6.24519 7.5606C6.48536 7.81211 6.94985 7.76226 7.28293 7.44505C7.616 7.1301 7.69077 6.66788 7.45286 6.41638C7.36223 6.32121 7.23988 6.2691 7.10393 6.25777C7.14471 6.23662 7.18625 6.21699 7.22855 6.19886C7.47325 6.09463 7.73382 6.04252 8.00118 6.04252C8.26855 6.04252 8.52911 6.09463 8.77382 6.19886C9.00946 6.29855 9.22245 6.4413 9.40371 6.62483Z" fill="#210F59"/></g><defs><clipPath id="clip0_65_6140"><rect width="16" height="16" fill="white" transform="translate(0 0.0273438)"/></clipPath></defs>'},"report-pageviews-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6140)"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.79663 10.1912C4.88818 10.3263 4.98868 10.4564 5.09682 10.5796C3.64301 10.072 2.2268 9.2014 0.978091 8.02736C2.2268 6.85334 3.64301 5.98275 5.09681 5.4752C4.98868 5.59836 4.88818 5.72847 4.79663 5.86355C4.65615 6.072 4.53606 6.29178 4.43863 6.52063C4.23698 6.99871 4.13502 7.50625 4.13502 8.02738C4.13502 8.54852 4.23698 9.05606 4.43863 9.53414C4.53606 9.76299 4.65615 9.98277 4.79663 10.1912ZM15.0219 8.02744C13.7755 9.19935 12.3621 10.0689 10.911 10.5769C11.0183 10.4545 11.118 10.3253 11.2088 10.1912C11.3493 9.98277 11.4694 9.76299 11.5668 9.53414C11.7685 9.05606 11.8704 8.54852 11.8704 8.02738C11.8704 7.50625 11.7685 6.99871 11.5668 6.52063C11.4694 6.29178 11.3493 6.072 11.2088 5.86355C11.118 5.72949 11.0183 5.60033 10.9111 5.47799C12.3621 5.98595 13.7755 6.85552 15.0219 8.02744ZM11.0684 8.02738C11.0684 6.34307 9.70272 4.97148 8.02064 4.96181L7.99997 4.96179L7.9885 4.96179C6.30473 4.96949 4.93711 6.34185 4.93711 8.02738C4.93711 9.71767 6.31245 11.093 8.00273 11.093C9.69302 11.093 11.0684 9.71767 11.0684 8.02738ZM10.1434 4.41573C9.43564 4.24774 8.72122 4.16168 8.02424 4.15973L8.00273 4.15967L7.98596 4.1597C7.28573 4.16084 6.56777 4.24692 5.85652 4.41573C5.16319 4.58113 4.46759 4.82584 3.78785 5.14305C3.12624 5.4512 2.47595 5.83185 1.85512 6.27368C1.24336 6.70645 0.658781 7.20266 0.117255 7.74418C-0.039085 7.90053 -0.039085 8.15429 0.117255 8.31063L0.122762 8.31604C0.662767 8.85545 1.24548 9.3498 1.8552 9.78112C2.47603 10.2229 3.12632 10.6036 3.78793 10.9118C4.46767 11.229 5.16327 11.4737 5.8566 11.6391C6.5726 11.809 7.29539 11.8951 8.00005 11.8951C8.70471 11.8951 9.4275 11.809 10.1435 11.6391C10.8368 11.4737 11.5324 11.229 12.2122 10.9118C12.8738 10.6036 13.5241 10.2229 14.1449 9.78112C14.7567 9.34835 15.3412 8.85214 15.8828 8.31062C16.0391 8.15428 16.0391 7.90051 15.8828 7.74417L15.8773 7.73877C15.3373 7.19935 14.7545 6.70501 14.1448 6.27368C13.524 5.83185 12.8737 5.4512 12.2121 5.14305C11.5323 4.82584 10.8367 4.58113 10.1434 4.41573ZM9.40371 6.62483C9.58724 6.80609 9.72999 7.01908 9.82968 7.25472C9.93391 7.49943 9.98602 7.75999 9.98602 8.02736C9.98602 8.29472 9.93391 8.55529 9.82968 8.8C9.72999 9.03564 9.58724 9.24862 9.40371 9.42989C9.22245 9.61342 9.00946 9.75616 8.77382 9.85586C8.52911 9.96008 8.26855 10.0122 8.00118 10.0122C7.73382 10.0122 7.47325 9.96008 7.22855 9.85586C6.9929 9.75616 6.77992 9.61342 6.59865 9.42989C6.41512 9.24862 6.27238 9.03564 6.17268 8.8C6.06846 8.55529 6.01634 8.29472 6.01634 8.02736C6.01634 7.79851 6.05486 7.57647 6.1319 7.36348C6.15456 7.43599 6.19081 7.50396 6.24519 7.5606C6.48536 7.81211 6.94985 7.76226 7.28293 7.44505C7.616 7.1301 7.69077 6.66788 7.45286 6.41638C7.36223 6.32121 7.23988 6.2691 7.10393 6.25777C7.14471 6.23662 7.18625 6.21699 7.22855 6.19886C7.47325 6.09463 7.73382 6.04252 8.00118 6.04252C8.26855 6.04252 8.52911 6.09463 8.77382 6.19886C9.00946 6.29855 9.22245 6.4413 9.40371 6.62483Z" fill="#6527F5"/></g><defs><clipPath id="clip0_65_6140"><rect width="16" height="16" fill="white" transform="translate(0 0.0273438)"/></clipPath></defs>'},"report-totalusers":{viewBox:"0 0 21 11",content:'<path d="M1.11963 14.8276V13.2987C1.11963 12.4877 1.4418 11.7099 2.01527 11.1364C2.58874 10.5629 3.36654 10.2408 4.17755 10.2408H7.23547C8.04648 10.2408 8.82427 10.5629 9.39774 11.1364C9.97121 11.7099 10.2934 12.4877 10.2934 13.2987V14.8276M11.0579 1.16639C11.7156 1.33481 12.2986 1.71735 12.715 2.25372C13.1313 2.79008 13.3573 3.44976 13.3573 4.12875C13.3573 4.80774 13.1313 5.46741 12.715 6.00378C12.2986 6.54015 11.7156 6.92269 11.0579 7.09111M14.8803 14.8276V13.2987C14.8764 12.6238 14.6493 11.9691 14.2345 11.4367C13.8196 10.9043 13.2403 10.5242 12.5868 10.3554M2.64859 4.12493C2.64859 4.93594 2.97076 5.71373 3.54423 6.2872C4.1177 6.86067 4.8955 7.18285 5.70651 7.18285C6.51752 7.18285 7.29531 6.86067 7.86878 6.2872C8.44225 5.71373 8.76443 4.93594 8.76443 4.12493C8.76443 3.31392 8.44225 2.53612 7.86878 1.96265C7.29531 1.38918 6.51752 1.06701 5.70651 1.06701C4.8955 1.06701 4.1177 1.38918 3.54423 1.96265C2.97076 2.53612 2.64859 3.31392 2.64859 4.12493Z" stroke="#210F59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-totalusers-active":{viewBox:"0 0 21 11",content:'<path d="M1.11963 14.8276V13.2987C1.11963 12.4877 1.4418 11.7099 2.01527 11.1364C2.58874 10.5629 3.36654 10.2408 4.17755 10.2408H7.23547C8.04648 10.2408 8.82427 10.5629 9.39774 11.1364C9.97121 11.7099 10.2934 12.4877 10.2934 13.2987V14.8276M11.0579 1.16639C11.7156 1.33481 12.2986 1.71735 12.715 2.25372C13.1313 2.79008 13.3573 3.44976 13.3573 4.12875C13.3573 4.80774 13.1313 5.46741 12.715 6.00378C12.2986 6.54015 11.7156 6.92269 11.0579 7.09111M14.8803 14.8276V13.2987C14.8764 12.6238 14.6493 11.9691 14.2345 11.4367C13.8196 10.9043 13.2403 10.5242 12.5868 10.3554M2.64859 4.12493C2.64859 4.93594 2.97076 5.71373 3.54423 6.2872C4.1177 6.86067 4.8955 7.18285 5.70651 7.18285C6.51752 7.18285 7.29531 6.86067 7.86878 6.2872C8.44225 5.71373 8.76443 4.93594 8.76443 4.12493C8.76443 3.31392 8.44225 2.53612 7.86878 1.96265C7.29531 1.38918 6.51752 1.06701 5.70651 1.06701C4.8955 1.06701 4.1177 1.38918 3.54423 1.96265C2.97076 2.53612 2.64859 3.31392 2.64859 4.12493Z" stroke="#6527F5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-pageviews_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_266)"><path d="M4.8 13.255H4.26667C4.26667 13.3964 4.32286 13.5321 4.42288 13.6321C4.5229 13.7321 4.65855 13.7883 4.8 13.7883V13.255ZM11.2 13.255V13.7883C11.3414 13.7883 11.4771 13.7321 11.5771 13.6321C11.6771 13.5321 11.7333 13.3964 11.7333 13.255H11.2ZM4.8 13.7883H11.2V12.7217H4.8V13.7883ZM11.7333 13.255V11.1217H10.6667V13.255H11.7333ZM4.26667 11.1217V13.255H5.33333V11.1217H4.26667ZM8 7.38832C7.00986 7.38832 6.06027 7.78165 5.36014 8.48179C4.66 9.18192 4.26667 10.1315 4.26667 11.1217H5.33333C5.33333 10.4144 5.61428 9.73613 6.11438 9.23604C6.61448 8.73594 7.29276 8.45499 8 8.45499V7.38832ZM11.7333 11.1217C11.7333 10.1315 11.34 9.18192 10.6399 8.48179C9.93973 7.78165 8.99014 7.38832 8 7.38832V8.45499C8.70724 8.45499 9.38552 8.73594 9.88562 9.23604C10.3857 9.73613 10.6667 10.4144 10.6667 11.1217H11.7333ZM14.9333 13.255C14.9333 13.5163 14.8117 13.8097 14.5003 14.1254C14.1867 14.4443 13.7056 14.7547 13.0699 15.0278C11.7995 15.5718 10.0075 15.9217 8 15.9217V16.9883C10.1163 16.9883 12.0576 16.6214 13.4901 16.007C14.2048 15.7009 14.8181 15.3222 15.2597 14.8753C15.7035 14.4251 16 13.8769 16 13.255H14.9333ZM8 15.9217C5.99253 15.9217 4.20053 15.5718 2.93013 15.0278C2.2944 14.7547 1.81333 14.4443 1.49973 14.1254C1.18827 13.8097 1.06667 13.5185 1.06667 13.255H0C0 13.8769 0.296533 14.4251 0.740267 14.8753C1.18187 15.3233 1.7952 15.7009 2.51093 16.0081C3.94133 16.6214 5.88267 16.9883 8 16.9883V15.9217ZM1.06667 13.255C1.06667 12.9969 1.184 12.7078 1.48693 12.3963C1.792 12.0827 2.26027 11.7755 2.88 11.5035L2.45333 10.5265C1.7536 10.8326 1.15413 11.2091 0.7232 11.6529C0.288 12.0987 0 12.6417 0 13.255H1.06667ZM13.12 11.5035C13.7397 11.7755 14.208 12.0827 14.512 12.3963C14.8149 12.7078 14.9333 12.9969 14.9333 13.255H16C16 12.6417 15.712 12.0987 15.2768 11.6529C14.8459 11.2091 14.2464 10.8326 13.5467 10.5265L13.12 11.5035ZM8 5.25499C7.57565 5.25499 7.16869 5.08642 6.86863 4.78636C6.56857 4.4863 6.4 4.07933 6.4 3.65499H5.33333C5.33333 4.36223 5.61428 5.04051 6.11438 5.5406C6.61448 6.0407 7.29276 6.32165 8 6.32165V5.25499ZM9.6 3.65499C9.6 4.07933 9.43143 4.4863 9.13137 4.78636C8.83131 5.08642 8.42435 5.25499 8 5.25499V6.32165C8.70724 6.32165 9.38552 6.0407 9.88562 5.5406C10.3857 5.04051 10.6667 4.36223 10.6667 3.65499H9.6ZM8 2.05499C8.42435 2.05499 8.83131 2.22356 9.13137 2.52362C9.43143 2.82367 9.6 3.23064 9.6 3.65499H10.6667C10.6667 2.94774 10.3857 2.26947 9.88562 1.76937C9.38552 1.26927 8.70724 0.988319 8 0.988319V2.05499ZM8 0.988319C7.29276 0.988319 6.61448 1.26927 6.11438 1.76937C5.61428 2.26947 5.33333 2.94774 5.33333 3.65499H6.4C6.4 3.23064 6.56857 2.82367 6.86863 2.52362C7.16869 2.22356 7.57565 2.05499 8 2.05499V0.988319Z" fill="#210F59"/></g><defs><clipPath id="clip0_51_266"><rect width="16" height="16" fill="white" transform="translate(0 0.988319)"/></clipPath></defs>'},"report-pageviews_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_266)"><path d="M4.8 13.255H4.26667C4.26667 13.3964 4.32286 13.5321 4.42288 13.6321C4.5229 13.7321 4.65855 13.7883 4.8 13.7883V13.255ZM11.2 13.255V13.7883C11.3414 13.7883 11.4771 13.7321 11.5771 13.6321C11.6771 13.5321 11.7333 13.3964 11.7333 13.255H11.2ZM4.8 13.7883H11.2V12.7217H4.8V13.7883ZM11.7333 13.255V11.1217H10.6667V13.255H11.7333ZM4.26667 11.1217V13.255H5.33333V11.1217H4.26667ZM8 7.38832C7.00986 7.38832 6.06027 7.78165 5.36014 8.48179C4.66 9.18192 4.26667 10.1315 4.26667 11.1217H5.33333C5.33333 10.4144 5.61428 9.73613 6.11438 9.23604C6.61448 8.73594 7.29276 8.45499 8 8.45499V7.38832ZM11.7333 11.1217C11.7333 10.1315 11.34 9.18192 10.6399 8.48179C9.93973 7.78165 8.99014 7.38832 8 7.38832V8.45499C8.70724 8.45499 9.38552 8.73594 9.88562 9.23604C10.3857 9.73613 10.6667 10.4144 10.6667 11.1217H11.7333ZM14.9333 13.255C14.9333 13.5163 14.8117 13.8097 14.5003 14.1254C14.1867 14.4443 13.7056 14.7547 13.0699 15.0278C11.7995 15.5718 10.0075 15.9217 8 15.9217V16.9883C10.1163 16.9883 12.0576 16.6214 13.4901 16.007C14.2048 15.7009 14.8181 15.3222 15.2597 14.8753C15.7035 14.4251 16 13.8769 16 13.255H14.9333ZM8 15.9217C5.99253 15.9217 4.20053 15.5718 2.93013 15.0278C2.2944 14.7547 1.81333 14.4443 1.49973 14.1254C1.18827 13.8097 1.06667 13.5185 1.06667 13.255H0C0 13.8769 0.296533 14.4251 0.740267 14.8753C1.18187 15.3233 1.7952 15.7009 2.51093 16.0081C3.94133 16.6214 5.88267 16.9883 8 16.9883V15.9217ZM1.06667 13.255C1.06667 12.9969 1.184 12.7078 1.48693 12.3963C1.792 12.0827 2.26027 11.7755 2.88 11.5035L2.45333 10.5265C1.7536 10.8326 1.15413 11.2091 0.7232 11.6529C0.288 12.0987 0 12.6417 0 13.255H1.06667ZM13.12 11.5035C13.7397 11.7755 14.208 12.0827 14.512 12.3963C14.8149 12.7078 14.9333 12.9969 14.9333 13.255H16C16 12.6417 15.712 12.0987 15.2768 11.6529C14.8459 11.2091 14.2464 10.8326 13.5467 10.5265L13.12 11.5035ZM8 5.25499C7.57565 5.25499 7.16869 5.08642 6.86863 4.78636C6.56857 4.4863 6.4 4.07933 6.4 3.65499H5.33333C5.33333 4.36223 5.61428 5.04051 6.11438 5.5406C6.61448 6.0407 7.29276 6.32165 8 6.32165V5.25499ZM9.6 3.65499C9.6 4.07933 9.43143 4.4863 9.13137 4.78636C8.83131 5.08642 8.42435 5.25499 8 5.25499V6.32165C8.70724 6.32165 9.38552 6.0407 9.88562 5.5406C10.3857 5.04051 10.6667 4.36223 10.6667 3.65499H9.6ZM8 2.05499C8.42435 2.05499 8.83131 2.22356 9.13137 2.52362C9.43143 2.82367 9.6 3.23064 9.6 3.65499H10.6667C10.6667 2.94774 10.3857 2.26947 9.88562 1.76937C9.38552 1.26927 8.70724 0.988319 8 0.988319V2.05499ZM8 0.988319C7.29276 0.988319 6.61448 1.26927 6.11438 1.76937C5.61428 2.26947 5.33333 2.94774 5.33333 3.65499H6.4C6.4 3.23064 6.56857 2.82367 6.86863 2.52362C7.16869 2.22356 7.57565 2.05499 8 2.05499V0.988319Z" fill="#6527F5"/></g><defs><clipPath id="clip0_51_266"><rect width="16" height="16" fill="white" transform="translate(0 0.988319)"/></clipPath></defs>'},"report-average_session_duration":{viewBox:"0 0 16 16",content:'<g clip-path="url(#clip0_51_268)"><g clip-path="url(#clip1_51_268)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.14997 1.20821C7.19365 1.40112 7.15892 1.60348 7.05344 1.77079C6.94796 1.93811 6.78035 2.05669 6.58747 2.10046C6.44107 2.13377 6.29599 2.17259 6.15253 2.21685C6.05892 2.24575 5.96053 2.25593 5.86299 2.2468C5.76544 2.23768 5.67065 2.20944 5.58402 2.16368C5.49739 2.11792 5.42062 2.05555 5.3581 1.98013C5.29557 1.9047 5.24852 1.8177 5.21962 1.72409C5.19071 1.63048 5.18053 1.53209 5.18966 1.43455C5.19878 1.337 5.22702 1.24221 5.27278 1.15558C5.31854 1.06895 5.38091 0.992185 5.45633 0.92966C5.53176 0.867136 5.61876 0.82008 5.71237 0.791178C5.89142 0.735475 6.07295 0.686982 6.25697 0.645702C6.35255 0.623968 6.45146 0.621279 6.54807 0.637789C6.64469 0.654299 6.7371 0.689684 6.82003 0.741923C6.90296 0.794162 6.97478 0.86223 7.0314 0.942237C7.08801 1.02224 7.1283 1.11262 7.14997 1.20821ZM8.67934 1.20821C8.72311 1.01532 8.84169 0.847716 9.00901 0.742234C9.17633 0.636752 9.37868 0.60203 9.57159 0.645702C12.8944 1.39919 15.3757 4.36989 15.3757 7.921C15.3757 12.0413 12.035 15.3813 7.9154 15.3813C4.36355 15.3813 1.39285 12.9008 0.639354 9.57794C0.600679 9.38694 0.638274 9.18839 0.744098 9.02476C0.849923 8.86112 1.01558 8.74538 1.20563 8.70231C1.39568 8.65923 1.59505 8.69222 1.76109 8.79424C1.92713 8.89625 2.04666 9.05919 2.09412 9.24819C2.33151 10.288 2.84321 11.2452 3.57596 12.0201C4.30871 12.7951 5.23574 13.3596 6.26061 13.6548C7.28548 13.95 8.37074 13.9652 9.40347 13.6988C10.4362 13.4324 11.3787 12.8941 12.1328 12.1399C12.887 11.3858 13.4253 10.4433 13.6917 9.41057C13.9581 8.37783 13.9429 7.29258 13.6477 6.26771C13.3525 5.24284 12.788 4.3158 12.013 3.58305C11.2381 2.8503 10.2809 2.33861 9.2411 2.10121C9.04819 2.05728 8.88063 1.93851 8.77528 1.77105C8.66994 1.60358 8.63542 1.40112 8.67934 1.20821ZM3.89653 2.48989C3.96325 2.56173 4.01515 2.64601 4.04928 2.73792C4.0834 2.82983 4.09908 2.92756 4.0954 3.02553C4.09173 3.1235 4.06879 3.21979 4.02788 3.30889C3.98697 3.39798 3.9289 3.47814 3.85699 3.54478C3.74658 3.64674 3.64039 3.75268 3.53843 3.86259C3.40291 4.00361 3.21743 4.08576 3.02193 4.09137C2.82643 4.09698 2.63654 4.02559 2.49316 3.89257C2.34978 3.75955 2.26436 3.57555 2.25531 3.38018C2.24625 3.18481 2.31428 2.99369 2.44475 2.84799C2.57158 2.71072 2.70387 2.57817 2.84164 2.45035C2.91348 2.38363 2.99776 2.33173 3.08967 2.2976C3.18158 2.26348 3.27931 2.24781 3.37728 2.25148C3.47525 2.25515 3.57154 2.2781 3.66063 2.31901C3.74973 2.35991 3.82989 2.41798 3.89653 2.48989ZM7.91466 3.44481C8.11252 3.44481 8.30227 3.52341 8.44218 3.66332C8.58209 3.80323 8.66069 3.99299 8.66069 4.19085V7.61215L10.6802 9.63165C10.8161 9.77236 10.8913 9.96081 10.8896 10.1564C10.8879 10.352 10.8094 10.5391 10.6711 10.6775C10.5328 10.8158 10.3457 10.8942 10.1501 10.8959C9.95446 10.8976 9.76601 10.8224 9.62531 10.6865L7.38721 8.44845C7.24729 8.30857 7.16867 8.11885 7.16862 7.921V4.19085C7.16862 3.99299 7.24722 3.80323 7.38713 3.66332C7.52704 3.52341 7.7168 3.44481 7.91466 3.44481ZM1.71737 5.22634C1.90641 5.28463 2.06455 5.41562 2.15703 5.5905C2.2495 5.76538 2.26874 5.96982 2.2105 6.15888C2.16625 6.30234 2.12743 6.44742 2.09412 6.59381C2.04666 6.78282 1.92713 6.94575 1.76109 7.04777C1.59505 7.14978 1.39568 7.18278 1.20563 7.1397C1.01558 7.09662 0.849923 6.98089 0.744098 6.81725C0.638274 6.65361 0.600679 6.45507 0.639354 6.26407C0.681132 6.08005 0.729624 5.89851 0.784831 5.71946C0.843125 5.53043 0.974114 5.37228 1.14899 5.2798C1.32387 5.18733 1.52832 5.1681 1.71737 5.22634Z" fill="#210F59"/></g></g><defs><clipPath id="clip0_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath><clipPath id="clip1_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath></defs>'},"report-average_session_duration-active":{viewBox:"0 0 16 16",content:'<g clip-path="url(#clip0_51_268)"><g clip-path="url(#clip1_51_268)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.14997 1.20821C7.19365 1.40112 7.15892 1.60348 7.05344 1.77079C6.94796 1.93811 6.78035 2.05669 6.58747 2.10046C6.44107 2.13377 6.29599 2.17259 6.15253 2.21685C6.05892 2.24575 5.96053 2.25593 5.86299 2.2468C5.76544 2.23768 5.67065 2.20944 5.58402 2.16368C5.49739 2.11792 5.42062 2.05555 5.3581 1.98013C5.29557 1.9047 5.24852 1.8177 5.21962 1.72409C5.19071 1.63048 5.18053 1.53209 5.18966 1.43455C5.19878 1.337 5.22702 1.24221 5.27278 1.15558C5.31854 1.06895 5.38091 0.992185 5.45633 0.92966C5.53176 0.867136 5.61876 0.82008 5.71237 0.791178C5.89142 0.735475 6.07295 0.686982 6.25697 0.645702C6.35255 0.623968 6.45146 0.621279 6.54807 0.637789C6.64469 0.654299 6.7371 0.689684 6.82003 0.741923C6.90296 0.794162 6.97478 0.86223 7.0314 0.942237C7.08801 1.02224 7.1283 1.11262 7.14997 1.20821ZM8.67934 1.20821C8.72311 1.01532 8.84169 0.847716 9.00901 0.742234C9.17633 0.636752 9.37868 0.60203 9.57159 0.645702C12.8944 1.39919 15.3757 4.36989 15.3757 7.921C15.3757 12.0413 12.035 15.3813 7.9154 15.3813C4.36355 15.3813 1.39285 12.9008 0.639354 9.57794C0.600679 9.38694 0.638274 9.18839 0.744098 9.02476C0.849923 8.86112 1.01558 8.74538 1.20563 8.70231C1.39568 8.65923 1.59505 8.69222 1.76109 8.79424C1.92713 8.89625 2.04666 9.05919 2.09412 9.24819C2.33151 10.288 2.84321 11.2452 3.57596 12.0201C4.30871 12.7951 5.23574 13.3596 6.26061 13.6548C7.28548 13.95 8.37074 13.9652 9.40347 13.6988C10.4362 13.4324 11.3787 12.8941 12.1328 12.1399C12.887 11.3858 13.4253 10.4433 13.6917 9.41057C13.9581 8.37783 13.9429 7.29258 13.6477 6.26771C13.3525 5.24284 12.788 4.3158 12.013 3.58305C11.2381 2.8503 10.2809 2.33861 9.2411 2.10121C9.04819 2.05728 8.88063 1.93851 8.77528 1.77105C8.66994 1.60358 8.63542 1.40112 8.67934 1.20821ZM3.89653 2.48989C3.96325 2.56173 4.01515 2.64601 4.04928 2.73792C4.0834 2.82983 4.09908 2.92756 4.0954 3.02553C4.09173 3.1235 4.06879 3.21979 4.02788 3.30889C3.98697 3.39798 3.9289 3.47814 3.85699 3.54478C3.74658 3.64674 3.64039 3.75268 3.53843 3.86259C3.40291 4.00361 3.21743 4.08576 3.02193 4.09137C2.82643 4.09698 2.63654 4.02559 2.49316 3.89257C2.34978 3.75955 2.26436 3.57555 2.25531 3.38018C2.24625 3.18481 2.31428 2.99369 2.44475 2.84799C2.57158 2.71072 2.70387 2.57817 2.84164 2.45035C2.91348 2.38363 2.99776 2.33173 3.08967 2.2976C3.18158 2.26348 3.27931 2.24781 3.37728 2.25148C3.47525 2.25515 3.57154 2.2781 3.66063 2.31901C3.74973 2.35991 3.82989 2.41798 3.89653 2.48989ZM7.91466 3.44481C8.11252 3.44481 8.30227 3.52341 8.44218 3.66332C8.58209 3.80323 8.66069 3.99299 8.66069 4.19085V7.61215L10.6802 9.63165C10.8161 9.77236 10.8913 9.96081 10.8896 10.1564C10.8879 10.352 10.8094 10.5391 10.6711 10.6775C10.5328 10.8158 10.3457 10.8942 10.1501 10.8959C9.95446 10.8976 9.76601 10.8224 9.62531 10.6865L7.38721 8.44845C7.24729 8.30857 7.16867 8.11885 7.16862 7.921V4.19085C7.16862 3.99299 7.24722 3.80323 7.38713 3.66332C7.52704 3.52341 7.7168 3.44481 7.91466 3.44481ZM1.71737 5.22634C1.90641 5.28463 2.06455 5.41562 2.15703 5.5905C2.2495 5.76538 2.26874 5.96982 2.2105 6.15888C2.16625 6.30234 2.12743 6.44742 2.09412 6.59381C2.04666 6.78282 1.92713 6.94575 1.76109 7.04777C1.59505 7.14978 1.39568 7.18278 1.20563 7.1397C1.01558 7.09662 0.849923 6.98089 0.744098 6.81725C0.638274 6.65361 0.600679 6.45507 0.639354 6.26407C0.681132 6.08005 0.729624 5.89851 0.784831 5.71946C0.843125 5.53043 0.974114 5.37228 1.14899 5.2798C1.32387 5.18733 1.52832 5.1681 1.71737 5.22634Z" fill="#6527F5"/></g></g><defs><clipPath id="clip0_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath><clipPath id="clip1_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath></defs>'},"report-bounce_rate":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6103)"><g clip-path="url(#clip1_65_6103)"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4811 4.48853C11.5431 4.42892 11.6162 4.3821 11.6963 4.35075C11.7763 4.31941 11.8618 4.30415 11.9477 4.30585C12.0337 4.30755 12.1185 4.32618 12.1972 4.36067C12.276 4.39516 12.3472 4.44483 12.4067 4.50685L15.4513 7.67965C15.5681 7.80146 15.6332 7.96367 15.6332 8.13241C15.6332 8.30114 15.5681 8.46335 15.4513 8.58517L12.4067 11.758C12.3472 11.8199 12.2761 11.8696 12.1974 11.9041C12.1187 11.9386 12.0339 11.9572 11.948 11.9589C11.8621 11.9607 11.7767 11.9455 11.6967 11.9142C11.6167 11.883 11.5436 11.8362 11.4816 11.7767C11.4196 11.7172 11.37 11.6461 11.3355 11.5674C11.301 11.4887 11.2823 11.4039 11.2806 11.318C11.2789 11.2321 11.2941 11.1467 11.3253 11.0667C11.3566 10.9866 11.4033 10.9136 11.4628 10.8516L13.444 8.78668H7.36619C7.19266 8.78668 7.02625 8.71775 6.90355 8.59505C6.78085 8.47235 6.71191 8.30593 6.71191 8.13241C6.71191 7.95888 6.78085 7.79246 6.90355 7.66976C7.02625 7.54706 7.19266 7.47813 7.36619 7.47813H13.4448L11.4628 5.41324C11.3428 5.28803 11.2774 5.1203 11.281 4.94691C11.2846 4.77351 11.3569 4.60864 11.482 4.48853" fill="#210F59"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0.366943 1.1533C0.366943 0.979774 0.435876 0.813357 0.558576 0.690656C0.681277 0.567956 0.847694 0.499023 1.02122 0.499023H9.26945C9.44298 0.499023 9.60939 0.567956 9.73209 0.690656C9.8548 0.813357 9.92373 0.979774 9.92373 1.1533V3.7704C9.92373 3.94393 9.8548 4.11034 9.73209 4.23304C9.60939 4.35574 9.44298 4.42468 9.26945 4.42468C9.09593 4.42468 8.92951 4.35574 8.80681 4.23304C8.68411 4.11034 8.61518 3.94393 8.61518 3.7704V1.80757H1.67549V14.4569H8.6143V12.4941C8.6143 12.3205 8.68324 12.1541 8.80594 12.0314C8.92864 11.9087 9.09506 11.8398 9.26858 11.8398C9.44211 11.8398 9.60852 11.9087 9.73122 12.0314C9.85392 12.1541 9.92286 12.3205 9.92286 12.4941V15.1112C9.92286 15.2847 9.85392 15.4511 9.73122 15.5738C9.60852 15.6965 9.44211 15.7655 9.26858 15.7655H1.02122C0.847694 15.7655 0.681277 15.6965 0.558576 15.5738C0.435876 15.4511 0.366943 15.2847 0.366943 15.1112V1.1533Z" fill="#210F59"/></g></g><defs><clipPath id="clip0_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath><clipPath id="clip1_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath></defs>'},"report-bounce_rate-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6103)"><g clip-path="url(#clip1_65_6103)"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4811 4.48853C11.5431 4.42892 11.6162 4.3821 11.6963 4.35075C11.7763 4.31941 11.8618 4.30415 11.9477 4.30585C12.0337 4.30755 12.1185 4.32618 12.1972 4.36067C12.276 4.39516 12.3472 4.44483 12.4067 4.50685L15.4513 7.67965C15.5681 7.80146 15.6332 7.96367 15.6332 8.13241C15.6332 8.30114 15.5681 8.46335 15.4513 8.58517L12.4067 11.758C12.3472 11.8199 12.2761 11.8696 12.1974 11.9041C12.1187 11.9386 12.0339 11.9572 11.948 11.9589C11.8621 11.9607 11.7767 11.9455 11.6967 11.9142C11.6167 11.883 11.5436 11.8362 11.4816 11.7767C11.4196 11.7172 11.37 11.6461 11.3355 11.5674C11.301 11.4887 11.2823 11.4039 11.2806 11.318C11.2789 11.2321 11.2941 11.1467 11.3253 11.0667C11.3566 10.9866 11.4033 10.9136 11.4628 10.8516L13.444 8.78668H7.36619C7.19266 8.78668 7.02625 8.71775 6.90355 8.59505C6.78085 8.47235 6.71191 8.30593 6.71191 8.13241C6.71191 7.95888 6.78085 7.79246 6.90355 7.66976C7.02625 7.54706 7.19266 7.47813 7.36619 7.47813H13.4448L11.4628 5.41324C11.3428 5.28803 11.2774 5.1203 11.281 4.94691C11.2846 4.77351 11.3569 4.60864 11.482 4.48853" fill="#210F59"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0.366943 1.1533C0.366943 0.979774 0.435876 0.813357 0.558576 0.690656C0.681277 0.567956 0.847694 0.499023 1.02122 0.499023H9.26945C9.44298 0.499023 9.60939 0.567956 9.73209 0.690656C9.8548 0.813357 9.92373 0.979774 9.92373 1.1533V3.7704C9.92373 3.94393 9.8548 4.11034 9.73209 4.23304C9.60939 4.35574 9.44298 4.42468 9.26945 4.42468C9.09593 4.42468 8.92951 4.35574 8.80681 4.23304C8.68411 4.11034 8.61518 3.94393 8.61518 3.7704V1.80757H1.67549V14.4569H8.6143V12.4941C8.6143 12.3205 8.68324 12.1541 8.80594 12.0314C8.92864 11.9087 9.09506 11.8398 9.26858 11.8398C9.44211 11.8398 9.60852 11.9087 9.73122 12.0314C9.85392 12.1541 9.92286 12.3205 9.92286 12.4941V15.1112C9.92286 15.2847 9.85392 15.4511 9.73122 15.5738C9.60852 15.6965 9.44211 15.7655 9.26858 15.7655H1.02122C0.847694 15.7655 0.681277 15.6965 0.558576 15.5738C0.435876 15.4511 0.366943 15.2847 0.366943 15.1112V1.1533Z" fill="#6527F5"/></g></g><defs><clipPath id="clip0_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath><clipPath id="clip1_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath></defs>'},"report-revenue_sales":{viewBox:"0 0 16 17",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0637 0.771255C9.48809 0.28068 8.7565 0.0112305 8.00017 0.0112305C7.24384 0.0112305 6.51225 0.28068 5.9366 0.771255L5.63754 1.02577C5.38285 1.24303 5.06611 1.37441 4.73241 1.40118L4.3403 1.433C3.58627 1.493 2.87829 1.8197 2.34335 2.35449C1.80841 2.88929 1.48152 3.59718 1.42131 4.35119L1.39029 4.74331C1.36374 5.07691 1.23265 5.39364 1.01568 5.64843L0.760363 5.94749C0.269577 6.52319 0 7.25495 0 8.01146C0 8.76797 0.269577 9.49972 0.760363 10.0754L1.01488 10.3745C1.23214 10.6292 1.36351 10.9459 1.39029 11.2796L1.42211 11.6717C1.48211 12.4258 1.80881 13.1337 2.3436 13.6687C2.87839 14.2036 3.58629 14.5305 4.3403 14.5907L4.73241 14.6217C5.06602 14.6483 5.38275 14.7794 5.63754 14.9964L5.9366 15.2509C6.5123 15.7417 7.24406 16.0112 8.00057 16.0112C8.75708 16.0112 9.48883 15.7417 10.0645 15.2509L10.3636 14.9964C10.6184 14.7794 10.9351 14.6483 11.2687 14.6217L11.6608 14.5899C12.4149 14.5299 13.1228 14.2032 13.6578 13.6684C14.1927 13.1336 14.5196 12.4257 14.5798 11.6717L14.61 11.2796C14.6368 10.9459 14.7682 10.6292 14.9855 10.3745L15.24 10.0746C15.7306 9.49898 16 8.76739 16 8.01106C16 7.25473 15.7306 6.52314 15.24 5.94749L14.9855 5.64843C14.7682 5.39374 14.6368 5.077 14.61 4.74331L14.579 4.35119C14.519 3.59716 14.1923 2.88918 13.6575 2.35424C13.1227 1.8193 12.4148 1.49241 11.6608 1.4322L11.2687 1.40118C10.9351 1.37463 10.6184 1.24354 10.3636 1.02657L10.0637 0.771255ZM6.96898 1.9826C7.25678 1.73741 7.62249 1.60275 8.00057 1.60275C8.37864 1.60275 8.74436 1.73741 9.03216 1.9826L9.33201 2.23791C9.84149 2.67214 10.475 2.9346 11.1423 2.98794L11.5344 3.01975C11.9112 3.04992 12.2649 3.21329 12.5322 3.48059C12.7995 3.74789 12.9629 4.10164 12.9931 4.47845L13.0249 4.87056C13.0782 5.53786 13.3407 6.17133 13.7749 6.68081L14.0294 6.98067C14.2746 7.26846 14.4093 7.63418 14.4093 8.01225C14.4093 8.39033 14.2746 8.75605 14.0294 9.04384L13.7749 9.34369C13.3407 9.85318 13.0782 10.4866 13.0249 11.1539L12.9931 11.5461C12.9629 11.9229 12.7995 12.2766 12.5322 12.5439C12.2649 12.8112 11.9112 12.9746 11.5344 13.0048L11.1423 13.0366C10.475 13.0899 9.84149 13.3524 9.33201 13.7866L9.03216 14.0411C8.74436 14.2863 8.37864 14.421 8.00057 14.421C7.62249 14.421 7.25678 14.2863 6.96898 14.0411L6.66913 13.7866C6.15965 13.3524 5.52617 13.0899 4.85888 13.0366L4.46676 13.0048C4.08981 12.9748 3.73587 12.8115 3.46841 12.5442C3.20095 12.2768 3.03746 11.923 3.00727 11.5461L2.97545 11.1539C2.92235 10.4867 2.66017 9.85328 2.22622 9.34369L1.9717 9.04384C1.72652 8.75605 1.59186 8.39033 1.59186 8.01225C1.59186 7.63418 1.72652 7.26846 1.9717 6.98067L2.22622 6.68081C2.66045 6.17133 2.92292 5.53786 2.97625 4.87056L3.00806 4.47845C3.03805 4.1015 3.20135 3.74756 3.46866 3.4801C3.73598 3.21264 4.08983 3.04915 4.46676 3.01896L4.85888 2.98794C5.52617 2.9346 6.15965 2.67214 6.66913 2.23791L6.96898 1.9826ZM10.949 6.18848C11.0249 6.11511 11.0855 6.02735 11.1272 5.93031C11.1689 5.83327 11.1909 5.72891 11.1918 5.6233C11.1927 5.51769 11.1726 5.41296 11.1326 5.31521C11.0926 5.21746 11.0335 5.12866 10.9588 5.05398C10.8842 4.9793 10.7954 4.92024 10.6976 4.88025C10.5999 4.84026 10.4951 4.82013 10.3895 4.82105C10.2839 4.82197 10.1795 4.84391 10.0825 4.8856C9.98547 4.92728 9.89771 4.98787 9.82434 5.06384L5.05215 9.83603C4.97619 9.9094 4.91559 9.99716 4.87391 10.0942C4.83222 10.1912 4.81028 10.2956 4.80937 10.4012C4.80845 10.5068 4.82857 10.6116 4.86856 10.7093C4.90856 10.807 4.96761 10.8959 5.04229 10.9705C5.11697 11.0452 5.20577 11.1043 5.30352 11.1443C5.40127 11.1842 5.506 11.2044 5.61161 11.2035C5.71722 11.2025 5.82159 11.1806 5.91862 11.1389C6.01566 11.0972 6.10343 11.0366 6.1768 10.9607L10.949 6.18848ZM7.2052 6.02305C7.2052 6.33946 7.07951 6.64292 6.85577 6.86666C6.63203 7.0904 6.32857 7.21609 6.01216 7.21609C5.69574 7.21609 5.39228 7.0904 5.16854 6.86666C4.9448 6.64292 4.81911 6.33946 4.81911 6.02305C4.81911 5.70663 4.9448 5.40318 5.16854 5.17944C5.39228 4.9557 5.69574 4.83 6.01216 4.83C6.32857 4.83 6.63203 4.9557 6.85577 5.17944C7.07951 5.40318 7.2052 5.70663 7.2052 6.02305ZM9.98898 11.1929C10.3054 11.1929 10.6089 11.0672 10.8326 10.8435C11.0563 10.6197 11.182 10.3163 11.182 9.99987C11.182 9.68345 11.0563 9.38 10.8326 9.15626C10.6089 8.93252 10.3054 8.80682 9.98898 8.80682C9.67256 8.80682 9.36911 8.93252 9.14537 9.15626C8.92163 9.38 8.79593 9.68345 8.79593 9.99987C8.79593 10.3163 8.92163 10.6197 9.14537 10.8435C9.36911 11.0672 9.67256 11.1929 9.98898 11.1929Z" fill="#210F59"/>'},"report-revenue_sales-active":{viewBox:"0 0 16 17",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0637 0.771255C9.48809 0.28068 8.7565 0.0112305 8.00017 0.0112305C7.24384 0.0112305 6.51225 0.28068 5.9366 0.771255L5.63754 1.02577C5.38285 1.24303 5.06611 1.37441 4.73241 1.40118L4.3403 1.433C3.58627 1.493 2.87829 1.8197 2.34335 2.35449C1.80841 2.88929 1.48152 3.59718 1.42131 4.35119L1.39029 4.74331C1.36374 5.07691 1.23265 5.39364 1.01568 5.64843L0.760363 5.94749C0.269577 6.52319 0 7.25495 0 8.01146C0 8.76797 0.269577 9.49972 0.760363 10.0754L1.01488 10.3745C1.23214 10.6292 1.36351 10.9459 1.39029 11.2796L1.42211 11.6717C1.48211 12.4258 1.80881 13.1337 2.3436 13.6687C2.87839 14.2036 3.58629 14.5305 4.3403 14.5907L4.73241 14.6217C5.06602 14.6483 5.38275 14.7794 5.63754 14.9964L5.9366 15.2509C6.5123 15.7417 7.24406 16.0112 8.00057 16.0112C8.75708 16.0112 9.48883 15.7417 10.0645 15.2509L10.3636 14.9964C10.6184 14.7794 10.9351 14.6483 11.2687 14.6217L11.6608 14.5899C12.4149 14.5299 13.1228 14.2032 13.6578 13.6684C14.1927 13.1336 14.5196 12.4257 14.5798 11.6717L14.61 11.2796C14.6368 10.9459 14.7682 10.6292 14.9855 10.3745L15.24 10.0746C15.7306 9.49898 16 8.76739 16 8.01106C16 7.25473 15.7306 6.52314 15.24 5.94749L14.9855 5.64843C14.7682 5.39374 14.6368 5.077 14.61 4.74331L14.579 4.35119C14.519 3.59716 14.1923 2.88918 13.6575 2.35424C13.1227 1.8193 12.4148 1.49241 11.6608 1.4322L11.2687 1.40118C10.9351 1.37463 10.6184 1.24354 10.3636 1.02657L10.0637 0.771255ZM6.96898 1.9826C7.25678 1.73741 7.62249 1.60275 8.00057 1.60275C8.37864 1.60275 8.74436 1.73741 9.03216 1.9826L9.33201 2.23791C9.84149 2.67214 10.475 2.9346 11.1423 2.98794L11.5344 3.01975C11.9112 3.04992 12.2649 3.21329 12.5322 3.48059C12.7995 3.74789 12.9629 4.10164 12.9931 4.47845L13.0249 4.87056C13.0782 5.53786 13.3407 6.17133 13.7749 6.68081L14.0294 6.98067C14.2746 7.26846 14.4093 7.63418 14.4093 8.01225C14.4093 8.39033 14.2746 8.75605 14.0294 9.04384L13.7749 9.34369C13.3407 9.85318 13.0782 10.4866 13.0249 11.1539L12.9931 11.5461C12.9629 11.9229 12.7995 12.2766 12.5322 12.5439C12.2649 12.8112 11.9112 12.9746 11.5344 13.0048L11.1423 13.0366C10.475 13.0899 9.84149 13.3524 9.33201 13.7866L9.03216 14.0411C8.74436 14.2863 8.37864 14.421 8.00057 14.421C7.62249 14.421 7.25678 14.2863 6.96898 14.0411L6.66913 13.7866C6.15965 13.3524 5.52617 13.0899 4.85888 13.0366L4.46676 13.0048C4.08981 12.9748 3.73587 12.8115 3.46841 12.5442C3.20095 12.2768 3.03746 11.923 3.00727 11.5461L2.97545 11.1539C2.92235 10.4867 2.66017 9.85328 2.22622 9.34369L1.9717 9.04384C1.72652 8.75605 1.59186 8.39033 1.59186 8.01225C1.59186 7.63418 1.72652 7.26846 1.9717 6.98067L2.22622 6.68081C2.66045 6.17133 2.92292 5.53786 2.97625 4.87056L3.00806 4.47845C3.03805 4.1015 3.20135 3.74756 3.46866 3.4801C3.73598 3.21264 4.08983 3.04915 4.46676 3.01896L4.85888 2.98794C5.52617 2.9346 6.15965 2.67214 6.66913 2.23791L6.96898 1.9826ZM10.949 6.18848C11.0249 6.11511 11.0855 6.02735 11.1272 5.93031C11.1689 5.83327 11.1909 5.72891 11.1918 5.6233C11.1927 5.51769 11.1726 5.41296 11.1326 5.31521C11.0926 5.21746 11.0335 5.12866 10.9588 5.05398C10.8842 4.9793 10.7954 4.92024 10.6976 4.88025C10.5999 4.84026 10.4951 4.82013 10.3895 4.82105C10.2839 4.82197 10.1795 4.84391 10.0825 4.8856C9.98547 4.92728 9.89771 4.98787 9.82434 5.06384L5.05215 9.83603C4.97619 9.9094 4.91559 9.99716 4.87391 10.0942C4.83222 10.1912 4.81028 10.2956 4.80937 10.4012C4.80845 10.5068 4.82857 10.6116 4.86856 10.7093C4.90856 10.807 4.96761 10.8959 5.04229 10.9705C5.11697 11.0452 5.20577 11.1043 5.30352 11.1443C5.40127 11.1842 5.506 11.2044 5.61161 11.2035C5.71722 11.2025 5.82159 11.1806 5.91862 11.1389C6.01566 11.0972 6.10343 11.0366 6.1768 10.9607L10.949 6.18848ZM7.2052 6.02305C7.2052 6.33946 7.07951 6.64292 6.85577 6.86666C6.63203 7.0904 6.32857 7.21609 6.01216 7.21609C5.69574 7.21609 5.39228 7.0904 5.16854 6.86666C4.9448 6.64292 4.81911 6.33946 4.81911 6.02305C4.81911 5.70663 4.9448 5.40318 5.16854 5.17944C5.39228 4.9557 5.69574 4.83 6.01216 4.83C6.32857 4.83 6.63203 4.9557 6.85577 5.17944C7.07951 5.40318 7.2052 5.70663 7.2052 6.02305ZM9.98898 11.1929C10.3054 11.1929 10.6089 11.0672 10.8326 10.8435C11.0563 10.6197 11.182 10.3163 11.182 9.99987C11.182 9.68345 11.0563 9.38 10.8326 9.15626C10.6089 8.93252 10.3054 8.80682 9.98898 8.80682C9.67256 8.80682 9.36911 8.93252 9.14537 9.15626C8.92163 9.38 8.79593 9.68345 8.79593 9.99987C8.79593 10.3163 8.92163 10.6197 9.14537 10.8435C9.36911 11.0672 9.67256 11.1929 9.98898 11.1929Z" fill="#6527F5"/>'},"report-average_revenue_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_291)"><g clip-path="url(#clip1_51_291)"><path d="M15.8183 3.22621V2.10932H14.143V0.992432H13.0261V2.10932H12.4677C11.8517 2.10932 11.3508 2.6108 11.3508 3.22621V4.3431C11.3508 4.95906 11.8517 5.45998 12.4677 5.45998H14.7014V6.57687H11.3508V7.69376H13.0261V8.81065H14.143V7.69376H14.7014C15.3174 7.69376 15.8183 7.19283 15.8183 6.57687V5.45998C15.8183 4.84458 15.3174 4.3431 14.7014 4.3431H12.4677V3.22621H15.8183ZM12.4677 11.0444V12.1613H13.9118L11.9092 14.1639L10.6293 12.8834C10.5247 12.7788 10.3829 12.7199 10.235 12.7198H10.2339C10.086 12.7199 9.94418 12.7788 9.83961 12.8834L6.88321 15.8392L7.67285 16.6289L10.2344 14.0678L11.5144 15.3483C11.6191 15.453 11.7611 15.5119 11.9092 15.5119C12.0573 15.5119 12.1993 15.453 12.304 15.3483L14.7014 12.9509V14.3951H15.8183V11.0444H12.4677ZM1.29877 16.6289H0.181885V13.8366C0.181885 11.681 1.9354 9.92753 4.09099 9.92753H7.44166C8.5524 9.92753 9.614 10.4022 10.3551 11.2304L9.52298 11.9753C9.26108 11.6825 8.94031 11.4482 8.58166 11.2878C8.22301 11.1273 7.83455 11.0444 7.44166 11.0444H4.09099C2.55136 11.0444 1.29877 12.297 1.29877 13.8366V16.6289ZM5.76632 8.81065C6.80308 8.81065 7.79738 8.3988 8.53048 7.6657C9.26358 6.9326 9.67543 5.9383 9.67543 4.90154C9.67543 3.86478 9.26358 2.87048 8.53048 2.13738C7.79738 1.40428 6.80308 0.992432 5.76632 0.992432C4.72956 0.992432 3.73527 1.40428 3.00217 2.13738C2.26907 2.87048 1.85722 3.86478 1.85722 4.90154C1.85722 5.9383 2.26907 6.9326 3.00217 7.6657C3.73527 8.3988 4.72956 8.81065 5.76632 8.81065ZM5.76632 2.10932C6.50687 2.10932 7.21708 2.4035 7.74072 2.92714C8.26436 3.45078 8.55854 4.161 8.55854 4.90154C8.55854 5.64208 8.26436 6.35229 7.74072 6.87594C7.21708 7.39958 6.50687 7.69376 5.76632 7.69376C5.02578 7.69376 4.31557 7.39958 3.79193 6.87594C3.26828 6.35229 2.9741 5.64208 2.9741 4.90154C2.9741 4.161 3.26828 3.45078 3.79193 2.92714C4.31557 2.4035 5.02578 2.10932 5.76632 2.10932Z" fill="#210F59"/></g></g><defs><clipPath id="clip0_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath><clipPath id="clip1_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath></defs>'},"report-average_revenue_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_291)"><g clip-path="url(#clip1_51_291)"><path d="M15.8183 3.22621V2.10932H14.143V0.992432H13.0261V2.10932H12.4677C11.8517 2.10932 11.3508 2.6108 11.3508 3.22621V4.3431C11.3508 4.95906 11.8517 5.45998 12.4677 5.45998H14.7014V6.57687H11.3508V7.69376H13.0261V8.81065H14.143V7.69376H14.7014C15.3174 7.69376 15.8183 7.19283 15.8183 6.57687V5.45998C15.8183 4.84458 15.3174 4.3431 14.7014 4.3431H12.4677V3.22621H15.8183ZM12.4677 11.0444V12.1613H13.9118L11.9092 14.1639L10.6293 12.8834C10.5247 12.7788 10.3829 12.7199 10.235 12.7198H10.2339C10.086 12.7199 9.94418 12.7788 9.83961 12.8834L6.88321 15.8392L7.67285 16.6289L10.2344 14.0678L11.5144 15.3483C11.6191 15.453 11.7611 15.5119 11.9092 15.5119C12.0573 15.5119 12.1993 15.453 12.304 15.3483L14.7014 12.9509V14.3951H15.8183V11.0444H12.4677ZM1.29877 16.6289H0.181885V13.8366C0.181885 11.681 1.9354 9.92753 4.09099 9.92753H7.44166C8.5524 9.92753 9.614 10.4022 10.3551 11.2304L9.52298 11.9753C9.26108 11.6825 8.94031 11.4482 8.58166 11.2878C8.22301 11.1273 7.83455 11.0444 7.44166 11.0444H4.09099C2.55136 11.0444 1.29877 12.297 1.29877 13.8366V16.6289ZM5.76632 8.81065C6.80308 8.81065 7.79738 8.3988 8.53048 7.6657C9.26358 6.9326 9.67543 5.9383 9.67543 4.90154C9.67543 3.86478 9.26358 2.87048 8.53048 2.13738C7.79738 1.40428 6.80308 0.992432 5.76632 0.992432C4.72956 0.992432 3.73527 1.40428 3.00217 2.13738C2.26907 2.87048 1.85722 3.86478 1.85722 4.90154C1.85722 5.9383 2.26907 6.9326 3.00217 7.6657C3.73527 8.3988 4.72956 8.81065 5.76632 8.81065ZM5.76632 2.10932C6.50687 2.10932 7.21708 2.4035 7.74072 2.92714C8.26436 3.45078 8.55854 4.161 8.55854 4.90154C8.55854 5.64208 8.26436 6.35229 7.74072 6.87594C7.21708 7.39958 6.50687 7.69376 5.76632 7.69376C5.02578 7.69376 4.31557 7.39958 3.79193 6.87594C3.26828 6.35229 2.9741 5.64208 2.9741 4.90154C2.9741 4.161 3.26828 3.45078 3.79193 2.92714C4.31557 2.4035 5.02578 2.10932 5.76632 2.10932Z" fill="#6527F5"/></g></g><defs><clipPath id="clip0_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath><clipPath id="clip1_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath></defs>'},"report-average_revenue_per_session":{viewBox:"0 0 16 17",content:'<path d="M11.5999 2.18463C11.3877 2.18463 11.1842 2.10034 11.0342 1.95031C10.8842 1.80028 10.7999 1.5968 10.7999 1.38463C10.7999 1.17245 10.8842 0.968969 11.0342 0.81894C11.1842 0.668911 11.3877 0.584625 11.5999 0.584625H14.7999C15.0121 0.584625 15.2156 0.668911 15.3656 0.81894C15.5156 0.968969 15.5999 1.17245 15.5999 1.38463V4.58462C15.5999 4.7968 15.5156 5.00028 15.3656 5.15031C15.2156 5.30034 15.0121 5.38462 14.7999 5.38462C14.5877 5.38462 14.3842 5.30034 14.2342 5.15031C14.0842 5.00028 13.9999 4.7968 13.9999 4.58462V3.31582L9.3655 7.95022C9.21548 8.1002 9.01203 8.18445 8.7999 8.18445C8.58777 8.18445 8.38432 8.1002 8.2343 7.95022L5.9999 5.71582L1.7655 9.95022C1.61462 10.096 1.41254 10.1766 1.20278 10.1748C0.993024 10.1729 0.792374 10.0888 0.644048 9.94048C0.495721 9.79215 0.411586 9.5915 0.409763 9.38174C0.40794 9.17199 0.488576 8.96991 0.634302 8.81902L5.4343 4.01902C5.58432 3.86905 5.78777 3.7848 5.9999 3.7848C6.21203 3.7848 6.41548 3.86905 6.5655 4.01902L8.7999 6.25342L12.8687 2.18463H11.5999ZM1.9999 13.3846V15.7846C1.9999 15.9968 1.91562 16.2003 1.76559 16.3503C1.61556 16.5003 1.41208 16.5846 1.1999 16.5846C0.987729 16.5846 0.784246 16.5003 0.634217 16.3503C0.484188 16.2003 0.399902 15.9968 0.399902 15.7846V13.3846C0.399902 13.1725 0.484188 12.969 0.634217 12.8189C0.784246 12.6689 0.987729 12.5846 1.1999 12.5846C1.41208 12.5846 1.61556 12.6689 1.76559 12.8189C1.91562 12.969 1.9999 13.1725 1.9999 13.3846ZM5.9999 10.1846C5.9999 9.97245 5.91562 9.76897 5.76559 9.61894C5.61556 9.46891 5.41208 9.38462 5.1999 9.38462C4.98773 9.38462 4.78425 9.46891 4.63422 9.61894C4.48419 9.76897 4.3999 9.97245 4.3999 10.1846V15.7846C4.3999 15.9968 4.48419 16.2003 4.63422 16.3503C4.78425 16.5003 4.98773 16.5846 5.1999 16.5846C5.41208 16.5846 5.61556 16.5003 5.76559 16.3503C5.91562 16.2003 5.9999 15.9968 5.9999 15.7846V10.1846ZM9.1999 10.9846C9.41208 10.9846 9.61556 11.0689 9.76559 11.2189C9.91562 11.369 9.9999 11.5725 9.9999 11.7846V15.7846C9.9999 15.9968 9.91562 16.2003 9.76559 16.3503C9.61556 16.5003 9.41208 16.5846 9.1999 16.5846C8.98773 16.5846 8.78425 16.5003 8.63422 16.3503C8.48419 16.2003 8.3999 15.9968 8.3999 15.7846V11.7846C8.3999 11.5725 8.48419 11.369 8.63422 11.2189C8.78425 11.0689 8.98773 10.9846 9.1999 10.9846ZM13.9999 7.78462C13.9999 7.57245 13.9156 7.36897 13.7656 7.21894C13.6156 7.06891 13.4121 6.98462 13.1999 6.98462C12.9877 6.98462 12.7842 7.06891 12.6342 7.21894C12.4842 7.36897 12.3999 7.57245 12.3999 7.78462V15.7846C12.3999 15.9968 12.4842 16.2003 12.6342 16.3503C12.7842 16.5003 12.9877 16.5846 13.1999 16.5846C13.4121 16.5846 13.6156 16.5003 13.7656 16.3503C13.9156 16.2003 13.9999 15.9968 13.9999 15.7846V7.78462Z" fill="#210F59"/>'},"report-average_revenue_per_session-active":{viewBox:"0 0 16 17",content:'<path d="M11.5999 2.18463C11.3877 2.18463 11.1842 2.10034 11.0342 1.95031C10.8842 1.80028 10.7999 1.5968 10.7999 1.38463C10.7999 1.17245 10.8842 0.968969 11.0342 0.81894C11.1842 0.668911 11.3877 0.584625 11.5999 0.584625H14.7999C15.0121 0.584625 15.2156 0.668911 15.3656 0.81894C15.5156 0.968969 15.5999 1.17245 15.5999 1.38463V4.58462C15.5999 4.7968 15.5156 5.00028 15.3656 5.15031C15.2156 5.30034 15.0121 5.38462 14.7999 5.38462C14.5877 5.38462 14.3842 5.30034 14.2342 5.15031C14.0842 5.00028 13.9999 4.7968 13.9999 4.58462V3.31582L9.3655 7.95022C9.21548 8.1002 9.01203 8.18445 8.7999 8.18445C8.58777 8.18445 8.38432 8.1002 8.2343 7.95022L5.9999 5.71582L1.7655 9.95022C1.61462 10.096 1.41254 10.1766 1.20278 10.1748C0.993024 10.1729 0.792374 10.0888 0.644048 9.94048C0.495721 9.79215 0.411586 9.5915 0.409763 9.38174C0.40794 9.17199 0.488576 8.96991 0.634302 8.81902L5.4343 4.01902C5.58432 3.86905 5.78777 3.7848 5.9999 3.7848C6.21203 3.7848 6.41548 3.86905 6.5655 4.01902L8.7999 6.25342L12.8687 2.18463H11.5999ZM1.9999 13.3846V15.7846C1.9999 15.9968 1.91562 16.2003 1.76559 16.3503C1.61556 16.5003 1.41208 16.5846 1.1999 16.5846C0.987729 16.5846 0.784246 16.5003 0.634217 16.3503C0.484188 16.2003 0.399902 15.9968 0.399902 15.7846V13.3846C0.399902 13.1725 0.484188 12.969 0.634217 12.8189C0.784246 12.6689 0.987729 12.5846 1.1999 12.5846C1.41208 12.5846 1.61556 12.6689 1.76559 12.8189C1.91562 12.969 1.9999 13.1725 1.9999 13.3846ZM5.9999 10.1846C5.9999 9.97245 5.91562 9.76897 5.76559 9.61894C5.61556 9.46891 5.41208 9.38462 5.1999 9.38462C4.98773 9.38462 4.78425 9.46891 4.63422 9.61894C4.48419 9.76897 4.3999 9.97245 4.3999 10.1846V15.7846C4.3999 15.9968 4.48419 16.2003 4.63422 16.3503C4.78425 16.5003 4.98773 16.5846 5.1999 16.5846C5.41208 16.5846 5.61556 16.5003 5.76559 16.3503C5.91562 16.2003 5.9999 15.9968 5.9999 15.7846V10.1846ZM9.1999 10.9846C9.41208 10.9846 9.61556 11.0689 9.76559 11.2189C9.91562 11.369 9.9999 11.5725 9.9999 11.7846V15.7846C9.9999 15.9968 9.91562 16.2003 9.76559 16.3503C9.61556 16.5003 9.41208 16.5846 9.1999 16.5846C8.98773 16.5846 8.78425 16.5003 8.63422 16.3503C8.48419 16.2003 8.3999 15.9968 8.3999 15.7846V11.7846C8.3999 11.5725 8.48419 11.369 8.63422 11.2189C8.78425 11.0689 8.98773 10.9846 9.1999 10.9846ZM13.9999 7.78462C13.9999 7.57245 13.9156 7.36897 13.7656 7.21894C13.6156 7.06891 13.4121 6.98462 13.1999 6.98462C12.9877 6.98462 12.7842 7.06891 12.6342 7.21894C12.4842 7.36897 12.3999 7.57245 12.3999 7.78462V15.7846C12.3999 15.9968 12.4842 16.2003 12.6342 16.3503C12.7842 16.5003 12.9877 16.5846 13.1999 16.5846C13.4121 16.5846 13.6156 16.5003 13.7656 16.3503C13.9156 16.2003 13.9999 15.9968 13.9999 15.7846V7.78462Z" fill="#6527F5"/>'},"report-new_users":{viewBox:"0 0 16 17",content:'<path d="M12.6742 14.6295H14.3495C14.4969 14.6318 14.6426 14.5984 14.7743 14.5321C14.9059 14.4659 15.0196 14.3687 15.1055 14.249C15.1914 14.1293 15.2471 13.9905 15.2677 13.8446C15.2884 13.6986 15.2734 13.5499 15.224 13.411C14.7968 12.4967 14.1205 11.7213 13.2727 11.1738C12.4249 10.6263 11.4399 10.329 10.4308 10.3159M10.4308 8.15394C10.8079 8.15404 11.1813 8.07986 11.5298 7.93562C11.8782 7.79138 12.1948 7.57992 12.4615 7.3133C12.7282 7.04669 12.9397 6.73016 13.084 6.38177C13.2284 6.03339 13.3027 5.65998 13.3027 5.28289C13.3037 4.90513 13.2302 4.53088 13.0863 4.18159C12.9424 3.8323 12.731 3.51484 12.4643 3.2474C12.1975 2.97996 11.8805 2.76779 11.5316 2.62306C11.1826 2.47834 10.8086 2.40389 10.4308 2.404M5.9448 8.24405C6.80811 8.24198 7.63534 7.89752 8.24498 7.28626C8.85462 6.675 9.19689 5.84687 9.19668 4.98356C9.19668 4.1209 8.85399 3.29357 8.244 2.68358C7.63401 2.07359 6.80668 1.7309 5.94402 1.7309C5.08136 1.7309 4.25403 2.07359 3.64404 2.68358C3.03405 3.29357 2.69136 4.1209 2.69136 4.98356C2.69136 5.84687 3.03382 6.67492 3.64361 7.28603C4.2534 7.89714 5.08071 8.2414 5.94402 8.24327M9.6159 15.5643C9.8919 15.5639 10.1624 15.4872 10.3975 15.3425C10.6325 15.1978 10.823 14.9909 10.9477 14.7447C11.0724 14.4985 11.1265 14.2225 11.104 13.9474C11.0815 13.6723 10.9833 13.4088 10.8203 13.1861C10.2491 12.428 9.51387 11.8089 8.66955 11.3752C7.82523 10.9415 6.89373 10.7044 5.9448 10.6818C4.99581 10.7045 4.06427 10.9417 3.21995 11.3755C2.37562 11.8094 1.6404 12.4286 1.06934 13.1869C0.906866 13.4096 0.809109 13.6729 0.786862 13.9477C0.764614 14.2225 0.818741 14.4981 0.943265 14.7441C1.06779 14.9901 1.25787 15.1968 1.49251 15.3416C1.72715 15.4863 1.99723 15.5634 2.27292 15.5643H9.6159Z" stroke="#210F59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-new_users-active":{viewBox:"0 0 16 17",content:'<path d="M12.6742 14.6295H14.3495C14.4969 14.6318 14.6426 14.5984 14.7743 14.5321C14.9059 14.4659 15.0196 14.3687 15.1055 14.249C15.1914 14.1293 15.2471 13.9905 15.2677 13.8446C15.2884 13.6986 15.2734 13.5499 15.224 13.411C14.7968 12.4967 14.1205 11.7213 13.2727 11.1738C12.4249 10.6263 11.4399 10.329 10.4308 10.3159M10.4308 8.15394C10.8079 8.15404 11.1813 8.07986 11.5298 7.93562C11.8782 7.79138 12.1948 7.57992 12.4615 7.3133C12.7282 7.04669 12.9397 6.73016 13.084 6.38177C13.2284 6.03339 13.3027 5.65998 13.3027 5.28289C13.3037 4.90513 13.2302 4.53088 13.0863 4.18159C12.9424 3.8323 12.731 3.51484 12.4643 3.2474C12.1975 2.97996 11.8805 2.76779 11.5316 2.62306C11.1826 2.47834 10.8086 2.40389 10.4308 2.404M5.9448 8.24405C6.80811 8.24198 7.63534 7.89752 8.24498 7.28626C8.85462 6.675 9.19689 5.84687 9.19668 4.98356C9.19668 4.1209 8.85399 3.29357 8.244 2.68358C7.63401 2.07359 6.80668 1.7309 5.94402 1.7309C5.08136 1.7309 4.25403 2.07359 3.64404 2.68358C3.03405 3.29357 2.69136 4.1209 2.69136 4.98356C2.69136 5.84687 3.03382 6.67492 3.64361 7.28603C4.2534 7.89714 5.08071 8.2414 5.94402 8.24327M9.6159 15.5643C9.8919 15.5639 10.1624 15.4872 10.3975 15.3425C10.6325 15.1978 10.823 14.9909 10.9477 14.7447C11.0724 14.4985 11.1265 14.2225 11.104 13.9474C11.0815 13.6723 10.9833 13.4088 10.8203 13.1861C10.2491 12.428 9.51387 11.8089 8.66955 11.3752C7.82523 10.9415 6.89373 10.7044 5.9448 10.6818C4.99581 10.7045 4.06427 10.9417 3.21995 11.3755C2.37562 11.8094 1.6404 12.4286 1.06934 13.1869C0.906866 13.4096 0.809109 13.6729 0.786862 13.9477C0.764614 14.2225 0.818741 14.4981 0.943265 14.7441C1.06779 14.9901 1.25787 15.1968 1.49251 15.3416C1.72715 15.4863 1.99723 15.5634 2.27292 15.5643H9.6159Z" stroke="#6527F5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-ecommerce_purchases":{viewBox:"0 0 16 16",content:'<path d="M15.5541 3.52685C15.5006 3.46289 15.4338 3.41144 15.3583 3.37614C15.2827 3.34084 15.2004 3.32256 15.117 3.32257H3.63441L3.20164 0.94305C3.1778 0.811844 3.10867 0.693165 3.0063 0.607704C2.90393 0.522244 2.77482 0.475422 2.64146 0.475403H0.881201C0.730178 0.475403 0.58534 0.535396 0.478551 0.642186C0.371761 0.748975 0.311768 0.893813 0.311768 1.04484C0.311768 1.19586 0.371761 1.3407 0.478551 1.44749C0.58534 1.55428 0.730178 1.61427 0.881201 1.61427H2.16243L3.98176 11.6C4.03536 11.8961 4.16623 12.1729 4.36115 12.4022C4.09213 12.6535 3.89795 12.9743 3.80016 13.3292C3.70236 13.6841 3.70477 14.0592 3.80711 14.4128C3.90945 14.7664 4.10773 15.0847 4.37996 15.3325C4.65219 15.5803 4.98773 15.7479 5.34937 15.8166C5.71102 15.8854 6.08463 15.8526 6.4288 15.722C6.77296 15.5914 7.07422 15.368 7.29917 15.0766C7.52412 14.7852 7.66397 14.4372 7.70322 14.0712C7.74246 13.7051 7.67957 13.3354 7.5215 13.0029H10.7545C10.6271 13.2696 10.5611 13.5615 10.5616 13.8571C10.5616 14.2513 10.6785 14.6366 10.8974 14.9643C11.1164 15.2921 11.4277 15.5475 11.7919 15.6984C12.1561 15.8492 12.5568 15.8887 12.9434 15.8118C13.33 15.7349 13.6851 15.5451 13.9639 15.2664C14.2426 14.9876 14.4324 14.6325 14.5093 14.2459C14.5862 13.8593 14.5467 13.4586 14.3959 13.0944C14.245 12.7302 13.9896 12.4189 13.6618 12.2C13.3341 11.981 12.9488 11.8641 12.5546 11.8641H5.6623C5.52895 11.864 5.39983 11.8172 5.29746 11.7318C5.19509 11.6463 5.12596 11.5276 5.10212 11.3964L4.87649 10.1558H13.1333C13.5333 10.1557 13.9207 10.0152 14.2278 9.75886C14.5349 9.50248 14.7423 9.14645 14.8138 8.75283L15.6793 3.99379C15.694 3.91153 15.6904 3.82706 15.6687 3.74637C15.6471 3.66567 15.6079 3.59073 15.5541 3.52685ZM6.57553 13.8571C6.57553 14.026 6.52544 14.1912 6.43158 14.3316C6.33773 14.4721 6.20433 14.5816 6.04825 14.6462C5.89218 14.7109 5.72044 14.7278 5.55475 14.6948C5.38906 14.6619 5.23686 14.5805 5.11741 14.4611C4.99795 14.3416 4.9166 14.1894 4.88365 14.0237C4.85069 13.858 4.8676 13.6863 4.93225 13.5302C4.9969 13.3741 5.10638 13.2407 5.24684 13.1469C5.38731 13.053 5.55245 13.0029 5.72138 13.0029C5.94792 13.0029 6.16517 13.0929 6.32536 13.2531C6.48554 13.4133 6.57553 13.6306 6.57553 13.8571ZM13.4087 13.8571C13.4087 14.026 13.3586 14.1912 13.2648 14.3316C13.1709 14.4721 13.0375 14.5816 12.8814 14.6462C12.7254 14.7109 12.5536 14.7278 12.3879 14.6948C12.2223 14.6619 12.0701 14.5805 11.9506 14.4611C11.8312 14.3416 11.7498 14.1894 11.7168 14.0237C11.6839 13.858 11.7008 13.6863 11.7655 13.5302C11.8301 13.3741 11.9396 13.2407 12.08 13.1469C12.2205 13.053 12.3856 13.0029 12.5546 13.0029C12.7811 13.0029 12.9984 13.0929 13.1586 13.2531C13.3187 13.4133 13.4087 13.6306 13.4087 13.8571ZM13.6934 8.54925C13.6695 8.68082 13.6001 8.79978 13.4973 8.88529C13.3945 8.97079 13.2649 9.01738 13.1311 9.0169H4.66936L3.84154 4.46144H14.4344L13.6934 8.54925Z" fill="#210F59"/>'},"report-ecommerce_purchases-active":{viewBox:"0 0 16 16",content:'<path d="M15.5541 3.52685C15.5006 3.46289 15.4338 3.41144 15.3583 3.37614C15.2827 3.34084 15.2004 3.32256 15.117 3.32257H3.63441L3.20164 0.94305C3.1778 0.811844 3.10867 0.693165 3.0063 0.607704C2.90393 0.522244 2.77482 0.475422 2.64146 0.475403H0.881201C0.730178 0.475403 0.58534 0.535396 0.478551 0.642186C0.371761 0.748975 0.311768 0.893813 0.311768 1.04484C0.311768 1.19586 0.371761 1.3407 0.478551 1.44749C0.58534 1.55428 0.730178 1.61427 0.881201 1.61427H2.16243L3.98176 11.6C4.03536 11.8961 4.16623 12.1729 4.36115 12.4022C4.09213 12.6535 3.89795 12.9743 3.80016 13.3292C3.70236 13.6841 3.70477 14.0592 3.80711 14.4128C3.90945 14.7664 4.10773 15.0847 4.37996 15.3325C4.65219 15.5803 4.98773 15.7479 5.34937 15.8166C5.71102 15.8854 6.08463 15.8526 6.4288 15.722C6.77296 15.5914 7.07422 15.368 7.29917 15.0766C7.52412 14.7852 7.66397 14.4372 7.70322 14.0712C7.74246 13.7051 7.67957 13.3354 7.5215 13.0029H10.7545C10.6271 13.2696 10.5611 13.5615 10.5616 13.8571C10.5616 14.2513 10.6785 14.6366 10.8974 14.9643C11.1164 15.2921 11.4277 15.5475 11.7919 15.6984C12.1561 15.8492 12.5568 15.8887 12.9434 15.8118C13.33 15.7349 13.6851 15.5451 13.9639 15.2664C14.2426 14.9876 14.4324 14.6325 14.5093 14.2459C14.5862 13.8593 14.5467 13.4586 14.3959 13.0944C14.245 12.7302 13.9896 12.4189 13.6618 12.2C13.3341 11.981 12.9488 11.8641 12.5546 11.8641H5.6623C5.52895 11.864 5.39983 11.8172 5.29746 11.7318C5.19509 11.6463 5.12596 11.5276 5.10212 11.3964L4.87649 10.1558H13.1333C13.5333 10.1557 13.9207 10.0152 14.2278 9.75886C14.5349 9.50248 14.7423 9.14645 14.8138 8.75283L15.6793 3.99379C15.694 3.91153 15.6904 3.82706 15.6687 3.74637C15.6471 3.66567 15.6079 3.59073 15.5541 3.52685ZM6.57553 13.8571C6.57553 14.026 6.52544 14.1912 6.43158 14.3316C6.33773 14.4721 6.20433 14.5816 6.04825 14.6462C5.89218 14.7109 5.72044 14.7278 5.55475 14.6948C5.38906 14.6619 5.23686 14.5805 5.11741 14.4611C4.99795 14.3416 4.9166 14.1894 4.88365 14.0237C4.85069 13.858 4.8676 13.6863 4.93225 13.5302C4.9969 13.3741 5.10638 13.2407 5.24684 13.1469C5.38731 13.053 5.55245 13.0029 5.72138 13.0029C5.94792 13.0029 6.16517 13.0929 6.32536 13.2531C6.48554 13.4133 6.57553 13.6306 6.57553 13.8571ZM13.4087 13.8571C13.4087 14.026 13.3586 14.1912 13.2648 14.3316C13.1709 14.4721 13.0375 14.5816 12.8814 14.6462C12.7254 14.7109 12.5536 14.7278 12.3879 14.6948C12.2223 14.6619 12.0701 14.5805 11.9506 14.4611C11.8312 14.3416 11.7498 14.1894 11.7168 14.0237C11.6839 13.858 11.7008 13.6863 11.7655 13.5302C11.8301 13.3741 11.9396 13.2407 12.08 13.1469C12.2205 13.053 12.3856 13.0029 12.5546 13.0029C12.7811 13.0029 12.9984 13.0929 13.1586 13.2531C13.3187 13.4133 13.4087 13.6306 13.4087 13.8571ZM13.6934 8.54925C13.6695 8.68082 13.6001 8.79978 13.4973 8.88529C13.3945 8.97079 13.2649 9.01738 13.1311 9.0169H4.66936L3.84154 4.46144H14.4344L13.6934 8.54925Z" fill="#6527F5"/>'},"report-engagement_rate":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6074)"><path d="M7.95758 0.979736H9.11628C9.26431 0.979736 9.40627 1.03854 9.51094 1.14321C9.61561 1.24788 9.67442 1.38985 9.67442 1.53788C9.67442 1.6859 9.61561 1.82787 9.51094 1.93254C9.40627 2.03721 9.26431 2.09602 9.11628 2.09602H8C6.23033 2.09602 4.95926 2.0975 3.99181 2.22699C3.04 2.35499 2.46623 2.59909 2.04205 3.02253C1.6186 3.44671 1.37526 4.01974 1.24726 4.97155C1.11777 5.93899 1.11628 7.21006 1.11628 8.97974C1.11628 10.7494 1.11777 12.0205 1.24726 12.9879C1.37526 13.9397 1.61935 14.5135 2.04279 14.9377C2.46698 15.3611 3.04 15.6045 3.99181 15.7325C4.95926 15.862 6.23033 15.8635 8 15.8635C9.76967 15.8635 11.0407 15.862 12.0082 15.7325C12.96 15.6045 13.5338 15.3604 13.958 14.9369C14.3814 14.5128 14.6247 13.9397 14.7527 12.9879C14.8822 12.0205 14.8837 10.7494 14.8837 8.97974V7.86346C14.8837 7.71543 14.9425 7.57346 15.0472 7.46879C15.1519 7.36412 15.2938 7.30532 15.4419 7.30532C15.5899 7.30532 15.7319 7.36412 15.8365 7.46879C15.9412 7.57346 16 7.71543 16 7.86346V9.02215C16 10.7405 16 12.0867 15.8586 13.1375C15.7142 14.2121 15.4121 15.0605 14.7468 15.7265C14.0807 16.3926 13.2324 16.694 12.157 16.8383C11.107 16.9797 9.76074 16.9797 8.04242 16.9797H7.95758C6.23926 16.9797 4.89302 16.9797 3.84223 16.8383C2.76763 16.694 1.91926 16.3918 1.25321 15.7265C0.587163 15.0605 0.285767 14.2121 0.141395 13.1368C4.43569e-08 12.0867 0 10.7405 0 9.02215V8.93732C0 7.21899 4.43569e-08 5.87276 0.141395 4.82197C0.285767 3.74736 0.587907 2.89899 1.25321 2.23295C1.91926 1.5669 2.76763 1.2655 3.84298 1.12113C4.89302 0.979736 6.23926 0.979736 7.95758 0.979736Z" fill="#210F59"/><path d="M12.0781 7.06273C12.1918 7.1575 12.2631 7.29351 12.2765 7.44088C12.2899 7.58824 12.2443 7.73489 12.1496 7.8486L10.7885 9.48208C10.5444 9.77529 10.3241 10.0402 10.1187 10.2263C9.89394 10.4272 9.61264 10.608 9.24055 10.608C8.86845 10.608 8.58641 10.4279 8.36241 10.2255C8.15701 10.0395 7.93673 9.77529 7.6919 9.48134L7.47459 9.22087C7.1985 8.88971 7.02883 8.68804 6.88892 8.56227C6.85252 8.52696 6.81175 8.49645 6.76762 8.47148L6.76241 8.46925L6.75943 8.46776L6.75199 8.47148C6.70759 8.4964 6.66657 8.52691 6.62994 8.56227C6.49078 8.68878 6.3211 8.88971 6.04501 9.22087L4.70771 10.8253C4.61177 10.935 4.47675 11.0028 4.33149 11.0143C4.18623 11.0257 4.04226 10.9799 3.93032 10.8866C3.81839 10.7933 3.74736 10.66 3.73243 10.515C3.7175 10.3701 3.75984 10.2251 3.85041 10.1109L5.21152 8.47743C5.45562 8.18422 5.6759 7.91929 5.88129 7.73325C6.10603 7.53232 6.38734 7.35148 6.75943 7.35148C7.13152 7.35148 7.41357 7.53157 7.63757 7.73399C7.84296 7.92004 8.06324 8.18422 8.30808 8.47818L8.52538 8.73864C8.80148 9.0698 8.97115 9.27148 9.11106 9.39725C9.17506 9.45529 9.21376 9.47911 9.23236 9.48804L9.2398 9.49176L9.24352 9.49027L9.24873 9.48804C9.29287 9.46306 9.33363 9.43255 9.37003 9.39725C9.5092 9.27073 9.67887 9.0698 9.95496 8.73864L11.2923 7.13418C11.387 7.02053 11.523 6.94916 11.6704 6.93577C11.8178 6.92237 11.9644 6.96804 12.0781 7.06273Z" fill="#210F59"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.2094 0.979736C12.4693 0.979736 11.7594 1.27376 11.2361 1.79711C10.7127 2.32047 10.4187 3.03029 10.4187 3.77043C10.4187 4.51057 10.7127 5.2204 11.2361 5.74376C11.7594 6.26711 12.4693 6.56113 13.2094 6.56113C13.9495 6.56113 14.6594 6.26711 15.1827 5.74376C15.7061 5.2204 16.0001 4.51057 16.0001 3.77043C16.0001 3.03029 15.7061 2.32047 15.1827 1.79711C14.6594 1.27376 13.9495 0.979736 13.2094 0.979736ZM11.535 3.77043C11.535 3.32635 11.7114 2.90046 12.0254 2.58644C12.3394 2.27243 12.7653 2.09602 13.2094 2.09602C13.6535 2.09602 14.0794 2.27243 14.3934 2.58644C14.7074 2.90046 14.8838 3.32635 14.8838 3.77043C14.8838 4.21452 14.7074 4.64041 14.3934 4.95443C14.0794 5.26844 13.6535 5.44485 13.2094 5.44485C12.7653 5.44485 12.3394 5.26844 12.0254 4.95443C11.7114 4.64041 11.535 4.21452 11.535 3.77043Z" fill="#210F59"/></g><defs><clipPath id="clip0_65_6074"><rect width="16" height="16" fill="white" transform="translate(0 0.979736)"/></clipPath></defs>'},"report-engagement_rate-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6074)"><path d="M7.95758 0.979736H9.11628C9.26431 0.979736 9.40627 1.03854 9.51094 1.14321C9.61561 1.24788 9.67442 1.38985 9.67442 1.53788C9.67442 1.6859 9.61561 1.82787 9.51094 1.93254C9.40627 2.03721 9.26431 2.09602 9.11628 2.09602H8C6.23033 2.09602 4.95926 2.0975 3.99181 2.22699C3.04 2.35499 2.46623 2.59909 2.04205 3.02253C1.6186 3.44671 1.37526 4.01974 1.24726 4.97155C1.11777 5.93899 1.11628 7.21006 1.11628 8.97974C1.11628 10.7494 1.11777 12.0205 1.24726 12.9879C1.37526 13.9397 1.61935 14.5135 2.04279 14.9377C2.46698 15.3611 3.04 15.6045 3.99181 15.7325C4.95926 15.862 6.23033 15.8635 8 15.8635C9.76967 15.8635 11.0407 15.862 12.0082 15.7325C12.96 15.6045 13.5338 15.3604 13.958 14.9369C14.3814 14.5128 14.6247 13.9397 14.7527 12.9879C14.8822 12.0205 14.8837 10.7494 14.8837 8.97974V7.86346C14.8837 7.71543 14.9425 7.57346 15.0472 7.46879C15.1519 7.36412 15.2938 7.30532 15.4419 7.30532C15.5899 7.30532 15.7319 7.36412 15.8365 7.46879C15.9412 7.57346 16 7.71543 16 7.86346V9.02215C16 10.7405 16 12.0867 15.8586 13.1375C15.7142 14.2121 15.4121 15.0605 14.7468 15.7265C14.0807 16.3926 13.2324 16.694 12.157 16.8383C11.107 16.9797 9.76074 16.9797 8.04242 16.9797H7.95758C6.23926 16.9797 4.89302 16.9797 3.84223 16.8383C2.76763 16.694 1.91926 16.3918 1.25321 15.7265C0.587163 15.0605 0.285767 14.2121 0.141395 13.1368C4.43569e-08 12.0867 0 10.7405 0 9.02215V8.93732C0 7.21899 4.43569e-08 5.87276 0.141395 4.82197C0.285767 3.74736 0.587907 2.89899 1.25321 2.23295C1.91926 1.5669 2.76763 1.2655 3.84298 1.12113C4.89302 0.979736 6.23926 0.979736 7.95758 0.979736Z" fill="#6527F5"/><path d="M12.0781 7.06273C12.1918 7.1575 12.2631 7.29351 12.2765 7.44088C12.2899 7.58824 12.2443 7.73489 12.1496 7.8486L10.7885 9.48208C10.5444 9.77529 10.3241 10.0402 10.1187 10.2263C9.89394 10.4272 9.61264 10.608 9.24055 10.608C8.86845 10.608 8.58641 10.4279 8.36241 10.2255C8.15701 10.0395 7.93673 9.77529 7.6919 9.48134L7.47459 9.22087C7.1985 8.88971 7.02883 8.68804 6.88892 8.56227C6.85252 8.52696 6.81175 8.49645 6.76762 8.47148L6.76241 8.46925L6.75943 8.46776L6.75199 8.47148C6.70759 8.4964 6.66657 8.52691 6.62994 8.56227C6.49078 8.68878 6.3211 8.88971 6.04501 9.22087L4.70771 10.8253C4.61177 10.935 4.47675 11.0028 4.33149 11.0143C4.18623 11.0257 4.04226 10.9799 3.93032 10.8866C3.81839 10.7933 3.74736 10.66 3.73243 10.515C3.7175 10.3701 3.75984 10.2251 3.85041 10.1109L5.21152 8.47743C5.45562 8.18422 5.6759 7.91929 5.88129 7.73325C6.10603 7.53232 6.38734 7.35148 6.75943 7.35148C7.13152 7.35148 7.41357 7.53157 7.63757 7.73399C7.84296 7.92004 8.06324 8.18422 8.30808 8.47818L8.52538 8.73864C8.80148 9.0698 8.97115 9.27148 9.11106 9.39725C9.17506 9.45529 9.21376 9.47911 9.23236 9.48804L9.2398 9.49176L9.24352 9.49027L9.24873 9.48804C9.29287 9.46306 9.33363 9.43255 9.37003 9.39725C9.5092 9.27073 9.67887 9.0698 9.95496 8.73864L11.2923 7.13418C11.387 7.02053 11.523 6.94916 11.6704 6.93577C11.8178 6.92237 11.9644 6.96804 12.0781 7.06273Z" fill="#6527F5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.2094 0.979736C12.4693 0.979736 11.7594 1.27376 11.2361 1.79711C10.7127 2.32047 10.4187 3.03029 10.4187 3.77043C10.4187 4.51057 10.7127 5.2204 11.2361 5.74376C11.7594 6.26711 12.4693 6.56113 13.2094 6.56113C13.9495 6.56113 14.6594 6.26711 15.1827 5.74376C15.7061 5.2204 16.0001 4.51057 16.0001 3.77043C16.0001 3.03029 15.7061 2.32047 15.1827 1.79711C14.6594 1.27376 13.9495 0.979736 13.2094 0.979736ZM11.535 3.77043C11.535 3.32635 11.7114 2.90046 12.0254 2.58644C12.3394 2.27243 12.7653 2.09602 13.2094 2.09602C13.6535 2.09602 14.0794 2.27243 14.3934 2.58644C14.7074 2.90046 14.8838 3.32635 14.8838 3.77043C14.8838 4.21452 14.7074 4.64041 14.3934 4.95443C14.0794 5.26844 13.6535 5.44485 13.2094 5.44485C12.7653 5.44485 12.3394 5.26844 12.0254 4.95443C11.7114 4.64041 11.535 4.21452 11.535 3.77043Z" fill="#6527F5"/></g><defs><clipPath id="clip0_65_6074"><rect width="16" height="16" fill="white" transform="translate(0 0.979736)"/></clipPath></defs>'},"report-sessions_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6090)"><path d="M14.6873 16.0506C14.2638 7.72924 1.73674 7.72924 1.31323 16.0506M10.6751 4.45973C10.6751 5.16913 10.3933 5.84948 9.89165 6.35111C9.39003 6.85273 8.70968 7.13454 8.00027 7.13454C7.29087 7.13454 6.61052 6.85273 6.10889 6.35111C5.60727 5.84948 5.32546 5.16913 5.32546 4.45973C5.32546 3.75032 5.60727 3.06997 6.10889 2.56835C6.61052 2.06672 7.29087 1.78491 8.00027 1.78491C8.70968 1.78491 9.39003 2.06672 9.89165 2.56835C10.3933 3.06997 10.6751 3.75032 10.6751 4.45973Z" stroke="#210F59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M6.44702 14.0189L7.36894 15.0719C7.45364 15.169 7.60611 15.161 7.681 15.0576L9.55337 12.4657" stroke="#210F59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_65_6090"><rect width="16" height="16" fill="white" transform="translate(0 0.917725)"/></clipPath></defs>'},"report-sessions_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6090)"><path d="M14.6873 16.0506C14.2638 7.72924 1.73674 7.72924 1.31323 16.0506M10.6751 4.45973C10.6751 5.16913 10.3933 5.84948 9.89165 6.35111C9.39003 6.85273 8.70968 7.13454 8.00027 7.13454C7.29087 7.13454 6.61052 6.85273 6.10889 6.35111C5.60727 5.84948 5.32546 5.16913 5.32546 4.45973C5.32546 3.75032 5.60727 3.06997 6.10889 2.56835C6.61052 2.06672 7.29087 1.78491 8.00027 1.78491C8.70968 1.78491 9.39003 2.06672 9.89165 2.56835C10.3933 3.06997 10.6751 3.75032 10.6751 4.45973Z" stroke="#6527F5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M6.44702 14.0189L7.36894 15.0719C7.45364 15.169 7.60611 15.161 7.681 15.0576L9.55337 12.4657" stroke="#6527F5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_65_6090"><rect width="16" height="16" fill="white" transform="translate(0 0.917725)"/></clipPath></defs>'},"report-settings":{viewBox:"0 0 27 27",content:'<path d="M13.3923 7.61566C12.2095 7.61566 11.0532 7.96641 10.0697 8.62357C9.08617 9.28073 8.31963 10.2148 7.86697 11.3076C7.41431 12.4004 7.29588 13.6029 7.52664 14.763C7.7574 15.9231 8.327 16.9888 9.1634 17.8252C9.9998 18.6616 11.0654 19.2312 12.2256 19.4619C13.3857 19.6927 14.5882 19.5743 15.681 19.1216C16.7738 18.6689 17.7078 17.9024 18.365 16.9189C19.0222 15.9354 19.3729 14.7791 19.3729 13.5962C19.3713 12.0106 18.7406 10.4904 17.6194 9.36915C16.4982 8.24793 14.978 7.61731 13.3923 7.61566ZM13.3923 17.5833C12.6037 17.5833 11.8329 17.3495 11.1772 16.9114C10.5216 16.4733 10.0105 15.8506 9.70875 15.122C9.40698 14.3935 9.32802 13.5918 9.48186 12.8184C9.63571 12.045 10.0154 11.3346 10.573 10.777C11.1306 10.2194 11.8411 9.83964 12.6145 9.6858C13.3879 9.53196 14.1896 9.61091 14.9181 9.91268C15.6466 10.2145 16.2693 10.7255 16.7074 11.3812C17.1455 12.0368 17.3794 12.8077 17.3794 13.5962C17.3794 14.6537 16.9593 15.6678 16.2116 16.4155C15.4639 17.1632 14.4497 17.5833 13.3923 17.5833ZM24.3567 13.8654C24.3617 13.686 24.3617 13.5065 24.3567 13.3271L26.2157 11.0047C26.3132 10.8827 26.3806 10.7396 26.4127 10.5868C26.4447 10.434 26.4404 10.2758 26.4001 10.125C26.0954 8.97948 25.6395 7.87961 25.0445 6.85438C24.9666 6.72021 24.8584 6.60608 24.7287 6.52107C24.5989 6.43607 24.451 6.38253 24.2969 6.36472L21.3415 6.03579C21.2186 5.90621 21.094 5.78161 20.9677 5.662L20.6189 2.69911C20.6009 2.54488 20.5472 2.39697 20.462 2.26718C20.3767 2.13739 20.2624 2.0293 20.128 1.95154C19.1023 1.35764 18.0026 0.902224 16.8573 0.597185C16.7064 0.557062 16.5482 0.552958 16.3954 0.585205C16.2426 0.617451 16.0995 0.685146 15.9777 0.782833L13.6614 2.63183H13.1232L10.8007 0.776603C10.6788 0.67913 10.5357 0.611661 10.3829 0.579631C10.2301 0.547602 10.0719 0.551907 9.92108 0.592201C8.77573 0.897495 7.67594 1.35334 6.65044 1.9478C6.51628 2.02571 6.40215 2.13385 6.31714 2.26364C6.23213 2.39342 6.17859 2.54126 6.16078 2.69538L5.83185 5.65577C5.70227 5.77953 5.57768 5.90413 5.45806 6.02955L2.49518 6.3697C2.34095 6.38765 2.19304 6.44137 2.06324 6.5266C1.93345 6.61183 1.82537 6.72621 1.74761 6.86061C1.1537 7.88623 0.698291 8.98601 0.393252 10.1312C0.353128 10.2822 0.349024 10.4404 0.381271 10.5932C0.413518 10.746 0.481213 10.8891 0.578899 11.0109L2.4279 13.3271V13.8654L0.572669 16.1878C0.475196 16.3098 0.407727 16.4529 0.375698 16.6057C0.343668 16.7585 0.347974 16.9167 0.388268 17.0675C0.693002 18.213 1.14887 19.3129 1.74387 20.3381C1.82177 20.4723 1.92992 20.5864 2.0597 20.6714C2.18949 20.7564 2.33732 20.81 2.49144 20.8278L5.44685 21.1567C5.57062 21.2863 5.69521 21.4109 5.82064 21.5305L6.16577 24.4934C6.18371 24.6476 6.23744 24.7955 6.32267 24.9253C6.4079 25.0551 6.52227 25.1632 6.65667 25.241C7.68229 25.8349 8.78207 26.2903 9.92731 26.5953C10.0782 26.6354 10.2365 26.6395 10.3893 26.6073C10.542 26.575 10.6851 26.5074 10.807 26.4097L13.1232 24.5607C13.3026 24.5656 13.482 24.5656 13.6614 24.5607L15.9839 26.4196C16.1058 26.5171 16.249 26.5846 16.4018 26.6166C16.5546 26.6486 16.7127 26.6443 16.8636 26.604C18.0091 26.2993 19.109 25.8434 20.1342 25.2484C20.2684 25.1705 20.3825 25.0624 20.4675 24.9326C20.5525 24.8028 20.606 24.655 20.6238 24.5009L20.9528 21.5455C21.0824 21.4225 21.207 21.2979 21.3266 21.1717L24.2895 20.8228C24.4437 20.8049 24.5916 20.7511 24.7214 20.6659C24.8512 20.5807 24.9593 20.4663 25.037 20.3319C25.6309 19.3063 26.0863 18.2065 26.3914 17.0613C26.4315 16.9103 26.4356 16.7521 26.4034 16.5993C26.3711 16.4465 26.3034 16.3034 26.2057 16.1816L24.3567 13.8654ZM22.3507 13.0555C22.3719 13.4157 22.3719 13.7768 22.3507 14.137C22.3359 14.3836 22.4132 14.6269 22.5675 14.8198L24.3356 17.0289C24.1327 17.6736 23.8729 18.299 23.5593 18.8978L20.7435 19.2168C20.4982 19.244 20.2718 19.3612 20.108 19.5457C19.8682 19.8155 19.6128 20.0709 19.343 20.3107C19.1585 20.4745 19.0413 20.7009 19.0141 20.9461L18.7013 23.7595C18.1027 24.0733 17.4772 24.333 16.8324 24.5357L14.6221 22.7677C14.4452 22.6264 14.2255 22.5495 13.9991 22.5497H13.9393C13.5791 22.5709 13.218 22.5709 12.8578 22.5497C12.6112 22.5348 12.3679 22.6121 12.175 22.7665L9.9597 24.5357C9.31497 24.3329 8.68953 24.0731 8.09077 23.7595L7.77181 20.9474C7.74459 20.7022 7.62738 20.4758 7.44287 20.312C7.17311 20.0721 6.91767 19.8167 6.67786 19.5469C6.51405 19.3624 6.28764 19.2452 6.04242 19.218L3.22905 18.904C2.91529 18.3053 2.65552 17.6799 2.45282 17.0351L4.22083 14.8248C4.37521 14.6319 4.45247 14.3886 4.43763 14.142C4.41647 13.7818 4.41647 13.4207 4.43763 13.0605C4.45247 12.8139 4.37521 12.5706 4.22083 12.3777L2.45282 10.1636C2.65571 9.5189 2.91547 8.89347 3.22905 8.2947L6.04117 7.97574C6.2864 7.94852 6.51281 7.83132 6.67661 7.64681C6.91642 7.37705 7.17187 7.1216 7.44163 6.88179C7.62687 6.71788 7.74456 6.49095 7.77181 6.24511L8.08454 3.43298C8.68322 3.11923 9.30867 2.85946 9.95348 2.65675L12.1638 4.42476C12.3567 4.57914 12.6 4.6564 12.8466 4.64156C13.2068 4.6204 13.5679 4.6204 13.9281 4.64156C14.1747 4.6564 14.418 4.57914 14.6109 4.42476L16.8249 2.65675C17.4697 2.85964 18.0951 3.11941 18.6939 3.43298L19.0128 6.24511C19.04 6.49033 19.1572 6.71674 19.3418 6.88054C19.6115 7.12036 19.867 7.3758 20.1068 7.64556C20.2706 7.83007 20.497 7.94727 20.7422 7.97449L23.5556 8.28723C23.8693 8.88591 24.1291 9.51136 24.3318 10.1562L22.5638 12.3665C22.4079 12.561 22.3306 12.8068 22.347 13.0555H22.3507Z" fill="#393F4C"/>'},"report-settings-close":{viewBox:"0 0 16 16",content:'<path d="M14.3651 15.7956L8.0051 9.42563L1.6451 15.7956L0.225098 14.3756L6.5951 8.01563L0.225098 1.65563L1.6451 0.235626L8.0051 6.60563L14.3651 0.245626L15.7751 1.65563L9.4151 8.01563L15.7751 14.3756L14.3651 15.7956Z" fill="black" />'},"report-forms-completions":{viewBox:"0 0 17 17",content:'<g clip-path="url(#clip0_20_2615)"><path d="M9.85661 6.11994H3.63439C3.51651 6.11994 3.40347 6.07312 3.32012 5.98977C3.23677 5.90642 3.18994 5.79337 3.18994 5.6755V3.89772C3.18994 3.77985 3.23677 3.6668 3.32012 3.58345C3.40347 3.5001 3.51651 3.45328 3.63439 3.45328H9.85661C9.97448 3.45328 10.0875 3.5001 10.1709 3.58345C10.2542 3.6668 10.3011 3.77985 10.3011 3.89772V5.6755C10.3011 5.79337 10.2542 5.90642 10.1709 5.98977C10.0875 6.07312 9.97448 6.11994 9.85661 6.11994ZM4.07883 5.23106H9.41216V4.3155H4.07883V5.23106Z" fill="#210F59"/><path d="M9.85661 7.0444H3.63439C3.51651 7.0444 3.40347 7.09123 3.32012 7.17458C3.23677 7.25793 3.18994 7.37097 3.18994 7.48885V9.23107C3.18994 9.34894 3.23677 9.46199 3.32012 9.54534C3.40347 9.62869 3.51651 9.67551 3.63439 9.67551H8.68327L10.3011 8.03107V7.48885C10.3011 7.37097 10.2542 7.25793 10.1709 7.17458C10.0875 7.09123 9.97448 7.0444 9.85661 7.0444ZM9.41216 8.78663H4.07883V7.89774H9.41216V8.78663Z" fill="#210F59"/><path d="M5.43878 14.7911V14.7644L5.581 14.1466H2.301V2.5644H11.1899V7.11995L12.0788 6.27995V2.11995C12.0788 2.00208 12.032 1.88903 11.9486 1.80568C11.8653 1.72233 11.7522 1.67551 11.6343 1.67551H1.85655C1.73868 1.67551 1.62563 1.72233 1.54228 1.80568C1.45893 1.88903 1.41211 2.00208 1.41211 2.11995V14.5644C1.41211 14.6823 1.45893 14.7953 1.54228 14.8787C1.62563 14.962 1.73868 15.0088 1.85655 15.0088H5.41211C5.41571 14.9357 5.42462 14.8629 5.43878 14.7911Z" fill="#210F59"/><path d="M10.3013 9.30661L9.95459 9.65772C10.0406 9.63999 10.1194 9.59718 10.1811 9.53469C10.2428 9.4722 10.2846 9.39284 10.3013 9.30661Z" fill="#210F59"/><path d="M3.18994 12.7599C3.18994 12.8778 3.23677 12.9909 3.32012 13.0742C3.40347 13.1576 3.51651 13.2044 3.63439 13.2044H5.7855L5.91883 12.6266L5.97661 12.3822V12.3599H4.07883V11.4533H6.89661L7.7855 10.5644H3.63439C3.51651 10.5644 3.40347 10.6112 3.32012 10.6946C3.23677 10.7779 3.18994 10.891 3.18994 11.0088V12.7599Z" fill="#210F59"/><path d="M15.4077 8.19551L13.9099 6.69773C13.8435 6.63108 13.7645 6.5782 13.6775 6.54211C13.5906 6.50603 13.4974 6.48746 13.4033 6.48746C13.3091 6.48746 13.2159 6.50603 13.129 6.54211C13.042 6.5782 12.9631 6.63108 12.8966 6.69773L6.80327 12.8266L6.30104 14.9644C6.28224 15.0566 6.28182 15.1517 6.2998 15.244C6.31778 15.3364 6.35381 15.4244 6.40583 15.5028C6.45785 15.5813 6.52484 15.6487 6.60295 15.7012C6.68106 15.7537 6.76877 15.7903 6.86104 15.8088C6.90686 15.8133 6.953 15.8133 6.99882 15.8088C7.05331 15.8173 7.10878 15.8173 7.16327 15.8088L9.31882 15.3333L15.4077 9.23107C15.4742 9.16497 15.527 9.08638 15.563 8.99981C15.5991 8.91323 15.6176 8.82039 15.6176 8.72662C15.6176 8.63285 15.5991 8.54001 15.563 8.45344C15.527 8.36686 15.4742 8.28827 15.4077 8.22218V8.19551ZM8.86549 14.5244L7.23882 14.8844L7.63438 13.2711L12.2033 8.65329L13.4566 9.90662L8.86549 14.5244ZM13.9588 9.4044L12.7055 8.15107L13.4122 7.45329L14.6744 8.71551L13.9588 9.4044Z" fill="#210F59"/></g><defs><clipPath id="clip0_20_2615"><rect width="16" height="16" fill="white" transform="translate(0.523438 0.786621)"/></clipPath></defs>'},"report-forms-impressions":{viewBox:"0 0 17 17",content:'<path d="M3.15057 7.69571C3.15057 7.2135 3.34213 6.75104 3.6831 6.41006C4.02408 6.06909 4.48654 5.87753 4.96875 5.87753C5.45096 5.87753 5.91342 6.06909 6.2544 6.41006C6.59537 6.75104 6.78693 7.2135 6.78693 7.69571C6.78693 8.17792 6.59537 8.64039 6.2544 8.98136C5.91342 9.32234 5.45096 9.51389 4.96875 9.51389C4.48654 9.51389 4.02408 9.32234 3.6831 8.98136C3.34213 8.64039 3.15057 8.17792 3.15057 7.69571ZM4.96875 6.96844C4.77587 6.96844 4.59088 7.04506 4.45449 7.18145C4.3181 7.31784 4.24148 7.50283 4.24148 7.69571C4.24148 7.8886 4.3181 8.07358 4.45449 8.20997C4.59088 8.34636 4.77587 8.42299 4.96875 8.42299C5.16163 8.42299 5.34662 8.34636 5.48301 8.20997C5.6194 8.07358 5.69602 7.8886 5.69602 7.69571C5.69602 7.50283 5.6194 7.31784 5.48301 7.18145C5.34662 7.04506 5.16163 6.96844 4.96875 6.96844ZM4.96875 10.9684C4.48654 10.9684 4.02408 11.16 3.6831 11.501C3.34213 11.8419 3.15057 12.3044 3.15057 12.7866C3.15057 13.2688 3.34213 13.7313 3.6831 14.0723C4.02408 14.4132 4.48654 14.6048 4.96875 14.6048C5.45096 14.6048 5.91342 14.4132 6.2544 14.0723C6.59537 13.7313 6.78693 13.2688 6.78693 12.7866C6.78693 12.3044 6.59537 11.8419 6.2544 11.501C5.91342 11.16 5.45096 10.9684 4.96875 10.9684ZM4.24148 12.7866C4.24148 12.5937 4.3181 12.4088 4.45449 12.2724C4.59088 12.136 4.77587 12.0593 4.96875 12.0593C5.16163 12.0593 5.34662 12.136 5.48301 12.2724C5.6194 12.4088 5.69602 12.5937 5.69602 12.7866C5.69602 12.9795 5.6194 13.1645 5.48301 13.3009C5.34662 13.4373 5.16163 13.5139 4.96875 13.5139C4.77587 13.5139 4.59088 13.4373 4.45449 13.3009C4.3181 13.1645 4.24148 12.9795 4.24148 12.7866ZM8.24148 7.51389C8.24148 7.36923 8.29894 7.23049 8.40124 7.1282C8.50353 7.02591 8.64227 6.96844 8.78693 6.96844H14.2415C14.3861 6.96844 14.5249 7.02591 14.6272 7.1282C14.7295 7.23049 14.7869 7.36923 14.7869 7.51389C14.7869 7.65856 14.7295 7.7973 14.6272 7.89959C14.5249 8.00188 14.3861 8.05935 14.2415 8.05935H8.78693C8.64227 8.05935 8.50353 8.00188 8.40124 7.89959C8.29894 7.7973 8.24148 7.65856 8.24148 7.51389ZM8.78693 12.0593C8.64227 12.0593 8.50353 12.1168 8.40124 12.2191C8.29894 12.3214 8.24148 12.4601 8.24148 12.6048C8.24148 12.7495 8.29894 12.8882 8.40124 12.9905C8.50353 13.0928 8.64227 13.1503 8.78693 13.1503H14.2415C14.3861 13.1503 14.5249 13.0928 14.6272 12.9905C14.7295 12.8882 14.7869 12.7495 14.7869 12.6048C14.7869 12.4601 14.7295 12.3214 14.6272 12.2191C14.5249 12.1168 14.3861 12.0593 14.2415 12.0593H8.78693ZM3.15057 3.87753C3.15057 3.73287 3.20804 3.59413 3.31033 3.49184C3.41262 3.38954 3.55136 3.33208 3.69602 3.33208H14.2415C14.3861 3.33208 14.5249 3.38954 14.6272 3.49184C14.7295 3.59413 14.7869 3.73287 14.7869 3.87753C14.7869 4.02219 14.7295 4.16093 14.6272 4.26323C14.5249 4.36552 14.3861 4.42298 14.2415 4.42298H3.69602C3.55136 4.42298 3.41262 4.36552 3.31033 4.26323C3.20804 4.16093 3.15057 4.02219 3.15057 3.87753ZM3.69602 0.786621C2.97271 0.786621 2.27901 1.07396 1.76755 1.58542C1.25609 2.09688 0.96875 2.79058 0.96875 3.51389V14.0593C0.96875 14.7827 1.25609 15.4764 1.76755 15.9878C2.27901 16.4993 2.97271 16.7866 3.69602 16.7866H14.2415C14.9648 16.7866 15.6585 16.4993 16.17 15.9878C16.6814 15.4764 16.9688 14.7827 16.9688 14.0593V3.51389C16.9688 2.79058 16.6814 2.09688 16.17 1.58542C15.6585 1.07396 14.9648 0.786621 14.2415 0.786621H3.69602ZM2.05966 3.51389C2.05966 3.0799 2.23206 2.66369 2.53894 2.35681C2.84582 2.04993 3.26203 1.87753 3.69602 1.87753H14.2415C14.6755 1.87753 15.0917 2.04993 15.3986 2.35681C15.7054 2.66369 15.8778 3.0799 15.8778 3.51389V14.0593C15.8778 14.4933 15.7054 14.9096 15.3986 15.2164C15.0917 15.5233 14.6755 15.6957 14.2415 15.6957H3.69602C3.26203 15.6957 2.84582 15.5233 2.53894 15.2164C2.23206 14.9096 2.05966 14.4933 2.05966 14.0593V3.51389Z" fill="#210F59"/>'}},{__:t,sprintf:S2}=wp.i18n,O={install(s){const e=this;s.prototype.$miOverviewTooltips=function(i){if(!i.title)return document.querySelectorAll(".monsterinsights-line-chart-tooltip").forEach(function(H){H.style.opacity=0}),!1;let r=i.title[0],n=i.title[1],d=parseInt(i.title[2]),c=i.title[3],g=i.title[4]?i.title[4]:i.title[2],m=i.title[5]?i.title[5]:[],C=document.getElementById("monsterinsights-chartjs-line-"+g+"-tooltip");if(C===null&&(C=document.createElement("div"),document.body.appendChild(C),C.setAttribute("id","monsterinsights-chartjs-line-"+g+"-tooltip"),C.classList.add("monsterinsights-line-chart-tooltip")),!i.opacity){C.style.opacity=0;return}C.classList.remove("above"),C.classList.remove("below"),C.classList.remove("no-transform"),i.yAlign?C.classList.add(i.yAlign):C.classList.add("no-transform");let u="";d&&(d===0?u+="0%":d>0?u+='<span class="monsterinsights-green"><span class="monsterinsights-arrow monsterinsights-up"></span>&nbsp;'+d+"%</span>":u+='<span class="monsterinsights-red"><span class="monsterinsights-arrow monsterinsights-down"></span>&nbsp;'+Math.abs(d)+"%</span>");let h='<div class="monsterinsights-reports-overview-datagraph-tooltip-container monsterinsights-reports-tooltip">';h+='<div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+n+"</div>",h+='<div class="monsterinsights-reports-overview-datagraph-tooltip-trend">'+u+"</div>",c&&(h+='<div class="monsterinsights-reports-overview-datagraph-tooltip-descriptor">'+c+"</div>"),h+='<div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+r+"</div>",m&&(h+="<hr>",m.forEach(y=>{y.title&&(h+=y.title+"<br>")})),h+="</div>",C.innerHTML=h;const L=this._chart.canvas.getBoundingClientRect();C.style.opacity="1",C.style.left=L.left+window.pageXOffset+i.x+"px",C.style.top=L.top+window.pageYOffset+i.y+"px",C.style.fontFamily="Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif;",C.style.fontSize=i.fontSize,C.style.fontStyle=i._fontStyle,C.style.padding=i.yPadding+"px "+i.xPadding+"px",C.style.zIndex=99999,C.style.pointerEvents="none"},s.prototype.$miPieTooltips=function(i){if(!i.title)return document.querySelectorAll(".monsterinsights-pie-chart-tooltip").forEach(function(C){C.style.opacity=0}),!1;let r=i.title[0],n=i.title[1],d=i.title[2],c=document.getElementById("monsterinsights-chartjs-pie-"+d+"-tooltip");c===null&&(c=document.createElement("div"),document.body.appendChild(c),c.setAttribute("id","monsterinsights-chartjs-pie-"+d+"-tooltip")),c.classList.remove("above"),c.classList.remove("below"),c.classList.remove("no-transform"),i.yAlign?c.classList.add(i.yAlign):c.classList.add("no-transform");let g='<div class="monsterinsights-reports-overview-datagraph-tooltip-container monsterinsights-reports-doughnut-tooltip">';g+='<div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+n+"%</div>",g+='<div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+r+"</div>",g+="</div>",c.innerHTML=g,c.style.opacity=1,c.style.padding=i.yPadding+"px "+i.xPadding+"px",c.style.zIndex="99999"},s.prototype.$mi_loading_toast=function(i,r=!0){let n="monsterinsights-swal monsterinsights-swal-loading";(window.scrollY>0||r)&&(n+=" monsterinsights-swal-full-height"),s.prototype.$swal({customClass:{container:n},icon:"info",title:i||t("Loading new report data","google-analytics-for-wordpress"),html:t("Please wait...","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,loaderHtml:e.getCustomLoaderHtml(),didOpen:function(){s.prototype.$swal.showLoading()}})},s.prototype.$mi_success_toast=function(i){let{toast:r=!0,position:n="top-end",showConfirmButton:d=!1,icon:c="success",timer:g=3e3,customClass:m="mi-success",showCloseButton:C=!0,title:u=t("Settings Updated","google-analytics-for-wordpress"),showCancelButton:h=!1,confirmButtonText:L="",cancelButtonText:y="",text:H=""}=i;return s.prototype.$swal({customClass:{container:"monsterinsights-swal "+(m||"")},toast:r,position:n,showConfirmButton:d,icon:c,showCloseButton:C,title:u,timer:g,showCancelButton:h,confirmButtonText:L,cancelButtonText:y,text:H,showClass:{popup:""},hideClass:{popup:""}})},s.prototype.$mi_error_toast=function(i){let r=t("Continue %s","google-analytics-for-wordpress"),{icon:n="error",customContainerClass:d="monsterinsights-swal monsterinsights-swal-succcess",allowOutsideClick:c=!1,allowEscapeKey:g=!1,allowEnterKey:m=!1,title:C=t("Error","google-analytics-for-wordpress"),html:u=t("Please try again.","google-analytics-for-wordpress"),confirmButtonText:h=S2(r,'<i class="monstericon-long-arrow-right-light"></i>'),showCancelButton:L=!1,cancelButtonText:y="Cancel",footer:H=!1}=i;return s.prototype.$swal({icon:n,customClass:{container:d},allowOutsideClick:c,allowEscapeKey:g,allowEnterKey:m,title:C,html:'<div class="monsterinsights-swal-icons"><i class="monstericon-exclamation-em-solid"></i><i class="monstericon-exclamation-em-solid"></i><i class="monstericon-exclamation-em-solid"></i><i class="monstericon-exclamation-em-solid"></i></div><p>'+u+"</p>",footer:H,showCancelButton:L,cancelButtonText:y,confirmButtonText:h,didOpen:function(){s.prototype.$swal.hideLoading()}})},s.prototype.$mi_get_upsell_content=function(i){let r={};const n={overview:{mainheading:t("Publishers Report","google-analytics-for-wordpress"),title:t("Improve Your Conversion Rate With Insights Into Which Content Works Best.","google-analytics-for-wordpress"),subtitle:t("Stop guessing about what content your visitors are interested in. MonsterInsights Publisher Report shows you exactly which content gets the most visits, so you can analyze and optimize it for higher conversions.","google-analytics-for-wordpress"),features:[t("Top Landing Pages","google-analytics-for-wordpress"),t("Top Affilliate Links","google-analytics-for-wordpress"),t("Top Exit Pages","google-analytics-for-wordpress"),t("Top Download Links","google-analytics-for-wordpress"),t("Top Outbound Links","google-analytics-for-wordpress"),t("Scroll Depth","google-analytics-for-wordpress")]},publisher:{mainheading:t("Publishers Report","google-analytics-for-wordpress"),title:t("Improve Your Conversion Rate With Insights Into Which Content Works Best.","google-analytics-for-wordpress"),features:[t("Top Landing Pages","google-analytics-for-wordpress"),t("Top Affilliate Links","google-analytics-for-wordpress"),t("Top Exit Pages","google-analytics-for-wordpress"),t("Top Download Links","google-analytics-for-wordpress"),t("Top Outbound Links","google-analytics-for-wordpress"),t("Scroll Depth","google-analytics-for-wordpress")]},countries:{mainheading:t("Country / Region Report","google-analytics-for-wordpress"),title:t("View the most popular countries and regions on your website","google-analytics-for-wordpress"),features:[t("Top Page Views","google-analytics-for-wordpress"),t("Top Countries","google-analytics-for-wordpress"),t("Top Regions","google-analytics-for-wordpress"),t("Bounce Rates","google-analytics-for-wordpress"),t("Engaged Sessions","google-analytics-for-wordpress"),t("Purchases","google-analytics-for-wordpress")]},engagement_pages:{mainheading:t("Pages Report","google-analytics-for-wordpress"),title:t("View the most popular pages on your website.","google-analytics-for-wordpress"),features:[t("Page Name","google-analytics-for-wordpress"),t("New Sessions","google-analytics-for-wordpress"),t("Sessions","google-analytics-for-wordpress"),t("Bounce Rate","google-analytics-for-wordpress"),t("Engaged Sessions","google-analytics-for-wordpress"),t("Exits","google-analytics-for-wordpress")]},ecommerce:{mainheading:t("eCommerce Report","google-analytics-for-wordpress"),title:t("Increase Sales and Make More Money With Enhanced eCommerce Insights.","google-analytics-for-wordpress"),features:[t("10+ eCommerce Integrations","google-analytics-for-wordpress"),t("Average Order Value","google-analytics-for-wordpress"),t("Total Revenue","google-analytics-for-wordpress"),t("Sessions to Purchase","google-analytics-for-wordpress"),t("Top Conversion Sources","google-analytics-for-wordpress"),t("Top Products","google-analytics-for-wordpress"),t("Number of Transactions","google-analytics-for-wordpress"),t("Time to Purchase","google-analytics-for-wordpress")]},ecommerce_funnel:{mainheading:t("eCommerce Funnel Report","google-analytics-for-wordpress"),title:t("Visually see how customers move through your eCommerce website to make a purchase.","google-analytics-for-wordpress"),features:[t("Step by Step Funnel View","google-analytics-for-wordpress"),t("Conversion Rates","google-analytics-for-wordpress"),t("Breakdowns by Devices","google-analytics-for-wordpress"),t("Abandonment Rates","google-analytics-for-wordpress"),t("Breakdowns by Channel","google-analytics-for-wordpress"),t("Date Picker","google-analytics-for-wordpress")]},ecommerce_coupons:{mainheading:t("Coupon Report","google-analytics-for-wordpress"),title:t("Learn  which coupons are generating the most sales for your store.","google-analytics-for-wordpress"),features:[t("Coupons","google-analytics-for-wordpress"),t("Average Order Value","google-analytics-for-wordpress"),t("Total Revenue","google-analytics-for-wordpress"),t("Sessions to Purchase","google-analytics-for-wordpress"),t("Top Conversion Sources","google-analytics-for-wordpress"),t("Top Products","google-analytics-for-wordpress"),t("Number of Transactions","google-analytics-for-wordpress"),t("Time to Purchase","google-analytics-for-wordpress")]},cart_abandonment:{mainheading:t("Cart Abandonment Report","google-analytics-for-wordpress"),title:t("See which products are left the most so that that you can optimize and increase your sales.","google-analytics-for-wordpress"),features:[t("Products Abandoned","google-analytics-for-wordpress"),t("Revenue Abandoned","google-analytics-for-wordpress"),t("Quantity Abandoned","google-analytics-for-wordpress"),t("Checkout Abandonment","google-analytics-for-wordpress"),t("Cart Abandonment","google-analytics-for-wordpress"),t("Day-by-day Breakdowns","google-analytics-for-wordpress")]},dimensions:{mainheading:t("Dimensions Report","google-analytics-for-wordpress"),title:t("Increase Engagement and Unlock New Insights About Your Site.","google-analytics-for-wordpress"),features:[t("Author Tracking","google-analytics-for-wordpress"),t("User ID Tracking","google-analytics-for-wordpress"),t("Post Types","google-analytics-for-wordpress"),t("Tag Tracking","google-analytics-for-wordpress"),t("Categories","google-analytics-for-wordpress"),t("SEO Scores","google-analytics-for-wordpress"),t("Publish Times","google-analytics-for-wordpress"),t("Focus Keywords","google-analytics-for-wordpress")]},forms:{mainheading:t("Forms Report","google-analytics-for-wordpress"),title:t("Track Every Type of Web Form and Gain Visibility Into Your Customer Journey.","google-analytics-for-wordpress"),columns:1,features:[t("Conversion Counts","google-analytics-for-wordpress"),t("Impression Counts","google-analytics-for-wordpress"),t("Conversion Rates","google-analytics-for-wordpress")]},queries:{mainheading:t("Search Console Report","google-analytics-for-wordpress"),title:t("See Exactly How Visitors Find Your Website From Google.","google-analytics-for-wordpress"),columns:1,features:[t("Top Google Search Terms","google-analytics-for-wordpress"),t("Number of Clicks","google-analytics-for-wordpress"),t("Click-through Ratio","google-analytics-for-wordpress"),t("Average Results Position","google-analytics-for-wordpress")]},realtime:{mainheading:t("Realtime Report","google-analytics-for-wordpress"),title:t("See Who And What is Happening on Your Website in Realtime.","google-analytics-for-wordpress"),features:[t("Top Page Views","google-analytics-for-wordpress"),t("Current Active Users","google-analytics-for-wordpress"),t("Top Referral Sources","google-analytics-for-wordpress"),t("Pageviews Per Minute","google-analytics-for-wordpress"),t("Top Countries","google-analytics-for-wordpress"),t("Top Cities","google-analytics-for-wordpress")]},sitespeed:{mainheading:t("Site Speed Report","google-analytics-for-wordpress"),title:t("Improve Your User Experience and Improve Search Engine Rankings.","google-analytics-for-wordpress"),features:[t("Overall Site Speed Score","google-analytics-for-wordpress"),t("Server Response Times","google-analytics-for-wordpress"),t("Mobile and Desktop Scores","google-analytics-for-wordpress"),t("First Contentful Paint","google-analytics-for-wordpress"),t("Automatic Recommendations","google-analytics-for-wordpress"),t("Total Blocking Time","google-analytics-for-wordpress"),t("On-Demand Audits","google-analytics-for-wordpress"),t("Time to Interactive","google-analytics-for-wordpress")]},traffic:{mainheading:t("Traffic Report","google-analytics-for-wordpress"),title:t("Learn how visitors arrive to your website and which are the most engaged or profitable.","google-analytics-for-wordpress"),features:[t("Channel Breakdowns","google-analytics-for-wordpress"),t("Session Counts","google-analytics-for-wordpress"),t("Pages/Session","google-analytics-for-wordpress"),t("Conversion Rate","google-analytics-for-wordpress"),t("Revenue","google-analytics-for-wordpress"),t("Engaged Sessions","google-analytics-for-wordpress")]},traffic_landing_pages:{mainheading:t("Landing Page Report","google-analytics-for-wordpress"),title:t("Find out which pages are making your first impression.","google-analytics-for-wordpress"),features:[t("Page Name","google-analytics-for-wordpress"),t("Pages/Session","google-analytics-for-wordpress"),t("Sessions","google-analytics-for-wordpress"),t("Purchases","google-analytics-for-wordpress"),t("Session Counts","google-analytics-for-wordpress"),t("Conversion Rates","google-analytics-for-wordpress")]},traffic_technology:{mainheading:t("Technology Report","google-analytics-for-wordpress"),title:t("Optimize your website for your top devices and browsers.","google-analytics-for-wordpress"),features:[t("Browser Names","google-analytics-for-wordpress"),t("Device Types","google-analytics-for-wordpress"),t("Export to PDF","google-analytics-for-wordpress"),t("Date Filtering","google-analytics-for-wordpress")]},traffic_campaign:{mainheading:t("Campaigns Report","google-analytics-for-wordpress"),title:t("Measure how effective your marketing campaigns are performing.","google-analytics-for-wordpress"),features:[t("Campaign Names","google-analytics-for-wordpress"),t("Easy Filtering","google-analytics-for-wordpress"),t("Pages / Session","google-analytics-for-wordpress"),t("Purchases","google-analytics-for-wordpress"),t("eCommerce Conversion Rates","google-analytics-for-wordpress"),t("Revenue","google-analytics-for-wordpress")]},traffic_source_medium:{mainheading:t("Source and Medium Report","google-analytics-for-wordpress"),title:t("Uncover which traffic sources are creating engagement and sales from your website.","google-analytics-for-wordpress"),features:[t("Source Name","google-analytics-for-wordpress"),t("Pages / Sessions","google-analytics-for-wordpress"),t("Session Counts","google-analytics-for-wordpress"),t("Purchases","google-analytics-for-wordpress"),t("Easy Filtering","google-analytics-for-wordpress"),t("Conversion Rates","google-analytics-for-wordpress")]},traffic_social:{mainheading:t("Social Media Report","google-analytics-for-wordpress"),title:t("See Which Social Media Networks are Making You Money.","google-analytics-for-wordpress"),features:[t("Social Networks","google-analytics-for-wordpress"),t("Sessions","google-analytics-for-wordpress"),t("Bounce Rate","google-analytics-for-wordpress"),t("Purchases","google-analytics-for-wordpress"),t("Revenue","google-analytics-for-wordpress"),t("Conversion Rate","google-analytics-for-wordpress")]},media:{mainheading:t("Media Report","google-analytics-for-wordpress"),title:t("Easily See Which Videos Are Most Popular.","google-analytics-for-wordpress"),features:[t("Videos Plays, Average Duration, and Completions","google-analytics-for-wordpress"),t("Works with YouTube, Vimeo, and HTML 5 Videos","google-analytics-for-wordpress"),t("Compare stats over time","google-analytics-for-wordpress")]},exceptions:{mainheading:t("Exceptions Report","google-analytics-for-wordpress"),title:t("Be Notified About Important Website Events","google-analytics-for-wordpress"),features:[t("Traffic Changes","google-analytics-for-wordpress"),t("Conversion Rate Changes","google-analytics-for-wordpress"),t("Revenue Changes","google-analytics-for-wordpress"),t("Landing Page Changes","google-analytics-for-wordpress"),t("Engagement Rates","google-analytics-for-wordpress"),t("Email Notifications","google-analytics-for-wordpress"),t("Campaign Alerts","google-analytics-for-wordpress"),t("Site Notes Integration","google-analytics-for-wordpress")]},ai_insights:{mainheading:t("AI Insights","google-analytics-for-wordpress"),title:t("Get Actionable Insights with the Power of AI","google-analytics-for-wordpress"),features:[t("On Demand Insights","google-analytics-for-wordpress"),t("Traffic Insights","google-analytics-for-wordpress"),t("Visitor Insights","google-analytics-for-wordpress"),t("Campaign Insights","google-analytics-for-wordpress"),t("Store Insights","google-analytics-for-wordpress"),t("Form Insights","google-analytics-for-wordpress")]},ai_insights_chat:{mainheading:t("Conversations AI","google-analytics-for-wordpress"),title:t("Chat with your website’s analytics, no experience needed.","google-analytics-for-wordpress"),features:[t("Get insights about your website by simply asking","google-analytics-for-wordpress"),t("Ask for specific metrics or trends over time","google-analytics-for-wordpress"),t("Understand your marketing ROI","google-analytics-for-wordpress"),t("Create charts and graphs for easy analysis","google-analytics-for-wordpress"),t("Pin and save your conversations for later use","google-analytics-for-wordpress")]}};return n[i]&&(r=n[i]),r},s.prototype.$mi_intervals=function(){return I},s.directive("icon",{bind(i,{value:r}){o(i,r)},update(i,{value:r,oldValue:n}){r!==n&&o(i,r)}});function o(i,r){const n=D2[r];if(n===void 0){i.innerHTML="";return}i.classList.contains("icon--tab")||i.classList.add("icon--tab"),i.innerHTML=`<svg viewBox="${n.viewBox}" fill="none">${n.content}</svg>`}},getCustomLoaderHtml(){return'<div class="monsterinsights-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>'}},{__:S}=wp.i18n,U={install(s,{store:e}){s.prototype.$mi_loading_toast=function(){},s.prototype.$mi_error_toast=function(o){let{icon:i="error",customContainerClass:r="monsterinsights-swal",allowOutsideClick:n=!1,allowEscapeKey:d=!1,allowEnterKey:c=!1,title:g=S("Error","google-analytics-for-wordpress"),html:m=S("Please try again.","google-analytics-for-wordpress"),footer:C=!1,report:u="general"}=o;o={icon:i,customClass:{container:r},allowOutsideClick:n,allowEscapeKey:d,allowEnterKey:c,title:g,html:m,footer:C,report:u},e.commit("$_widget/SET_ERROR",{report:u,title:o.title,content:o.html,footer:o.footer})},s.prototype.$mi_intervals=function(){return I}}};const E=document.getElementById("monsterinsights-dashboard-widget"),W=document.getElementById("monsterinsights-reminder-notice"),F=document.querySelectorAll(".monsterinsights-setup-wizard-link");function E2(){let s=new FormData;s.append("action","monsterinsights_generate_setup_wizard_url"),s.append("nonce",window.monsterinsights.nonce),fetch(window.monsterinsights.ajax,{method:"POST",body:s}).then(e=>e.json()).then(e=>{const{data:o}=e;e.success&&o.wizard_url&&(window.location.href=o.wizard_url)}).catch(e=>{console.error("Error launching wizard:",e)})}F&&F.forEach(s=>{s.addEventListener("click",E2)});a.config.productionTip=!1;E&&({}.NODE_ENV!=="production"&&(a.config.devtools=!0,a.config.performance=!0),Y({ctrl:!0}),a.use(A),a.use(G),a.use(j,{defaultTemplate:'<div class="monsterinsights-tooltip" role="tooltip"><div class="monsterinsights-tooltip-arrow"></div><div class="monsterinsights-tooltip-inner"></div></div>',defaultArrowSelector:".monsterinsights-tooltip-arrow, .monsterinsights-tooltip__arrow",defaultInnerSelector:".monsterinsights-tooltip-inner, .monsterinsights-tooltip__inner"}),a.use(z),a.use(O),a.use(U,{store:_}),a.use(X),new a({store:_,mounted:()=>{_.dispatch("$_app/init"),_.dispatch("$_license/getLicense"),_.dispatch("$_queue/add",()=>_.dispatch("$_notifications/getNotifications"))},render:s=>s(k2)}).$mount(E));W&&(a.use(A),a.use(z),a.use(O),a.use(U,{store:_}),new a({store:_,mounted:()=>{_.dispatch("$_app/init"),_.dispatch("$_license/getLicense")},render:s=>s(T2)}).$mount(W));
