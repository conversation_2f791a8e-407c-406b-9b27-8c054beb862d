import{A as Re,h as x,m as C,n as l,V as w,f as Zt,u as Wt,M as qe,g as zt,j as Kt,k as qt,a as ne,H as Gt,l as Xe,o as Jt,c as ze,r as Qt,q as Xt,s as Ge,t as pe,v as es,b as Lt,w as ts,i as ss,e as rs,p as os}from"./chunks/vendor-0853f02f.min.js";import{h as is,a as as,i as ns,R as ls,D as cs}from"./chunks/date-intervals-018a2dda.min.js";import{S as Pe}from"./chunks/SettingsInfoTooltip-4f19e286.min.js";import{R as Et,a as E,b as ps,c as ds}from"./chunks/report-table-helper-39dc7ff9.min.js";import{S as At,c as Ft,d as ue,h as hs,b as et,A as gs,U as us,T as ms,e as _s,f as fs,g as vs,N as ws,s as ys,L as Cs}from"./chunks/index-42e15aa7.min.js";import{u as bs,i as xs,M as tt,s as fe}from"./chunks/index-468bfca9.min.js";import{L as ks}from"./chunks/LaunchWizardButton-0eeb5248.min.js";import"./chunks/SlideDownUp-22dab176.min.js";const $s={name:"ReportOverviewLineChartApex",props:{chartData:Object,tooltipDescriptor:String,chartStyles:{type:Object,default(){return{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'}}}},components:{apexchart:Re},methods:{chartOptions(){const s=this.chartData,e=this.tooltipDescriptor,t=this.$formatNumber,r=this.siteNotes,o=this.chartStyles,n=s.datasets?Math.min(...s.data):1;let a=this;return{chart:{type:"area",foreColor:o.foreColor,stacked:!1,dropShadow:{enabled:!0,enabledSeries:[0],top:-2,left:2,blur:5,opacity:.06},animations:{enabled:!1},toolbar:{show:!1},width:"100%",height:355,zoom:!1},padding:{left:0},colors:o.colors,stroke:{curve:"straight",width:3},dataLabels:{enabled:!1},markers:{size:4,colors:o.markersColors,strokeColor:o.markersStrokeColor,strokeWidth:1,strokeOpacity:1,fillOpacity:1,hover:{size:5,strokeColor:o.markersStrokeColorHover,colors:o.markersColorsHover}},xaxis:{type:"datetime",axisTicks:{show:!1},labels:{format:"d MMM"},tooltip:{enabled:!1}},yaxis:{tooltip:{enabled:!1},labels:{formatter(p){return n>0&&n<1?p.toFixed(1):p&&p.toFixed(0)}}},grid:{xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}},borderColor:o.borderColor,strokeDashArray:0,position:"back",padding:{top:0,right:24,bottom:0,left:20}},tooltip:{custom({dataPointIndex:p}){let m=s.labels[p]?s.labels[p]:"0",b=t(s.data[p]),k=p>0?parseInt(s.trend[p-1]):0,U="";k===0?U="0%":k>0?U='<span class="monsterinsights-green"><span class="monsterinsights-arrow monsterinsights-up"></span>'+k+"%</span>":U='<span class="monsterinsights-red"><span class="monsterinsights-arrow monsterinsights-down"></span>'+Math.abs(k)+"%</span>";let H=s.timestamps[p]*1e3,O="";r[H]&&(r[H].forEach(function(Fe){O+="<span>"+Fe.title+"</span><br />"}),O!==""&&(O='<div class="monsterinsights-reports-apex-datagraph-tooltip-sitenotes"><hr />'+O+"</div>"));let ce="";if(a.date.compareReport&&a.chartData.compare&&a.chartData.compare[p]){let De=x.unix(a.chartData.compare[p][0]),Fe=t(a.chartData.compare[p][1]);ce=ce+'<div class="monsterinsights-reports-overview-datagraph-tooltip-title monsterinsights-reports-overview-datagraph-tooltip-title-compare">'+De.format("D MMM")+'</div><div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+Fe+"</div>"}return'<div id="monsterinsights-chartjs-line-apex-tooltip" class="monsterinsights-line-chart-apex-tooltip top"><div class="monsterinsights-reports-datagraph-tooltip-container monsterinsights-reports-tooltip" > <div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+m+'</div><div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+b+"</div>"+ce+'<div class="monsterinsights-reports-overview-datagraph-tooltip-descriptor">'+e+'</div><div class="monsterinsights-reports-overview-datagraph-tooltip-trend">'+U+"</div>"+O+"</div></div>"},x:{show:!0,format:"dd MMM yyyy",formatter:void 0},style:{fontSize:o.fontSize,fontFamily:o.fontFamily}},legend:{position:"bottom",horizontalAlign:"center"},fill:{type:"solid",opacity:.15},annotations:{points:this.chartAnnotationsPoints()}}},chartSeries(){const s=this.date.intervalText;if(typeof this.chartData.data>"u")return[{name:s,data:[]}];const e=[];for(let r=0;r<this.chartData.data.length;r++)e.push([this.chartData.timestamps[r]*1e3,this.chartData.data[r]]);let t=[{name:s,data:e,zIndex:2}];if(this.date.compareReport&&this.chartData.compare){let r=[];e.forEach((o,n)=>{let a=this.chartData.compare[n]?this.chartData.compare[n][1]:0;r.push([o[0],a])}),t.push({name:this.date.intervalCompareText,data:r,zIndex:1})}return t},chartAnnotationsPoints(){let s=[];const e=this.siteNotes;for(let t in e){let r=e[t];if(!r[0])continue;let o=r[0].important,n=r[0].color?r[0].color:"#3a93dd".replace("#",""),a="";o?a='data:image/svg+xml,%3C%3Fxml version="1.0" encoding="utf-8"%3F%3E%3Csvg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 25 25" style="enable-background:new 0 0 25 25;" xml:space="preserve"%3E%3Cstyle type="text/css"%3E.st0%7Bfill:%23'+n+';%7D.st1%7Bfill:%23FFFFFF;%7D.st2%7Bfill:%23FFD74B;%7D%3C/style%3E%3Cg%3E%3Cg%3E%3Cpath class="st0" d="M22.5,0.7H2.5c-0.6,0-1.1,0.5-1.1,1v18.6c0,0.6,0.5,1,1.1,1h6.4c0.2,0,0.4,0.1,0.5,0.2l2.5,2.6 c0.3,0.3,0.8,0.3,1.1,0l2.5-2.6c0.1-0.1,0.3-0.2,0.5-0.2h6.4c0.6,0,1.1-0.5,1.1-1V1.7C23.6,1.1,23.1,0.7,22.5,0.7z"/%3E%3Cpath class="st1" d="M12.5,24.6C12.5,24.6,12.5,24.6,12.5,24.6c-0.3,0-0.6-0.1-0.8-0.3l-2.5-2.6c-0.1-0.1-0.2-0.1-0.3-0.1H2.5 c-0.8,0-1.4-0.6-1.4-1.3V1.7c0-0.7,0.6-1.3,1.4-1.3h19.9c0.8,0,1.4,0.6,1.4,1.3v18.6c0,0.7-0.6,1.3-1.4,1.3h-6.4 c-0.1,0-0.2,0-0.3,0.1l-2.5,2.6C13.1,24.5,12.8,24.6,12.5,24.6z M2.5,1C2.1,1,1.7,1.3,1.7,1.7v18.6c0,0.4,0.4,0.7,0.8,0.7h6.4 c0.3,0,0.6,0.1,0.8,0.3l2.5,2.6c0.1,0.1,0.2,0.1,0.3,0.1c0,0,0,0,0,0c0.1,0,0.2,0,0.3-0.1l2.5-2.6c0.2-0.2,0.5-0.3,0.8-0.3h6.4 c0.4,0,0.8-0.3,0.8-0.7V1.7c0-0.4-0.4-0.7-0.8-0.7H2.5z"/%3E%3C/g%3E%3Cpath class="st2" d="M13.1,4.9l1.7,3.2c0.1,0.2,0.3,0.3,0.5,0.3L19.1,9c0.6,0.1,0.8,0.7,0.4,1.1l-2.7,2.5c-0.2,0.1-0.2,0.4-0.2,0.6 l0.6,3.5c0.1,0.5-0.5,0.9-1,0.7l-3.4-1.7c-0.2-0.1-0.4-0.1-0.6,0l-3.4,1.7c-0.5,0.2-1.1-0.2-1-0.7l0.6-3.5c0-0.2,0-0.4-0.2-0.6 l-2.7-2.5C5.1,9.7,5.3,9,5.9,9l3.8-0.5c0.2,0,0.4-0.2,0.5-0.3l1.7-3.2C12.1,4.4,12.9,4.4,13.1,4.9L13.1,4.9z"/%3E%3C/g%3E%3C/svg%3E%0A':a="data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20viewBox%3D%220%200%2042.4%2047.9%22%20style%3D%22enable-background%3Anew%200%200%2042.4%2047.9%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E.st0%7Bfill%3A%23"+n+"%3B%7D.st1%7Bfill%3A%23FFFFFF%3B%7D%3C%2Fstyle%3E%3Cg%3E%3Cg%3E%3Cpath%20class%3D%22st0%22%20d%3D%22M38.9%2C1.6H3.5c-1.1%2C0-1.9%2C0.9-1.9%2C1.9V39c0%2C1.1%2C0.9%2C1.9%2C1.9%2C1.9h12.8l4.9%2C5.1l4.9-5.1h12.8c1.1%2C0%2C1.9-0.9%2C1.9-1.9V3.6C40.9%2C2.5%2C40%2C1.6%2C38.9%2C1.6z%22%2F%3E%3Cpath%20class%3D%22st1%22%20d%3D%22M21.2%2C46.6l-5.1-5.3H3.5c-1.3%2C0-2.3-1-2.3-2.3V3.6c0-1.3%2C1-2.3%2C2.3-2.3h35.4c1.3%2C0%2C2.3%2C1%2C2.3%2C2.3V39c0%2C1.3-1%2C2.3-2.3%2C2.3H26.3L21.2%2C46.6z%20M3.5%2C2C2.6%2C2%2C1.9%2C2.7%2C1.9%2C3.6V39c0%2C0.9%2C0.7%2C1.6%2C1.6%2C1.6h12.9l4.8%2C5l4.8-5h12.9c0.9%2C0%2C1.6-0.7%2C1.6-1.6V3.6c0-0.9-0.7-1.6-1.6-1.6H3.5z%22%2F%3E%3C%2Fg%3E%3Cg%3E%3Cg%3E%3Cpath%20class%3D%22st1%22%20d%3D%22M33.1%2C14.6H9.3c-0.6%2C0-1.2-0.6-1.2-1.2s0.5-1.2%2C1.2-1.2h23.7c0.6%2C0%2C1.2%2C0.6%2C1.2%2C1.2S33.7%2C14.6%2C33.1%2C14.6z%22%2F%3E%3C%2Fg%3E%3Cg%3E%3Cpath%20class%3D%22st1%22%20d%3D%22M33.1%2C22H9.3c-0.6%2C0-1.2-0.6-1.2-1.2s0.5-1.2%2C1.2-1.2h23.7c0.6%2C0%2C1.2%2C0.6%2C1.2%2C1.2S33.7%2C22%2C33.1%2C22z%22%2F%3E%3C%2Fg%3E%3Cg%3E%3Cpath%20class%3D%22st1%22%20d%3D%22M33.1%2C29.4H9.3c-0.6%2C0-1.2-0.6-1.2-1.2s0.5-1.2%2C1.2-1.2h23.7c0.6%2C0%2C1.2%2C0.6%2C1.2%2C1.2S33.7%2C29.4%2C33.1%2C29.4z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E",s.push({x:t,y:0,marker:0,image:{path:a}})}return s}},computed:{...C({date:"$_reports/date"}),siteNotes(){let s=this.chartData&&this.chartData.notes?this.chartData.notes:[];if(typeof s>"u")return[];if(s.length==0)return s;let e={...s},t=this.chartData,r=[];return Object.keys(e).forEach(o=>{const n=t.labels.indexOf(o),a=this.chartData.timestamps[n]*1e3;r[a]=[...e[o]]}),r}}};var Rs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"apexchart-overview apexchart-line-chart"},[t("apexchart",{attrs:{options:e.chartOptions(),series:e.chartSeries(),height:"355"}})],1)},Ps=[],Ds=l($s,Rs,Ps,!1,null,null,null,null);const J=Ds.exports,Ss={COLORS:["#2870C8","#489DFF","#BDDEFF","#7AAEE3","#5FB2F2","#9FCAFF","#166BAF","#80B9FF","#007EFF","#DBEDFF","#4CA6BC","#5E2CEC","#CB4CD0","#F164F6","#FDC5FF","#BDE9F3","#B79EFF","#5DC8E2","#FCA4FF","#BDE2FF","#4CA6BC","#5E2CEC","#CB4CD0","#F164F6","#FDC5FF","#BDE9F3","#B79EFF","#5DC8E2","#FCA4FF","#BDE2FF"]},Ts=null,Ms=null;var Ls=l(Ss,Ts,Ms,!1,null,null,null,null);const me=Ls.exports,Es={name:"ReportPieChartApex",props:{chartData:Object,height:{type:Number,required:!0},width:{type:Number,required:!0},size:{type:Number,required:!0},colors:{type:Array,required:!1},withLegend:{type:Boolean,required:!1}},components:{apexchart:Re},methods:{chartOptions(){return{chart:{type:"donut"},colors:this.chartColors(),plotOptions:{pie:{donut:{size:this.size+"%"}}},labels:this.chartLabels(),legend:{show:this.withLegend,position:"bottom",horizontalAlign:"left",offsetY:8,onItemHover:{highlightDataSeries:!1}},dataLabels:{enabled:!1},grid:{padding:{top:0,bottom:0,left:0,right:0}}}},chartSeries(){let s=[];return typeof this.chartData.values<"u"&&this.chartData.values.forEach(function(e){typeof e<"u"?s.push(e):s.push(0)}),s},chartLabels(){let s=[];return typeof this.chartData.labels<"u"&&this.chartData.labels.forEach(function(e){typeof e<"u"?s.push(e):s.push("")}),s},chartColors(){return this.colors||me.COLORS}}};var As=function(){var e=this,t=e._self._c;return t("div",{staticClass:"apexchart-overview apexchart-pie-chart"},[t("apexchart",{attrs:{options:e.chartOptions(),series:e.chartSeries(),width:e.width,height:e.height}})],1)},Fs=[],Ns=l(Es,As,Fs,!1,null,null,null,null);const Is=Ns.exports,Bs={name:"ReportOverviewPieChartApex",components:{SettingsInfoTooltip:Pe,ReportPieChartApex:Is},props:{chartData:[Object,Boolean],title:String,tooltip:String,legend:{type:Boolean,default:!0},id:String,enableTooltips:{type:Boolean,default:!0},cutoutPercentage:Number,width:{type:Number,default:200},height:{type:Number,default:200},size:{type:Number,default:50},icon:{default:"",type:String}},computed:{...C({date:"$_reports/date"}),titleClass(){return"monsterinsights-report-title "+this.icon},tooltipId(){return"monsterinsights-chartjs-pie-"+this.id+"-tooltip"},style(){return this.date.compareReport?"":"max-width: "+this.width+"px; max-height: "+this.width+"px;"},colors(){return me.COLORS},chartWidth(){return this.width},chartHeight(){return this.height},chartSize(){return this.size},compareReportChartData(){if(this.date.compareReport&&this.chartData.compare){if(this.chartData.compare.length&&!this.chartData.compare.labels)return{values:this.chartData.compare,labels:this.chartData.labels};if(this.chartData.compare.values&&this.chartData.compare.labels)return this.chartData.compare}return{}}},methods:{labelBackground(s){return"background-color: "+s+";"},chartClasses(){let s="monsterinsights-reports-pie-chart";return typeof this.chartData.values<"u"&&this.chartData.values.length>7&&(s+=" --long-list"),s}}};var Os=function(){var e=this,t=e._self._c;return e.chartData?t("div",{class:e.chartClasses(),attrs:{id:e.id}},[e.title?t("h3",{class:e.titleClass,domProps:{textContent:e._s(e.title)}}):e._e(),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e(),t("div",{class:{"monsterinsights-reports-compare-pie-charts":e.date.compareReport}},[t("div",{staticClass:"monsterinsights-reports-pie-chart-holder"},[e.date.compareReport?t("p",{staticClass:"monsterinsights-reports-pie-chart-holder-compare-date"},[e._v(" "+e._s(e.date.intervalText)+" ")]):e._e(),t("div",{staticClass:"monsterinsights-reports-pie-chart-and-legend"},[t("report-pie-chart-apex",{staticClass:"monsterinsights-pie-chart",style:e.style,attrs:{"chart-data":e.chartData,width:e.chartWidth,height:e.chartHeight,size:e.chartSize}}),e.legend?t("ul",{staticClass:"monsterinsights-pie-chart-legend"},e._l(e.chartData.values,function(r,o){return t("li",{key:o},[t("span",{staticClass:"monsterinsights-pie-chart-legend-color",style:e.labelBackground(e.colors[o])}),t("span",{staticClass:"monsterinsights-pie-chart-legend-text-and-value"},[t("span",{staticClass:"monsterinsights-pie-chart-legend-text",domProps:{textContent:e._s(e.chartData.labels[o])}}),t("span",{staticClass:"monsterinsights-pie-chart-legend-value"},[e._v(" "+e._s(r||0)+"% ")])])])}),0):e._e()],1),t("div",{staticClass:"monsterinsights-pie-chart-tooltip",attrs:{id:e.tooltipId}})]),e.date.compareReport?t("div",{staticClass:"monsterinsights-reports-pie-chart-holder monsterinsights-reports-compare-pie-chart-holder"},[t("p",{staticClass:"monsterinsights-reports-pie-chart-holder-compare-date"},[e._v(" "+e._s(e.date.intervalCompareText)+" ")]),t("div",{staticClass:"monsterinsights-reports-pie-chart-and-legend"},[t("report-pie-chart-apex",{staticClass:"monsterinsights-pie-chart",style:e.style,attrs:{"chart-data":e.compareReportChartData,width:e.chartWidth,height:e.chartHeight,size:e.chartSize}}),e.legend?t("ul",{staticClass:"monsterinsights-pie-chart-legend"},e._l(e.compareReportChartData.values,function(r,o){return t("li",{key:o},[t("span",{staticClass:"monsterinsights-pie-chart-legend-color",style:e.labelBackground(e.colors[o])}),t("span",{staticClass:"monsterinsights-pie-chart-legend-text-and-value"},[t("span",{staticClass:"monsterinsights-pie-chart-legend-text",domProps:{textContent:e._s(e.chartData.labels[o])}}),t("span",{staticClass:"monsterinsights-pie-chart-legend-value"},[e._v(" "+e._s(r||0)+"% ")])])])}),0):e._e()],1),t("div",{staticClass:"monsterinsights-pie-chart-tooltip",attrs:{id:e.tooltipId}})]):e._e()])],1):e._e()},Us=[],Hs=l(Bs,Os,Us,!1,null,null,null,null);const Je=Hs.exports,{__:h,sprintf:st}=wp.i18n,Vs={name:"ReportSiteSummary",computed:{...C({site_summary:"$_reports/site_summary",addons:"$_addons/addons"}),isLoaded(){return this.loaded},totalPageViews(){return this.$formatNumber(this.site_summary.summary[0])},totalSessions(){return this.$formatNumber(this.site_summary.summary[1])},totalPosts(){return this.$formatNumber(this.site_summary.summary.total_posts)},totalPages(){return this.$formatNumber(this.site_summary.summary.total_pages)},totalComments(){return this.$formatNumber(this.site_summary.summary.total_comments)},hasMostPopularPageData(){var s,e;return!!((e=(s=this.site_summary)==null?void 0:s.popular_page)!=null&&e.url)},hasMostPopularPostData(){var s,e;return!!((e=(s=this.site_summary)==null?void 0:s.popular_post)!=null&&e.url)},mostPopularPageUrl(){var s;return(s=this.site_summary)==null?void 0:s.popular_page.url},mostPopularPostTitle(){var s,e,t;return(s=this.site_summary)!=null&&s.popular_post.title?(e=this.site_summary)==null?void 0:e.popular_post.title:(t=this.site_summary)==null?void 0:t.popular_post.url},mostPopularPageThumbnail(){var s;return(s=this.site_summary)==null?void 0:s.popular_page.img},mostPopularPostThumbnail(){var s;return(s=this.site_summary)==null?void 0:s.popular_post.img},canSeeProducts(){return this.$isPro()?this.isAddonActive("woocommerce"):!1},popularProducts(){return this.site_summary.popular_products},popularCategories(){return this.site_summary.popular_categories},isPro(){return this.$isPro()}},data(){return{loaded:!1,upgrade_button_url:this.$getUpgradeUrl("reports","site-summary"),addons_url:window.monsterinsights.addons_url,days:[h("Sun","google-analytics-for-wordpress"),h("Mon","google-analytics-for-wordpress"),h("Tue","google-analytics-for-wordpress"),h("Wed","google-analytics-for-wordpress"),h("Thu","google-analytics-for-wordpress"),h("Fri","google-analytics-for-wordpress"),h("Sat","google-analytics-for-wordpress")],hours:[h("12 AM","google-analytics-for-wordpress"),h("1 AM","google-analytics-for-wordpress"),h("2 AM","google-analytics-for-wordpress"),h("3 AM","google-analytics-for-wordpress"),h("4 AM","google-analytics-for-wordpress"),h("5 AM","google-analytics-for-wordpress"),h("6 AM","google-analytics-for-wordpress"),h("7 AM","google-analytics-for-wordpress"),h("8 AM","google-analytics-for-wordpress"),h("9 AM","google-analytics-for-wordpress"),h("10 AM","google-analytics-for-wordpress"),h("11 AM","google-analytics-for-wordpress"),h("12 PM","google-analytics-for-wordpress"),h("1 PM","google-analytics-for-wordpress"),h("2 PM","google-analytics-for-wordpress"),h("3 PM","google-analytics-for-wordpress"),h("4 PM","google-analytics-for-wordpress"),h("5 PM","google-analytics-for-wordpress"),h("6 PM","google-analytics-for-wordpress"),h("7 PM","google-analytics-for-wordpress"),h("8 PM","google-analytics-for-wordpress"),h("9 PM","google-analytics-for-wordpress"),h("10 PM","google-analytics-for-wordpress"),h("11 PM","google-analytics-for-wordpress")],texts:{upgrade_button:h("Upgrade","google-analytics-for-wordpress"),activate_button:h("Activate","google-analytics-for-wordpress"),upgrade_text:h("Unlock with MonsterInsights Pro","google-analytics-for-wordpress"),title:h("Stats at a Glance","google-analytics-for-wordpress"),all_time_stats_title:h("All Time Stats","google-analytics-for-wordpress"),most_popular_times_title:h("Most Popular Times","google-analytics-for-wordpress"),most_popular_categories_title:h("Most Popular Categories","google-analytics-for-wordpress"),most_popular_page_title:h("Your Most Popular Page","google-analytics-for-wordpress"),most_popular_post_title:h("Most Popular Post","google-analytics-for-wordpress"),most_popular_product_title:h("Most Popular Product","google-analytics-for-wordpress"),pageviews:h("Pageviews","google-analytics-for-wordpress"),sessions:h("Sessions","google-analytics-for-wordpress"),bounce_rate:h("Bounce Rate","google-analytics-for-wordpress"),engaged_sessions:h("Engaged Sessions","google-analytics-for-wordpress"),top_days_label:h("Top days","google-analytics-for-wordpress"),top_hours_label:h("Top hours","google-analytics-for-wordpress"),total_page_views_label:h("Total Page Views","google-analytics-for-wordpress"),total_sessions_label:h("Total Sessions","google-analytics-for-wordpress"),nr_of_posts_label:h("Number of Posts","google-analytics-for-wordpress"),nr_of_pages_label:h("Number of Pages","google-analytics-for-wordpress"),nr_of_comments_label:h("Number of Comments","google-analytics-for-wordpress"),of_categories:h("% of Categories","google-analytics-for-wordpress"),of_products:h("% of Sales","google-analytics-for-wordpress"),upgrade_to_dimensions:h("Unlock powerful insights with the Dimensions addon!","google-analytics-for-wordpress"),nothing_to_show:h("No data to show","google-analytics-for-wordpress")},demo:{pageUrl:"https://www.monsterinsights.com/pricing/",mostPopularPageData:[{label:h("Pageviews","google-analytics-for-wordpress"),value:64114},{label:h("Sessions","google-analytics-for-wordpress"),value:57738},{label:h("Bounce Rate","google-analytics-for-wordpress"),value:.74},{label:h("Engaged Sessions","google-analytics-for-wordpress"),value:14492}]}}},methods:{getPopularDays(){var t;return typeof this.site_summary.popular_days>"u"?null:((t=this.site_summary)==null?void 0:t.popular_days.slice(0,3)).map(r=>st("<div><h3>%1$s</h3><span>%2$s</span></div>",this.days[r.day],this.$formatNumber(r.percent)+h("% of Views","google-analytics-for-wordpress"))).join("")},getPopularHours(){var t;return typeof this.site_summary.popular_hours>"u"?null:((t=this.site_summary)==null?void 0:t.popular_hours.slice(0,3)).map(r=>st("<div><h3>%s</h3><span>%s</span></div>",this.hours[r.hour],this.$formatNumber(r.percent)+h("% of Views","google-analytics-for-wordpress"))).join("")},getMostPopularData(s="post"){var t,r;const e=s==="post"?(t=this.site_summary)==null?void 0:t.popular_post:(r=this.site_summary)==null?void 0:r.popular_page;return[{label:this.texts.pageviews,value:this.$formatNumber(e.pageviews)},{label:this.texts.sessions,value:this.$formatNumber(e.sessions)},{label:this.texts.bounce_rate,value:this.$formatNumber(e.bounceRate,2)+"%"},{label:this.texts.engaged_sessions,value:this.$formatNumber(e.engagedSessions)}]},isAddonActive(s){return this.addons[s]?this.addons[s].active:!1},canShowDimensions(){return!!this.isAddonActive("dimensions")}},mounted(){this.$store.dispatch("$_reports/getReportData","site_summary").then(()=>{this.loaded=!0}).catch(function(){this.loaded=!1})}};var Ys=function(){var e=this,t=e._self._c;return e.isLoaded?t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-site-summary"},[t("h1",{staticClass:"monsterinsights-report-site-summary-title",domProps:{textContent:e._s(e.texts.title)}}),t("div",{staticClass:"monsterinsights-report-site-summary-cards"},[t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.all_time_stats_title)}}),t("div",{staticClass:"monsterinsights-report-site-summary-all-time-stats"},[t("ul",[t("li",[t("span",{staticClass:"dashicons-before dashicons-visibility",domProps:{textContent:e._s(e.texts.total_page_views_label)}}),t("span",{domProps:{textContent:e._s(e.totalPageViews)}})]),t("li",[t("span",{staticClass:"dashicons-before dashicons-groups",domProps:{textContent:e._s(e.texts.total_sessions_label)}}),t("span",{domProps:{textContent:e._s(e.totalSessions)}})]),t("li",[t("span",{staticClass:"dashicons-before dashicons-edit-page",domProps:{textContent:e._s(e.texts.nr_of_posts_label)}}),t("span",{domProps:{textContent:e._s(e.totalPosts)}})]),t("li",[t("span",{staticClass:"dashicons-before dashicons-text-page",domProps:{textContent:e._s(e.texts.nr_of_pages_label)}}),t("span",{domProps:{textContent:e._s(e.totalPages)}})]),t("li",[t("span",{staticClass:"dashicons-before dashicons-admin-comments",domProps:{textContent:e._s(e.texts.nr_of_comments_label)}}),t("span",{domProps:{textContent:e._s(e.totalComments)}})])])])]),t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_times_title)}}),e.getPopularDays()!==null||e.getPopularHours()!==null?t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-times"},[t("h4",{domProps:{textContent:e._s(e.texts.top_days_label)}}),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-days",domProps:{innerHTML:e._s(e.getPopularDays())}}),t("h4",{domProps:{textContent:e._s(e.texts.top_hours_label)}}),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-hours",domProps:{innerHTML:e._s(e.getPopularHours())}})]):t("div",[t("span",{domProps:{textContent:e._s(e.texts.nothing_to_show)}})])]),e.canShowDimensions()&&e.popularCategories.length>0?t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_categories_title)}}),t("div",[t("ol",{staticClass:"monsterinsights-report-site-summary-table"},e._l(e.popularCategories,function(r){return t("li",{key:r.title,staticClass:"monsterinsights-report-site-summary-table-item"},[t("span",[t("div",{staticClass:"monsterinsights-report-site-summary-table-item-title"},[e._v(e._s(r.title))]),t("div",[e._v(e._s(e.$formatNumber(r.percent,0)+e.texts.of_categories))])])])}),0)])]):e.canShowDimensions()?e._e():t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_categories_title)}}),t("div",{staticClass:"monsterinsights-report-site-summary-upsell-banner"},[t("span",{staticClass:"dashicons-before dashicons-lock"}),t("span",{domProps:{textContent:e._s(e.texts.upgrade_to_dimensions)}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button monsterinsights-button-small",attrs:{href:e.addons_url},domProps:{textContent:e._s(e.isPro?e.texts.activate_button:e.texts.upgrade_button)}})]),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page-content monsterinsights-report-site-summary-upsell-blur"},[t("div",{staticClass:"monsterinsights-report-site-summary-subtitle",domProps:{textContent:e._s(e.demo.pageUrl)}}),t("div",{staticClass:"monsterinsights-report-site-summary-two-column monsterinsights-report-site-summary-quarters"},e._l(e.demo.mostPopularPageData,function(r){return t("div",{key:r.label},[t("strong",{domProps:{textContent:e._s(r.label)}}),t("span",{domProps:{textContent:e._s(r.value)}})])}),0)])]),t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_page_title)}}),e.isPro?e._e():t("div",{staticClass:"monsterinsights-report-site-summary-upsell monsterinsights-report-site-summary-most-popular-page"},[t("div",{staticClass:"monsterinsights-report-site-summary-upsell-banner"},[t("span",{staticClass:"dashicons-before dashicons-lock"}),t("span",{domProps:{textContent:e._s(e.texts.upgrade_text)}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button monsterinsights-button-small",attrs:{target:"_blank",href:e.upgrade_button_url},domProps:{textContent:e._s(e.texts.upgrade_button)}})]),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page-content monsterinsights-report-site-summary-upsell-blur"},[t("div",{staticClass:"monsterinsights-report-site-summary-subtitle",domProps:{textContent:e._s(e.demo.pageUrl)}}),t("div",{staticClass:"monsterinsights-report-site-summary-two-column monsterinsights-report-site-summary-quarters"},e._l(e.demo.mostPopularPageData,function(r){return t("div",{key:r.label},[t("strong",{domProps:{textContent:e._s(r.label)}}),t("span",{domProps:{textContent:e._s(r.value)}})])}),0)]),e._m(0)]),e.isPro&&e.hasMostPopularPageData?t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page"},[t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page-content"},[t("div",{staticClass:"monsterinsights-report-site-summary-subtitle",domProps:{textContent:e._s(e.mostPopularPageUrl)}}),t("div",{staticClass:"monsterinsights-report-site-summary-two-column monsterinsights-report-site-summary-quarters"},e._l(e.getMostPopularData("page"),function(r){return t("div",{key:r.label},[t("strong",{domProps:{textContent:e._s(r.label)}}),t("span",{domProps:{textContent:e._s(r.value)}})])}),0)]),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-image"},[e.mostPopularPageThumbnail?t("img",{attrs:{src:e.mostPopularPageThumbnail,alt:""}}):e._e(),e.mostPopularPageThumbnail?e._e():t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-placeholder dashicons-before dashicons-format-image"})])]):e.isPro?t("div",[t("span",{domProps:{textContent:e._s(e.texts.nothing_to_show)}})]):e._e()]),e.hasMostPopularPostData?t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_post_title)}}),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page"},[t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-page-content"},[t("div",{staticClass:"monsterinsights-report-site-summary-subtitle",domProps:{textContent:e._s(e.mostPopularPostTitle)}}),t("div",{staticClass:"monsterinsights-report-site-summary-two-column monsterinsights-report-site-summary-quarters"},e._l(e.getMostPopularData("post"),function(r){return t("div",{key:r.label},[t("strong",{domProps:{textContent:e._s(r.label)}}),t("span",{domProps:{textContent:e._s(r.value)}})])}),0)]),t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-image"},[e.mostPopularPostThumbnail?t("img",{attrs:{src:e.mostPopularPostThumbnail,alt:""}}):e._e(),e.mostPopularPostThumbnail?e._e():t("div",{staticClass:"monsterinsights-report-site-summary-most-popular-placeholder dashicons-before dashicons-format-image"})])])]):e._e(),e.canSeeProducts?t("div",{staticClass:"monsterinsights-report-site-summary-card"},[t("h3",{staticClass:"monsterinsights-report-site-summary-card-title",domProps:{textContent:e._s(e.texts.most_popular_product_title)}}),e.canSeeProducts&&e.popularProducts.length>0?t("div",[t("ol",{staticClass:"monsterinsights-report-site-summary-table"},e._l(e.popularProducts,function(r){return t("li",{key:r.title,staticClass:"monsterinsights-report-site-summary-table-item"},[t("span",[t("div",{staticClass:"monsterinsights-report-site-summary-table-item-title"},[e._v(e._s(r.title))]),t("div",[e._v(e._s(e.$formatNumber(r.percent,0)+e.texts.of_products))])])])}),0)]):t("div",[t("span",{domProps:{textContent:e._s(e.texts.nothing_to_show)}})])]):e._e()])]):e._e()},js=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"monsterinsights-report-site-summary-most-popular-image monsterinsights-report-site-summary-upsell-blur"},[e("div",{staticClass:"monsterinsights-report-site-summary-most-popular-placeholder dashicons-before dashicons-format-image"})])}],Zs=l(Vs,Ys,js,!1,null,null,null,null);const Ws=Zs.exports,{__:rt}=wp.i18n,zs={name:"ReportsPagination",props:{limit:Number,length:Number},data(){return{currentLimit:this.limit,text_show:rt("Show","google-analytics-for-wordpress"),text_all:rt("All","google-analytics-for-wordpress")}},methods:{sendUpdate(s){this.currentLimit=s,this.$emit("updated",s)},getButtonClass(s){let e="monsterinsights-button";return s===this.currentLimit&&(e+=" monsterinsights-selected-interval"),e}}};var Ks=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-table-box-pagination"},[t("span",{domProps:{textContent:e._s(e.text_show)}}),t("div",{staticClass:"monsterinsights-buttons-toggle"},[t("button",{class:e.getButtonClass(10),on:{click:function(r){return e.sendUpdate(10)}}},[e._v(" 10 ")]),e.length>10?t("button",{class:e.getButtonClass(25),on:{click:function(r){return e.sendUpdate(25)}}},[e._v(" 25 ")]):e._e(),e.length>25?t("button",{class:e.getButtonClass(50),on:{click:function(r){return e.sendUpdate(50)}}},[e._v(" 50 ")]):e._e(),e.length>50?t("button",{class:e.getButtonClass(e.length),on:{click:function(r){return e.sendUpdate(e.length)}}},[e._v(" "+e._s(e.text_all)+" ")]):e._e()])])},qs=[],Gs=l(zs,Ks,qs,!1,null,null,null,null);const Qe=Gs.exports,{__:ot}=wp.i18n,Js={name:"ReportListBox",components:{ReportsPagination:Qe,SettingsInfoTooltip:Pe},props:{title:String,id:String,tooltip:String,rows:Array,icon:{default:"",type:String},withPagination:{default:!0,type:Boolean},reportname:String,main_column_title:String},data(){return{limit:10,text_date_1:ot("Date 1","google-analytics-for-wordpress"),text_date_2:ot("Date 2","google-analytics-for-wordpress")}},computed:{...C({date:"$_reports/date"}),componentClass(){let s="monsterinsights-table-box";return this.paginate&&(s+=" monsterinsights-has-pagination"),s},titleClass(){let s="monsterinsights-report-title ";return this.icon&&(s+=this.icon),this.paginate&&(s+=" monsterinsights-has-pagination"),s},paginate(){return this.withPagination&&this.rows.length>10},tableRows(){let s=[...this.rows];if(s.length<10)for(;s.length<10;)s.push({number:"",text:"",right:"",sessions:""});return s=s.slice(0,this.limit),s}},methods:{hasButtonSlot(){return this.$slots.button},changeLimit(s){this.limit=s},changeTitle(s){s===!0?(this.visual_title="Pages",this.display_page_title=!0):(this.visual_title=this.title,this.display_page_title=!1)},displayMainColumn(s){return this.display_page_title===!0?s.page_path:s.text},compareChange(s){if(typeof s.compare_change>"u")return"";let e=s.compare_change>0?"up":"down",t=s.compare_change>0?"green":"red";return'<span class="monsterinsights-arrow monsterinsights-'+e+" monsterinsights-"+t+'"></span> '+Math.abs(s.compare_change)+"%"}}};var Qs=function(){var e=this,t=e._self._c;return t("div",{class:e.componentClass,attrs:{id:e.id}},[e.title?t("h3",{class:e.titleClass,domProps:{textContent:e._s(e.title)}}):e._e(),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e(),t("div",{staticClass:"monsterinsights-table-box-list"},[e.date.compareReport&&e.rows.length?[t("ul",{staticClass:"monsterinsights-table-box-list-compare-date-legends"},[t("li",[t("strong",{domProps:{textContent:e._s(e.text_date_1)}}),e._v(": "+e._s(e.date.intervalText))]),t("li",[t("strong",{domProps:{textContent:e._s(e.text_date_2)}}),e._v(": "+e._s(e.date.intervalCompareText))])]),t("div",{staticClass:"monsterinsights-table-box-list-compare-thead"},[t("div",{staticClass:"monsterinsights-table-box-list-compare-th monsterinsights-table-box-list-compare-th-text"}),t("div",{staticClass:"monsterinsights-table-box-list-compare-th monsterinsights-table-box-list-compare-th-date"},[e._v(" "+e._s(e.text_date_1)+" ")]),t("div",{staticClass:"monsterinsights-table-box-list-compare-th monsterinsights-table-box-list-compare-th-date"},[e._v(" "+e._s(e.text_date_2)+" ")]),t("div",{staticClass:"monsterinsights-table-box-list-compare-th monsterinsights-table-box-list-compare-th-change"},[e._v(" Change% ")])])]:e._e(),e._l(e.tableRows,function(r,o){return t("div",{key:o,class:{"monsterinsights-table-list-item":!0,"monsterinsights-table-list-item-compare":e.date.compareReport}},[t("div",{staticClass:"monsterinsights-table-list-item-td-text"},[t("span",{staticClass:"monsterinsights-reports-list-count",domProps:{textContent:e._s(r.number)}}),t("span",{staticClass:"monsterinsights-reports-list-text",domProps:{innerHTML:e._s(r.text)}})]),t("div",{staticClass:"monsterinsights-table-list-item-td-number"},[t("span",{staticClass:"monsterinsights-reports-list-number",domProps:{innerHTML:e._s(r.number?e.$formatNumber(r.right):"")}})]),e.date.compareReport?t("div",{staticClass:"monsterinsights-table-list-item-td-number"},[t("span",{staticClass:"monsterinsights-reports-list-number",domProps:{innerHTML:e._s(typeof r.compare_value>"u"?"":e.$formatNumber(r.compare_value))}})]):e._e(),e.date.compareReport?t("div",{staticClass:"monsterinsights-table-list-item-td-change",domProps:{innerHTML:e._s(e.compareChange(r))}}):e._e()])})],2),e.hasButtonSlot()||e.paginate?t("div",{staticClass:"monsterinsights-table-box-footer"},[e._t("button"),e.paginate?t("reports-pagination",{attrs:{limit:e.limit,length:e.rows.length},on:{updated:e.changeLimit}}):e._e()],2):e._e()],1)},Xs=[],er=l(Js,Qs,Xs,!1,null,null,null,null);const tr=er.exports,{__:Ne}=wp.i18n;w.directive("click-outside",{bind:function(s,e,t){s.clickOutsideEvent=function(r){s===r.target||s.contains(r.target)||t.context[e.expression](r)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}});const sr={name:"ReportsPdfExport",props:{buttonText:{type:String,default:Ne("Export PDF Report","google-analytics-for-wordpress")}},data(){return{dropdownVisible:!1,text_upgrade:Ne("Upgrade to PRO","google-analytics-for-wordpress"),upgrade_url:this.$getUpgradeUrl("reports","pdfexport"),text_customize_date:Ne("You can export PDF reports only in the PRO version.","google-analytics-for-wordpress")}},computed:{dropdownClass(){let s="monsterinsights-reports-intervals-dropdown";return this.dropdownVisible||(s+=" monsterinsights-hide"),s}},methods:{maybeHideDropdown(){this.dropdownVisible&&(this.dropdownVisible=!1)}}};var rr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-export-pdf-report monsterinsights-export-pdf-report-lite"},[t("div",{staticClass:"monsterinsights-reports-interval-dropdown-container"},[t("button",{class:["monsterinsights-button"],domProps:{innerHTML:e._s(e.buttonText)},on:{click:function(r){r.stopPropagation(),e.dropdownVisible=!e.dropdownVisible}}}),t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.maybeHideDropdown,expression:"maybeHideDropdown"}],class:e.dropdownClass},[t("div",{staticClass:"monsterinsights-lite-datepicker"},[t("p",{domProps:{innerHTML:e._s(e.text_customize_date)}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button-text",attrs:{href:e.upgrade_url,target:"_blank"}},[t("span",{domProps:{textContent:e._s(e.text_upgrade)}}),t("i",{staticClass:"monstericon-long-arrow-right-light"})])])])])])},or=[],ir=l(sr,rr,or,!1,null,null,null,null);const F=ir.exports,ar={name:"ReportUpsellInline",props:{title:String}};var nr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-report-box monsterinsights-upsell-inline"},[t("div",{staticClass:"monsterinsights-upsell-inline-content"},[t("h3",{domProps:{textContent:e._s(e.title)}}),t("div",{staticClass:"monsterinsights-upsell-content"},[e._t("default")],2)])])},lr=[],cr=l(ar,nr,lr,!1,null,null,null,null);const pr=cr.exports,{__:ve,sprintf:dr}=wp.i18n,hr={name:"ReportOverviewUpsell",components:{ReportUpsellInline:pr},data(){return{text_upsell_title:ve("Upgrade to MonsterInsights Pro to Unlock More Actionable Insights","google-analytics-for-wordpress"),text_upsell_content_1:ve("It's easy to double your traffic and sales when you know exactly how people find and use your website. MonsterInsights Pro shows you the stats that matter!","google-analytics-for-wordpress"),text_upsell_content_2:ve("Upgrade to MonsterInsights Pro and Save 50% OFF!","google-analytics-for-wordpress"),text_upsell_content_3:dr(ve("Use coupon code %1$sLITEUPGRADE%2$s","google-analytics-for-wordpress"),"<b>","</b>"),text_upsell_button:ve("Upgrade to MonsterInsights Pro","google-analytics-for-wordpress"),upgrade_link:this.$getUpgradeUrl("report","overview")}}};var gr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-report-row monsterinsights-overview-upsell-desktop"},[t("report-upsell-inline",{attrs:{title:e.text_upsell_title}},[t("p",{domProps:{textContent:e._s(e.text_upsell_content_1)}}),t("p",[t("span",{staticClass:"monsterinsights-green-text"},[t("b",{domProps:{innerHTML:e._s(e.text_upsell_content_2)}})]),t("br"),t("span",{staticClass:"monsterinsights-light",domProps:{innerHTML:e._s(e.text_upsell_content_3)}})]),t("a",{staticClass:"monsterinsights-button monsterinsights-button-green",attrs:{href:e.upgrade_link,target:"_blank"},domProps:{textContent:e._s(e.text_upsell_button)}})])],1)},ur=[],mr=l(hr,gr,ur,!1,null,null,null,null);const _r=mr.exports,{__:Ie,sprintf:fr}=wp.i18n;w.directive("click-outside",{bind:function(s,e,t){s.clickOutsideEvent=function(r){s===r.target||s.contains(r.target)||t.context[e.expression](r)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}});const vr={name:"ReportsDatePicker",data(){return{dropdownVisible:!1,text_30_days:Ie("Last 30 days","google-analytics-for-wordpress"),text_customize_date:fr(Ie("You can customize your %sdate range only in the PRO version.","google-analytics-for-wordpress"),"<br />"),text_upgrade:Ie("Upgrade to PRO","google-analytics-for-wordpress"),upgrade_url:this.$getUpgradeUrl("reports","datepicker")}},computed:{...C({date:"$_reports/date",activeReport:"$_reports/activeReport"}),selectedIntervalText(){return this.getIntervalFormatted(this.text_30_days,x().subtract(30,"days"),x().subtract(1,"days"))},local_date(){return this.date.text},dropdownClass(){let s="monsterinsights-reports-intervals-dropdown";return this.dropdownVisible||(s+=" monsterinsights-hide"),s}},methods:{getFormattedDate(s){if(s instanceof Date){let e=this.addLeadingZero(s.getMonth()+1),t=this.addLeadingZero(s.getDate());s=s.getFullYear()+"-"+e+"-"+t}return s},addLeadingZero(s){return s<10&&s>0?0+s.toString():s},getInterval(s,e,t,r){this.dropdownVisible=!1,this.interval=r,this.$store.commit("$_reports/UPDATE_DATE",{start:s.format("YYYY-MM-DD"),end:e.format("YYYY-MM-DD")}),window.blur(),this.$store.dispatch("$_reports/getReportData",this.activeReport),this.local_date=""},getIntervalText(s,e){return s.format("YYYYMMDD")===e.format("YYYYMMDD")?s.format("MMMM D, YYYY"):s.format("MMMM D")+" - "+e.format("MMMM D, YYYY")},getIntervalFormatted(s,e,t){return"<b>"+s+":</b> "+this.getIntervalText(e,t)},getButtonClass(s){let e="monsterinsights-button";return s===this.interval&&(e+=" monsterinsights-selected-interval"),e},maybeHideDropdown(){this.dropdownVisible&&(this.dropdownVisible=!1)}}};var wr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-reports-datepicker"},[t("div",{staticClass:"monsterinsights-reports-interval-dropdown-container"},[t("button",{staticClass:"monsterinsights-reports-interval-date-info",on:{click:function(r){r.stopPropagation(),e.dropdownVisible=!e.dropdownVisible}}},[t("span",{domProps:{innerHTML:e._s(e.selectedIntervalText)}}),t("i",{staticClass:"monstericon-calendar-alt"})]),t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.maybeHideDropdown,expression:"maybeHideDropdown"}],class:e.dropdownClass},[t("div",{staticClass:"monsterinsights-lite-datepicker"},[t("p",{domProps:{innerHTML:e._s(e.text_customize_date)}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button-text",attrs:{href:e.upgrade_url,target:"_blank"}},[t("span",{domProps:{textContent:e._s(e.text_upgrade)}}),t("i",{staticClass:"monstericon-long-arrow-right-light"})])])])])])},yr=[],Cr=l(vr,wr,yr,!1,null,null,null,null);const Nt=Cr.exports,{__:it,sprintf:br}=wp.i18n,xr={name:"ReportOverviewUpsellMobile",data(){return{text_upsell_title:br(it("Upgrade to Pro and unlock addons and other great features. %1$sSave 50%% automatically!%2$s","google-analytics-for-wordpress"),'<span class="monsterinsights-green-text"><b>',"</b></span>"),text_upsell_button:it("Upgrade Now","google-analytics-for-wordpress"),upgrade_link:this.$getUpgradeUrl("report","overview-mobile")}}};var kr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-report-row monsterinsights-mobile-upsell"},[t("div",{staticClass:"monsterinsights-notice monsterinsights-notice-success"},[t("div",{staticClass:"monsterinsights-notice-inner"},[t("span",{domProps:{innerHTML:e._s(e.text_upsell_title)}}),t("div",{staticClass:"monsterinsights-notice-button"},[t("a",{staticClass:"monsterinsights-button monsterinsights-button-green",attrs:{href:e.upgrade_link,target:"_blank",rel:"noopener"},domProps:{textContent:e._s(e.text_upsell_button)}})])])])])},$r=[],Rr=l(xr,kr,$r,!1,null,null,null,null);const Pr=Rr.exports,Dr={name:"SiteNotesMediaIconField"};var Sr=function(){var e=this;return e._self._c,e._e()},Tr=[],Mr=l(Dr,Sr,Tr,!1,null,null,null,null);const Lr=Mr.exports,{__:Z}=wp.i18n,Er={name:"SiteNotesOverviewList",components:{Fragment:Zt,SiteNotesDatePickerField:At,SiteNotesMediasField:Ft,SiteNotesMediaIconField:Lr},data(){return{notes:[],notes_all_count:0,notes_important_count:0,filters:{important:null,date_range:{start:null,end:null,interval:"last30days"}},showProgressBar:!1,texts:{note_date:Z("Date","google-analytics-for-wordpress"),note_title:Z("Site Note","google-analytics-for-wordpress"),note_category:Z("Category","google-analytics-for-wordpress"),show:Z("Show:","google-analytics-for-wordpress"),all:Z("All","google-analytics-for-wordpress"),important:Z("Important","google-analytics-for-wordpress"),add_new:Z("+ Add New Site Note","google-analytics-for-wordpress"),empty:Z("There aren’t any site notes. Go ahead and create one!","google-analytics-for-wordpress"),edit:Z("Edit","google-analytics-for-wordpress"),selectCategories:Z("Select Category","google-analytics-for-wordpress"),save_changes:Z("Save Changes","google-analytics-for-wordpress"),cancel:Z("Cancel","google-analytics-for-wordpress")},selectedNote:null,isSaving:!1,noteCategories:[],selectedCategory:null}},computed:{...C({date:"$_reports/date"}),starClasses(){return"monsterinsights-star"+(this.selectedNote.important?"":"-empty")},categoryChoices(){return this.noteCategories?this.noteCategories.map(s=>(s.text=s.name,s.handle=this.selectCategory,s)):[]}},methods:{toggleImportant(s){let e=s;s.important?e.important=!1:e.important=!0,ue.saveNote(e).then(()=>{this.fetchNotes({},!0)})},importantClass(s){return"monsterinsights-star"+(s.important?"":"-empty")},categoryStyle(s){return{backgroundColor:s.category&&s.category.background_color?s.category.background_color:"#E2EFFB"}},fetchNotes(s,e){e||(this.showProgressBar=!0),this.filters.date_range=this.date;const t=Object.assign({orderby:"id",order:"asc",per_page:"-1",filter:this.filters},s);ue.getNotes(t).then(r=>{this.notes=[],this.notes_all_count=r.pagination.total_published?r.pagination.total_published:0,this.notes_important_count=r.pagination.total_important?r.pagination.total_important:0,this.notes=r.items}).finally(()=>{e||(this.showProgressBar=!1)})},removeFilters(){this.filters.important=null,this.fetchNotes()},filterImportant(){this.filters.important=!0,this.fetchNotes()},AddNewClick(){this.$emit("monsterinsights-site-notes-overview-show-create")},filtersClass(s=null){return s===this.filters.important?"selected":""},toggleStar(){this.selectedNote.important=!this.selectedNote.important},fetchCategories(s){const e=Object.assign({page:1,orderby:"name",order:"asc",select:["name"]},s);ue.getCategories(e).then(t=>{this.noteCategories=t.items})},saveNote(){this.isSaving=!0,ue.saveNote(this.selectedNote).then(()=>{this.fetchNotes(),this.$store.dispatch("$_reports/getReportData","overview").then(()=>{this.$emit("refresh-overview-report")})}).finally(()=>{this.isSaving=!1,this.cancelEdit()})},cancelEdit(){this.selectedNote={id:null,note_title:"",category:null,note_date:null,note_date_ymd:null,important:!1,medias:[]}},selectCategory(s){this.selectedNote.category=s},formatDate(s,e="YYYY-MM-DD"){return x.utc(new Date(s)).format(e)}},watch:{date:{handler(){this.fetchNotes()},deep:!0}},mounted(){this.fetchNotes(),this.fetchCategories()}};var Ar=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"monsterinsights-overview-notes__header"},[t("div",{staticClass:"monsterinsights-overview-notes__header-left"},[t("div",{staticClass:"monsterinsights-overview-notes__header-left-links"},[e._v(" "+e._s(e.texts.show)+" "),t("a",{class:e.filtersClass(),attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.removeFilters()}}},[t("span",{staticClass:"text",domProps:{textContent:e._s(e.texts.all)}}),e._v(" "),t("span",{domProps:{textContent:e._s(`(${e.notes_all_count})`)}})]),t("a",{class:e.filtersClass(!0),attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.filterImportant()}}},[t("span",{staticClass:"text important",domProps:{textContent:e._s(e.texts.important)}}),e._v(" "),t("span",{domProps:{textContent:e._s(`(${e.notes_important_count})`)}})])])]),t("div",{staticClass:"monsterinsights-overview-notes__header-right"},[t("button",{staticClass:"monsterinsights-button",domProps:{textContent:e._s(e.texts.add_new)},on:{click:e.AddNewClick}})])]),t("div",{staticClass:"monsterinsights-overview-notes__body"},[t("div",{staticClass:"monsterinsights-overview-notes__body-list"},[e.showProgressBar?t("div",{staticClass:"monsterinsights-loader"}):e._e(),e.showProgressBar?e._e():t("table",{staticClass:"monsterinsights-overview-notes__body-list-table"},[t("thead",[t("tr",[t("th",{domProps:{textContent:e._s(e.texts.note_date)}}),t("th",{domProps:{textContent:e._s(e.texts.note_title)}}),t("th",{domProps:{textContent:e._s(e.texts.note_category)}})])]),t("tbody",[e._l(e.notes,function(r){return t("fragment",{key:r.id},[t("tr",[t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-notedate"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("div",{staticClass:"monsterinsights-flex-container",staticStyle:{"flex-grow":"1"}},[t("a",{staticClass:"monsterinsights-toggle-note-important",attrs:{href:"#"},on:{click:function(o){return o.preventDefault(),e.toggleImportant(r)}}},[t("div",{class:e.importantClass(r)})]),t("div",{domProps:{textContent:e._s(e.formatDate(r.note_date,"DD MMM"))}})]),t("site-notes-media-icon-field",{attrs:{medias:r.medias}})],1)]),t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-note"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("div",{domProps:{innerHTML:e._s(r.note_title)}}),t("a",{staticClass:"monsterinsights-overview-notes__body-list-table-column-note-edit",attrs:{href:"#"},domProps:{textContent:e._s(e.texts.edit)},on:{click:function(o){o.preventDefault(),e.selectedNote={...r}}}})])]),t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-notecategory"},[r.category&&r.category.background_color?t("span",{staticClass:"monsterinsights-category-with-bg",style:{backgroundColor:r.category.background_color},domProps:{textContent:e._s(r.category?r.category.name:"")}}):t("span",{staticClass:"monsterinsights-category-without-bg",domProps:{textContent:e._s(r.category?r.category.name:"")}})])]),e.selectedNote&&e.selectedNote.id===r.id?t("tr",{staticClass:"edit-row"},[t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-notedate"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("a",{class:e.starClasses,attrs:{href:"#"},on:{click:function(o){return o.preventDefault(),e.toggleStar()}}}),t("site-notes-date-picker-field",{attrs:{disabled:e.isSaving},model:{value:e.selectedNote.note_date_ymd,callback:function(o){e.$set(e.selectedNote,"note_date_ymd",o)},expression:"selectedNote.note_date_ymd"}})],1)]),t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-note"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.selectedNote.note_title,expression:"selectedNote.note_title"}],attrs:{disabled:e.isSaving,rows:"1"},domProps:{value:e.selectedNote.note_title},on:{input:function(o){o.target.composing||e.$set(e.selectedNote,"note_title",o.target.value)}}}),t("site-notes-medias-field",{attrs:{"show-label":!1},model:{value:e.selectedNote.medias,callback:function(o){e.$set(e.selectedNote,"medias",o)},expression:"selectedNote.medias"}})],1),t("div",{staticClass:"monsterinsights-buttons"},[t("button",{staticClass:"monsterinsights-button",domProps:{textContent:e._s(e.texts.save_changes)},on:{click:e.saveNote}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button-secondary",attrs:{href:"#"},domProps:{textContent:e._s(e.texts.cancel)},on:{click:function(o){return o.preventDefault(),e.cancelEdit.apply(null,arguments)}}})])]),t("td",{staticClass:"monsterinsights-overview-notes__body-list-table-column-notecategory"},[t("select",{directives:[{name:"model",rawName:"v-model",value:r.category.id,expression:"note.category.id"}],on:{change:function(o){var n=Array.prototype.filter.call(o.target.options,function(a){return a.selected}).map(function(a){var d="_value"in a?a._value:a.value;return d});e.$set(r.category,"id",o.target.multiple?n:n[0])}}},[t("option",{attrs:{value:"0"},domProps:{textContent:e._s(e.texts.selectCategories)}}),e._l(e.noteCategories,function(o,n){return t("option",{key:n,domProps:{value:o.id,textContent:e._s(o.name)}})})],2)])]):e._e()])}),e.notes.length<=0&&!e.showProgressBar?t("tr",[t("td",{attrs:{colspan:"3"}},[t("div",{staticClass:"monsterinsights-overview-notes__body-list-table-empty",domProps:{textContent:e._s(e.texts.empty)}})])]):e._e()],2)])])])])},Fr=[],Nr=l(Er,Ar,Fr,!1,null,null,null,null);const Ir=Nr.exports,{__:se}=wp.i18n,Br={name:"SiteNotesOverviewCreate",components:{SiteNotesMediasField:Ft,SiteNotesDatePickerField:At},data(){return{note:{id:null,note_title:"",category:{id:0},note_date:null,note_date_ymd:null,important:!1,medias:{}},texts:{note_date:se("Date","google-analytics-for-wordpress"),note_title:se("Site Note","google-analytics-for-wordpress"),note_category:se("Category","google-analytics-for-wordpress"),selectCategories:se("Select Category","google-analytics-for-wordpress"),save_changes:se("Add Site Note","google-analytics-for-wordpress"),site_notes:se("Site Notes","google-analytics-for-wordpress"),add_new:se("Add New","google-analytics-for-wordpress"),edit:se("Edit Note","google-analytics-for-wordpress"),cancel:se("Cancel","google-analytics-for-wordpress")},noteCategories:[],message:"",isEdit:!1,isSaving:!1}},methods:{fetchCategories(s){const e=Object.assign({page:1,orderby:"name",order:"asc",select:["name"]},s);ue.getCategories(e).then(t=>{this.noteCategories=t.items})},saveNote(){this.isSaving=!0,ue.saveNote(this.note).then(()=>{this.$store.dispatch("$_reports/getReportData","overview").then(()=>{this.$emit("refresh-overview-report")})}).finally(()=>{this.isSaving=!1,this.note={id:null,note_title:"",category:{id:0},note_date:null,note_date_ymd:null,important:!1,medias:{}},this.updateDatepickerDate()})},toggleStar(){this.note.important=!this.note.important},showListClick(){this.$emit("monsterinsights-site-notes-overview-show-list")},isPro(){return this.$isPro()},updateDatepickerDate(){this.date.end===""?this.note.note_date_ymd=x().subtract(1,"day").format("YYYY-MM-DD"):this.note.note_date_ymd=this.date.end}},computed:{...C({date:"$_reports/date"}),starClasses(){return"monsterinsights-star"+(this.note.important?"":"-empty")},datepickerEndDate(){return this.date.end}},mounted(){this.fetchCategories(),this.updateDatepickerDate()},watch:{datepickerEndDate(){this.updateDatepickerDate()}}};var Or=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"monsterinsights-overview-notes__header"},[t("div",{staticClass:"monsterinsights-overview-notes__header-left"}),t("div",{staticClass:"monsterinsights-overview-notes__header-right"},[t("div",{staticClass:"monsterinsights-button-group"},[t("button",{staticClass:"monsterinsights-button monsterinsights-button-secondary",domProps:{textContent:e._s(e.texts.cancel)},on:{click:e.showListClick}}),t("button",{staticClass:"monsterinsights-button",domProps:{textContent:e._s(e.texts.save_changes)},on:{click:e.saveNote}})])])]),t("div",{staticClass:"monsterinsights-overview-notes__body"},[t("table",{staticClass:"monsterinsights-site-notes-create__table",class:e.isSaving?"loading":""},[t("thead",[t("tr",[t("th",{domProps:{textContent:e._s(e.texts.note_date)}}),t("th",{domProps:{textContent:e._s(e.texts.note_title)}}),t("th",{domProps:{textContent:e._s(e.texts.note_category)}})])]),t("tbody",[t("tr",[t("td",{staticClass:"monsterinsights-site-notes-create__table-column-notedate"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("a",{class:e.starClasses,attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleStar()}}}),t("site-notes-date-picker-field",{attrs:{disabled:e.isSaving},model:{value:e.note.note_date_ymd,callback:function(r){e.$set(e.note,"note_date_ymd",r)},expression:"note.note_date_ymd"}})],1)]),t("td",{staticClass:"monsterinsights-site-notes-create__table-column-note"},[t("div",{staticClass:"monsterinsights-flex-container"},[t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.note.note_title,expression:"note.note_title"}],attrs:{disabled:e.isSaving,rows:"1"},domProps:{value:e.note.note_title},on:{input:function(r){r.target.composing||e.$set(e.note,"note_title",r.target.value)}}}),t("site-notes-medias-field",{attrs:{"show-label":!1},model:{value:e.note.medias,callback:function(r){e.$set(e.note,"medias",r)},expression:"note.medias"}})],1)]),t("td",{staticClass:"monsterinsights-site-notes-create__table-column-notecategory"},[t("select",{directives:[{name:"model",rawName:"v-model",value:e.note.category.id,expression:"note.category.id"}],on:{change:function(r){var o=Array.prototype.filter.call(r.target.options,function(n){return n.selected}).map(function(n){var a="_value"in n?n._value:n.value;return a});e.$set(e.note.category,"id",r.target.multiple?o:o[0])}}},[t("option",{attrs:{value:"0"},domProps:{textContent:e._s(e.texts.selectCategories)}}),e._l(e.noteCategories,function(r,o){return t("option",{key:o,domProps:{value:r.id,textContent:e._s(r.name)}})})],2)])])])])])])},Ur=[],Hr=l(Br,Or,Ur,!1,null,null,null,null);const Vr=Hr.exports,Yr={name:"SiteNotesOverview",components:{SiteNotesOverviewList:Ir,SiteNotesOverviewCreate:Vr},data(){return{toggle_list_add:!0,texts:{}}},computed:{},methods:{showListClick(){this.toggle_list_add=!0},AddNewClick(){this.toggle_list_add=!1},siteNoteSaved(){this.showListClick(),this.$emit("refresh-overview-report")}},mounted(){}};var jr=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-overview-notes"},[e.toggle_list_add?t("site-notes-overview-list",{on:{"monsterinsights-site-notes-overview-show-create":e.AddNewClick,"refresh-overview-report":e.siteNoteSaved}}):e._e(),e.toggle_list_add?e._e():t("site-notes-overview-create",{on:{"monsterinsights-site-notes-overview-show-list":e.showListClick,"refresh-overview-report":e.siteNoteSaved}})],1)},Zr=[],Wr=l(Yr,jr,Zr,!1,null,null,null,null);const te=Wr.exports,{__:g,sprintf:re}=wp.i18n,zr={name:"ReportOverview",mixins:[hs],components:{ReportOverviewUpsellMobile:Pr,ReportOverviewDatePicker:Nt,ReportOverviewUpsell:_r,ReportListBox:tr,ReportsPdfExport:F,ReportOverviewPieChartApex:Je,ReportInfobox:Et,ReportOverviewLineChartApex:J,SiteNotesOverview:te,ReportSiteSummary:Ws},computed:{...C({overview:"$_reports/overview",date:"$_reports/date",license:"$_license/license",license_network:"$_license/license_network",included_metrics:"$_settings/includedMetrics"}),gaLinks(){return!!this.overview.galinks},infoboxRange(){return this.overview.infobox&&this.overview.infobox.range?this.overview.infobox.range:0},infoboxSessionsData(){return this.infoboxData("sessions")},infoboxPageviewsData(){return this.infoboxData("pageviews")},infoboxDurationData(){return this.infoboxData("duration")},infoboxBounceData(){return this.infoboxData("bounce_rate",!0)},infoboxTotalUsersData(){return this.infoboxData("totalusers")},infoboxNewUsersData(){return this.infoboxData("new_users")},newVsReturningData(){if(this.overview.newvsreturn){let s={values:[this.overview.newvsreturn.new,this.overview.newvsreturn.returning],labels:[g("New","google-analytics-for-wordpress"),g("Returning","google-analytics-for-wordpress")]};return this.date.compareReport&&(this.overview.newvsreturn.compare?s.compare=[this.overview.newvsreturn.compare.new,this.overview.newvsreturn.compare.returning]:s.compare=s.values),s}return{values:[],labels:[]}},devicesData(){if(this.overview.devices){let s={values:[this.overview.devices.desktop,this.overview.devices.tablet,this.overview.devices.mobile],labels:[g("Desktop","google-analytics-for-wordpress"),g("Tablet","google-analytics-for-wordpress"),g("Mobile","google-analytics-for-wordpress")]};return this.date.compareReport&&(this.overview.compare&&this.overview.compare.devices?s.compare=[this.overview.compare.devices.desktop,this.overview.compare.devices.tablet,this.overview.compare.devices.mobile]:s.compare=s.values),s}return{values:[],labels:[]}},countriesData(){let s=[],e=0;return this.overview.countries&&this.overview.countries.forEach(function(t){e++,s.push({number:e+".",text:t.name,right:t.sessions,compare_value:typeof t.compare_sessions>"u"?t.sessions:t.compare_sessions,compare_change:typeof t.compare_change>"u"?100:t.compare_change,page_path:t.page_path,users:t.users,engagement_rate:t.engagement_rate})}),s},referralsData(){let s=[],e=0;return this.overview.referrals&&this.overview.referrals.forEach(function(t){e++,s.push({number:e+".",text:'<img src="https://www.google.com/s2/favicons?domain=http://'+t.url+'" />'+t.url,right:t.sessions,compare_value:typeof t.compare_sessions>"u"?t.sessions:t.compare_sessions,compare_change:typeof t.compare_change>"u"?100:t.compare_change,page_path:t.page_path,users:t.users,engagement_rate:t.engagement_rate})}),s},topPostsData(){let s=[],e=0;return this.overview.toppages&&this.overview.toppages.forEach(function(t){e++;let r=t.hostname?'<a href="'+t.hostname+t.url+'" target="_blank" rel="noreferrer noopener">'+t.title+"</a>":t.title;s.push({number:e+".",text:r,right:t.sessions,compare_value:typeof t.compare_sessions>"u"?t.sessions:t.compare_sessions,compare_change:typeof t.compare_change>"u"?100:t.compare_change,page_path:t.page_path,users:t.users,engagement_rate:t.engagement_rate})}),s}},data(){return{show_overview_notes:!1,chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},displayMetrics:!1,topValues:{sessions:0,pageviews:0,totalusers:0,pageviews_per_user:0,average_session_duration:0,bounce_rate:0,revenue_sales:0,average_revenue_per_user:0,average_revenue_per_session:0,new_users:0,ecommerce_purchases:0,engagement_rate:0,sessions_per_user:0},text_site_notes:g("Site Notes","google-analytics-for-wordpress"),text_overview:g("Overview Report","google-analytics-for-wordpress"),text_sessions:g("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:re(g("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:g("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:re(g("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),text_totalusers_tooltip:re(g("Unique %s Users","google-analytics-for-wordpress"),"<br />"),text_bounce_rate_tooltip:re(g("Bounce %s Percentage","google-analytics-for-wordpress"),"<br />"),text_revenue_sales_tooltip:re(g("Average %s Sales","google-analytics-for-wordpress"),"<br />"),text_revenue_per_user_tooltip:re(g("Average %s Revenue","google-analytics-for-wordpress"),"<br />"),text_new_users_tooltip:re(g("Unique %s Users","google-analytics-for-wordpress"),"<br />"),text_ecommerce_purchases_tooltip:re(g("Unique %s Purchases","google-analytics-for-wordpress"),"<br />"),text_engagement_rate_tooltip:re(g("Sessions %s Percentage","google-analytics-for-wordpress"),"<br />"),text_duration:g("Avg. Session Duration","google-analytics-for-wordpress"),text_bounce:g("Bounce Rate","google-analytics-for-wordpress"),text_revenue_sales:g("Revenue / Sales","google-analytics-for-wordpress"),text_average_revenue_per_user:g("Revenue / User","google-analytics-for-wordpress"),text_average_revenue_per_session:g("Revenue / Session","google-analytics-for-wordpress"),text_new_users:g("New Users","google-analytics-for-wordpress"),text_ecommerce_purchases:g("Purchases","google-analytics-for-wordpress"),text_engagement_rate:g("Engagement Rate","google-analytics-for-wordpress"),text_sessions_per_user:g("Sessions / User","google-analytics-for-wordpress"),text_total_users:g("Total Users","google-analytics-for-wordpress"),text_new_vs_returning:g("New vs. Returning Visitors","google-analytics-for-wordpress"),text_device_breakdown:g("Device Breakdown","google-analytics-for-wordpress"),text_countries:g("Top 10 Countries","google-analytics-for-wordpress"),text_countries_button:g("View Countries Report","google-analytics-for-wordpress"),text_referrals:g("Top 10 Referrals","google-analytics-for-wordpress"),text_referral_button:g("View All Referral Sources","google-analytics-for-wordpress"),text_top_posts:g("Top Posts/Pages","google-analytics-for-wordpress"),text_top_posts_button:g("View Full Posts/Pages Report","google-analytics-for-wordpress"),current_tab:"",text_infobox_tooltip_sessions:g("A session is the browsing session of a single user to your site.","google-analytics-for-wordpress"),text_infobox_tooltip_pageviews:g("A pageview is defined as a view of a page on your site that is being tracked by the Analytics tracking code. Each refresh of a page is also a new pageview.","google-analytics-for-wordpress"),text_infobox_tooltip_average:g("Average session duration is calculated by dividing the total time spent by all users on your site (in seconds) by the number of sessions.","google-analytics-for-wordpress"),text_infobox_tooltip_bounce:g("Bounce Rate represents the percentage of sessions that don't meet the criteria for an engaged session. A session counts as engaged if the visitor completes an event (like a click or form submission), stays on your site for 10 seconds or longer, or views more than one page.","google-analytics-for-wordpress"),text_infobox_tooltip_totalusers:g("The number of distinct tracked users","google-analytics-for-wordpress"),text_pie_tooltip_newvsreturning:g("This graph shows what percent of your user sessions come from new versus repeat visitors.","google-analytics-for-wordpress"),text_pie_tooltip_devices:g("This graph shows the percentage of sessions on your site from different types of devices: traditional desktops/laptops, tablets, and mobile phones.","google-analytics-for-wordpress"),text_countries_tooltip:g("This list shows the top countries based on the number of sessions, showing where your website's visitors are from.","google-analytics-for-wordpress"),text_referral_tooltip:g("This list shows the top websites that send users to your website, known as referral traffic.","google-analytics-for-wordpress"),text_top_posts_tooltip:g("This list shows your website's most viewed posts and pages based on pageviews.","google-analytics-for-wordpress"),text_export_pdf_overview_report:g("Export PDF Overview Report","google-analytics-for-wordpress"),text_waiting_for_your_stats:g("We're Waiting for Your Stats!","google-analytics-for-wordpress"),text_waiting_to_process_your_data:g("We're waiting to process your data which might take up to 24 hours.","google-analytics-for-wordpress"),text_close_site_notes:g("Close Site Notes","google-analytics-for-wordpress"),overviewKey:1,text_infobox_tooltip_newusers:g("Users who interacted with your site for the first time.","google-analytics-for-wordpress"),text_pageviews_per_user:g("Page Views / User","google-analytics-for-wordpress"),text_average_session_duration:g("Session Duration","google-analytics-for-wordpress"),column_countries:g("Countries","google-analytics-for-wordpress"),column_referrals:g("Referrals","google-analytics-for-wordpress"),column_top_pages:g("Landing Page","google-analytics-for-wordpress"),text_edit_metrics:g("Edit Metrics","google-analytics-for-wordpress"),text_up_to_7_metrics:g("Select up to 7 metrics","google-analytics-for-wordpress"),upgrade_text:g("You can view this only in the PRO version","google-analytics-for-wordpress"),upgrade_button_url:this.$getUpgradeUrl("reports","site-summary"),upgrade_button:g("Upgrade","google-analytics-for-wordpress"),displayUpsell:!1}},methods:{infoboxData(s,e=!1){let t={};return this.overview.infobox&&this.overview.infobox[s]&&(t.change=this.overview.infobox[s].prev,t.value=this.overview.infobox[s].value.toString(),typeof this.overview.infobox[s].compare_value>"u"?t.compareValue=t.value:t.compareValue=this.overview.infobox[s].compare_value?this.overview.infobox[s].compare_value.toString():"0",this.overview.infobox[s].prev===0?t.direction="":this.overview.infobox[s].prev>0?(t.direction="up",t.color="green"):(t.direction="down",t.color="red")),e&&(t.direction==="down"?t.color="green":t.color="red"),t},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button--home":"monsterinsights-deactive-tab-button"},sessionsData(){return this.overview.overviewgraph?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.sessions.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.sessions.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.sessions:[]}:{}},pageviewsData(){return this.overview.overviewgraph?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.pageviews.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.pageviews.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.pageviews:[]}:{}},totalusersData(){return this.overview.overviewgraph&&this.overview.overviewgraph.totalusers?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.totalusers.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.totalusers.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.totalusers:[]}:{}},pageviewsPerUserData(){return this.overview.overviewgraph&&this.overview.overviewgraph.pageviews_per_user?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.pageviews_per_user.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.pageviews_per_user.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.pageviews_per_user:[]}:{}},sessionDurationData(){return this.overview.overviewgraph&&this.overview.overviewgraph.average_session_duration?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.average_session_duration.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.average_session_duration.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.average_session_duration:[]}:{}},bounceRateData(){return this.overview.overviewgraph&&this.overview.overviewgraph.bounce_rate?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.bounce_rate.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.bounce_rate.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.bounce_rate:[]}:{}},revenueSalesData(){return this.isPro()!==!0?{}:this.overview.overviewgraph&&this.overview.overviewgraph.revenue_sales?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.revenue_sales.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.revenue_sales.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.revenue_sales:[]}:{}},averageRevenuePerUserData(){return this.isPro()!==!0?{}:this.overview.overviewgraph&&this.overview.overviewgraph.average_revenue_per_user?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.average_revenue_per_user.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.average_revenue_per_user.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.average_revenue_per_user:[]}:{}},averageRevenuePerSessionData(){return this.isPro()!==!0?{}:this.overview.overviewgraph&&this.overview.overviewgraph.average_revenue_per_session?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.average_revenue_per_session.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.average_revenue_per_session.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.average_revenue_per_session:[]}:{}},newUsersData(){return this.overview.overviewgraph&&this.overview.overviewgraph.new_users?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.new_users.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.new_users.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.new_users:[]}:{}},ecommercePurchasesData(){return this.isPro()!==!0?{}:this.overview.overviewgraph&&this.overview.overviewgraph.ecommerce_purchases?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.ecommerce_purchases.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.ecommerce_purchases.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.ecommerce_purchases:[]}:{}},engagementRateData(){return this.overview.overviewgraph&&this.overview.overviewgraph.engagement_rate?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.engagement_rate.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.engagement_rate.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.engagement_rate:[]}:{}},sessionsPerUserData(){return this.overview.overviewgraph&&this.overview.overviewgraph.sessions_per_user?{labels:this.overview.overviewgraph.labels,data:this.overview.overviewgraph.sessions_per_user.datapoints,timestamps:this.overview.overviewgraph.timestamps,trend:this.overview.overviewgraph.sessions_per_user.trendpoints,notes:this.overview.overviewgraph.notes,compare:this.overview.compare?this.overview.compare.overviewgraph.sessions_per_user:[]}:{}},showChart(){let s=!0;return this.overview.overviewgraph&&this.overview.overviewgraph.count===0&&(s=!1),s},toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},updateMetrics(s){if(this.canNotDisplayMetric(s))return;let e=this.included_metrics.filter(t=>t.checked).map(t=>t.id).join(",");e.length<1&&(e="sessions"),this.$store.dispatch("$_settings/updateIncludedMetrics",{selected_metrics:e}).then(t=>{if(t.success){if(this.$store.commit("$_reports/ENABLE_REPORT_RELOAD"),this.$mi_success_toast({}),this.$store.dispatch("$_reports/getReportData","overview"),this.included_metrics.length>0){const r=this.included_metrics.filter(o=>o.checked);this.current_tab=r[0].id}this.displayMetrics=!1,this.$store.dispatch("$_reports/maybeCloseSwal"),this.$store.commit("$_reports/DISABLE_REPORT_RELOAD")}else this.$mi_error_toast({title:t.error})})},isMetricIsAvailable(s){const e=this.included_metrics.find(t=>t.id===s);return this.overview.overviewgraph&&this.overview.overviewgraph[s]&&(this.topValues[s]=this.overview.overviewgraph[s].total),this.current_tab===""&&e.checked===!0&&(this.current_tab=e.id),e.isPro===!0&&this.isPro()!==!0?!1:e?e.checked:!1},toggleMetricsSettings(){this.displayMetrics=!this.displayMetrics},isPro(){return this.$isPro()},canNotDisplayMetric(s){return this.included_metrics.filter(t=>t.checked).map(t=>t.id).length>=7&&s.checked===!1?!0:!(s.isPro===!1||this.isPro()===!0)},metricItemClass(s){return this.canNotDisplayMetric(s)?"monsterinsights-metric-settings-item--disabled":"monsterinsights-metric-settings-item"},iconName(s){return this.current_tab===s?"report-"+s+"-active":`report-${s}`},canClickMetric(s,e){this.canNotDisplayMetric(s)===!0&&e.preventDefault(),s.isPro===!0&&this.isPro()===!1&&(this.displayUpsell=!0,this.upgrade_button_url=this.$getUpgradeUrl("reports",`overviewchart-${s.id}`),e.preventDefault())},handleClickOutside(s){(s.target.className==="monsterinsights-metric-settings-item"||s.target.className==="monsterinsights-metric-settings"||this.$refs.upSellContainer&&!this.$refs.upSellContainer.contains(s.target)&&this.displayUpsell===!0)&&(this.displayUpsell=!1,this.upgrade_button_url=this.$getUpgradeUrl("reports","overviewchart"))}},mounted(){this.$store.dispatch("$_settings/getUserIncludedMetrics"),this.$store.dispatch("$_reports/getReportData","overview"),document.addEventListener("click",this.handleClickOutside)},updated(){this.$route.path==="/top-landing-pages"&&this.$nextTick(()=>{const s=document.getElementById("top-landing-pages");s&&s.scrollIntoView({behavior:"smooth"})})},beforeDestroy(){document.removeEventListener("click",this.handleClickOutside)}};var Kr=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-overview"},[t("report-overview-upsell-mobile"),t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_overview)}}),t("report-overview-date-picker"),t("reports-pdf-export")],1),e.showChart()?t("div",{staticClass:"monsterinsights-report-tabs monsterinsights-report-row",attrs:{id:"monsterinsights-report-overview"}},[t("div",{staticClass:"monsterinsights-report-tabs-navigation--home"},[e.isMetricIsAvailable("sessions")?t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("sessions"),expression:"iconName('sessions')"}]}),t("span",{domProps:{textContent:e._s(e.text_sessions)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.sessions))+" ")])]):e._e(),e.isMetricIsAvailable("pageviews")?t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("pageviews"),expression:"iconName('pageviews')"}]}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.pageviews))+" ")])]):e._e(),e.isMetricIsAvailable("totalusers")?t("button",{class:e.activeTabButtonClass("totalusers"),on:{click:function(r){return e.switchTab("totalusers")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("totalusers"),expression:"iconName('totalusers')"}]}),t("span",{domProps:{textContent:e._s(e.text_total_users)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.totalusers))+" ")])]):e._e(),e.isMetricIsAvailable("pageviews_per_user")?t("button",{class:e.activeTabButtonClass("pageviews_per_user"),on:{click:function(r){return e.switchTab("pageviews_per_user")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("pageviews_per_user"),expression:"iconName('pageviews_per_user')"}]}),t("span",{domProps:{textContent:e._s(e.text_pageviews_per_user)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.pageviews_per_user))+" ")])]):e._e(),e.isMetricIsAvailable("average_session_duration")?t("button",{class:e.activeTabButtonClass("average_session_duration"),on:{click:function(r){return e.switchTab("average_session_duration")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("average_session_duration"),expression:"iconName('average_session_duration')"}]}),t("span",{domProps:{textContent:e._s(e.text_average_session_duration)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.average_session_duration))+" ")])]):e._e(),e.isMetricIsAvailable("bounce_rate")?t("button",{class:e.activeTabButtonClass("bounce_rate"),on:{click:function(r){return e.switchTab("bounce_rate")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("bounce_rate"),expression:"iconName('bounce_rate')"}]}),t("span",{domProps:{textContent:e._s(e.text_bounce)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.topValues.bounce_rate)+"% ")])]):e._e(),e.isMetricIsAvailable("revenue_sales")?t("button",{class:e.activeTabButtonClass("revenue_sales"),on:{click:function(r){return e.switchTab("revenue_sales")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("revenue_sales"),expression:"iconName('revenue_sales')"}]}),t("span",{domProps:{textContent:e._s(e.text_revenue_sales)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.revenue_sales))+" ")])]):e._e(),e.isMetricIsAvailable("average_revenue_per_user")?t("button",{class:e.activeTabButtonClass("average_revenue_per_user"),on:{click:function(r){return e.switchTab("average_revenue_per_user")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("average_revenue_per_user"),expression:"iconName('average_revenue_per_user')"}]}),t("span",{domProps:{textContent:e._s(e.text_average_revenue_per_user)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.average_revenue_per_user))+" ")])]):e._e(),e.isMetricIsAvailable("average_revenue_per_session")?t("button",{class:e.activeTabButtonClass("average_revenue_per_session"),on:{click:function(r){return e.switchTab("average_revenue_per_session")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("average_revenue_per_session"),expression:"iconName('average_revenue_per_session')"}]}),t("span",{domProps:{textContent:e._s(e.text_average_revenue_per_session)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.average_revenue_per_session))+" ")])]):e._e(),e.isMetricIsAvailable("new_users")?t("button",{class:e.activeTabButtonClass("new_users"),on:{click:function(r){return e.switchTab("new_users")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("new_users"),expression:"iconName('new_users')"}]}),t("span",{domProps:{textContent:e._s(e.text_new_users)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.new_users))+" ")])]):e._e(),e.isMetricIsAvailable("ecommerce_purchases")?t("button",{class:e.activeTabButtonClass("ecommerce_purchases"),on:{click:function(r){return e.switchTab("ecommerce_purchases")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("ecommerce_purchases"),expression:"iconName('ecommerce_purchases')"}]}),t("span",{domProps:{textContent:e._s(e.text_ecommerce_purchases)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.ecommerce_purchases))+" ")])]):e._e(),e.isMetricIsAvailable("engagement_rate")?t("button",{class:e.activeTabButtonClass("engagement_rate"),on:{click:function(r){return e.switchTab("engagement_rate")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("engagement_rate"),expression:"iconName('engagement_rate')"}]}),t("span",{domProps:{textContent:e._s(e.text_engagement_rate)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.topValues.engagement_rate)+"% ")])]):e._e(),e.isMetricIsAvailable("sessions_per_user")?t("button",{class:e.activeTabButtonClass("sessions_per_user"),on:{click:function(r){return e.switchTab("sessions_per_user")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:e.iconName("sessions_per_user"),expression:"iconName('sessions_per_user')"}]}),t("span",{domProps:{textContent:e._s(e.text_sessions_per_user)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.$formatNumber(e.topValues.sessions_per_user))+" ")])]):e._e(),t("button",{staticClass:"monsterinsights-settings-button",on:{click:function(r){return e.toggleMetricsSettings()}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-settings",expression:"'report-settings'"}]})]),e.displayMetrics?t("div",{ref:"upSellContainer",staticClass:"monsterinsights-metric-settings"},[t("div",{staticClass:"monsterinsights-metric-settings__header"},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-settings-close",expression:"'report-settings-close'"}],on:{click:function(r){return e.toggleMetricsSettings()}}})]),t("h2",{staticClass:"monsterinsights-metric-settings__title"},[e._v(" "+e._s(e.text_edit_metrics)+" ")]),t("span",{staticClass:"monsterinsights-metric-settings__description"},[e._v(" "+e._s(e.text_up_to_7_metrics)+" ")]),e._l(e.included_metrics,function(r,o){return t("div",{key:o,staticClass:"metric-item"},[t("div",{class:e.metricItemClass(r)},[t("input",{directives:[{name:"model",rawName:"v-model",value:r.checked,expression:"metric.checked"}],attrs:{id:r.id,type:"checkbox"},domProps:{checked:Array.isArray(r.checked)?e._i(r.checked,null)>-1:r.checked},on:{change:[function(n){var a=r.checked,d=n.target,p=!!d.checked;if(Array.isArray(a)){var m=null,b=e._i(a,m);d.checked?b<0&&e.$set(r,"checked",a.concat([m])):b>-1&&e.$set(r,"checked",a.slice(0,b).concat(a.slice(b+1)))}else e.$set(r,"checked",p)},function(n){return e.updateMetrics(r)}],click:function(n){return e.canClickMetric(r,n)}}}),t("label",{attrs:{for:r.id}},[t("span",{staticClass:"checkbox"}),e._v(" "+e._s(r.name)+" ")])])])}),e.displayUpsell?t("div",{staticClass:"monsterinsights-reports-metric-upsell"},[t("div",{staticClass:"monsterinsights-lite-datepicker"},[t("span",{staticClass:"dashicons-before dashicons-lock"}),t("div",{staticClass:"monsterinsights-reports-metric-upsell__text",domProps:{textContent:e._s(e.upgrade_text)}}),t("a",{staticClass:"monsterinsights-button monsterinsights-button monsterinsights-button-small",attrs:{target:"_blank",href:e.upgrade_button_url},domProps:{textContent:e._s(e.upgrade_button)}})])]):e._e()],2):e._e()]),t("div",{staticClass:"monsterinsights-report-tabs-content"},[e.current_tab==="sessions"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="totalusers"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.totalusersData(),tooltipDescriptor:e.text_totalusers_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews_per_user"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsPerUserData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="average_session_duration"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.sessionDurationData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="bounce_rate"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.bounceRateData(),tooltipDescriptor:e.text_bounce_rate_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="revenue_sales"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.revenueSalesData(),tooltipDescriptor:e.text_revenue_sales_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="average_revenue_per_user"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.averageRevenuePerUserData(),tooltipDescriptor:e.text_revenue_per_user_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="average_revenue_per_session"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.averageRevenuePerSessionData(),tooltipDescriptor:e.text_revenue_per_user_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="new_users"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.newUsersData(),tooltipDescriptor:e.text_new_users_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="ecommerce_purchases"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.ecommercePurchasesData(),tooltipDescriptor:e.text_ecommerce_purchases_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="engagement_rate"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.engagementRateData(),tooltipDescriptor:e.text_engagement_rate_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="sessions_per_user"?t("div",[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.sessionsPerUserData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.overview.show_chart_overlay?t("div",{staticClass:"monsterinsights-report-tabs-content-overlay"},[t("div",{staticClass:"monsterinsights-report-tabs-content-overlay-box"},[t("div",{staticClass:"monsterinsights-report-tabs-content-overlay-box-icon"},[t("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.40686 12.8342C1.88862 12.8197 1.3904 12.5195 1.12016 11.9178C0.844361 11.305 0.925545 10.7056 1.38262 10.2007C1.82635 9.71029 2.3924 9.56127 3.02296 9.76145C3.23204 9.82817 3.36216 9.80371 3.52563 9.65913C4.85793 8.48142 6.19801 7.31149 7.54142 6.1449C7.70157 6.00589 7.76051 5.87799 7.72158 5.65557C7.567 4.77702 8.09859 4.01634 8.96714 3.83507C9.79343 3.66158 10.5897 4.16091 10.7921 5.02057C10.8555 5.28747 10.9834 5.39089 11.2236 5.47097C12.1933 5.79681 13.1564 6.14045 14.1195 6.48631C14.3019 6.55193 14.4287 6.54637 14.5877 6.40402C15.0314 6.00922 15.563 5.91358 16.1179 6.11265C16.3259 6.18716 16.4404 6.14045 16.5872 6.01478L17.0591 5.60925C18.1158 4.7009 19.1722 3.79274 20.2438 2.90313C20.424 2.75363 20.4056 2.61111 20.3856 2.4555L20.3856 2.45544L20.3856 2.45538L20.3856 2.45532C20.3816 2.42462 20.3776 2.39341 20.3751 2.36154C20.305 1.48965 20.8221 0.796812 21.6629 0.636669C22.4491 0.487648 23.2142 0.94472 23.4478 1.72653C23.6213 2.30815 23.5056 2.84307 23.0786 3.28124C22.656 3.71385 22.1333 3.89735 21.5461 3.70495C21.2603 3.61154 21.0801 3.66269 20.8622 3.85286C19.7067 4.85931 18.539 5.85242 17.369 6.84107C17.1867 6.99565 17.1199 7.12466 17.1622 7.386C17.3023 8.26567 16.6962 9.03969 15.811 9.1776C15.0158 9.30104 14.2218 8.74054 14.065 7.91536C14.025 7.70629 13.9393 7.61954 13.7514 7.55393C12.7449 7.20918 11.7429 6.85331 10.7409 6.49743C10.5786 6.4396 10.4607 6.43182 10.3072 6.5675C9.85348 6.97119 9.31522 7.07906 8.74138 6.8711C8.56789 6.80771 8.46892 6.84107 8.34436 6.95006C6.96647 8.16002 5.58858 9.36887 4.2029 10.5699C4.06327 10.691 4.08094 10.7961 4.10685 10.9501L4.10837 10.9592C4.27852 11.9712 3.55788 12.8353 2.41131 12.8353L2.40686 12.8342ZM20.0248 14.9084V7.8922C20.0248 7.10817 20.235 6.90132 21.0235 6.90132C21.2299 6.90132 21.4361 6.90089 21.6424 6.90047H21.6425C22.1064 6.89953 22.57 6.89858 23.0342 6.90243C23.5791 6.90688 23.8515 7.16266 23.8515 7.70091C23.856 12.5163 23.856 17.3317 23.8515 22.146C23.8515 22.6687 23.5858 22.9189 23.062 22.9233C22.3235 22.93 21.5851 22.9289 20.8455 22.9233C20.2639 22.9189 20.027 22.6687 20.0259 22.0492C20.0235 20.3493 20.024 18.6487 20.0244 16.9484V16.9483V16.9482C20.0246 16.2682 20.0248 15.5883 20.0248 14.9084ZM11.185 18.7474L11.1847 16.9545H11.1858C11.1858 16.3521 11.1861 15.7498 11.1863 15.1475V15.1456V15.1455C11.1868 13.9418 11.1873 12.7382 11.1858 11.5341C11.1858 10.9058 10.9456 10.6522 10.3317 10.6456C9.62 10.6367 8.90826 10.6367 8.19651 10.6456C7.60265 10.6533 7.34131 10.9114 7.34131 11.5008C7.33797 15.1284 7.33797 18.755 7.34131 22.3827C7.34131 22.9632 7.61933 23.2323 8.20207 23.2368C8.90047 23.2423 9.59776 23.2412 10.2962 23.2368C10.9312 23.2334 11.1847 22.9765 11.1847 22.3337C11.1855 21.1386 11.1852 19.9429 11.185 18.7474ZM17.5182 17.893L17.5184 19.3144V19.3198C17.5187 20.2666 17.5189 21.2136 17.5182 22.1601C17.5182 22.8441 17.2858 23.0776 16.6163 23.0787L16.268 23.0793H16.2676C15.6862 23.0804 15.1044 23.0815 14.5233 23.0787C13.9573 23.0754 13.6848 22.8218 13.6826 22.2558C13.6759 19.3554 13.6759 16.4551 13.6826 13.5547C13.6837 12.9942 13.9628 12.7395 14.53 12.7362C15.2417 12.7317 15.9524 12.7317 16.6641 12.7362C17.258 12.7395 17.516 12.992 17.5171 13.5836C17.5195 14.6098 17.519 15.6355 17.5186 16.6614V16.6615C17.5184 17.072 17.5182 17.4824 17.5182 17.893ZM1.00389 19.2723C1.00426 19.5411 1.00463 19.8098 1.00463 20.0785L1.00352 20.0774C1.00352 20.3295 1.0031 20.5818 1.00268 20.8341C1.00173 21.4019 1.00078 21.9699 1.00463 22.5373C1.00797 23.099 1.27265 23.3759 1.8287 23.3837C2.55268 23.3937 3.27777 23.3925 4.00174 23.3837C4.58337 23.377 4.85028 23.1056 4.85139 22.5207C4.85584 20.8937 4.85584 19.2678 4.85139 17.6408C4.85028 17.0758 4.59116 16.8111 4.02732 16.8034C3.30334 16.7945 2.57826 16.7956 1.85428 16.8034C1.26375 16.81 1.00797 17.0703 1.00463 17.6586C1.00241 18.1969 1.00315 18.7347 1.00389 19.2723Z",fill:"#FEFEFE"}})])]),t("h3",{domProps:{textContent:e._s(e.text_waiting_for_your_stats)}}),t("p",{domProps:{textContent:e._s(e.text_waiting_to_process_your_data)}})])]):e._e()]),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]):e._e(),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),t("ReportSiteSummary"),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-infobox-row",attrs:{id:"monsterinsights-report-infobox"}},[t("report-infobox",{attrs:{title:e.text_sessions,value:e.$formatNumber(e.infoboxSessionsData.value),change:e.infoboxSessionsData.change,color:e.infoboxSessionsData.color,direction:e.infoboxSessionsData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_sessions,"compare-value":e.$formatNumber(e.infoboxSessionsData.compareValue)}}),t("report-infobox",{attrs:{title:e.text_pageviews,value:e.$formatNumber(e.infoboxPageviewsData.value),change:e.infoboxPageviewsData.change,color:e.infoboxPageviewsData.color,direction:e.infoboxPageviewsData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_pageviews,"compare-value":e.$formatNumber(e.infoboxPageviewsData.compareValue)}}),t("report-infobox",{attrs:{title:e.text_duration,value:e.infoboxDurationData.value,change:e.infoboxDurationData.change,color:e.infoboxDurationData.color,direction:e.infoboxDurationData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_average,"compare-value":e.infoboxDurationData.compareValue}}),t("report-infobox",{attrs:{title:e.text_bounce,value:e.infoboxBounceData.value,change:e.infoboxBounceData.change,color:e.infoboxBounceData.color,direction:e.infoboxBounceData.direction,days:e.infoboxRange,tooltip:e.text_infobox_tooltip_bounce,"compare-value":e.infoboxBounceData.compareValue}})],1),t("report-overview-upsell"),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex"},[t("report-overview-pie-chart-apex",{attrs:{id:"newvsreturning",chartData:e.newVsReturningData,title:e.text_new_vs_returning,tooltip:e.text_pie_tooltip_newvsreturning,width:200,height:200,size:50}}),t("report-overview-pie-chart-apex",{attrs:{id:"devices",chartData:e.devicesData,title:e.text_device_breakdown,tooltip:e.text_pie_tooltip_devices,width:200,height:200,size:50}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-report-2-columns"},[t("report-list-box",{attrs:{id:"monsterinsights-report-top-countries",reportname:"countries",title:e.text_countries,rows:e.countriesData,main_column_title:e.column_countries,tooltip:e.text_countries_tooltip}},[e.gaLinks?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.overview.galinks.countries,target:"_blank"},domProps:{textContent:e._s(e.text_countries_button)},slot:"button"}):e._e()]),t("report-list-box",{attrs:{id:"monsterinsights-report-top-referrals",reportname:"referrals",title:e.text_referrals,rows:e.referralsData,main_column_title:e.column_referrals,tooltip:e.text_referral_tooltip}},[e.gaLinks?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.overview.galinks.referrals,target:"_blank"},domProps:{textContent:e._s(e.text_referral_button)},slot:"button"}):e._e()])],1),t("div",{staticClass:"monsterinsights-report-row",attrs:{id:"top-landing-pages"}},[t("report-list-box",{attrs:{tooltip:e.text_top_posts_tooltip,title:e.text_top_posts,rows:e.topPostsData,reportname:"toppages",main_column_title:e.column_top_pages}},[e.gaLinks?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.overview.galinks.topposts,target:"_blank"},domProps:{textContent:e._s(e.text_top_posts_button)},slot:"button"}):e._e()])],1)],1)},qr=[],Gr=l(zr,Kr,qr,!1,null,null,null,null);const at=Gr.exports,{__:nt}=wp.i18n,Jr={name:"ReportTableBox",components:{ReportsPagination:Qe,SettingsInfoTooltip:Pe},props:{title:String,tooltip:String,rows:Array,headers:Array,button:Object,emptytext:String,error:String,mobileWidth:{default:783,type:Number},icon:{default:"",type:String},compareSingleColumns:{type:Array,default:()=>[0]},hasCompareChangeColumn:{type:Boolean,default:!1},compareChangeOpposite:{type:Array,default:()=>[]}},data(){return{paginate:!1,limit:10,text_show:nt("Show","google-analytics-for-wordpress"),activeRow:"",isMobile:!1,resizing:!1}},computed:{...C({mobileTableExpanded:"$_reports/mobileTableExpanded",date:"$_reports/date"}),mobileHeaders(){let s=[];return this.headers.forEach(function(e,t){t>0&&s.push(e)}),s},emptyTable(){let s=[[this.emptytext]];for(;s.length<10;)s.push(["&nbsp;"]);return s},componentClass(){let s="monsterinsights-table-box";return this.isMobile&&(s+=" monsterinsights-table-box-mobile"),this.title||(s+=" monsterinsights-table-box-no-title"),this.date.compareReport&&(s+=" monsterinsights-table-box-compare-report"),this.paginate&&(s+=" monsterinsights-has-pagination"),s},tableRows(){let s=this.rows.filter(()=>!0);if(s.length<10){let e=[],t=0;for(;t<this.headers.length;){let r=[""];t>0&&this.date.compareReport&&(r.push(""),this.hasCompareChangeColumn&&r.push("")),e.push(r),t++}for(;s.length<10;)s.push(e)}return s=s.slice(0,this.limit),s},titleClass(){let s="monsterinsights-report-title ";return this.icon&&(s+=this.icon),this.paginate&&(s+=" monsterinsights-has-pagination"),s},compareDateColumns(){if(!this.date.compareReport)return[];let s=[];return this.headers.forEach((e,t)=>{this.compareSingleColumns.includes(t)||(s.push(this.date.intervalText,this.date.intervalCompareText),this.hasCompareChangeColumn&&s.push(nt("Change%","google-analytics-for-wordpress")))}),s}},methods:{hasButtonSlot(){return this.rows.length>10&&(this.paginate=!0),this.$slots.button},getButtonClass(s){let e="monsterinsights-button";return s===this.limit&&(e+=" monsterinsights-selected-interval"),e},cellText(s,e,t){return s===""?"&nbsp;":e===0?'<span class="monsterinsights-reports-list-count">'+(t+1)+'.</span><span class="monsterinsights-reports-list-title">'+s+"</span>":s},rowClass(s){let e="monsterinsights-table-list-item";return(this.mobileTableExpanded||this.activeRow===s)&&window.innerWidth<this.mobileWidth&&(e+=" monsterinsights-table-list-item-active"),this.tableRows[s][0]===""&&(e+=" monsterinsights-table-list-item-empty"),e},showMobileRow(s,e){return window.innerWidth<this.mobileWidth&&e>0&&(this.mobileTableExpanded||s===this.activeRow)},handleResize(){this.resizing||(this.resizing=!0,window.requestAnimationFrame?window.requestAnimationFrame(this.resizeCallback):setTimeout(this.resizeCallback,66))},resizeCallback(){this.isMobile=window.innerWidth<this.mobileWidth,this.resizing=!1},cellClass(s){return s++,"monsterinsights-table-cell-"+s},toggleMobileTables(s){if(this.mobileTableExpanded)return!1;this.activeRow=this.activeRow===s?"":s},changeLimit(s){this.limit=s},getRowSpan(s){return this.compareSingleColumns.includes(s)&&this.date.compareReport?2:1},getColSpan(s){return!this.compareSingleColumns.includes(s)&&this.date.compareReport?this.hasCompareChangeColumn?3:2:1}},mounted(){window.addEventListener("resize",this.handleResize),this.handleResize()},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)}};var Qr=function(){var e=this,t=e._self._c;return t("div",{class:e.componentClass},[e.title?t("h3",{class:e.titleClass,domProps:{textContent:e._s(e.title)}}):e._e(),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e(),t("div",{staticClass:"monsterinsights-table-box-list monsterinsights-table-box-table"},[e.error?t("div",[t("h3",{domProps:{textContent:e._s(e.error)}})]):e.rows.length>0?t("table",{class:{"monsterinsights-report-box-table-compare-report":e.date.compareReport}},[e.headers.length>0?t("thead",{class:{"monsterinsights-report-table-head-compare-report":e.date.compareReport}},[t("tr",{staticClass:"monsterinsights-report-box-table-head-columns"},e._l(e.headers,function(r,o){return t("th",{key:o,attrs:{rowspan:e.getRowSpan(o),colspan:e.getColSpan(o)},domProps:{textContent:e._s(r)}})}),0),e.date.compareReport?t("tr",{class:{"monsterinsights-report-table-head-date-range-row":!0,"monsterinsights-report-table-head-date-range-row-has-change-col":e.hasCompareChangeColumn}},e._l(e.compareDateColumns,function(r,o){return t("th",{key:o},[e._v(" "+e._s(r)+" ")])}),0):e._e()]):e._e(),t("tbody",{class:{"monsterinsights-report-table-body-compare-report":e.date.compareReport}},e._l(e.tableRows,function(r,o){return t("tr",{key:o,class:e.rowClass(o),on:{click:function(n){return e.toggleMobileTables(o)}}},e._l(e.tablePrepareColumnItems(r),function(n,a){return t("td",{key:a+"td"+o,class:e.cellClass(a)},[e.tableIsCurrentColumn(a)?[e.showMobileRow(o,a)?t("div",{staticClass:"monsterinsights-table-mobile-heading",domProps:{textContent:e._s(e.headers[a])}}):e._e(),t("div",{staticClass:"monsterinsights-table-item-content",domProps:{innerHTML:e._s(e.cellText(n,a,o))}})]:e._e(),e.tableIsPreviousColumn(a)?[t("span",{staticClass:"monsterinsights-table-item-compare-report-content",domProps:{innerHTML:e._s(n)}})]:e._e(),e.tableIsChangeColumn(n,a)?[t("div",{staticClass:"monsterinsights-table-item-compare-report-change",domProps:{innerHTML:e._s(e.tableCompareChange(n,a))}})]:e._e()],2)}),0)}),0)]):t("div",{staticClass:"monsterinsights-table-no-data"},[t("h3",{domProps:{textContent:e._s(e.emptytext)}})])]),e.hasButtonSlot()||e.paginate?t("div",{staticClass:"monsterinsights-table-box-footer"},[e._t("button"),e.paginate?t("reports-pagination",{attrs:{limit:e.limit,length:e.rows.length},on:{updated:e.changeLimit}}):e._e()],2):e._e()],1)},Xr=[],eo=l(Jr,Qr,Xr,!1,null,null,null,null);const _e=eo.exports,to={name:"MiLiteReportsDatePicker",extends:Nt},so=null,ro=null;var oo=l(to,so,ro,!1,null,null,null,null);const I=oo.exports,B={computed:{...C({license:"$_license/license",license_network:"$_license/license_network"}),licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type}},methods:{showUpsell(){return this.$isPro()?this.licenseLevel==="basic":!0},showUpsellPlus(){return this.$isPro()?this.licenseLevel==="plus"||this.licenseLevel==="basic":!0}}},{__:_,sprintf:lt}=wp.i18n,io={name:"ReportEcommerce",components:{ReportsDatePicker:I,ReportUpsellOverlay:E,ReportTableBox:_e,ReportInfobox:Et,ReportsPdfExport:F,ReportOverviewLineChartApex:J,SiteNotesOverview:te},mixins:[B],data(){return{current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},text_sessions:_("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:lt(_("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:_("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:lt(_("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),text_ecommerce:_("eCommerce Report","google-analytics-for-wordpress"),text_export_pdf_ecommerce_report:_("Export PDF eCommerce Report","google-analytics-for-wordpress"),text_conversion_rate:_("Conversion Rate","google-analytics-for-wordpress"),text_transactions:_("Transactions","google-analytics-for-wordpress"),text_revenue:_("Revenue","google-analytics-for-wordpress"),text_average_order_value:_("Avg. Order Value","google-analytics-for-wordpress"),text_top_products:_("Top Products","google-analytics-for-wordpress"),text_empty_top_products:_("No product sales tracked during this time period.","google-analytics-for-wordpress"),top_products_headers:[_("Product Name","google-analytics-for-wordpress"),_("Quantity","google-analytics-for-wordpress"),_("% of Sales","google-analytics-for-wordpress"),_("Total Revenue","google-analytics-for-wordpress")],text_top_conversions:_("Top Conversion Sources","google-analytics-for-wordpress"),text_empty_top_conversions:_("No conversion sources tracked during this time period.","google-analytics-for-wordpress"),text_add_to_cart:_("Total Add to Carts","google-analytics-for-wordpress"),text_new_customers:_("New Customers","google-analytics-for-wordpress"),text_abandoned_checkout:_("Abandoned Checkouts","google-analytics-for-wordpress"),text_removed_from_cart:_("Total Removed from Cart","google-analytics-for-wordpress"),text_time_to_purchase:_("Time to Purchase","google-analytics-for-wordpress"),text_sessions_to_purchase:_("Sessions to Purchase","google-analytics-for-wordpress"),text_empty_generic:_("No data for this time period.","google-analytics-for-wordpress"),time_to_purchase_headers:[_("Days","google-analytics-for-wordpress"),_("Transactions","google-analytics-for-wordpress"),_("% of Total","google-analytics-for-wordpress")],sessions_to_purchase_headers:[_("Sessions","google-analytics-for-wordpress"),_("Transactions","google-analytics-for-wordpress"),_("% of Total","google-analytics-for-wordpress")],text_conversion_rate_tooltip:_("The percentage of website sessions resulting in a transaction.","google-analytics-for-wordpress"),text_transactions_tooltip:_("The number of orders on your website.","google-analytics-for-wordpress"),text_revenue_tooltip:_("The total amount of revenue you made from all your orders.","google-analytics-for-wordpress"),text_average_order_value_tooltip:_("The average amount spent on each order made on your website.","google-analytics-for-wordpress"),text_top_products_tooltip:_("This list shows the top selling products on your website.","google-analytics-for-wordpress"),text_top_conversions_tooltip:_("This list shows the top referral websites in terms of product revenue.","google-analytics-for-wordpress"),text_add_to_cart_tooltip:_("The number of times products on your site were added to the cart.","google-analytics-for-wordpress"),text_removed_from_cart_tooltip:_("The number of times products on your site were removed from the cart.","google-analytics-for-wordpress"),text_new_customers_tooltip:_("The percentage of first time purchasers.","google-analytics-for-wordpress"),text_abandoned_checkout_tooltip:_("The percentage of checkouts that do not result in a transaction.","google-analytics-for-wordpress"),text_time_to_purchase_tooltip:_("This list shows how many days from first visit it took users to purchase products from your site.","google-analytics-for-wordpress"),text_sessions_to_purchase_tooltip:_("This list shows the number of sessions it took users before they purchased a product from your website.","google-analytics-for-wordpress"),text_top_products_button:_("View Full Top Products Report","google-analytics-for-wordpress"),text_conversions_button:_("View Top Conversions Sources Report","google-analytics-for-wordpress"),text_days_button:_("View Time to Purchase Report","google-analytics-for-wordpress"),text_sessions_button:_("View Session to Purchase Report","google-analytics-for-wordpress"),show_overview_notes:!1,text_site_notes:_("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:_("Close Site Notes","google-analytics-for-wordpress")}},computed:{ecommerce(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_ecommerce"]:this.$store.getters["$_reports/ecommerce"]},topConversionsHeaders(){return[_("Sources","google-analytics-for-wordpress"),_("Total Users","google-analytics-for-wordpress"),_("% of Users","google-analytics-for-wordpress"),_("Revenue","google-analytics-for-wordpress")]},infoboxConversionRateData(){return this.infoboxDataPercentage("conversionrate")},infoboxTransactionsData(){return this.infoboxData("transactions")},infoboxRevenueData(){return this.infoboxData("revenue")},infoboxOrderValueData(){return this.infoboxData("ordervalue")},infoboxAddToCartData(){return this.infoboxData("addtocart")},infoboxRemFromCartData(){return this.infoboxData("remfromcart")},infoboxRange(){return this.ecommerce.infobox&&this.ecommerce.infobox.range?this.ecommerce.infobox.range:0},topProductsrows(){let s=[];return this.ecommerce.products&&this.ecommerce.products.forEach(e=>{s.push([this.tableFormatRowData(e.name),this.tableFormatRowData(e.quantity,"",this.$formatNumber),this.tableFormatRowData(e.percent,"%",this.$formatNumber),this.tableFormatRowData(e.revenue,"",this.$formatNumber)])}),s},topConversionsrows(){let s=[];return this.ecommerce.conversions&&this.ecommerce.conversions.forEach(e=>{s.push([['<img class="monsterinsights-reports-referral-icon"  src="https://www.google.com/s2/favicons?domain='+e.url+'" alt="referral-icon" /> '+e.url],this.tableFormatRowData(e.sessions,"",this.$formatNumber),this.tableFormatRowData(e.percent,"%",this.$formatNumber),this.tableFormatRowData(e.revenue,"",this.$formatNumber)])}),s},timeToPurchaseRows(){let s=[];if(this.ecommerce.days){for(let e in this.ecommerce.days)if(this.ecommerce.days.hasOwnProperty(e)){let t=this.ecommerce.days[e],r=this.$formatNumber(t.percent),o=this.$formatNumber(t.transactions);r+="%",s.push(["&nbsp;",o,r])}}return s},sessionsToPurchaseRows(){let s=[];if(this.ecommerce.sessions){for(let e in this.ecommerce.sessions)if(this.ecommerce.sessions.hasOwnProperty(e)){let t=this.ecommerce.sessions[e],r=this.$formatNumber(t.percent),o=this.$formatNumber(t.transactions);r+="%",s.push(["&nbsp;",o,r])}}return s},hasTimeToPurchase(){return typeof this.ecommerce.days<"u"},hasSessionsToPurchase(){return typeof this.ecommerce.sessions<"u"},hasNewCustomersData(){return typeof this.infoboxNewCustomersData.value<"u"},hasAbandonedCheckoutData(){return typeof this.infoboxAbandonedCheckoutsData.value<"u"},infoboxNewCustomersData(){return this.infoboxDataPercentage("newcustomers")},infoboxAbandonedCheckoutsData(){return this.infoboxDataPercentage("abandonedcheckouts")}},methods:{getColor(s){return s==="up"?"red":"green"},toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.ecommerce.sessions_chart?{labels:this.ecommerce.sessions_chart.categories,data:this.ecommerce.sessions_chart.sessions,timestamps:this.ecommerce.sessions_chart.timestamps,trend:this.ecommerce.sessions_chart.session_trendpoints,notes:this.ecommerce.sessions_chart.notes,compare:this.ecommerce.sessions_compare_chart?this.ecommerce.sessions_compare_chart.sessions:[]}:{}},pageviewsData(){return this.ecommerce.sessions_chart?{labels:this.ecommerce.sessions_chart.categories,data:this.ecommerce.sessions_chart.page_views,timestamps:this.ecommerce.sessions_chart.timestamps,trend:this.ecommerce.sessions_chart.page_view_trendpoints,notes:this.ecommerce.sessions_chart.notes,compare:this.ecommerce.sessions_compare_chart?this.ecommerce.sessions_compare_chart.page_views:[]}:{}},infoboxDataPercentage(s,e=!1){let t=this.infoboxData(s,e);return t.value&&(t.value=this.$formatNumber(t.value)+"%"),t.compareValue&&(t.compareValue=this.$formatNumber(t.compareValue)+"%"),t},infoboxData(s,e=!1){let t={};return this.ecommerce.infobox&&this.ecommerce.infobox[s]&&(t.change=this.ecommerce.infobox[s].prev,t.value=this.ecommerce.infobox[s].value.toString(),t.compareValue=this.ecommerce.infobox[s].compare_value?this.ecommerce.infobox[s].compare_value.toString():"0",this.ecommerce.infobox[s].prev===0?t.direction="":this.ecommerce.infobox[s].prev>0?(t.direction="up",t.color="green"):(t.direction="down",t.color="red")),e&&(t.direction==="down"?t.color="green":t.color="red"),t},gaLinks(s){return typeof this.ecommerce.galinks<"u"&&this.ecommerce.galinks[s]?this.ecommerce.galinks[s]:!1}},mounted(){if(this.showUpsellPlus()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_reports/getReportData","ecommerce")}};var ao=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-ecommerce"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_ecommerce)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_ecommerce}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),e.ecommerce.infobox?t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-infobox-row",attrs:{id:"monsterinsights-report-ecommerce-infobox"}},[t("report-infobox",{attrs:{id:"monsterinsights-report-ecommerce-conversion-rate",title:e.text_conversion_rate,value:e.infoboxConversionRateData.value,change:e.infoboxConversionRateData.change,color:e.infoboxConversionRateData.color,direction:e.infoboxConversionRateData.direction,days:e.infoboxRange,tooltip:e.text_conversion_rate_tooltip,"compare-value":e.infoboxConversionRateData.compareValue}}),t("report-infobox",{attrs:{title:e.text_transactions,value:e.$formatNumber(e.infoboxTransactionsData.value),change:e.infoboxTransactionsData.change,color:e.infoboxTransactionsData.color,direction:e.infoboxTransactionsData.direction,days:e.infoboxRange,tooltip:e.text_transactions_tooltip,"compare-value":e.$formatNumber(e.infoboxTransactionsData.compareValue)}}),t("report-infobox",{attrs:{id:"monsterinsights-report-ecommerce-revenue",title:e.text_revenue,value:e.$formatNumber(e.infoboxRevenueData.value),change:e.infoboxRevenueData.change,color:e.infoboxRevenueData.color,direction:e.infoboxRevenueData.direction,days:e.infoboxRange,tooltip:e.text_revenue_tooltip,"compare-value":e.$formatNumber(e.infoboxRevenueData.compareValue)}}),t("report-infobox",{attrs:{title:e.text_average_order_value,value:e.$formatNumber(e.infoboxOrderValueData.value),change:e.infoboxOrderValueData.change,color:e.infoboxOrderValueData.color,direction:e.infoboxOrderValueData.direction,days:e.infoboxRange,tooltip:e.text_average_order_value_tooltip,"compare-value":e.$formatNumber(e.infoboxOrderValueData.compareValue)}})],1):e._e(),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_top_products,emptytext:e.text_empty_top_products,headers:e.top_products_headers,rows:e.topProductsrows,tooltip:e.text_top_products_tooltip,"has-compare-change-column":!0}},[e.gaLinks("products")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("products"),target:"_blank"},domProps:{textContent:e._s(e.text_top_products_button)},slot:"button"}):e._e()])],1),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_top_conversions,emptytext:e.text_empty_top_conversions,headers:e.topConversionsHeaders,rows:e.topConversionsrows,tooltip:e.text_top_conversions_tooltip,"has-compare-change-column":!0}})],1),t("div",{class:["monsterinsights-report-row monsterinsights-report-infobox-row",{"monsterinsights-report-2-columns":!e.hasNewCustomersData&&!e.hasAbandonedCheckoutData}]},[t("report-infobox",{attrs:{title:e.text_add_to_cart,value:e.$formatNumber(e.infoboxAddToCartData.value),change:e.infoboxAddToCartData.change,color:e.infoboxAddToCartData.color,direction:e.infoboxAddToCartData.direction,days:e.infoboxRange,tooltip:e.text_add_to_cart_tooltip,"compare-value":e.$formatNumber(e.infoboxAddToCartData.compareValue)}}),t("report-infobox",{attrs:{id:"monsterinsights-report-removed-from-cart",title:e.text_removed_from_cart,value:e.$formatNumber(e.infoboxRemFromCartData.value),change:e.infoboxRemFromCartData.change,color:e.infoboxRemFromCartData.color,direction:e.infoboxRemFromCartData.direction,days:e.infoboxRange,tooltip:e.text_removed_from_cart_tooltip,"compare-value":e.$formatNumber(e.infoboxRemFromCartData.compareValue)}}),e.hasNewCustomersData?t("report-infobox",{attrs:{title:e.text_new_customers,value:e.infoboxNewCustomersData.value,change:e.infoboxNewCustomersData.change,color:e.infoboxNewCustomersData.color,direction:e.infoboxNewCustomersData.direction,days:e.infoboxRange,tooltip:e.text_new_customers_tooltip,"compare-value":e.infoboxNewCustomersData.compareValue}}):e._e(),e.hasAbandonedCheckoutData?t("report-infobox",{attrs:{title:e.text_abandoned_checkout,value:e.infoboxAbandonedCheckoutsData.value,change:e.infoboxAbandonedCheckoutsData.change,color:e.getColor(e.infoboxAbandonedCheckoutsData.direction),direction:e.infoboxAbandonedCheckoutsData.direction,days:e.infoboxRange,tooltip:e.text_abandoned_checkout_tooltip,"compare-value":e.infoboxAbandonedCheckoutsData.compareValue}}):e._e()],1),e.hasTimeToPurchase||e.hasSessionsToPurchase?t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex"},[e.hasTimeToPurchase?t("report-table-box",{attrs:{title:e.text_time_to_purchase,emptytext:e.text_empty_generic,headers:e.time_to_purchase_headers,rows:e.timeToPurchaseRows,tooltip:e.text_time_to_purchase_tooltip}},[e.gaLinks("days")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("days"),target:"_blank"},domProps:{textContent:e._s(e.text_days_button)},slot:"button"}):e._e()]):e._e(),e.hasSessionsToPurchase?t("report-table-box",{attrs:{title:e.text_sessions_to_purchase,emptytext:e.text_empty_generic,headers:e.sessions_to_purchase_headers,rows:e.sessionsToPurchaseRows,tooltip:e.text_sessions_to_purchase_tooltip}},[e.gaLinks("sessions")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("sessions"),target:"_blank"},domProps:{textContent:e._s(e.text_sessions_button)},slot:"button"}):e._e()]):e._e()],1):e._e(),e.showUpsellPlus()?t("report-upsell-overlay",{attrs:{report:"ecommerce"}}):e._e()],1)},no=[],lo=l(io,ao,no,!1,null,null,null,null);const co=lo.exports,{__:Se}=wp.i18n,po={name:"ReportTable",components:{ReportsPagination:Qe},props:{title:String,rows:Array,columns:{type:Array},button:Object,emptytext:{type:String,default:Se("No data currently for the report","google-analytics-for-wordpress")},error:String,mobileWidth:{default:783,type:Number},icon:{default:"",type:String},allowSearch:{type:Boolean,default:!1},tableWrapperClass:{default:"",type:String},withExpandableData:{default:!1,type:Boolean},addEmptyRows:{type:Boolean,default:!0},updateMobileToggle:{required:!1,type:Function},hasCompareChangeColumn:{type:Boolean,default:!1},compareChangeOpposite:{type:Array,default:()=>[]},compareSingleColumns:{type:Array,default:()=>[0]},showPagination:{type:Boolean,default:!0},tableTitle:{type:String,default:""}},data(){return{paginate:!1,limit:10,text_show:Se("Show","google-analytics-for-wordpress"),text_search_placeholder:Se("Filter",this.$_textDomain),activeRow:null,isMobile:!1,resizing:!1,keywords:"",keywordsTimeout:null,orderby:{type:String},order:"",extendedRows:[]}},computed:{...C({mobileTableExpanded:"$_reports/mobileTableExpanded",date:"$_reports/date"}),mobileHeaders(){let s=[];return this.columns.forEach(function(e,t){t>0&&s.push(e)}),s},emptyTable(){let s=[[this.emptytext]];for(;s.length<10;)s.push(["&nbsp;"]);return s},componentClass(){let s="monsterinsights-table-box monsterinsights-table";return s+=" "+this.tableWrapperClass,this.isMobile&&(s+=" monsterinsights-table-box-mobile monsterinsights-table-mobile"),s},tableRows(){var o;let s=this.rows.filter(()=>!0);const e=this.columns,t=this.order,r=this.orderby;if(this.keywords!==""&&(s=s.filter(n=>n.findIndex(d=>typeof d[0]>"u"?!1:String(d[0]).toLowerCase().includes(String(this.keywords).toLowerCase()))>=0)),t&&r&&s.length>0){const n=e.findIndex(p=>p.key===r),a=(o=s.find(p=>p[n]&&p[n][0]!==""))==null?void 0:o[n][0],d=!isNaN(parseFloat(a))&&isFinite(a);s.sort((p,m)=>{var U,H;const b=((U=p[n])==null?void 0:U[0])||"",k=((H=m[n])==null?void 0:H[0])||"";if(d){const O=parseFloat(b)||0,ce=parseFloat(k)||0;return t==="asc"?O-ce:ce-O}else{const O=String(b).localeCompare(String(k),void 0,{numeric:!0,sensitivity:"base"});return t==="asc"?O:-O}})}if(this.addEmptyRows&&s.length<10){let n=[],a=0;for(;a<this.columns.length;){let d=[""];a>0&&this.date.compareReport&&(d.push(""),this.hasCompareChangeColumn&&d.push("")),n.push(d),a++}for(;s.length<10;)s.push(n)}return this.showPagination&&(s=s.slice(0,this.limit)),s},wrapperClass(){let s="monsterinsights-report-table-wrapper";return this.allowSearch&&(s+=" monsterinsights-has-filter"),this.paginate&&(s+=" monsterinsights-has-pagination"),s},titleClass(){let s="monsterinsights-report-title ";return this.icon&&(s+=this.icon),this.paginate&&(s+=" monsterinsights-has-pagination"),s},compareDateColumns(){if(!this.date.compareReport)return[];let s=[];return this.columns.forEach((e,t)=>{t!=0&&(s.push(this.date.intervalText,this.date.intervalCompareText),this.hasCompareChangeColumn&&s.push(Se("Change%","google-analytics-for-wordpress")))}),s}},methods:{getExtendedData(s){if(!this.withExpandableData||typeof s._extendedData>"u")return null;const{_extendedData:e}=s;return typeof e.rows<"u"?e.rows:null},isExpandableRow(s){if(!this.withExpandableData||typeof s._extendedData>"u")return!1;const{_extendedData:e}=s;return typeof e.rows<"u"&&e.rows.length>0},extendRow(s){if(!s)return;const t=s.target.parentElement.parentElement.parentElement.getAttribute("data-key");this.extendedRows[t]=!this.extendedRows[t],this.extendedRows[t]?(s.target.classList.remove("dashicons-arrow-down-alt2"),s.target.classList.add("dashicons-arrow-up-alt2")):(s.target.classList.remove("dashicons-arrow-up-alt2"),s.target.classList.add("dashicons-arrow-down-alt2"))},hasButtonSlot(){return this.rows.length>10&&this.showPagination&&(this.paginate=!0),this.$slots.button},getButtonClass(s){let e="monsterinsights-button";return s===this.limit&&(e+=" monsterinsights-selected-interval"),e},cellText(s,e,t){return s===""?"&nbsp;":e===0?'<span class="monsterinsights-reports-list-count">'+(t+1)+'.</span><span class="monsterinsights-reports-list-title">'+s+"</span>":s},rowClass(s,e=!1){let t="monsterinsights-table-list-item";return(this.mobileTableExpanded||this.isActiveRowMatch(s))&&window.innerWidth<this.mobileWidth&&(t+=" monsterinsights-table-list-item-active"),e?t+=" monsterinsights-table-is-extended":this.tableRows[s][0]===""&&(t+=" monsterinsights-table-list-item-empty"),t},showMobileRow(s,e){return window.innerWidth<this.mobileWidth&&e>0&&(this.mobileTableExpanded||this.isActiveRowMatch(s))},handleResize(){this.resizing||(this.resizing=!0,window.requestAnimationFrame?window.requestAnimationFrame(this.resizeCallback):setTimeout(this.resizeCallback,66))},resizeCallback(){this.isMobile=window.innerWidth<this.mobileWidth,this.resizing=!1},cellClass(s){return s++,"monsterinsights-table-cell-"+s},toggleMobileTables(s){if(this.mobileTableExpanded)return!1;this.updateMobileToggle&&(s=this.updateMobileToggle(s)),this.activeRow=this.isToggleMobileActiveRowMatch(s)?null:s},changeLimit(s){this.limit=s},changeSort(s,e){e.target.closest("a").blur(),this.orderby!==s?(this.orderby=s,this.order="asc"):this.order=this.order==="asc"?"desc":"asc"},getToolTipProps(s){return s?{content:s,autoHide:!1,trigger:"hover focus click"}:!1},arrayEquals(s,e){return!s||!e?!1:s.length==e.length&&s.every(function(t,r){return t==e[r]})},isToggleMobileActiveRowMatch(s){return typeof s=="number"?this.activeRow===s:this.arrayEquals(this.activeRow,s)},isActiveRowMatch(s){return typeof this.activeRow=="number"||!this.activeRow?s===this.activeRow:this.activeRow.includes(s)},compareReportContent(s,e){return!this.tableRows[s]||!this.tableRows[s][e]||this.tableRows[s][e][1]=="undefined"?"":this.tableRows[s][e][1]},compareChange(s,e){let t=Number(s),r=t>0?"up":"down",o=t>0?"green":"red";return this.compareChangeOpposite.includes(e)&&(o=t<=0?"green":"red"),'<span class="monsterinsights-arrow monsterinsights-'+r+" monsterinsights-"+o+'"></span> '+Math.abs(t)+"%"},getColSpan(s){return s!=0&&this.date.compareReport?this.hasCompareChangeColumn?3:2:1},isEM(){return this.$store.state.isEM}},mounted(){window.addEventListener("resize",this.handleResize),this.handleResize()},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)}};var ho=function(){var e=this,t=e._self._c;return t("div",{class:e.wrapperClass},[e.title?t("h2",{class:e.titleClass,domProps:{textContent:e._s(e.title)}}):e._e(),e.allowSearch?t("div",{staticClass:"alignright filters search-filters"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.keywords,expression:"keywords"}],attrs:{placeholder:e.text_search_placeholder,type:"search"},domProps:{value:e.keywords},on:{input:function(r){r.target.composing||(e.keywords=r.target.value)}}})]):e._e(),e._t("before-table"),t("div",{class:e.componentClass},[t("div",{staticClass:"monsterinsights-table-box-list monsterinsights-table-box-table monsterinsights-report-table"},[e.tableTitle?t("h3",{class:e.isEM?"monsterinsights-table-box-list__title monstericon-clipboard":"monsterinsights-table-box-list__title"},[e._v(" "+e._s(e.tableTitle)+" ")]):e._e(),e.error?t("div",[t("h3",{domProps:{textContent:e._s(e.error)}})]):e.rows.length>0?t("table",{class:{"monsterinsights-report-table-compare-report-table":e.date.compareReport}},[t("thead",{class:{"monsterinsights-report-table-head-compare-report":e.date.compareReport}},[t("tr",{staticClass:"monsterinsights-report-table-head-columns"},e._l(e.columns,function(r,o){return t("th",{key:o,staticClass:"manage-column column-title column-primary",class:{sortable:r.sortable,sorted:e.orderby===r.key,[e.order]:!0},attrs:{id:r.key,scope:"col",rowspan:o==0&&e.date.compareReport?2:1,colspan:e.getColSpan(o)}},[r.sortable?[t("a",{attrs:{href:"javascript:",role:"button"},on:{click:n=>{e.changeSort(r.key,n)}}},[r!=null&&r.tooltip?t("span",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.getToolTipProps(r==null?void 0:r.tooltip),expression:"getToolTipProps(column?.tooltip)"}],staticClass:"monsterinsights-info monsterinsights-inline"},[t("i",{staticClass:"monstericon monstericon-info-circle-regular"})]):e._e(),t("span",[e._v(e._s(typeof r.title=="function"?r.title():r.title))]),t("span",{staticClass:"sorting-indicator"})])]:t("span",[e._v(e._s(typeof r.title=="function"?r.title():r.title))])],2)}),0),e.date.compareReport?t("tr",{class:{"monsterinsights-report-table-head-date-range-row":!0,"monsterinsights-report-table-head-date-range-row-has-change-col":e.hasCompareChangeColumn}},e._l(e.compareDateColumns,function(r,o){return t("th",{key:o},[e._v(" "+e._s(r)+" ")])}),0):e._e()]),t("tbody",{class:{"monsterinsights-report-table-body-compare-report":e.date.compareReport}},[e._l(e.tableRows,function(r,o){return[t("tr",{key:o,class:e.rowClass(o),attrs:{"data-key":o,"data-extendable":JSON.stringify(e.getExtendedData(r))},on:{click:function(n){return e.toggleMobileTables(o)}}},e._l(e.tablePrepareColumnItems(r),function(n,a){return t("td",{key:a+"td"+o,class:e.cellClass(a)},[e.tableIsCurrentColumn(a)?[e.showMobileRow(o,a)?t("div",{staticClass:"monsterinsights-table-mobile-heading",domProps:{textContent:e._s(e.columns[a].title)}}):e._e(),t("div",{staticClass:"monsterinsights-table-item-content"},[t("span",{domProps:{innerHTML:e._s(e.cellText(n,a,o))}}),e.isExpandableRow(r)&&a===0?[t("div",{staticClass:"monsterinsights-table-item-extend-button dashicons dashicons-before dashicons-arrow-down-alt2",on:{click:e.extendRow}})]:e._e()],2)]:e._e(),e.tableIsPreviousColumn(a)?[t("span",{staticClass:"monsterinsights-table-item-compare-report-content",domProps:{innerHTML:e._s(n)}})]:e._e(),e.tableIsChangeColumn(n,a)?[t("div",{staticClass:"monsterinsights-table-item-compare-report-change",domProps:{innerHTML:e._s(e.tableCompareChange(n,a))}})]:e._e()],2)}),0),typeof e.extendedRows[o]<"u"&&e.extendedRows[o]?e._l(e.getExtendedData(r),function(n,a){return t("tr",{key:o+""+a,class:e.rowClass(a,!0),attrs:{"data-extendedBy":o},on:{click:function(d){return e.toggleMobileTables(a)}}},e._l(e.tablePrepareColumnItems(n),function(d,p){return t("td",{key:p+"td"+o+"-"+a,class:e.cellClass(p)},[e.tableIsCurrentColumn(p)?[e.showMobileRow(a,p)?t("div",{staticClass:"monsterinsights-table-mobile-heading",domProps:{textContent:e._s(e.columns[p].title)}}):e._e(),t("div",{staticClass:"monsterinsights-table-item-content",domProps:{innerHTML:e._s(e.cellText(d,p,a))}})]:e._e(),e.tableIsPreviousColumn(p)?[t("span",{staticClass:"monsterinsights-table-item-compare-report-content",domProps:{innerHTML:e._s(d)}})]:e._e(),e.tableIsChangeColumn(d,p)?[t("div",{staticClass:"monsterinsights-table-item-compare-report-change",domProps:{innerHTML:e._s(e.tableCompareChange(d,p))}})]:e._e()],2)}),0)}):e._e()]})],2)]):t("div",{staticClass:"monsterinsights-table-no-data"},[t("h3",{domProps:{textContent:e._s(e.emptytext)}})])]),e.hasButtonSlot()||e.paginate?t("div",{staticClass:"monsterinsights-table-box-footer"},[e._t("button"),e.paginate?t("reports-pagination",{attrs:{limit:e.limit,length:e.rows.length},on:{updated:e.changeLimit}}):e._e()],2):e._e()])],2)},go=[],uo=l(po,ho,go,!1,null,null,null,null);const Q=uo.exports,{__:Y}=wp.i18n,mo={name:"ReportTrafficEcommerceCoupons",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportTable:Q},mixins:[B],data(){return{text_ecommerce_coupons:Y("Coupons Report","google-analytics-for-wordpress"),fake_headers:[Y("Coupon Name","google-analytics-for-wordpress"),Y("Sessions","google-analytics-for-wordpress"),Y("Engaged Sessions","google-analytics-for-wordpress"),Y("Pages / Sessions","google-analytics-for-wordpress"),Y("Purchases","google-analytics-for-wordpress"),Y("Conversion Rate","google-analytics-for-wordpress"),Y("Revenue","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:Y("An unknown error has occurred.","google-analytics-for-wordpress")}}},mounted(){if(this.$store.commit("$_reports/UPDATE_REQUIRED_ADDON","ecommerce"),this.showUpsellPlus()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","ecommerce_coupons")},computed:{ecommerce_coupons(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_ecommerce_coupons"]:this.$store.getters["$_reports/ecommerce_coupons"]},ecommerceCoupons(){return this.ecommerce_coupons.coupons_table?this.ecommerce_coupons.coupons_table:[]}},methods:{getEcommerceCouponsHeaders(){return[{title:Y("Coupon Name","google-analytics-for-wordpress"),key:"ecommerce_coupons",sortable:!0},{title:Y("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0},{title:Y("Transactions","google-analytics-for-wordpress"),key:"transactions",sortable:!0},{title:Y("Average Order Value","google-analytics-for-wordpress"),key:"average_order_value",sortable:!0}]},prepareEcommerceCouponsRows(){let s=[];return this.ecommerceCoupons.length>0&&typeof this.ecommerceCoupons[0]<"u"&&Object.values(this.ecommerceCoupons).forEach(e=>{s.push([this.tableFormatRowData(e.coupon_name),this.tableFormatRowData(e.revenue),this.tableFormatRowData(e.transactions),this.tableFormatRowData(e.average_order_value)])}),s},prepareEcommerceCouponsError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getEcommerceCouponsEmptyText(){return Y("No data currently for the eCommerce Coupons report","google-analytics-for-wordpress")}}};var _o=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-ecommerce-coupons"},[t("div",{staticClass:"monsterinsights-report-top"},[t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_ecommerce_coupons}})],1),t("div",{staticClass:"monsterinsights-report-row"},[t("ReportTable",{attrs:{title:e.text_ecommerce_coupons,rows:e.prepareEcommerceCouponsRows(),columns:e.getEcommerceCouponsHeaders(),error:e.prepareEcommerceCouponsError(e.ecommerceCoupons),emptytext:e.getEcommerceCouponsEmptyText(),"allow-search":e.reportTableSearch,"has-compare-change-column":!0}})],1),e.showUpsellPlus()?t("report-upsell-overlay",{attrs:{report:"ecommerce_coupons"}}):e._e()],1)},fo=[],vo=l(mo,_o,fo,!1,null,null,null,null);const wo=vo.exports,{__:M}=wp.i18n,yo={name:"ReportCartAbandonment",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportTable:Q},mixins:[B],data(){return{text_cart_abandonment:M("Cart Abandonment","google-analytics-for-wordpress"),text_cart_abandonment_by_day:M("Cart Abandonment by Day","google-analytics-for-wordpress"),fake_headers:[M("Product","google-analytics-for-wordpress"),M("Quantity","google-analytics-for-wordpress"),M("Revenue","google-analytics-for-wordpress"),M("Cart Abandonment","google-analytics-for-wordpress"),M("Checkout Abandonment","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:M("An unknown error has occurred.","google-analytics-for-wordpress")}}},mounted(){if(this.$store.commit("$_reports/UPDATE_REQUIRED_ADDON","ecommerce"),this.showUpsellPlus())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","cart_abandonment")},computed:{...C({date:"$_reports/date",license:"$_license/license",license_network:"$_license/license_network"}),cart_abandonment(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_cart_abandonment"]:this.$store.getters["$_reports/cart_abandonment"]},cart_abandonment_rows(){var e;const s=(e=this.cart_abandonment)!=null&&e.cart_abandonment?this.cart_abandonment.cart_abandonment:[];return s.length>0?s:[]},cart_abandonment_rows_by_day(){var e;const s=(e=this.cart_abandonment)!=null&&e.cart_abandonment_by_day?this.cart_abandonment.cart_abandonment_by_day:[];return s.length>0?s:[]}},methods:{getCartAbandonmentHeaders(){return[{title:M("Product","google-analytics-for-wordpress"),key:"name",sortable:!0},{title:M("Quantity","google-analytics-for-wordpress"),key:"purchase_quantity",sortable:!0},{title:M("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0},{title:M("Cart Abandonment","google-analytics-for-wordpress"),key:"cart_abandonment",sortable:!0,tooltip:M("The percentage of shoppers who add items to their online shopping cart but do not complete the purchase or start the checkout process.","google-analytics-for-wordpress")},{title:M("Checkout Abandonment","google-analytics-for-wordpress"),key:"checkout_abandonment",sortable:!0,tooltip:M("The percentage of shoppers who start the checkout process but fails to complete the transaction. It's a more focused metric that signals issues with the final stages of a purchase.","google-analytics-for-wordpress")}]},getCartAbandonmentByDayHeaders(){return[{title:M("Date","google-analytics-for-wordpress"),key:"date",sortable:!0},{title:M("Quantity","google-analytics-for-wordpress"),key:"purchase_quantity",sortable:!0},{title:M("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0},{title:M("Cart Abandonment","google-analytics-for-wordpress"),key:"cart_abandonment",sortable:!0,tooltip:M("The percentage of shoppers who add items to their online shopping cart but do not complete the purchase or start the checkout process.","google-analytics-for-wordpress")},{title:M("Checkout Abandonment","google-analytics-for-wordpress"),key:"checkout_abandonment",sortable:!0,tooltip:M("The percentage of shoppers who start the checkout process but fails to complete the transaction. It's a more focused metric that signals issues with the final stages of a purchase.","google-analytics-for-wordpress")}]},prepareCartAbandonmentRows(){let s=[];return this.cart_abandonment_rows.forEach(e=>{s.push([this.tableFormatRowData(e.name),this.tableFormatRowData(e.purchase_quantity,"",this.$formatNumber),this.tableFormatRowData(e.revenue,"",this.$formatNumber),this.tableFormatRowData(e.cart_abandonment,"%",this.$formatNumber),this.tableFormatRowData(e.checkout_abandonment,"%",this.$formatNumber)])}),s},prepareCartAbandonmentRowsByDay(){let s=[];return this.cart_abandonment_rows_by_day.forEach(e=>{s.push([this.tableFormatRowData(e.date),this.tableFormatRowData(e.purchase_quantity,"",this.$formatNumber),this.tableFormatRowData(e.revenue,"",this.$formatNumber),this.tableFormatRowData(e.cart_abandonment,"%",this.$formatNumber),this.tableFormatRowData(e.checkout_abandonment,"%",this.$formatNumber)])}),s},prepareCartAbandonmentError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getCartAbandonmentEmptyText(){return M("No data currently for the Cart Abandonment report","google-analytics-for-wordpress")}}};var Co=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-engagement-pages"},[t("div",{staticClass:"monsterinsights-report-top"},[t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_cart_abandonment}})],1),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_cart_abandonment,rows:e.prepareCartAbandonmentRows(),columns:e.getCartAbandonmentHeaders(),emptytext:e.getCartAbandonmentEmptyText(),"allow-search":e.reportTableSearch}})],1),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_cart_abandonment_by_day,rows:e.prepareCartAbandonmentRowsByDay(),columns:e.getCartAbandonmentByDayHeaders(),emptytext:e.getCartAbandonmentEmptyText(),"allow-search":e.reportTableSearch}})],1),e.showUpsellPlus()?t("report-upsell-overlay",{attrs:{report:"cart_abandonment"}}):e._e()],1)},bo=[],xo=l(yo,Co,bo,!1,null,null,null,null);const ko=xo.exports,{__:oe,sprintf:Be}=wp.i18n,$o={name:"UpsellOverlay",props:{report:String,title:{type:String,default:oe("Overview Report","google-analytics-for-wordpress")},heading:{type:String,default:oe("Report","google-analytics-for-wordpress")},subTitle:{type:String,default:""},features:{type:Array,default(){return[]}},upgradeHref:{type:String,default:null},previewClass:{type:String,default:null},withProBadge:{type:Boolean,default:!0},onClose:{type:Function,default:()=>{}}},data(){return{text_upsell_button:oe("Upgrade and Unlock","google-analytics-for-wordpress"),preview_button:oe("Preview","google-analytics-for-wordpress"),report_class:"upsell-"+this.report,pro_badge_text:oe("PRO","google-analytics-for-wordpress"),textSeeSampleReport:oe("See a Sample Report","google-analytics-for-wordpress"),showOverlay:!0}},computed:{...C({noauth:"$_reports/noauth"}),featuresClass(){return this.featuresList.length>4?"columns-2":"columns-1"},upsellData(){return this.$mi_get_upsell_content(this.report)},featuresList(){return this.features===[]?this.features:this.upsellData.features},contentClass(){return"monsterinsights-modal-overlay-content "+this.previewClass},footer_notice(){return Be(oe("%1$sPlus%2$s, you unlock %1$sall%2$s of ExactMetrics advanced Reports when you upgrade to Pro. %3$sLearn more about %5$s%4$s","google-analytics-for-wordpress"),"<strong>","</strong>",'<a target="_blank" href="'+this.getUpgradeLink()+'">',"</a>",this.isAgency?"Agency":"Pro")},subTitleText(){return this.subTitle?this.subTitle:Be(oe("What’s in the %s?","google-analytics-for-wordpress"),this.heading)}},methods:{getUpgradeLink(){return this.upgradeHref?this.upgradeHref:this.$getUpgradeUrl("report",this.report)},seeSampleReport(){this.showOverlay=!1,this.$store.commit("$_reports/DISABLE_BLUR"),this.$store.dispatch("$_app/addNotice",{id:"lite_sample_report",content:Be(oe("This is a sample report. %1$sUpgrade to Pro%2$s to unlock for your website.","google-analytics-for-wordpress"),'<a target="_blank" href="'+this.getUpgradeLink()+'">',"</a>"),type:"warning",dismissable:!0},{root:!0})}},mounted(){document.body.classList.add("monsterinsights-reporting-page-with-upsell-modal")},destroyed(){document.body.classList.remove("monsterinsights-reporting-page-with-upsell-modal"),this.$store.dispatch("$_app/removeNotice","lite_sample_report")}};var Ro=function(){var e=this,t=e._self._c;return e.showOverlay?t("div",{staticClass:"monsterinsights-modal-overlay"},[t("div",{staticClass:"monsterinsights-modal-overlay-top"},[t("h3",{domProps:{textContent:e._s(e.heading)}}),t("div",{staticClass:"monsterinsights-modal-overlay-pro-badge",domProps:{textContent:e._s(e.pro_badge_text)}})]),t("div",{class:e.contentClass},[t("div",{staticClass:"monsterinsights-modal-overlay-content-wrapper"},[e.title?t("h3",{domProps:{textContent:e._s(e.title)}}):e._e(),e.subTitleText?t("h4",{domProps:{textContent:e._s(e.subTitleText)}}):e._e(),t("div",{staticClass:"monsterinsights-modal-overlay-content__features"},[e.featuresList?t("ul",{class:e.featuresClass},e._l(e.featuresList,function(r,o){return t("li",{key:o,domProps:{textContent:e._s(r)}})}),0):e._e(),t("div",{staticClass:"monsterinsights-modal-overlay-footer",domProps:{innerHTML:e._s(e.footer_notice)}})])]),t("div",{staticClass:"monsterinsights-modal-overlay-actions"},[t("a",{staticClass:"monsterinsights-button monsterinsights-green-button upgrade-button",attrs:{href:e.getUpgradeLink(),target:"_blank"},domProps:{textContent:e._s(e.text_upsell_button)}}),t("a",{staticClass:"monsterinsights-modal-overlay-actions-sample-report",attrs:{href:"#"},domProps:{textContent:e._s(e.textSeeSampleReport)},on:{click:function(r){return r.preventDefault(),e.seeSampleReport.apply(null,arguments)}}})])])]):e._e()},Po=[],Do=l($o,Ro,Po,!1,null,null,null,null);const It=Do.exports,{__:A,sprintf:ct}=wp.i18n;Wt([Kt,qt]);const So={name:"ReportEcommerceFunnelPro",components:{ReportUpsellOverlay:E,UpsellOverlay:It,SiteNotesOverview:te,ReportTable:Q,ReportsDatePicker:I,ReportsPdfExport:F,Multiselect:qe},mixins:[B],data(){return{text_ecommerce_funnel:A("eCommerce Funnel","google-analytics-for-wordpress"),text_choose_breakdown:A("Choose Breakdown","google-analytics-for-wordpress"),text_conversion_rate_by_stages:A("Conversion Rate By Stages","google-analytics-for-wordpress"),text_abandonment_rate:A("Abandonment Rate","google-analytics-for-wordpress"),text_view_item:A("View Item","google-analytics-for-wordpress"),text_add_to_cart:A("Add to Cart","google-analytics-for-wordpress"),text_purchase:A("Purchase","google-analytics-for-wordpress"),text_site_notes:A("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:A("Close Site Notes","google-analytics-for-wordpress"),providerNotSupportedText:A("Our funnel report only supports WooCommerce and Easy Digital Downloads.","google-analytics-for-wordpress"),breakdownOptions:[{value:"none",label:A("None","google-analytics-for-wordpress")},{value:"channel",label:A("Channel","google-analytics-for-wordpress")},{value:"country",label:A("Country","google-analytics-for-wordpress")}],breakdownValue:{value:"none",label:"None"},chartColors:["#FF893A","#8E71CC","#1EC185"],chartDomTimer:null,funnelLoaded:!1,showSiteNotes:!1,isMI:!0,text_upsell_heading:A("eCommerce Funnel Report","google-analytics-for-wordpress"),upgrade_button_url:this.$getUpgradeUrl("reports","ecommerce-funnel")}},computed:{...C({addons:"$_addons/addons"}),ecommerceFunnel(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_ecommerce_funnel"]:this.$store.getters["$_reports/ecommerce_funnel"]},getTableRows(){return this.ecommerceFunnel.funnel_table[this.breakdownValue.value].map(s=>{let e=[[this.getTableItemStep(s)],[ct("%s (%s%%)",s.users,s.users_rate)],s.step==="3. Purchase"?["--"]:[s.completion_rate==="--"?"--":String(s.completion_rate)+"%"],s.step==="3. Purchase"?["--"]:[s.abandonments],s.step==="3. Purchase"?["--"]:[s.abandonments_rate==="--"?"--":String(s.abandonments_rate)+"%"]];return this.breakdownValue.value!=="none"&&e.splice(1,0,[s.breakdown_category]),e})},getTableColumns(){let s=[{title:A("Step","google-analytics-for-wordpress"),key:"step"},{title:A("Users (% of step 1)","google-analytics-for-wordpress"),key:"users"},{title:A("Completion Rate","google-analytics-for-wordpress"),key:"completion-rate"},{title:A("Abandonments","google-analytics-for-wordpress"),key:"abandonments"},{title:A("Abandonment Rate","google-analytics-for-wordpress"),key:"abandonment-rate"}];return this.breakdownValue.value!=="none"&&s.splice(1,0,{title:ct(A("%s Category","google-analytics-for-wordpress"),this.breakdownValue.label),key:"category"}),s},showReport(){if(this.showUpsellPlus()||!this.addons.easy_digital_downloads)return!0;let s=this.addons.woocommerce&&this.addons.woocommerce.active,e=this.addons.easy_digital_downloads&&this.addons.easy_digital_downloads.active;return s||e}},created(){this.$store.commit("$_reports/UPDATE_DATE_STORE",{compareReport:!1})},mounted(){this.showUpsellPlus()?this.$store.commit("$_reports/ENABLE_BLUR"):(this.$store.commit("$_reports/UPDATE_REQUIRED_ADDON","ecommerce"),this.$store.dispatch("$_reports/getReportData","ecommerce_funnel")),this.initFunnelChart()},methods:{getTableItemStep(s){return this.breakdownValue.value==="none"||s.breakdown_category==="Total"?s.step:""},initFunnelChart(){if(this.ecommerceFunnel.funnel_chart.length<1)return;if(!this.$refs.chartElement){this.chartDomTimer=setInterval(this.initFunnelChart,100);return}clearInterval(this.chartDomTimer);const s=this;let e=zt(this.$refs.chartElement),t={series:[{name:"Funnel",type:"funnel",left:0,right:0,top:0,bottom:0,width:"100%",min:0,max:this.ecommerceFunnel.funnel_chart[0].value,minSize:"0%",maxSize:"100%",sort:"descending",gap:0,label:{show:!0,position:"inside",fontSize:18,color:"#fff"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{color(r){return s.chartColors[r.dataIndex]},borderColor:"#fff",borderWidth:3},emphasis:{label:{fontSize:20}},data:this.ecommerceFunnel.funnel_chart}],animation:!1};e.on("finished",function(){s.funnelLoaded=!0}),e.setOption(t)},updateMobileToggle(s){if(this.breakdownValue.value==="none")return s;let e=this.ecommerceFunnel.funnel_table[this.breakdownValue.value+"_category_length"],t=[s];for(let r=1;r<e;r++)t.push(s+r);return t},toggleNotes(){this.showSiteNotes=!this.showSiteNotes}},watch:{"ecommerceFunnel.funnel_chart":function(s){s&&this.initFunnelChart()}}};var To=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-ecommerce-funnel"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_ecommerce_funnel)}}),t("reports-date-picker",{attrs:{"compare-options":!1}}),t("reports-pdf-export",{attrs:{"report-title":e.text_ecommerce_funnel}})],1),e.showReport?[e.ecommerceFunnel.funnel_chart.length>0?t("div",{class:{"monsterinsights-report-row":!0,"monsterinsights-report-ecommerce-funnel-box-visibility-hidden":!e.funnelLoaded}},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-chart-wrapper"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-header"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-header-col-1",domProps:{textContent:e._s(e.text_conversion_rate_by_stages)}}),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-header-col-2",domProps:{textContent:e._s(e.text_abandonment_rate)}})]),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-1"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-1-item",domProps:{textContent:e._s(e.text_view_item)}}),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-1-item monsterinsights-report-ecommerce-funnel-box-body-col-1-item-border",domProps:{textContent:e._s(e.text_add_to_cart)}}),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-1-item",domProps:{textContent:e._s(e.text_purchase)}})]),t("div",{ref:"chartElement",attrs:{id:"monsterinsights-report-ecommerce-funnel-chart"}}),e.ecommerceFunnel.funnel_table.none.length>0?t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item"},[t("span",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item-inner"},[e._v(e._s(e.ecommerceFunnel.funnel_table.none[0].completion_rate)+"%")]),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item-inner-bg-border"})]),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item"},[t("span",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item-inner"},[e._v(e._s(e.ecommerceFunnel.funnel_table.none[1].completion_rate)+"%")]),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-3-item-inner-bg-border"})])]):e._e(),e.ecommerceFunnel.funnel_table.none.length>0?t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-4"},[t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-4-item"},[e._v(" "+e._s(e.ecommerceFunnel.funnel_table.none[0].abandonments_rate)+"% ")]),t("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-4-item monsterinsights-report-ecommerce-funnel-box-body-col-4-item-border"},[e._v(" "+e._s(e.ecommerceFunnel.funnel_table.none[1].abandonments_rate)+"% ")]),e._m(0)]):e._e()])]),e.showSiteNotes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]):e._e(),e.showSiteNotes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview"),e.showSiteNotes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{rows:e.getTableRows,columns:e.getTableColumns,"add-empty-rows":!1,"table-wrapper-class":"monsterinsights-ecommerce-funnel-table","update-mobile-toggle":e.updateMobileToggle,"show-pagination":!1},scopedSlots:e._u([e.ecommerceFunnel.funnel_chart.length>0?{key:"before-table",fn:function(){return[t("div",{staticClass:"monsterinsights-ecommerce-funnel-table-header"},[t("p",[e._v(e._s(e.text_choose_breakdown))]),t("multiselect",{attrs:{options:e.breakdownOptions,multiple:!1,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"","allow-empty":!1},model:{value:e.breakdownValue,callback:function(r){e.breakdownValue=r},expression:"breakdownValue"}})],1)]},proxy:!0}:null],null,!0)})],1)]:t("div",{staticClass:"monsterinsights-notice monsterinsights-notice-info"},[t("div",{staticClass:"monsterinsights-notice-inner"},[t("div",{staticClass:"notice-content"},[t("span",{staticClass:"monsterinsights-notice-content"},[e._v(e._s(e.providerNotSupportedText))])])])]),e.showUpsellPlus()?[e.isMI?t("ReportUpsellOverlay",{attrs:{report:"ecommerce_funnel"}}):t("UpsellOverlay",{attrs:{report:"ecommerce_funnel",heading:e.text_upsell_heading,title:e.text_ecommerce_funnel,upgradeHref:e.upgrade_button_url,previewClass:"monsterinsights-em-ecommerce-funnel-upsell-screen"}})]:e._e()],2)},Mo=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"monsterinsights-report-ecommerce-funnel-box-body-col-4-item"},[e("span",{staticClass:"monsterinsights-report-ecommerce-funnel-box-visibility-hidden"},[s._v("--")])])}],Lo=l(So,To,Mo,!1,null,null,null,null);const Eo=Lo.exports,Ao={name:"ReportEcommerceFunnel",extends:Eo},Fo=null,No=null;var Io=l(Ao,Fo,No,!1,null,null,null,null);const Bo=Io.exports,{__:Oe}=wp.i18n,Oo={name:"SemRushCta",data(){return{isCtaOpen:!1,text_cta_title:Oe("Want to See your Competitor's Top Keywords?","google-analytics-for-wordpress"),text_cta_content:Oe("MonsterInsights have partnered with SEMRush, the powerful all-in-one marketing toolkit that helps you get competitive <br>intelligence on SEO, paid traffic, social media, and more.","google-analytics-for-wordpress"),text_cta_btn:Oe("Claim your 30 Day Free Trial","google-analytics-for-wordpress")}},mounted(){this.getSemRushCtaStatus()},methods:{closeCTA(){const s=this;let e=new FormData;e.append("action","monsterinsights_vue_dismiss_semrush_cta"),e.append("nonce",w.prototype.$mi.nonce),ne.post(w.prototype.$mi.ajax,e).then(t=>{t.data.dismissed==="yes"?s.isCtaOpen=!1:s.isCtaOpen=!0}).catch(function(){s.isCtaOpen=!0})},getSemRushCtaStatus(){const s=this;let e=new FormData;e.append("action","monsterinsights_get_sem_rush_cta_status"),e.append("nonce",this.$mi.nonce),ne.post(this.$mi.ajax,e).then(function(t){t.data.dismissed==="yes"?s.isCtaOpen=!1:s.isCtaOpen=!0}).catch(function(){s.isCtaOpen=!1})}}};var Uo=function(){var e=this,t=e._self._c;return e.isCtaOpen?t("div",{staticClass:"monsterinsights-semrush-cta"},[t("button",{staticClass:"monsterinsights-semrush-cta-close",on:{click:function(r){return r.preventDefault(),e.closeCTA.apply(null,arguments)}}},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M2.32258 2.35484C3.89247 0.784946 5.78495 0 8 0C10.2151 0 12.0968 0.784946 13.6452 2.35484C15.2151 3.90323 16 5.78495 16 8C16 10.2151 15.2151 12.1075 13.6452 13.6774C12.0968 15.2258 10.2151 16 8 16C5.78495 16 3.89247 15.2258 2.32258 13.6774C0.774194 12.1075 0 10.2151 0 8C0 5.78495 0.774194 3.90323 2.32258 2.35484ZM11.9355 10.0968L9.80645 8L11.9355 5.90323C12.1075 5.70968 12.1075 5.52688 11.9355 5.35484L10.6452 4.06452C10.4731 3.89247 10.2903 3.89247 10.0968 4.06452L8 6.19355L5.90323 4.06452C5.70968 3.89247 5.52688 3.89247 5.35484 4.06452L4.06452 5.35484C3.89247 5.52688 3.89247 5.70968 4.06452 5.90323L6.19355 8L4.06452 10.0968C3.89247 10.2903 3.89247 10.4731 4.06452 10.6452L5.35484 11.9355C5.52688 12.1075 5.70968 12.1075 5.90323 11.9355L8 9.80645L10.0968 11.9355C10.2903 12.1075 10.4731 12.1075 10.6452 11.9355L11.9355 10.6452C12.1075 10.4731 12.1075 10.2903 11.9355 10.0968Z",fill:"#B6BCC8"}})])]),t("h3",{staticClass:"monsterinsights-semrush-cta-title",domProps:{textContent:e._s(e.text_cta_title)}}),t("p",{staticClass:"monsterinsights-semrush-cta-content",domProps:{innerHTML:e._s(e.text_cta_content)}}),t("a",{staticClass:"monsterinsights-button",attrs:{href:"https://www.monsterinsights.com/refer/semrush",target:"_blank"},domProps:{textContent:e._s(e.text_cta_btn)}})]):e._e()},Ho=[],Vo=l(Oo,Uo,Ho,!1,null,null,null,null);const Yo=Vo.exports,{__:V}=wp.i18n,jo={name:"InstallAndActivateAiseo",computed:{...C({addons:"$_addons/addons"})},data(){return{isCtaOpen:!1,isAiseoInstalled:!1,isAiseoActive:!1,installing:!1,activating:!1,text_cta_title:V("Want to increase your search traffic?","google-analytics-for-wordpress"),text_cta_content:V("All In One SEO is the most popular and best WordPress plugin to help you easily increase organic search traffic. Install it for free.","google-analytics-for-wordpress"),text_cta_install_activate_btn:V("Install Now","google-analytics-for-wordpress"),text_cta_activate_btn:V("Activate Now","google-analytics-for-wordpress"),installing_label:V("Installing...","google-analytics-for-wordpress")}},mounted(){this.aiseoStatus()},methods:{closeCTA(){const s=this;let e=new FormData;e.append("action","monsterinsights_vue_dismiss_aiseo_cta"),e.append("nonce",w.prototype.$mi.nonce),ne.post(w.prototype.$mi.ajax,e).then(t=>{t.data.dismissed==="yes"?s.isCtaOpen=!1:s.isCtaOpen=!0}).catch(function(){s.isCtaOpen=!0})},aiseoStatus(){const s=this;let e=new FormData;e.append("action","monsterinsights_get_aiseo_cta_status"),e.append("nonce",this.$mi.nonce),ne.post(this.$mi.ajax,e).then(function(t){t.data.dismissed==="yes"?s.isCtaOpen=!1:s.isCtaOpen=!0}).catch(function(){s.isCtaOpen=!1})},aioseoBaseName(){return this.addons.aioseo!==void 0?this.addons.aioseo.basename:""},aioseoInstalled(){return this.addons.aioseo!==void 0?this.addons.aioseo.installed:!1},aioseoActive(){return this.addons.aioseo!==void 0?this.addons.aioseo.active:!1},clickAction(s){s?this.activatePlugin():this.installPlugin()},async installPlugin(){let s=this;this.installing=!0,this.yoast_button_text=V("Installing AIOSEO...","google-analytics-for-wordpress"),(await et.installAioseo()).success===!0?(this.installing=!1,this.yoast_button_text=V("Activate AIOSEO","google-analytics-for-wordpress"),this.$swal({icon:"success",customClass:{container:"monsterinsights-swal"},title:V("Congrats! All-in-One SEO Installed.","google-analytics-for-wordpress"),allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!1,showCancelButton:!0,confirmButtonText:V("Activate AIOSEO","google-analytics-for-wordpress"),cancelButtonText:V("Dismiss","google-analytics-for-wordpress")}).then(function(t){if(t.value)s.activatePlugin();else return window.location=window.location.href,!1})):(this.installing=!1,this.yoast_button_text=V("Switch to AIOSEO","google-analytics-for-wordpress"),this.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-loading monsterinsights-seo-swal"},icon:"error",title:V("Installation Failed. Please refresh and try again.","google-analytics-for-wordpress")}))},async activatePlugin(){let s=this;this.activating=!0,this.yoast_button_activate_text=V("Activating AIOSEO...","google-analytics-for-wordpress"),await et.activateAioseo(this.aioseoBaseName()).then(function(e){e===!0?window.location.reload():(s.activating=!1,this.yoast_button_activate_text=V("Activate AIOSEO","google-analytics-for-wordpress"),s.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-loading monsterinsights-seo-swal"},icon:"error",title:V("Activation Failed. Please refresh and try again.","google-analytics-for-wordpress")}))})}}};var Zo=function(){var e=this,t=e._self._c;return e.isCtaOpen?t("div",{staticClass:"monsterinsights-install-activate-aiseo"},[t("button",{staticClass:"monsterinsights-aiseo-cta-close",on:{click:function(r){return r.preventDefault(),e.closeCTA.apply(null,arguments)}}},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M2.32258 2.35484C3.89247 0.784946 5.78495 0 8 0C10.2151 0 12.0968 0.784946 13.6452 2.35484C15.2151 3.90323 16 5.78495 16 8C16 10.2151 15.2151 12.1075 13.6452 13.6774C12.0968 15.2258 10.2151 16 8 16C5.78495 16 3.89247 15.2258 2.32258 13.6774C0.774194 12.1075 0 10.2151 0 8C0 5.78495 0.774194 3.90323 2.32258 2.35484ZM11.9355 10.0968L9.80645 8L11.9355 5.90323C12.1075 5.70968 12.1075 5.52688 11.9355 5.35484L10.6452 4.06452C10.4731 3.89247 10.2903 3.89247 10.0968 4.06452L8 6.19355L5.90323 4.06452C5.70968 3.89247 5.52688 3.89247 5.35484 4.06452L4.06452 5.35484C3.89247 5.52688 3.89247 5.70968 4.06452 5.90323L6.19355 8L4.06452 10.0968C3.89247 10.2903 3.89247 10.4731 4.06452 10.6452L5.35484 11.9355C5.52688 12.1075 5.70968 12.1075 5.90323 11.9355L8 9.80645L10.0968 11.9355C10.2903 12.1075 10.4731 12.1075 10.6452 11.9355L11.9355 10.6452C12.1075 10.4731 12.1075 10.2903 11.9355 10.0968Z",fill:"#B6BCC8"}})])]),t("h3",{staticClass:"monsterinsights-aiseo-cta-title",domProps:{textContent:e._s(e.text_cta_title)}}),t("p",{staticClass:"monsterinsights-aiseo-cta-content",domProps:{innerHTML:e._s(e.text_cta_content)}}),t("button",{staticClass:"monsterinsights-button",class:e.installing||e.activating?"installing":"",attrs:{disabled:e.installing||e.activating},on:{click:function(r){e.clickAction(e.aioseoInstalled())}}},[e.aioseoInstalled()?t("span",[e._v(" "+e._s(e.text_cta_activate_btn)+" ")]):t("span",[e._v(" "+e._s(e.installing?e.installing_label:e.text_cta_install_activate_btn)+" ")])])]):e._e()},Wo=[],zo=l(jo,Zo,Wo,!1,null,null,null,null);const Ko=zo.exports,{__:K}=wp.i18n,qo={name:"ReportSearchConsole",components:{ReportUpsellOverlay:E,ReportTableBox:_e,ReportsDatePicker:I,ReportsPdfExport:F,SemRushCta:Yo,InstallAndActivateAiseo:Ko},mixins:[B],created(){this.$store.commit("$_reports/UPDATE_DATE_STORE",{compareReport:!1})},mounted(){if(this.showUpsell()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_reports/getReportData","queries")},data(){return{text_search_console:K("Search Console Report","google-analytics-for-wordpress"),text_top_search_terms:K("Top 50 Google Search Terms","google-analytics-for-wordpress"),top_search_terms_headers:[K("Terms","google-analytics-for-wordpress"),K("Clicks","google-analytics-for-wordpress"),K("Impressions","google-analytics-for-wordpress"),K("CTR","google-analytics-for-wordpress"),K("Avg. Position","google-analytics-for-wordpress")],text_empty_generic:K("No data for this time period.","google-analytics-for-wordpress"),text_ga_button:K("View Full Queries Report","google-analytics-for-wordpress"),text_top_search_tooltip:K("This list shows the top 50 search terms people type into Google to find and click through to your website.","google-analytics-for-wordpress"),text_search_console_upsell:K("Search Console","google-analytics-for-wordpress")}},computed:{...C({addons:"$_addons/addons"}),queries(){return this.showUpsell()?this.$store.getters["$_reports/demo_queries"]:this.$store.getters["$_reports/queries"]},has_queries(){return this.queries.queries&&this.queries.queries.length>0},isAllInOneSeoInstalledAndActivated(){return this.addons.aioseo!==void 0?this.addons.aioseo.installed:this.addons.aioseo!==void 0?this.addons.aioseo.active:!1},topSearchTermsRows(){let s=[];return this.queries.queries&&this.queries.queries.forEach(e=>{let t=this.$formatNumber(e.ctr),r=this.$formatNumber(e.position,1),o=this.$formatNumber(e.clicks),n=this.$formatNumber(e.impressions);t+="%",s.push([Array.isArray(e.term)?e.term:[e.term],[o],[n],[t],[r]])}),s}},methods:{gaLinks(s){return typeof this.queries.galinks<"u"&&this.queries.galinks[s]?this.queries.galinks[s]:!1}}};var Go=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-search-console"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_search_console)}}),t("reports-date-picker",{attrs:{"compare-options":!1}}),t("reports-pdf-export",{attrs:{"report-title":e.text_search_console}})],1),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_top_search_terms,emptytext:e.text_empty_generic,headers:e.top_search_terms_headers,rows:e.topSearchTermsRows,tooltip:e.text_top_search_tooltip}},[e.gaLinks("queries")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("queries"),target:"_blank"},domProps:{textContent:e._s(e.text_ga_button)},slot:"button"}):e._e()])],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"queries"}}):e._e(),e.showUpsell()?e._e():t("div",[e.has_queries?t("div",[e.isAllInOneSeoInstalledAndActivated?t("div",[t("sem-rush-cta")],1):t("div",[t("install-and-activate-aiseo")],1)]):e._e()])],1)},Jo=[],Qo=l(qo,Go,Jo,!1,null,null,null,null);const Xo=Qo.exports,{__:de,sprintf:pt}=wp.i18n,ei={name:"ReportDimensions",components:{ReportUpsellOverlay:E,ReportTableBox:_e,ReportsDatePicker:I,ReportsPdfExport:F},mixins:[B],data(){return{text_dimensions:de("Dimensions Report","google-analytics-for-wordpress"),text_dimension_button:de("View %s Report","google-analytics-for-wordpress"),text_dimension_errors:{MISSING_CUSTOM_DIMENSION:de('Please set up custom dimension "%s" for this report to work.',"google-analytics-for-wordpress"),UNKNOWN_ERROR:de("An unknown error has occurred.","google-analytics-for-wordpress")},fake_headers:["Author","Percent"]}},computed:{dimensions(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_dimensions"]:this.$store.getters["$_reports/dimensions"]},enabledDimensions(){let s=[];if(this.dimensions.dimensions)for(let e in this.dimensions.dimensions)this.dimensions.dimensions.hasOwnProperty(e)&&this.dimensions.dimensions[e].enabled&&s.push(this.dimensions.dimensions[e]);return s}},methods:{getDimensionHeaders(s){return[s.name,de("Views","google-analytics-for-wordpress")]},prepareDimensionRows(s){let e=[];if(s.id&&this.dimensions[s.id]){let t=this.dimensions[s.id].data;t&&t.forEach(r=>{e.push([this.tableFormatRowData(r.label),this.tableFormatRowData(r.pageview,"",this.$formatNumber)])})}return e},prepareDimensionError(s){if(s.id&&this.dimensions[s.id]&&this.dimensions[s.id].error){const e=this.dimensions[s.id];return this.sprintf(this.text_dimension_errors[e.error.code]||this.text_dimension_errors.UNKNOWN_ERROR,e.error.data||"")}return""},sprintf:pt,gaLinks(s){return typeof this.dimensions.galinks<"u"&&this.dimensions.galinks[s]?this.dimensions.galinks[s]:!1},getDimensionEmptyText(s){return pt(de("No data currently for the %s report","google-analytics-for-wordpress"),s)}},mounted(){if(this.showUpsellPlus()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_reports/getReportData","dimensions")}};var ti=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-dimensions"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_dimensions)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_dimensions}})],1),e.dimensions.dimensions?t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-report-2-columns"},e._l(e.enabledDimensions,function(r,o){return t("report-table-box",{key:o,attrs:{title:r.label,tooltip:r.tooltip,rows:e.prepareDimensionRows(r),headers:e.getDimensionHeaders(r),error:e.prepareDimensionError(r),emptytext:e.getDimensionEmptyText(r.label)}})}),1):e._e(),e.showUpsellPlus()?t("report-upsell-overlay",{attrs:{report:"dimensions"}}):e._e()],1)},si=[],ri=l(ei,ti,si,!1,null,null,null,null);const oi=ri.exports,{__:$,sprintf:we}=wp.i18n,ii={name:"ReportForms",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,Multiselect:qe,SiteNotesOverview:te,ReportTable:Q},mixins:[B],computed:{forms(){return this.showUpsellPlus()?this.$store.getters["$_reports/demo_forms"]:this.$store.getters["$_reports/forms"]},formsRows(){let s=[],e;switch(this.breakdownValue.value){case"campaign":e=this.forms.forms_campaign;break;case"sourceMedium":e=this.forms.forms_source_medium;break;default:e=this.forms.forms}return e&&e.length&&e.forEach(t=>{s.push([this.tableFormatRowData(t.id),this.tableFormatRowData(t.impressions,"",this.$formatNumber),this.tableFormatRowData(t.conversions,"",this.$formatNumber),this.tableFormatRowData(t.conversionrate,"%",this.$formatNumber)])}),s},formError(){return this.forms.forms&&this.forms.forms.error?this.sprintf(this.text_errors[this.forms.forms.error.code]||this.text_errors.UNKNOWN_ERROR,this.forms.forms.error.data||""):""}},data(){return{text_forms:$("Forms Report","google-analytics-for-wordpress"),text_overview_title:$("Forms Overview Report","google-analytics-for-wordpress"),text_forms_table:$("Forms","google-analytics-for-wordpress"),text_forms_empty:$("There were no impressions for this date range.","google-analytics-for-wordpress"),text_forms_tooltip:$("This list shows the number of conversions, impressions and conversion rate for each form on your website with at least 1 impression during the selected time range.","google-analytics-for-wordpress"),text_forms_button:$("View Full Forms Report","google-analytics-for-wordpress"),text_errors:{MISSING_CUSTOM_DIMENSION:$('Please set up custom dimension "%s" for this report to work.',"google-analytics-for-wordpress"),UNKNOWN_ERROR:$("An unknown error has occurred.","google-analytics-for-wordpress")},text_sessions:$("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:we($("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:$("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:we($("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},show_overview_notes:!1,text_site_notes:$("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:$("Close Site Notes","google-analytics-for-wordpress"),text_formsimpressions:$("Form Impressions","google-analytics-for-wordpress"),text_formsimpressions_tooltip:we($("Unique Impressions","google-analytics-for-wordpress"),"<br />"),text_formcompletions:$("Form Completions","google-analytics-for-wordpress"),text_formcompletions_tooltip:we($("Unique Completions","google-analytics-for-wordpress"),"<br />"),topValues:{sessions:0,pageviews:0,formcompletions:0,formimpressions:0},text_choose_breakdown:$("Choose Breakdown","google-analytics-for-wordpress"),breakdownOptions:[{value:"none",label:$("None","google-analytics-for-wordpress")},{value:"sourceMedium",label:$("Source / Medium","google-analytics-for-wordpress")},{value:"campaign",label:$("Campaign","google-analytics-for-wordpress")}],breakdownValue:{value:"none",label:"None"}}},methods:{gaLink(){return this.showUpsellPlus()?this.$getUpgradeUrl("reports","forms"):typeof this.forms.galinks<"u"&&this.forms.galinks.forms?this.forms.galinks.forms:!1},sprintf:we,switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsKey(){return this.overviewKey+"-sessions"},sessionsData(){return this.forms.sessions_chart?{labels:this.forms.sessions_chart.categories,data:this.forms.sessions_chart.sessions,timestamps:this.forms.sessions_chart.timestamps,trend:this.forms.sessions_chart.session_trendpoints,notes:this.forms.sessions_chart.notes,compare:this.forms.sessions_compare_chart?this.forms.sessions_compare_chart.sessions:[]}:{}},pageviewsData(){return this.forms.sessions_chart?{labels:this.forms.sessions_chart.categories,data:this.forms.sessions_chart.page_views,timestamps:this.forms.sessions_chart.timestamps,trend:this.forms.sessions_chart.page_view_trendpoints,notes:this.forms.sessions_chart.notes,compare:this.forms.sessions_compare_chart?this.forms.sessions_compare_chart.page_views:[]}:{}},formImpressionsData(){return this.forms.overviewgraph&&this.forms.overviewgraph.impressions?{labels:this.forms.overviewgraph.labels,data:this.forms.overviewgraph.impressions.datapoints,timestamps:this.forms.overviewgraph.timestamps,trend:this.forms.overviewgraph.impressions.trendpoints,notes:this.forms.sessions_chart.notes,compare:this.forms.overviewgraph.compare?this.forms.overviewgraph.compare.impressions:[]}:{}},formCompletionsData(){return this.forms.overviewgraph&&this.forms.overviewgraph.conversions?{labels:this.forms.overviewgraph.labels,data:this.forms.overviewgraph.conversions.datapoints,timestamps:this.forms.overviewgraph.timestamps,trend:this.forms.overviewgraph.conversions.trendpoints,notes:this.forms.sessions_chart.notes,compare:this.forms.overviewgraph.compare?this.forms.overviewgraph.compare.impressions:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},formImpressionsKey(){return this.overviewKey+"-form-impressions"},formCompletionsKey(){return this.overviewKey+"-form-completions"},getTopValue(s){return this.forms.overviewgraph&&this.forms.overviewgraph[s]?this.$formatNumber(this.forms.overviewgraph[s].total):this.forms.sessions_chart?this.$formatNumber(this.forms.sessions_chart["total_"+s]):0},toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},getFormsHeaders(){return[{title:$("Form Name or ID","google-analytics-for-wordpress"),key:"form_name_or_id",sortable:!0},{title:$("Impressions","google-analytics-for-wordpress"),key:"impressions",sortable:!0},{title:$("Conversions","google-analytics-for-wordpress"),key:"conversions",sortable:!0},{title:$("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0}]}},mounted(){if(this.showUpsellPlus()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_reports/getReportData","forms")}};var ai=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-forms"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_overview_title)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_forms}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation--home"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-sessions",expression:"'report-sessions'"}]}),t("span",{domProps:{textContent:e._s(e.text_sessions)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.getTopValue("sessions"))+" ")])]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-pageviews",expression:"'report-pageviews'"}]}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.getTopValue("page_views"))+" ")])]),t("button",{class:e.activeTabButtonClass("formsimpressions"),on:{click:function(r){return e.switchTab("formsimpressions")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-forms-impressions",expression:"'report-forms-impressions'"}]}),t("span",{domProps:{textContent:e._s(e.text_formsimpressions)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.getTopValue("impressions"))+" ")])]),t("button",{class:e.activeTabButtonClass("formcompletions"),on:{click:function(r){return e.switchTab("formcompletions")}}},[t("span",{directives:[{name:"icon",rawName:"v-icon",value:"report-forms-completions",expression:"'report-forms-completions'"}]}),t("span",{domProps:{textContent:e._s(e.text_formcompletions)}}),t("div",{staticClass:"monsterinsights-report-tabs-navigation__top-value"},[e._v(" "+e._s(e.getTopValue("conversions"))+" ")])])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_pageviews_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="formsimpressions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.formImpressionsKey(),attrs:{id:"chart-overview-impressions","chart-data":e.formImpressionsData(),tooltipDescriptor:e.text_formsimpressions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="formcompletions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.formCompletionsKey(),attrs:{id:"chart-overview-completions","chart-data":e.formCompletionsData(),tooltipDescriptor:e.text_formcompletions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),t("div",{staticClass:"monsterinsights-report-row"},[t("div",{staticClass:"monsterinsights-ecommerce-funnel-table-header"},[t("h3",{staticClass:"monsterinsights-report-title",domProps:{textContent:e._s(e.text_forms)}}),t("p",[e._v(e._s(e.text_choose_breakdown))]),t("multiselect",{attrs:{options:e.breakdownOptions,multiple:!1,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"","allow-empty":!1},model:{value:e.breakdownValue,callback:function(r){e.breakdownValue=r},expression:"breakdownValue"}})],1),t("report-table",{attrs:{"table-title":e.text_forms_table,rows:e.formsRows,columns:e.getFormsHeaders(),error:e.formError,emptytext:e.text_forms_empty,"allow-search":!1}})],1),e.showUpsellPlus()?t("report-upsell-overlay",{attrs:{report:"forms"}}):e._e()],1)},ni=[],li=l(ii,ai,ni,!1,null,null,null,null);const ci=li.exports,{__:v,sprintf:dt}=wp.i18n,pi={name:"ReportRealTime",components:{ReportUpsellOverlay:E,SettingsInfoTooltip:Pe,ReportTableBox:_e,ReportOverviewLineChartApex:J},mixins:[B],computed:{realtime(){return this.showUpsell()?this.$store.getters["$_reports/demo_realtime"]:this.$store.getters["$_reports/realtime"]},realtimedata(){return this.realtime.realtime?this.realtime.realtime:!1},top_pages_rows(){let s=[];return this.realtimedata.toppages&&this.realtimedata.toppages.forEach(e=>{let t=this.$formatNumber(e.percent),r=this.$formatNumber(e.count);t+="%",s.push([[e.title],[r],[t]])}),s},top_referral_rows(){let s=[];return this.realtimedata.referrals&&this.realtimedata.referrals.forEach(e=>{let t=this.$formatNumber(e.percent),r=this.$formatNumber(e.count);t+="%",s.push([[e.source],[e.path],[e.campaign],[r],[t]])}),s},top_countries_rows(){let s=[];return this.realtimedata.countries&&this.realtimedata.countries.forEach(e=>{let t=this.$formatNumber(e.percent),r=this.$formatNumber(e.count);t+="%";let o=e.iso?e.iso.toLowerCase():"";s.push([['<span class="monsterinsights-flag monsterinsights-flag-'+o+'"></span> '+e.name],[r],[t]])}),s},top_cities_rows(){let s=[];return this.realtimedata.cities&&this.realtimedata.cities.forEach(e=>{let t=e.iso?e.iso.toLowerCase():"",r=this.$formatNumber(e.count);s.push([[e.city],[e.region],['<span class="monsterinsights-flag monsterinsights-flag-'+t+'"></span> '+e.name],[r]])}),s},hasRealTimeChart(){return typeof this.realtimedata.pageviewsovertime<"u"},hasReferralsData(){return typeof this.realtimedata.referrals<"u"}},data(){return{text_realtime:v("Real-Time Report","google-analytics-for-wordpress"),text_right_now:v("Right Now","google-analytics-for-wordpress"),text_active:v("Active users on site","google-analytics-for-wordpress"),text_graph_not_available:v("The real-time graph of visitors over time is not currently available for this site. Please try again later.","google-analytics-for-wordpress"),text_active_explainer:v("Important: this only includes users who are tracked in real-time. Not all users are tracked in real-time including (but not limited to) logged-in site administrators, certain mobile users, and users who match a Google Analytics filter.","google-analytics-for-wordpress"),text_refresh_explainer:v("The real-time report automatically updates approximately every 60 seconds.","google-analytics-for-wordpress"),text_refresh_ago:v("The real-time report was last updated %s seconds ago.","google-analytics-for-wordpress"),text_refresh_explainer_2:v("The latest data will be automatically shown on this page when it becomes available.","google-analytics-for-wordpress"),text_refresh_explainer_3:v("There is no need to refresh the browser (doing so won't have any effect).","google-analytics-for-wordpress"),text_graph_title:v("Pageviews Per Minute","google-analytics-for-wordpress"),text_chart_tooltip:dt(v("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),text_top_pages:v("Top Pages","google-analytics-for-wordpress"),text_top_pages_empty:v("No pageviews currently.","google-analytics-for-wordpress"),top_pages_headers:[v("Page","google-analytics-for-wordpress"),v("Pageview Count","google-analytics-for-wordpress"),v("Percent of Total","google-analytics-for-wordpress")],text_top_referral:v("Top Referral Traffic Sources","google-analytics-for-wordpress"),text_top_referral_empty:v("No referral traffic currently.","google-analytics-for-wordpress"),top_referral_headers:[v("Source","google-analytics-for-wordpress"),v("Path","google-analytics-for-wordpress"),v("Campaign","google-analytics-for-wordpress"),v("Count","google-analytics-for-wordpress"),v("Percent","google-analytics-for-wordpress")],text_top_countries:v("Top Countries","google-analytics-for-wordpress"),text_top_countries_empty:v("No traffic currently.","google-analytics-for-wordpress"),top_countries_headers:[v("Country","google-analytics-for-wordpress"),v("Count","google-analytics-for-wordpress"),v("Percent","google-analytics-for-wordpress")],text_top_cities:v("Top Cities","google-analytics-for-wordpress"),text_top_cities_empty:v("No traffic currently.","google-analytics-for-wordpress"),top_cities_headers:[v("City","google-analytics-for-wordpress"),v("State/Region","google-analytics-for-wordpress"),v("Country","google-analytics-for-wordpress"),v("Count","google-analytics-for-wordpress")],text_right_now_tooltip:v("This is the number of active users in the last 30 minutes on your site.","google-analytics-for-wordpress"),text_pageviews_tooltip:v("This graph shows the number of pageviews for each of the last 30 minutes.","google-analytics-for-wordpress"),text_top_pages_tooltip:v("This list shows the top pages users visited in the last 30 minutes.","google-analytics-for-wordpress"),text_referrals_tooltip:v("This list shows the top referral traffic sources for your site currently.","google-analytics-for-wordpress"),text_countries_tooltip:v("This list shows the top countries visitors are visiting within the last 30 minutes. ","google-analytics-for-wordpress"),text_city_tooltip:v("This list shows the top cities visitors are browsing from for the last 30 minutes.","google-analytics-for-wordpress"),text_top_pages_button:v("View All Real-Time Pageviews","google-analytics-for-wordpress"),text_referrals_button:v("View All Real-Time Traffic Sources","google-analytics-for-wordpress"),text_countries_button:v("View All Real-Time Traffic by Country","google-analytics-for-wordpress"),text_city_button:v("View All Real-Time Traffic by City","google-analytics-for-wordpress"),poll:!1,autofetch:!1,seconds:0,fetch_errors:0}},methods:{livechartData(){return this.realtimedata.pageviewsovertime?{labels:this.realtimedata.pageviewsovertime.labels,data:this.realtimedata.pageviewsovertime.datapoints,trend:this.realtimedata.pageviewsovertime.trendpoints,timestamps:this.realtimedata.pageviewsovertime.timestamps}:{}},getReportData(s){const e=this;if(!(e.fetch_errors>5||e.showUpsell())){if(!document.hasFocus()&&!s)for(;!document.hasFocus();){e.$swal({icon:"info",title:v("Real-Time Report Paused","google-analytics-for-wordpress"),text:v("The Real-Time Report automatically paused due to inactivity. Please refresh the page to resume the Real-Time Report.","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,showConfirmButton:!1});return}this.$store.dispatch("$_reports/getReportData","realtime").then(function(){e.startPoll(),e.seconds=0}).catch(function(){e.fetch_errors++})}},startPoll(){const s=this;this.poll=setInterval(function(){s.seconds++},1e3),this.autofetch=setTimeout(function(){clearInterval(s.poll),s.getReportData()},6e4)},stopPoll(){clearInterval(this.poll),clearTimeout(this.autofetch)},sprintf:dt,gaLinks(s){return this.showUpsell()?this.$getUpgradeUrl("reports","realtime"):typeof this.realtime.galinks<"u"&&this.realtime.galinks[s]?this.realtime.galinks[s]:!1}},mounted(){if(this.showUpsell()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.getReportData(!0)},beforeDestroy(){this.stopPoll()}};var di=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-realtime"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_realtime)}})]),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-report-2-columns"},[t("div",{staticClass:"monsterinsights-report-box"},[t("h3",{domProps:{textContent:e._s(e.text_right_now)}}),t("settings-info-tooltip",{attrs:{content:e.text_right_now_tooltip}}),t("div",{staticClass:"monsterinsights-realtime-box-content"},[e.realtimedata?[t("div",{staticClass:"monsterinsights-realtime-large",domProps:{textContent:e._s(e.realtimedata.now)}}),t("div",{staticClass:"monsterinsights-realtime-active",domProps:{textContent:e._s(e.text_active)}})]:t("p",{domProps:{textContent:e._s(e.text_graph_not_available)}}),t("p",{domProps:{textContent:e._s(e.text_active_explainer)}}),t("p",[t("span",{domProps:{textContent:e._s(e.text_refresh_explainer)}}),t("span",{domProps:{textContent:e._s(e.sprintf(e.text_refresh_ago,e.seconds))}}),t("span",{domProps:{textContent:e._s(e.text_refresh_explainer_2)}}),t("span",{domProps:{textContent:e._s(e.text_refresh_explainer_3)}})])],2)],1),e.hasRealTimeChart?t("div",{staticClass:"monsterinsights-report-box"},[t("h3",{domProps:{textContent:e._s(e.text_graph_title)}}),t("settings-info-tooltip",{attrs:{content:e.text_pageviews_tooltip}}),t("div",{staticClass:"monsterinsights-realtime-box-content"},[e.realtimedata?t("ReportOverviewLineChartApex",{attrs:{id:"realtime",chartData:e.livechartData(),tooltipDescriptor:e.text_chart_tooltip}}):e._e()],1)],1):t("report-table-box",{attrs:{title:e.text_top_pages,headers:e.top_pages_headers,rows:e.top_pages_rows,emptytext:e.text_top_pages_empty,tooltip:e.text_top_pages_tooltip}},[e.gaLinks("toppages")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("toppages"),target:"_blank"},domProps:{textContent:e._s(e.text_top_pages_button)},slot:"button"}):e._e()])],1),e.hasRealTimeChart?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_top_pages,headers:e.top_pages_headers,rows:e.top_pages_rows,emptytext:e.text_top_pages_empty,tooltip:e.text_top_pages_tooltip}},[e.gaLinks("toppages")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("toppages"),target:"_blank"},domProps:{textContent:e._s(e.text_top_pages_button)},slot:"button"}):e._e()])],1):e._e(),e.hasReferralsData?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_top_referral,headers:e.top_referral_headers,rows:e.top_referral_rows,emptytext:e.text_top_referral_empty,tooltip:e.text_referrals_tooltip}},[e.gaLinks("referrals")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("referrals"),target:"_blank"},domProps:{textContent:e._s(e.text_referrals_button)},slot:"button"}):e._e()])],1):e._e(),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-report-2-columns"},[t("report-table-box",{attrs:{title:e.text_top_countries,headers:e.top_countries_headers,rows:e.top_countries_rows,emptytext:e.text_top_countries_empty,tooltip:e.text_countries_tooltip}},[e.gaLinks("countries")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("countries"),target:"_blank"},domProps:{textContent:e._s(e.text_countries_button)},slot:"button"}):e._e()]),t("report-table-box",{attrs:{title:e.text_top_cities,headers:e.top_cities_headers,rows:e.top_cities_rows,emptytext:e.text_top_cities_empty,tooltip:e.text_city_tooltip}},[e.gaLinks("city")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("city"),target:"_blank"},domProps:{textContent:e._s(e.text_city_button)},slot:"button"}):e._e()])],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"realtime"}}):e._e()],1)},hi=[],gi=l(pi,di,hi,!1,null,null,null,null);const ui=gi.exports,mi={name:"ReportYearInReviewByMonth",components:{apexchart:Re},props:{chartData:[Object,Boolean],title:String,subTitle:String,id:String,icon:{type:String,default:"map"}},computed:{...C({date:"$_reports/date"}),tooltipId(){return"monsterinsights-chartjs-pie-"+this.id+"-tooltip"},titleClassName(){return"monsterinsights-report-title with-outbound-icon with-icon-"+this.icon},chartOptions(){return{chart:{type:"bar",toolbar:{show:!1}},plotOptions:{bar:{horizontal:!1}},xaxis:{categories:this.chartData.labels},yaxis:{decimalsInFloat:!1},colors:me.COLORS,dataLabels:{enabled:!1},tooltip:{y:{formatter:function(s){return Number.isInteger(s)?String(s)+"%":String(Number(s).toFixed(2))+"%"},title:{formatter:function(){return""}}}},fill:{opacity:1}}},chartSeries(){return[{name:this.date.intervalText,data:this.chartData.values}]},chartColors(){return me.COLORS}},methods:{getColors(){return[s=>(console.log(s),this.chartColors[s.dataPointIndex]?this.chartColors[s.dataPointIndex]:this.chartColors[0])]}}};var _i=function(){var e=this,t=e._self._c;return e.chartData?t("div",{staticClass:"monsterinsights-reports-visitorbymonth-chart monsterinsights-yir-chart"},[t("div",{staticClass:"monsterinsights-reports-visitorbymonth-header monsterinsights-yir-chart-header"},[t("div",{staticClass:"monsterinsights-reports-visitorbymonth-title monsterinsights-yir-chart-header-title"},[t("h3",{class:e.titleClassName,domProps:{textContent:e._s(e.title)}})]),t("div",{staticClass:"monsterinsights-reports-visitorbymonth-subtitle monsterinsights-yir-chart-header-subtitle"},[t("h4",{staticClass:"monsterinsights-report-subtitle",domProps:{textContent:e._s(e.subTitle)}})])]),t("div",{staticClass:"monsterinsights-reports-visitorbymonth-chart-content monsterinsights-yir-chart-content"},[t("div",{staticClass:"monsterinsights-reports-bar-chart-holder"},[t("div",{staticClass:"monsterinsights-chart-tooltip",attrs:{id:e.tooltipId}}),t("apexchart",{attrs:{options:e.chartOptions,series:e.chartSeries,width:"100%",height:"100%"}})],1)])]):e._e()},fi=[],vi=l(mi,_i,fi,!1,null,null,null,null);const wi=vi.exports,yi={name:"ReportYearInReviewTip",props:{title:String,summary:String,link:String,linkText:String}};var Ci=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-yir-year-in-review-tip monsterinsights-yir-tip"},[t("div",{staticClass:"monsterinsights-yir-tip-icon"}),t("div",{staticClass:"monsterinsights-yir-tip-content"},[t("h3",{staticClass:"monsterinsights-yir-tip-content-title",domProps:{textContent:e._s(e.title)}}),t("p",{staticClass:"monsterinsights-yir-tip-content-summary",domProps:{innerHTML:e._s(e.summary)}}),t("div",{staticClass:"monsterinsights-yir-tip-content-link-wrapper"},[t("a",{staticClass:"monsterinsights-yir-tip-content-link",attrs:{href:e.link,target:"_blank"},domProps:{textContent:e._s(e.linkText)}})])])])},bi=[],xi=l(yi,Ci,bi,!1,null,null,null,null);const ki=xi.exports,{__:ht}=wp.i18n,$i={name:"ReportYearInReviewListBox",props:{title:String,subTitle:String,tooltip:String,rows:Array},data(){return{limit:5,text_show:ht("Show","google-analytics-for-wordpress"),text_no_records:ht("No records found.","google-analytics-for-wordpress")}},methods:{tableRows(){let s=this.rows;if(s.length<5)for(;s.length<5;)s.push({number:"",text:"",right:""});return s=s.slice(0,this.limit),s}}};var Ri=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-year-in-review-table-box monsterinsights-yir-chart"},[t("div",{staticClass:"monsterinsights-year-in-review-table-box-header monsterinsights-yir-chart-header"},[t("div",{staticClass:"monsterinsights-year-in-review-table-box-title monsterinsights-yir-chart-header-title"},[e.title?t("h3",{staticClass:"monsterinsights-report-title",domProps:{textContent:e._s(e.title)}}):e._e()]),t("div",{staticClass:"monsterinsights-year-in-review-table-box-subtitle monsterinsights-yir-chart-header-subtitle"},[e.subTitle?t("h4",{staticClass:"monsterinsights-report-subtitle",domProps:{textContent:e._s(e.subTitle)}}):e._e()])]),t("div",{staticClass:"monsterinsights-year-in-review-table-box-list"},[e.rows.length>0?t("div",e._l(e.tableRows(),function(r,o){return t("div",{key:o,staticClass:"monsterinsights-table-list-item"},[t("span",{staticClass:"monsterinsights-reports-list-count",domProps:{textContent:e._s(r.number)}}),t("span",{staticClass:"monsterinsights-reports-list-text",domProps:{innerHTML:e._s(r.text)}}),t("span",{staticClass:"monsterinsights-reports-list-number"},[r.right?t("span",{domProps:{innerHTML:e._s(parseFloat(r.right).toLocaleString("en"))}}):e._e()])])}),0):e._e(),e.rows.length===0?t("div",{staticClass:"monsterinsights-table-list-item"},[t("span",{staticClass:"monsterinsights-reports-list-text",domProps:{innerHTML:e._s(e.text_no_records)}})]):e._e()]),e.tooltip?t("div",{staticClass:"monsterinsights-year-in-review-table-box-footer"},[t("span",{staticClass:"monsterinsights-yir-tooltip"},[t("span",{domProps:{textContent:e._s(e.tooltip)}})])]):e._e()])},Pi=[],Di=l($i,Ri,Pi,!1,null,null,null,null);const Si=Di.exports,{__:Ue}=wp.i18n,Ti={name:"ReportYearInReviewDeviceUsage",props:{deviceData:[Object,Boolean],title:String,subtitle:String,id:String},data(){return{labels:{mobile:Ue("Mobile","google-analytics-for-wordpress"),desktop:Ue("Desktop","google-analytics-for-wordpress"),tablet:Ue("Tablet","google-analytics-for-wordpress")}}},computed:{},methods:{}};var Mi=function(){var e=this,t=e._self._c;return e.deviceData?t("div",{staticClass:"monsterinsights-reports-device-usage"},[e.title?t("h3",{staticClass:"monsterinsights-report-title",domProps:{textContent:e._s(e.title)}}):e._e(),e.subtitle?t("h3",{staticClass:"monsterinsights-report-subtitle",domProps:{innerHTML:e._s(e.subtitle)}}):e._e(),t("div",{staticClass:"monsterinsights-reports-device-usage-cards"},[t("div",{staticClass:"monsterinsights-yir-card monsterinsights-yir-green2-card"},[t("h4",{staticClass:"monsterinsights-yir-card-title with-small-mobile-icon",domProps:{textContent:e._s(e.labels.mobile)}}),t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s((e.deviceData.mobile.value||0)+"%")}})]),t("div",{staticClass:"monsterinsights-yir-card monsterinsights-yir-orange-card"},[t("h4",{staticClass:"monsterinsights-yir-card-title with-small-desktop-icon",domProps:{textContent:e._s(e.labels.desktop)}}),t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s((e.deviceData.desktop.value||0)+"%")}})]),t("div",{staticClass:"monsterinsights-yir-card monsterinsights-yir-blue2-card"},[t("h4",{staticClass:"monsterinsights-yir-card-title with-small-tablet-icon",domProps:{textContent:e._s(e.labels.tablet)}}),t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s((e.deviceData.tablet.value||0)+"%")}})])])]):e._e()},Li=[],Ei=l(Ti,Mi,Li,!1,null,null,null,null);const Ai=Ei.exports,{__:X,sprintf:Fi}=wp.i18n,Ni={name:"YearInReviewReportsPdfExport",components:{},props:{reportTitle:{type:String,default:X("Overview Report","google-analytics-for-wordpress")},buttonText:{type:String,default:X("Download as PDF","google-analytics-for-wordpress")},reportContainer:{type:String,default:".monsterinsights-reports-page"},filename:{type:String,default:"monsterinsights-report.pdf"},quality:{type:Number,default:1},pdfdpi:{type:Number,default:300},orientation:{type:String,default:"portrait"},ignoreClasses:{type:Array,default(){return[".monsterinsights-yir-export-link",".monsterinsights-header",".monsterinsights-notices-area",".monsterinsights-navigation-bar",".monsterinsights-yir-year-in-review-plugins-block",".monsterinsights-yir-join-communities",".with-behavior-icon"]}},ignoreHeightOfClasses:{type:Array,default(){return[".monsterinsights-header",".monsterinsights-notices-area",".monsterinsights-navigation-bar",".monsterinsights-yir-year-in-review-plugins-block",".monsterinsights-yir-join-communities",".with-behavior-icon"]}}},data(){return{logo:this.$mi.assets+"/img/logo-MonsterInsights.png",downloading:!1,mobileWidth:783}},computed:{...C({date:"$_reports/date",activeReport:"$_reports/activeReport",settings:"$_settings/settings"}),getDateRange(){let s=x().subtract(30,"days").format("MMMM Do YYYY"),e=x().subtract(1,"days").format("MMMM Do YYYY");return this.date.end&&this.date.start&&(s=x(this.date.start).format("MMMM Do YYYY"),e=x(this.date.end).format("MMMM Do YYYY")),Fi("%1$s - %2$s",s,e)}},methods:{exportPDFReport(){let s=this;if(!document.querySelector(s.reportContainer)){s.downloading=!1,s.$swal({icon:"error",title:X("Download Failed","google-analytics-for-wordpress"),text:X("Download Failed, no report found!","google-analytics-for-wordpress"),confirmButtonText:X("Ok","google-analytics-for-wordpress")});return}document.body.classList.add("monsterinsights-downloading-pdf-report"),s.downloading=!0,s.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-loading monsterinsights-pdf-report-swal"},icon:"info",title:X("Generating PDF Report","google-analytics-for-wordpress"),didOpen:function(){s.$swal.showLoading()}}),s.ignoreContents(),setTimeout(()=>{s.doExport()},2e3)},async doExport(){let s=this,e=document.querySelector(s.reportContainer),t=s.getOuterHeight(e)-25-s.ignoreElementsHeight()+15,r={margin:[0,-70],filename:s.filename,image:{type:"jpeg",quality:this.quality},enableLinks:!1,html2canvas:{dpi:s.pdfdpi,scale:2,scrollY:0,height:t,onclone:n=>{n.querySelectorAll("[data-html2canvas-class]").forEach(d=>{const p=d.getAttribute("data-html2canvas-class");p&&(d.className=[d.className.trim(),p.trim()].join(" "))})}},jsPDF:{orientation:s.orientation,format:[980,t],unit:"px"}};try{await Gt().from(e).set(r).toPdf().get("pdf").then(function(n){n.setPage(1),n.setFontSize(20),n.setFont(void 0,"bold"),n.setTextColor("#393f4c")}).save()}catch(n){this.downloading=!1,s.pageBreaks("reset"),document.body.classList.remove("monsterinsights-downloading-pdf-report"),s.$swal({icon:"error",title:X("Download Failed","google-analytics-for-wordpress"),text:n,confirmButtonText:X("Ok","google-analytics-for-wordpress")});return}this.downloading=!1,s.pageBreaks("reset"),document.body.classList.remove("monsterinsights-downloading-pdf-report");let o=document.querySelectorAll(".monsterinsights-accordion-item");o.length&&o.forEach(function(n){n.setAttribute("style","margin-bottom: 0")}),s.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-succcess monsterinsights-pdf-report-swal"},icon:"success",title:X("Downloaded PDF report successfully!","google-analytics-for-wordpress"),confirmButtonText:X("Ok","google-analytics-for-wordpress")})},ignoreContents(){this.ignoreClasses.length&&this.ignoreClasses.forEach(s=>{let e=document.querySelectorAll(s);e.length&&e.forEach(function(t){if(s===".monsterinsights-active-tab-button")t.removeAttribute("data-html2canvas-ignore");else{let r=document.createAttribute("data-html2canvas-ignore");r.value="",t.setAttributeNode(r)}})})},ignoreElementsHeight(){let s=this,e=0;return s.ignoreHeightOfClasses.forEach(t=>{let r=document.querySelectorAll(t);r.length&&r.forEach(function(o){t===".monsterinsights-table-box-footer"&&o.parentNode.parentNode.querySelectorAll(t).length===2?e+=s.getOuterHeight(o)/2:t===".monsterinsights-mobile-upsell"?o.children.length===0||window.innerWidth>s.mobileWidth||screen.width>s.mobileWidth||(e+=s.getOuterHeight(o)):t===".monsterinsights-overview-upsell-desktop"&&(o.children.length===0||window.innerWidth<s.mobileWidth||screen.width<s.mobileWidth)||(e+=s.getOuterHeight(o))})}),e},getOuterHeight(s){let e=s.offsetHeight,t=getComputedStyle(s);return e+=parseInt(t.marginTop)+parseInt(t.marginBottom),e},getHeaderImage(){let s=new Image;return s.src=this.logo,this.settings.pdf_reports_header_image&&(s.src=this.settings.pdf_reports_header_image),document.location.protocol=="https:"&&s.src.startsWith("http://")&&(s.src="https://"+s.src.substring(7)),document.location.protocol=="http:"&&s.src.startsWith("https://")&&(s.src="http://"+s.src.substring(8)),s},mmToPx(s){return s*3.779527559055},pxToMm(s){return s*.2645833333},pageBreaks(s="add"){let e=this,t=document.querySelectorAll(".monsterinsights-report-row"),r=e.mmToPx(210.0015555555555),o=105,n=0;if(s==="reset"){t.forEach(function(d){d.setAttribute("style","margin-bottom: 25px;")});return}for(var a=0;a<t.length;a++){let d=e.getOuterHeight(t[a]),p=t[a].querySelector(".monsterinsights-table-box-footer");if(p&&(d=e.getOuterHeight(t[a])-e.getOuterHeight(p)),a===0&&(n+=o),t[a].classList.contains("monsterinsights-overview-upsell-desktop")||t[a].classList.contains("monsterinsights-mobile-upsell")||(n+=d,n<r))continue;let m=r-(n-d);m+=50;let b=a>0?a-1:0;(t[b].classList.contains("monsterinsights-overview-upsell-desktop")||t[a].classList.contains("monsterinsights-mobile-upsell"))&&(b=b>0?b-1:0),t[b].setAttribute("style","margin-bottom:"+Math.round(m)+"px;"),t.length===1&&t[0].setAttribute("style","margin-bottom: 0;"),n=d+25}}}};var Ii=function(){var e=this,t=e._self._c;return t("span",{attrs:{data:"html2canvas-ignore"}},[t("a",{staticClass:"monsterinsights-yir-export-link",domProps:{innerHTML:e._s(e.buttonText)},on:{click:function(r){return r.preventDefault(),e.exportPDFReport.apply(null,arguments)}}})])},Bi=[],Oi=l(Ni,Ii,Bi,!1,null,null,null,null);const Ui=Oi.exports,{__:c,_n:ye,sprintf:P}=wp.i18n,Hi={name:"YearInReview",components:{ReportYearInReviewByMonth:wi,ReportYearInReviewTip:ki,ReportYearInReviewListBox:Si,ReportYearInReviewDeviceUsage:Ai,AddonBlock:gs,YearInReviewReportsPdfExport:Ui},data(){return{text_calculating:c("Still Calculating...","google-analytics-for-wordpress"),text_back_to_overview_report:c("Back to Overview Report","google-analytics-for-wordpress"),text_audience_section_title:c("Audience","google-analytics-for-wordpress"),text_congrats:c("Congrats","google-analytics-for-wordpress"),text_popular:c("Congrats! This year your website had ","google-analytics-for-wordpress"),text_you_had:c("You had ","google-analytics-for-wordpress"),text_visitors:c(" visitors","google-analytics-for-wordpress"),text_best_month_visitors:c(" visitors","google-analytics-for-wordpress"),text_total_visitors:c("Total Visitors","google-analytics-for-wordpress"),text_total_sessions:c("Total Sessions","google-analytics-for-wordpress"),text_visitor_by_month_chart_title:c("Visitors by Month","google-analytics-for-wordpress"),text_section_demographics_title:c("Demographics","google-analytics-for-wordpress"),text_number_one:c("#1","google-analytics-for-wordpress"),text_countries:c("You Top 5 Countries","google-analytics-for-wordpress"),text_know_your_visitors:c("Let’s get to know your visitors a little better, shall we?","google-analytics-for-wordpress"),text_gender:c("Gender","google-analytics-for-wordpress"),text_female:c("Female","google-analytics-for-wordpress"),text_women:c("Women","google-analytics-for-wordpress"),text_male:c("Male","google-analytics-for-wordpress"),text_unknown:c("Unknown","google-analytics-for-wordpress"),text_average_age:c("Average Age","google-analytics-for-wordpress"),text_section_behavior_title:c("Behavior","google-analytics-for-wordpress"),text_top_pages_graph_title:c("Your Top 5 Pages","google-analytics-for-wordpress"),text_top_pages_graph_subtitle:c("Pageviews","google-analytics-for-wordpress"),text_time_spent:c("Time Spent on Site","google-analytics-for-wordpress"),text_minutes:c("minutes","google-analytics-for-wordpress"),text_device_type:c("Device Type","google-analytics-for-wordpress"),text_grow_traffic_tip_summary:c("Did you know that using internal links can give you huge SEO gains? Get the scoop on internal links and how to build them in our guide.","google-analytics-for-wordpress"),text_grow_traffic_tip_link_text:c("How to Add WordPress Internal Links to Improve Your SEO","google-analytics-for-wordpress"),text_grow_traffic_tip_link:this.$getUrl("yearinreview","tips","https://www.monsterinsights.com/how-to-add-wordpress-internal-links-to-improve-your-seo/?utm_source=drip&utm_medium=email&utm_campaign=newsletter&utm_content=YIR"),text_visitors_come_from:c("So, where did all of these visitors come from?","google-analytics-for-wordpress"),text_clicks:c("Clicks","google-analytics-for-wordpress"),text_top_keywords:c("Your Top 5 Keywords","google-analytics-for-wordpress"),text_top_keywords_tooltip:c("What keywords visitors searched for to find your site","google-analytics-for-wordpress"),text_top_referrals:c("Your Top 5 Referrals","google-analytics-for-wordpress"),text_pageviews:c("Pageviews","google-analytics-for-wordpress"),text_top_referrals_tooltip:c("The websites that link back to your website","google-analytics-for-wordpress"),text_opportunity_tip_title:c("Opportunity","google-analytics-for-wordpress"),text_opportunity_tip_summary:c("Did you know you might have direct traffic that's getting counted wrong by Google Analytics? Learn how to track it correctly in our direct traffic guide.","google-analytics-for-wordpress"),text_opportunity_tip_link_text:c("What is Direct Traffic in Google Analytics? (GA4 Updates)","google-analytics-for-wordpress"),text_opportunity_tip_link:this.$getUrl("yearinreview","tips","https://www.monsterinsights.com/what-is-direct-traffic-in-google-analytics/"),text_thank_you_section_title:c("Thank you for using MonsterInsights!","google-analytics-for-wordpress"),text_thank_you_section_summary:c("We’re grateful for your continued support. If there’s anything we can do to help you grow your business, please don’t hesitate to contact our team.","google-analytics-for-wordpress"),text_enjoying_monsterinsights:c("Enjoying MonsterInsights?","google-analytics-for-wordpress"),text_leave_review:c("Leave a five star review!","google-analytics-for-wordpress"),text_syed_balkhi:c("Syed Balkhi","google-analytics-for-wordpress"),text_chris_christoff:c("Chris Christoff","google-analytics-for-wordpress"),text_write_review:c("Write Review","google-analytics-for-wordpress"),text_plugins_section_title:c("Did you know over 25 million websites use our plugins?","google-analytics-for-wordpress"),text_communities_section_title:c("Join our Communities!","google-analytics-for-wordpress"),text_facebook_group:c("Facebook Group","google-analytics-for-wordpress"),text_facebook_group_summary:c("Join our team of WordPress experts and other motivated website owners in the WPBeginner Engage Facebook Group.","google-analytics-for-wordpress"),text_facebook_join_button:c("Join Now...It’s Free!","google-analytics-for-wordpress"),text_wpbeginner_community_title:c("WordPress Tutorials by WPBeginner","google-analytics-for-wordpress"),text_wpbeginner_community_summary:c("WPBeginner is the largest free WordPress resource site for beginners and non-techy users.","google-analytics-for-wordpress"),text_visit_wpbeginner:c("Visit WPBeginner","google-analytics-for-wordpress"),text_follow_us:c("Follow Us!","google-analytics-for-wordpress"),text_follow_us_summary:c("Follow MonsterInsights on social media to stay up to date with latest updates, trends, and tutorials on how to make the most out of analytics.","google-analytics-for-wordpress"),text_search_console_upsell_overlay_details:c("Upgrade to MonsterInsights Pro to Unlock Additional Actionable Insights","google-analytics-for-wordpress"),text_search_console_upsell_overlay_btn_text:c("Upgrade to MonsterInsights Pro","google-analytics-for-wordpress"),text_search_console_upsell_overlay_btn_link:this.$getUrl("yearinreview","searchconsole","https://www.monsterinsights.com/lite/"),months:{Jan:c("January","google-analytics-for-wordpress"),Feb:c("February","google-analytics-for-wordpress"),Mar:c("March","google-analytics-for-wordpress"),Apr:c("April","google-analytics-for-wordpress"),May:c("May","google-analytics-for-wordpress"),Jun:c("June","google-analytics-for-wordpress"),Jul:c("July","google-analytics-for-wordpress"),Aug:c("August","google-analytics-for-wordpress"),Sep:c("September","google-analytics-for-wordpress"),Oct:c("October","google-analytics-for-wordpress"),Nov:c("November","google-analytics-for-wordpress"),Dec:c("December","google-analytics-for-wordpress")},logo:this.$mi.assets+"/img/logo-MonsterInsights.png",logo2x:this.$mi.assets+"/img/<EMAIL> 2x"}},mounted(){this.$store.dispatch("$_reports/getReportData","yearinreview")},computed:{...C({yearinreview_data:"$_reports/yearinreview_data",addons:"$_addons/addons",yearInReview:"$_reports/yearinreview",date:"$_reports/date",license:"$_license/license",license_network:"$_license/license_network"}),link(){return this.$getUrl("logo","header","https://www.monsterinsights.com/lite/")},stringsWithYear(){return{text_year_in_review_still_calculating:P(c("Your %s Year in Review is still calculating. Please check back later to see how your website performed last year.","google-analytics-for-wordpress"),this.yearinreview_data.report_year),text_title:P(c("Your %s Analytics Report","google-analytics-for-wordpress"),this.yearinreview_data.report_year),text_sub_title:P(c("%s Year in Review","google-analytics-for-wordpress"),this.yearinreview_data.report_year),text_visitor_by_month_chart_tooltip:P(c("January 1, %1$s - December 31, %2$s","google-analytics-for-wordpress"),this.yearinreview_data.report_year,this.yearinreview_data.report_year),text_summary:P(c("See how your website performed this year and find tips along the way to help grow even more in %s!","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_audience_tip_title:P(c("A Tip for %s","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_grow_traffic_tip_title:P(c("A Tip For %s","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_plugins_section_summary:P(c("Try our other popular WordPress plugins to grow your website in %s.","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_communities_section_summary:P(c("Become a WordPress expert in %s. Join our amazing communities and take your website to the next level.","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_copyright_monsterinsights:P(c("© MonsterInsights, %s MonsterInsights, LLC","google-analytics-for-wordpress"),this.yearinreview_data.next_year),text_amazing_next_year:P(c("Here's to an amazing %s!","google-analytics-for-wordpress"),this.yearinreview_data.next_year),report_title:P(c("Year In Review %s!","google-analytics-for-wordpress"),this.yearinreview_data.report_year)}},topCountryName(){if(!(typeof this.yearInReview>"u"||this.yearInReview.countries.length<1))return this.yearInReview.countries[0].name},visitorByMonthData(){return{values:this.yearInReview.usersgraph.datapoints,labels:this.yearInReview.usersgraph.labels}},bestMonthVisitorsSummary(){return P(ye("Your best month was <strong>%1$s</strong> with <strong>%2$s visitor!</strong>","Your best month was <strong>%1$s</strong> with <strong>%2$s visitors!</strong>",this.yearInReview.usersgraph.max,"google-analytics-for-wordpress"),this.months[this.yearInReview.usersgraph.bestmonth],this.commaNumbers(this.yearInReview.usersgraph.max))},bestMonthTip(){return P(c("See the top %sTraffic Sources%s and %sTop Pages%s for the Month of %s in the Overview Report to replicate your success.","google-analytics-for-wordpress"),'<a href="'+this.$router.resolve({name:"overview"}).href+'">',"</a>",'<a href="'+this.$router.resolve({name:"overview"}).href+'">',"</a>",this.months[this.yearInReview.usersgraph.bestmonth])},countriesData(){let s=[],e=0;return this.yearInReview.countries&&this.yearInReview.countries.forEach(function(t){e++,s.push({number:e+".",text:'<span class="monsterinsights-flag monsterinsights-flag-'+t.iso.toLowerCase()+'"></span> '+t.name,right:t.sessions})}),s},demoGraphicsSummary(){return P(c("Your <strong>%1$s</strong> visitors came from <strong>%2$s</strong> different countries.","google-analytics-for-wordpress"),this.commaNumbers(this.yearInReview.info.users.value),this.commaNumbers(this.yearInReview.countries.length))},topCountryFlagClass(){return"monsterinsights-flag-"+this.yearInReview.countries[0].iso.toLowerCase()},topCountryVisitors(){return P(c("%s Visitors","google-analytics-for-wordpress"),this.commaNumbers(this.yearInReview.countries[0].sessions))},maxVisitorGender(){let s="",e=this.getMaxVisitorGenderObj();return e.gender==="male"&&(s=this.text_male.toLowerCase()),e.gender==="female"&&(s=this.text_female.toLowerCase()),e.gender==="unknown"&&(s=this.text_unknown.toLowerCase()),s},maxVisitorGenderSummary(){return P(c("%1$s&#37 of your visitors were %2$s","google-analytics-for-wordpress"),this.getMaxVisitorGenderObj().percent,this.maxVisitorGender)},maxVisitorAverageAge(){return this.getMaxVisitorAgeObj().age},maxVisitorAgeSummary(){return P(c("%1$s&#37 of your visitors were between the ages of %2$s","google-analytics-for-wordpress"),this.getMaxVisitorGenderObj().percent,this.maxVisitorAverageAge)},behaviourSummary(){const s=parseInt(this.yearInReview.info.users.value),e=parseInt(this.yearInReview.info.pageviews.value),t=s===0?0:e/s,r=P(ye("Your <strong>%s</strong> visitor","Your <strong>%s</strong> visitors",s,"google-analytics-for-wordpress"),this.commaNumbers(s)),o=P(ye("viewed a total of <strong>%s</strong> page.","viewed a total of <strong>%s</strong> pages.",e,"google-analytics-for-wordpress"),this.commaNumbers(e)),n=P(ye("That's an average of %s page for each visitor!","That's an average of %s pages for each visitor!",t,"google-analytics-for-wordpress"),this.commaNumbers(t,2));return r+" "+o+"<br /><span class='average-page-per-user'>"+n+"</span>"},topPages(){let s=[],e=0;return this.yearInReview.toppages&&this.yearInReview.toppages.forEach(function(t){e++;let r=t.hostname?'<a href="'+t.hostname+t.url+'" target="_blank" rel="noreferrer noopener">'+t.title+"</a>":t.title;s.push({number:e+".",text:r,right:t.sessions})}),s},eachVisitorSpentSummary(){return P(ye("Each visitor spent an average of <strong>%1$s minute</strong> on your website in <strong>%2$s</strong>.","Each visitor spent an average of <strong>%1$s minutes</strong> on your website in <strong>%2$s</strong>.",Math.floor(this.yearInReview.info.duration.avg_minutes),"google-analytics-for-wordpress"),this.commaNumbers(this.yearInReview.info.duration.avg_minutes,2),this.yearinreview_data.report_year)},mostVisitorsDevice(){if(this.yearInReview.devices){let s=this.yearInReview.devices,e=0,t=[];for(let r in s)s.hasOwnProperty(r)&&s[r]>e&&(t.name=r,t.percent=s[r],e=s[r]);return t}return!1},mostVisitorsDeviceClassName(){return"monsterinsights-yir-most-visitors-device is-device-"+this.mostVisitorsDevice.name},mostVisitorsDeviceSummary(){return P(c("Most of your visitors viewed your website from their <strong>%s</strong> device.","google-analytics-for-wordpress"),this.mostVisitorsDevice.name)},mostVisitorsDevicePercent(){return P(c("%1$s&#37 of your visitors were on a %2$s device.","google-analytics-for-wordpress"),this.mostVisitorsDevice.percent,this.mostVisitorsDevice.name)},devicesData(){return this.yearInReview.devices?{desktop:{label:c("Desktop","google-analytics-for-wordpress"),value:this.yearInReview.devices.desktop,color:"#6AB1FC"},tablet:{label:c("Tablet","google-analytics-for-wordpress"),value:this.yearInReview.devices.tablet,color:"#AAD3FF"},mobile:{label:c("Mobile","google-analytics-for-wordpress"),value:this.yearInReview.devices.mobile,color:"#338EEF"}}:!1},referralsData(){let s=[],e=0;return this.yearInReview.referrals&&this.yearInReview.referrals.forEach(function(t){e++,s.push({number:e+".",text:'<img src="https://www.google.com/s2/favicons?domain=http://'+t.url+'" />'+t.url,right:t.sessions})}),s},searchConsoleSampleData(){return[{number:1,text:"search term one",right:7978},{number:2,text:"search term two",right:79789},{number:3,text:"search three",right:897},{number:4,text:"search four",right:797},{number:5,text:"search term five",right:299}]}},methods:{isAddonActive(s){return this.addons[s]?this.addons[s].active:!1},commaNumbers(s,e=0){return parseFloat(parseFloat(s).toFixed(e)).toLocaleString("en")},isMoreVisitors(){return this.yearInReview.info.users.value>this.yearInReview.info.users.prev},getMaxVisitorGenderObj(){let s=this.yearInReview.gender,e=0,t={};return s===void 0||s.forEach(function(r){r.percent>e&&(e=r.percent,t=r)}),t},getMaxVisitorAgeObj(){let s=this.yearInReview.age,e=0,t={};return s.forEach(function(r){r.percent>e&&(e=r.percent,t=r)}),t},pluginsList(){let s=["optinmonster","wpforms-lite","userfeedback-lite","wp-mail-smtp","all-in-one-seo-pack","coming-soon","rafflepress","pushengage","instagram-feed","custom-facebook-feed","feeds-for-youtube","custom-twitter-feeds","trustpulse-api","searchwp-live-ajax-search","affiliate-wp","stripe","easy-digital-downloads","sugar-calendar-lite","charitable","insert-headers-and-footers","duplicator"],e=[];return s.forEach(t=>{if(this.addons[t]){let r=Object.create(this.addons[t]);r.type="licensed",e.push(r)}}),e}}};var Vi=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-reports-page",class:["monsterinsights-report-year-in-review","monsterinsights-report-row",e.yearInReview.is_enabled?"":"monsterinsights-yir-report-calculating-row"]},[e.yearInReview.is_enabled?e._e():t("div",{staticClass:"monsterinsights-yir-top-header monsterinsights-yir-report-calculating"},[t("p",{staticClass:"monsterinsights-yir-summary",domProps:{textContent:e._s(e.stringsWithYear.text_year_in_review_still_calculating)}}),t("router-link",{staticClass:"monsterinsights-navigation-tab-link",attrs:{to:"/"},domProps:{textContent:e._s(e.text_back_to_overview_report)}})],1),e.yearInReview.is_enabled?t("div",[t("div",{staticClass:"monsterinsights-logo-area",attrs:{"data-html2canvas-class":"display-flex-important"}},[t("a",{attrs:{href:e.link,target:"_blank",rel:"noopener"}},[t("img",{attrs:{src:e.logo,srcset:e.logo2x}})])]),t("header",{staticClass:"monsterinsights-yir-top-header"},[t("h1",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.stringsWithYear.text_title)}})]),t("section",{staticClass:"monsterinsights-yir-audience"},[t("year-in-review-reports-pdf-export",{staticClass:"monsterinsights-export-button",attrs:{"report-title":e.stringsWithYear.report_title}}),t("h2",{staticClass:"monsterinsights-yir-purple-title monsterinsights-yir-title",domProps:{textContent:e._s(e.text_audience_section_title)}}),t("h3",{staticClass:"monsterinsights-yir-summary"},[e.isMoreVisitors?t("span",{domProps:{textContent:e._s(e.text_popular)}}):e._e(),t("span",[t("strong",{domProps:{textContent:e._s(e.commaNumbers(e.yearInReview.info.users.value)+e.text_visitors)}}),e._v(".")]),t("br"),t("span",{domProps:{innerHTML:e._s(e.bestMonthVisitorsSummary)}})]),t("div",{staticClass:"monsterinsights-yir-total-visitors-sessions"},[t("div",{staticClass:"monsterinsights-yir-visitors monsterinsights-yir-card monsterinsights-yir-blue-card"},[t("h4",{staticClass:"monsterinsights-yir-card-title",domProps:{textContent:e._s(e.text_total_visitors)}}),t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s(e.commaNumbers(e.yearInReview.info.users.value))}})]),t("div",{staticClass:"monsterinsights-yir-sessions monsterinsights-yir-card monsterinsights-yir-green-card"},[t("h4",{staticClass:"monsterinsights-yir-card-title",domProps:{textContent:e._s(e.text_total_sessions)}}),t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s(e.commaNumbers(e.yearInReview.info.sessions.value))}})])]),t("div",{staticClass:"monsterinsights-yir-visitor-by-chart monsterinsights-yir-chart"},[t("report-year-in-review-by-month",{attrs:{id:"visitorbymonth",chartData:e.visitorByMonthData,title:e.text_visitor_by_month_chart_title,"sub-title":e.stringsWithYear.text_visitor_by_month_chart_tooltip}})],1),t("report-year-in-review-tip",{attrs:{title:e.stringsWithYear.text_audience_tip_title,summary:e.bestMonthTip}})],1),t("section",{staticClass:"monsterinsights-yir-demographics monsterinsights-yir-map-bg"},[t("h2",{staticClass:"monsterinsights-yir-title monsterinsights-yir-purple-title",domProps:{textContent:e._s(e.text_section_demographics_title)}}),t("h3",{staticClass:"monsterinsights-yir-summary",domProps:{innerHTML:e._s(e.demoGraphicsSummary)}}),t("div",{staticClass:"monsterinsights-yir-countries"},[t("div",{staticClass:"monsterinsights-yir-top-country"},[t("h4",{staticClass:"monsterinsights-yir-top-country-name",domProps:{textContent:e._s(e.topCountryName)}}),t("h5",{staticClass:"monsterinsights-yir-top-country-visitors",domProps:{textContent:e._s(e.topCountryVisitors)}})]),t("div",{staticClass:"monsterinsights-yir-top-countries-graph monsterinsights-yir-chart"},[t("report-year-in-review-list-box",{attrs:{title:e.text_countries,"sub-title":e.text_best_month_visitors,rows:e.countriesData}})],1)])]),t("section",{staticClass:"monsterinsights-yir-behavior"},[t("h2",{staticClass:"monsterinsights-yir-title monsterinsights-yir-purple-title",domProps:{textContent:e._s(e.text_section_behavior_title)}}),t("h3",{staticClass:"monsterinsights-yir-summary",domProps:{innerHTML:e._s(e.behaviourSummary)}}),t("div",{staticClass:"monsterinsights-yir-pages-data"},[t("div",{staticClass:"monsterinsights-yir-pages-summary"},[t("div",{staticClass:"with-behavior-icon"}),t("br"),t("span",{staticClass:"monsterinsights-yir-time-spent",domProps:{textContent:e._s(e.text_time_spent)}}),t("div",{staticClass:"monsterinsights-yir-total-time-spent-wrapper"},[t("h4",{staticClass:"monsterinsights-yir-total-time-spent"},[t("span",{staticClass:"monsterinsights-yir-number",domProps:{textContent:e._s(e.commaNumbers(e.yearInReview.info.duration.total_minutes))}}),t("span",{staticClass:"monsterinsights-yir-type",domProps:{textContent:e._s(e.text_minutes)}})]),t("h5",{staticClass:"monsterinsights-yir-each-visitor-spent monsterinsights-blue-warning",domProps:{innerHTML:e._s(e.eachVisitorSpentSummary)}})])]),t("div",{staticClass:"monsterinsights-yir-top-pages-graph monsterinsights-yir-chart"},[t("report-year-in-review-list-box",{attrs:{title:e.text_top_pages_graph_title,"sub-title":e.text_top_pages_graph_subtitle,rows:e.topPages}})],1)]),t("h3",{class:e.mostVisitorsDeviceClassName,domProps:{innerHTML:e._s(e.mostVisitorsDeviceSummary)}}),t("div",{staticClass:"monsterinsights-yir-visitors-info"},[t("report-year-in-review-device-usage",{attrs:{id:"devices",deviceData:e.devicesData,title:e.text_device_type,subtitle:e.mostVisitorsDevicePercent}})],1),t("div",{staticClass:"monsterinsights-yir-grow-traffic-tip"},[t("report-year-in-review-tip",{attrs:{title:e.stringsWithYear.text_grow_traffic_tip_title,summary:e.text_grow_traffic_tip_summary,"link-text":e.text_grow_traffic_tip_link_text,link:e.text_grow_traffic_tip_link}})],1)]),t("div",{staticClass:"monsterinsights-yir-visitors-come-from"},[t("h2",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_visitors_come_from)}}),t("div",{staticClass:"monsterinsights-yir-keywords-referrals"},[t("div",{staticClass:"monsterinsights-yir-referrals"},[t("report-year-in-review-list-box",{attrs:{title:e.text_top_referrals,"sub-title":e.text_pageviews,tooltip:e.text_top_referrals_tooltip,rows:e.referralsData}})],1)])]),t("div",{staticClass:"monsterinsights-yir-grow-traffic-tip"},[t("report-year-in-review-tip",{attrs:{title:e.text_opportunity_tip_title,summary:e.text_opportunity_tip_summary,"link-text":e.text_opportunity_tip_link_text,link:e.text_opportunity_tip_link}})],1),t("section",{staticClass:"monsterinsights-yir-thank-you"},[t("h2",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_thank_you_section_title)}}),t("h3",{staticClass:"monsterinsights-yir-summary",domProps:{textContent:e._s(e.text_thank_you_section_summary)}}),t("div",{staticClass:"monsterinsights-yir-dashboard-preview"}),t("h4",{staticClass:"monsterinsights-yir-amazing-year",domProps:{textContent:e._s(e.stringsWithYear.text_amazing_next_year)}}),t("div",{staticClass:"monsterinsights-yir-authors"},[t("div",{staticClass:"monsterinsights-yir-author"},[t("div",{staticClass:"monsterinsights-yir-thumbnail syed"}),t("span",{staticClass:"monsterinsights-yir-name",domProps:{textContent:e._s(e.text_syed_balkhi)}})]),t("div",{staticClass:"monsterinsights-yir-author"},[t("div",{staticClass:"monsterinsights-yir-thumbnail chris"}),t("span",{staticClass:"monsterinsights-yir-name",domProps:{textContent:e._s(e.text_chris_christoff)}})])]),t("div",{staticClass:"monsterinsights-yir-write-review"},[t("div",{staticClass:"monsterinsights-yir-content"},[t("span",{domProps:{textContent:e._s(e.text_enjoying_monsterinsights)}}),t("h3",{domProps:{textContent:e._s(e.text_leave_review)}})]),e._m(0),t("div",{staticClass:"monsterinsights-yir-review-button"},[t("a",{attrs:{href:"https://wordpress.org/support/view/plugin-reviews/google-analytics-for-wordpress?filter=5",target:"_blank"},domProps:{textContent:e._s(e.text_write_review)}})])])]),t("section",{staticClass:"monsterinsights-yir-year-in-review-plugins-block",attrs:{data:"html2canvas-ignore"}},[t("h2",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_plugins_section_title)}}),t("div",{staticClass:"monsterinsights-yir-plugins"},e._l(e.pluginsList(),function(r,o){return t("addon-block",{key:o,attrs:{addon:r,"is-addon":!1}})}),1)]),t("section",{staticClass:"monsterinsights-yir-join-communities",attrs:{data:"html2canvas-ignore"}},[t("h2",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_communities_section_title)}}),t("div",{staticClass:"monsterinsights-yir-communities"},[t("div",{staticClass:"monsterinsights-yir-community"},[t("div",{staticClass:"monsterinsights-yir-thumbnail with-chat-icon"}),t("h3",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_facebook_group)}}),t("p",{staticClass:"monsterinsights-yir-details",domProps:{textContent:e._s(e.text_facebook_group_summary)}}),t("a",{staticClass:"monsterinsights-yir-link",attrs:{href:"https://www.facebook.com/groups/wpbeginner/",target:"_blank"},domProps:{textContent:e._s(e.text_facebook_join_button)}})]),t("div",{staticClass:"monsterinsights-yir-community"},[t("div",{staticClass:"monsterinsights-yir-thumbnail with-pen-icon"}),t("h3",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_wpbeginner_community_title)}}),t("p",{staticClass:"monsterinsights-yir-details",domProps:{textContent:e._s(e.text_wpbeginner_community_summary)}}),t("a",{staticClass:"monsterinsights-yir-link",attrs:{href:"https://www.wpbeginner.com/",target:"_blank"},domProps:{textContent:e._s(e.text_visit_wpbeginner)}})]),t("div",{staticClass:"monsterinsights-yir-community"},[t("div",{staticClass:"monsterinsights-yir-thumbnail with-global-icon"}),t("h3",{staticClass:"monsterinsights-yir-title",domProps:{textContent:e._s(e.text_follow_us)}}),t("p",{staticClass:"monsterinsights-yir-details",domProps:{textContent:e._s(e.text_follow_us_summary)}}),e._m(1)])])]),t("footer",{staticClass:"monsterinsights-yir-footer"},[t("div",{domProps:{innerHTML:e._s(e.stringsWithYear.text_copyright_monsterinsights)}}),t("div",{staticClass:"monsterinsights-yir-text-right"},[t("div",{staticClass:"monsterinsights-footer-logo"},[t("a",{attrs:{href:e.link,target:"_blank",rel:"noopener"}},[t("img",{attrs:{src:e.logo,srcset:e.logo2x}})])])])])]):e._e()])},Yi=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"monsterinsights-yir-rating"},[e("ul",{staticClass:"monsterinsights-yir-five-star"},[e("li",{staticClass:"monsterinsights-yir-five-star-icon"}),e("li",{staticClass:"monsterinsights-yir-five-star-icon"}),e("li",{staticClass:"monsterinsights-yir-five-star-icon"}),e("li",{staticClass:"monsterinsights-yir-five-star-icon"}),e("li",{staticClass:"monsterinsights-yir-five-star-icon"})])])},function(){var s=this,e=s._self._c;return e("ul",{staticClass:"monsterinsights-yir-social-links"},[e("li",[e("a",{staticClass:"with-small-youtube-icon",attrs:{href:"https://www.youtube.com/channel/UCnB-GV6lyQYgBLLhQr-kuVw",target:"_blank"}})]),e("li",[e("a",{staticClass:"with-small-fb-icon",attrs:{href:"https://www.facebook.com/monsterinsights",target:"_blank"}})]),e("li",[e("a",{staticClass:"with-small-twitter-icon",attrs:{href:"https://twitter.com/monsterinsights",target:"_blank"}})]),e("li",[e("a",{staticClass:"with-small-linkedin-icon",attrs:{href:"https://www.linkedin.com/company/awesome-motive-inc./",target:"_blank"}})])])}],ji=l(Hi,Vi,Yi,!1,null,null,null,null);const Zi=ji.exports,Wi={name:"StatIcons",props:{icon:String}};var zi=function(){var e=this,t=e._self._c;return e.icon?t("span",{staticClass:"monsterinsights-stat-icon"},[e.icon=="danger"?t("span",[t("svg",{attrs:{width:"17",height:"15",viewBox:"0 0 17 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M16.5586 12.8086L9.99609 1.40625C9.50391 0.558594 8.21875 0.53125 7.72656 1.40625L1.16406 12.8086C0.671875 13.6562 1.30078 14.75 2.3125 14.75H15.4102C16.4219 14.75 17.0508 13.6836 16.5586 12.8086ZM8.875 10.4297C9.55859 10.4297 10.1328 11.0039 10.1328 11.6875C10.1328 12.3984 9.55859 12.9453 8.875 12.9453C8.16406 12.9453 7.61719 12.3984 7.61719 11.6875C7.61719 11.0039 8.16406 10.4297 8.875 10.4297ZM7.67188 5.91797C7.64453 5.72656 7.80859 5.5625 8 5.5625H9.72266C9.91406 5.5625 10.0781 5.72656 10.0508 5.91797L9.85938 9.63672C9.83203 9.82812 9.69531 9.9375 9.53125 9.9375H8.19141C8.02734 9.9375 7.89062 9.82812 7.86328 9.63672L7.67188 5.91797Z",fill:"#EB5757"}})])]):e.icon=="warning"?t("span",[t("svg",{attrs:{width:"14",height:"15",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M7 0.96875C3.25391 0.96875 0.21875 4.03125 0.21875 7.75C0.21875 11.4961 3.25391 14.5312 7 14.5312C10.7188 14.5312 13.7812 11.4961 13.7812 7.75C13.7812 4.03125 10.7188 0.96875 7 0.96875ZM7 3.97656C7.62891 3.97656 8.14844 4.49609 8.14844 5.125C8.14844 5.78125 7.62891 6.27344 7 6.27344C6.34375 6.27344 5.85156 5.78125 5.85156 5.125C5.85156 4.49609 6.34375 3.97656 7 3.97656ZM8.53125 10.9219C8.53125 11.1133 8.36719 11.25 8.20312 11.25H5.79688C5.60547 11.25 5.46875 11.1133 5.46875 10.9219V10.2656C5.46875 10.1016 5.60547 9.9375 5.79688 9.9375H6.125V8.1875H5.79688C5.60547 8.1875 5.46875 8.05078 5.46875 7.85938V7.20312C5.46875 7.03906 5.60547 6.875 5.79688 6.875H7.54688C7.71094 6.875 7.875 7.03906 7.875 7.20312V9.9375H8.20312C8.36719 9.9375 8.53125 10.1016 8.53125 10.2656V10.9219Z",fill:"#F2994A"}})])]):t("span",[t("svg",{attrs:{width:"14",height:"15",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M13.7812 7.75C13.7812 4.03125 10.7188 0.96875 7 0.96875C3.25391 0.96875 0.21875 4.03125 0.21875 7.75C0.21875 11.4961 3.25391 14.5312 7 14.5312C10.7188 14.5312 13.7812 11.4961 13.7812 7.75ZM6.20703 11.3594C6.04297 11.5234 5.74219 11.5234 5.57812 11.3594L2.73438 8.51562C2.57031 8.35156 2.57031 8.05078 2.73438 7.88672L3.36328 7.28516C3.52734 7.09375 3.80078 7.09375 3.96484 7.28516L5.90625 9.19922L10.0078 5.09766C10.1719 4.90625 10.4453 4.90625 10.6094 5.09766L11.2383 5.69922C11.4023 5.86328 11.4023 6.16406 11.2383 6.32812L6.20703 11.3594Z",fill:"#27AE60"}})])])]):e._e()},Ki=[],qi=l(Wi,zi,Ki,!1,null,null,null,null);const Bt=qi.exports,Gi={name:"AccordionItem",props:{item:Object},components:{StatIcons:Bt},data(){return{current_item_id:this.item.active,toggled:!1}},methods:{toggle(s){this.toggled=!0,this.current_item_id===s.id?this.current_item_id=0:this.current_item_id=s.id},startTransition(s){s.style.height=s.scrollHeight+"px"},endTransition(s){s.style.height=""}}};var Ji=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-accordion-item",class:{"is-active":e.item.id===e.current_item_id},attrs:{id:"monsterinsights-"+e.item.id}},[t("dt",{staticClass:"monsterinsights-accordion-item-title",on:{click:function(r){return e.toggle(e.item)}}},[t("button",{staticClass:"monsterinsights-accordion-item-trigger"},[t("h4",{staticClass:"monsterinsights-accordion-item-title-text"},[t("stat-icons",{attrs:{icon:e.item.icon}}),t("span",{staticClass:"title"},[e._v(e._s(e.item.title))])],1),t("span",{staticClass:"monsterinsights-accordion-item-trigger-icon",class:{"is-active":e.item.id===e.current_item_id}})])]),t("transition",{attrs:{name:"monsterinsights-accordion-item"},on:{enter:e.startTransition,"after-enter":e.endTransition,"before-leave":e.startTransition,"after-leave":e.endTransition}},[t("dd",{staticClass:"monsterinsights-accordion-item-details",class:e.item.id===e.current_item_id?"":"monsterinsights-hide"},[t("div",{staticClass:"monsterinsights-accordion-item-details-inner",domProps:{innerHTML:e._s(e.item.details)}})])])],1)},Qi=[],Xi=l(Gi,Ji,Qi,!1,null,null,null,null);const ea=Xi.exports,ta={name:"Accordion",components:{AccordionItem:ea},props:{content:Array}};var sa=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-accordion"},[t("dl",{staticClass:"monsterinsights-accordion monsterinsights-box",attrs:{role:"presentation"}},e._l(e.content,function(r,o){return t("accordion-item",{key:o,attrs:{item:r}})}),1)])},ra=[],oa=l(ta,sa,ra,!1,null,null,null,null);const ia=oa.exports,{__:aa}=wp.i18n,na={name:"SpeedIndicator",props:{percentage:Number,device:String},computed:{circle(){return this.percentage/100*100*Math.PI+", 9999"}},data(){return{text_overall_score:aa("Overall Score","google-analytics-for-wordpress")}},watch:{percentage:function(s){document.contains(document.querySelector("#monsterinsights-graph canvas"))&&document.querySelector("#monsterinsights-graph canvas").remove(),this.createCircle(s,this.barColor())}},methods:{barColor(){if(this.percentage>=0&&this.percentage<=49)return"#EB5757";if(this.percentage>=50&&this.percentage<=89)return"#F2994A";if(this.percentage>=90)return"#27AE60"},createCircle(s,e){var t=document.getElementById("monsterinsights-graph"),r={percent:s||25,size:t.getAttribute("data-size")||280,lineWidth:t.getAttribute("data-line")||15,rotate:t.getAttribute("data-rotate")||0},o=document.createElement("canvas"),n=document.createElement("span");n.textContent=r.percent+"%";var a=o.getContext("2d");o.width=o.height=r.size,t.appendChild(o),a.translate(r.size/2,r.size/2),a.rotate((-1/2+r.rotate/180)*Math.PI);var d=(r.size-r.lineWidth)/2,p=function(m,b,k){k=Math.min(Math.max(0,k||1),1),a.beginPath(),a.arc(0,0,d,0,Math.PI*2*k,!1),a.strokeStyle=m,a.lineCap="round",a.lineWidth=b,a.stroke()};p("#efefef",r.lineWidth,100/100),p(e,r.lineWidth,r.percent/100)}}};var la=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-progress-circle__container"},[t("span",{staticClass:"monsterinsights-progress-circle__percent"},[t("div",{staticClass:"monsterinsights-indicator-device-icon"},[e.device==="mobile"?t("span",[t("svg",{attrs:{width:"35",height:"56",viewBox:"0 0 35 56",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M29.75 0H5.25C2.29688 0 0 2.40625 0 5.25V50.75C0 53.7031 2.29688 56 5.25 56H29.75C32.5938 56 35 53.7031 35 50.75V5.25C35 2.40625 32.5938 0 29.75 0ZM17.5 52.5C15.5312 52.5 14 50.9688 14 49C14 47.1406 15.5312 45.5 17.5 45.5C19.3594 45.5 21 47.1406 21 49C21 50.9688 19.3594 52.5 17.5 52.5ZM29.75 40.6875C29.75 41.4531 29.0938 42 28.4375 42H6.5625C5.79688 42 5.25 41.4531 5.25 40.6875V6.5625C5.25 5.90625 5.79688 5.25 6.5625 5.25H28.4375C29.0938 5.25 29.75 5.90625 29.75 6.5625V40.6875Z",fill:"#393F4C"}})])]):t("span",[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"45pt",height:"45pt",viewBox:"0 0 64 56",version:"1.1"}},[t("g",{attrs:{id:"surface1"}},[t("path",{staticStyle:{stroke:"none","fill-rule":"nonzero",fill:"#393F4C","fill-opacity":"1"},attrs:{d:"M 58.085938 0 L 5.914062 0 C 2.976562 0 0.695312 2.40625 0.695312 5.25 L 0.695312 40.25 C 0.695312 43.203125 2.976562 45.5 5.914062 45.5 L 26.78125 45.5 L 25.042969 50.75 L 17.21875 50.75 C 15.695312 50.75 14.609375 51.953125 14.609375 53.375 C 14.609375 54.90625 15.695312 56 17.21875 56 L 46.78125 56 C 48.195312 56 49.390625 54.90625 49.390625 53.375 C 49.390625 51.953125 48.195312 50.75 46.78125 50.75 L 38.957031 50.75 L 37.21875 45.5 L 58.085938 45.5 C 60.914062 45.5 63.304688 43.203125 63.304688 40.25 L 63.304688 5.25 C 63.304688 2.40625 60.914062 0 58.085938 0 Z M 56.347656 38.5 L 7.652344 38.5 L 7.652344 7 L 56.347656 7 Z M 56.347656 38.5 "}})])])])]),t("div",{staticClass:"monsterinsights-indicator-percent-text",style:{color:e.barColor()}},[e._v(" "+e._s(e.percentage)+" ")]),t("span",{staticClass:"monsterinsights-indicator-overall-text"},[e._v(e._s(e.text_overall_score))])]),t("div",{staticClass:"monsterinsights-pdf-score monsterinsights-hide",attrs:{id:"monsterinsights-graph"}}),t("svg",{staticClass:"monsterinsights-progress-circle",attrs:{viewBox:"0 0 106 106",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[t("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[t("g",{attrs:{id:"ProgressBar",transform:"translate(-17.000000, -17.000000)"}},[t("circle",{attrs:{id:"Oval",stroke:"#FCEAD9","stroke-width":"8","fill-rule":"nonzero",cx:"70",cy:"70",r:"50"}}),t("path",{staticClass:"monsterinsights-progress-circle__path",attrs:{id:"Oval-Copy",d:"M70,120 C97.6142375,120 120,97.6142375 120,70 C120,42.3857625 97.6142375,20 70,20 C42.3857625,20 20,42.3857625 20,70 C20,97.6142375 42.3857625,120 70,120 Z",stroke:e.barColor(),"stroke-width":"8","stroke-dasharray":e.circle,"fill-rule":"nonzero",transform:"translate(70.000000, 70.000000) rotate(-125.000000) translate(-70.000000, -70.000000) "}})])])])])},ca=[],pa=l(na,la,ca,!1,null,null,null,null);const da=pa.exports,{__:u,sprintf:W}=wp.i18n,ha={name:"Layout",props:{sitespeed_data:Object,current_device:String},components:{StatIcons:Bt,Accordion:ia,SpeedIndicator:da},data(){return{text_how_to_improve:u("How to Improve","google-analytics-for-wordpress"),text_server_response_time:u("Server Response Time","google-analytics-for-wordpress"),text_first_contentful_paint:u("First Contentful Paint","google-analytics-for-wordpress"),text_total_blocking_time:u("Total Blocking Time","google-analytics-for-wordpress"),text_cumulative_layout_shift:u("Cumulative Layout Shift","google-analytics-for-wordpress"),text_largest_contentful_paint:u("Largest Contentful Paint","google-analytics-for-wordpress"),text_inp:u("Interaction to Next Paint","google-analytics-for-wordpress"),helper:is}},methods:{getSiteSpeedData(){return this.helper.keysReplaceHyphensWithUnderscores(this.sitespeed_data)},getStatIcon(s){const e=this.helper.getFormattedScore(s);if(e>=0&&e<=49)return"danger";if(e>=50&&e<=89)return"warning";if(e>=90)return"success"},getAccordionContent(){const s=this.getSiteSpeedData(),e=[];return s.cumulative_layout_shift&&e.push(this.getClsDescription()),s.largest_contentful_paint&&e.push(this.getLcpDescription()),s.server_response_time&&e.push(this.getSrtDescription()),s.first_contentful_paint&&e.push(this.getFcpDescription()),s.total_blocking_time&&e.push(this.getTbtDescription()),s.interaction_to_next_paint&&e.push(this.getInpDescription()),e.sort((t,r)=>t.score-r.score),e},getClsDescription(){const s=this.getSiteSpeedData().cumulative_layout_shift;return{id:1,score:s.score,icon:this.getStatIcon(s.score),title:W(u("Cumulative Layout Shift - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p><strong>%2$s </strong>%3$s</p>
						<h2>%4$s</h2>
						<p>%5$s</p>
						<p>%6$s</p>
						<p>%7$s <a target="_blank" href="https://www.wpbeginner.com/wp-themes/how-to-add-custom-fonts-in-wordpress/" title="https://www.wpbeginner.com/wp-themes/how-to-add-custom-fonts-in-wordpress/">%8$s</a></p>`,u(`Cumulative Layout Shift is a measure of the largest burst of layout shift scores for every unexpected layout shift that occurs during the lifespan of a page.
 A layout shift occurs any time a visible element changes its position from one rendered frame to the next. `,"google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for 0.1 or less.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u("A common reason for unexpected layout shifts is images without specified dimensions or reserved space for their aspect ratio. This can also be the case for ads, embeds, or any other late-loaded content.","google-analytics-for-wordpress"),u("Changes to CSS property values can require the browser to react to these changes. Some values, such as box-shadow and box-sizing, trigger re-layout, paint, and composite. Changing the top and left properties also cause layout shifts, even when the element being moved is on its own layer. Avoid animating using these properties.","google-analytics-for-wordpress"),u("Downloading and rendering web fonts can cause layout shifts. If you are using custom fonts we recommend reading","google-analytics-for-wordpress"),u("this article","google-analytics-for-wordpress"))}},getLcpDescription(){const s=this.getSiteSpeedData().largest_contentful_paint;return{id:1,score:s.score,icon:this.getStatIcon(s.score),title:W(u("Largest Contentful Paint - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p><strong>%2$s </strong>%3$s</p>
						<h2>%4$s</h2>
						<p>%5$s</p>
						<p>%6$s</p>`,u("Largest Contentful Paint reports the render time of the largest image or text block visible in the viewport, relative to when the user first navigated to the page.","google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for 2.5 seconds or less.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u("A number of factors can affect how quickly the browser can load and render a web page(including Time to first byte), and delays across any of them can have a significant impact on Largest Contentful Paint.","google-analytics-for-wordpress"),u("To improve Largest Contentful Paint you have to look at the entire loading process and make sure every step along the way is optimized and there are no delays for resources.","google-analytics-for-wordpress"))}},getSrtDescription(){const s=this.getSiteSpeedData().server_response_time;return{id:2,score:s.score,icon:this.getStatIcon(s.score),title:W(u("Server Response Time - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p><strong>%2$s </strong>%3$s</p>
						<h2>%4$s</h2>
						<p>%5$s <a target="_blank" href="https://www.wpbeginner.com/plugins/best-wordpress-caching-plugins/" title="https://www.wpbeginner.com/plugins/best-wordpress-caching-plugins/">%7$s</a>.</p>
						<p>%6$s <a target="_blank" href="https://www.wpbeginner.com/wordpress-performance-speed" title="https://www.wpbeginner.com/wordpress-performance-speed">%7$s</a>.</p>`,u("Server Response Time is the time it takes to connect to the website server to the time it takes the server to process your request and start returning data to load the website.","google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for 600ms or less server response time.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u("To improve server response time, you should use a caching plugin if you aren't using one already. A caching plugin will save a static version of your website and instead of loading the database and PHP scripts, it will serve that to your user directly. You can learn more about caching plugins","google-analytics-for-wordpress"),u("We also have a great roundup of the best ways to improve your website’s performance that can be found","google-analytics-for-wordpress"),u("here","google-analytics-for-wordpress"))}},getFcpDescription(){const s=this.getSiteSpeedData().first_contentful_paint;return{id:3,score:s.score,icon:this.getStatIcon(s.score),title:W(u("First Contentful Paint - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p>%2$s</p>
						<p><strong>%3$s </strong>%4$s</p>
						<h2>%5$s</h2>
						<p>%6$s <a target="_blank" href="https://www.wpbeginner.com/wp-themes/how-to-add-custom-fonts-in-wordpress/" title="https://www.wpbeginner.com/wp-themes/how-to-add-custom-fonts-in-wordpress/">%7$s</a>.</p>
						<p>%8$s <a target="_blank" href="https://www.wpbeginner.com/beginners-guide/speed-wordpress-save-images-optimized-web/" title="https://www.wpbeginner.com/beginners-guide/speed-wordpress-save-images-optimized-web/">%9$s</a>.</p>`,u("First Contentful Paint looks at the time it takes for the first visible element on your page to be rendered, like images and fonts.","google-analytics-for-wordpress"),u("The score you get is based on a comparison of your page's first contentful paint with other live websites.","google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for a time of 1.5 seconds or less.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u(`The most common reason causing issues with the First Contentful Paint is using custom fonts that get rendered too slow as they have to load first.
 If you are using custom fonts, we recommend reading`,"google-analytics-for-wordpress"),u("this article","google-analytics-for-wordpress"),u(`Another way to improve First Contentful Paint is to make sure the images on your site are optimized to load as fast as possible.
For tips on how to speed up image loading time, check out`,"google-analytics-for-wordpress"),u("this helpful guide","google-analytics-for-wordpress"))}},getTbtDescription(){const s=this.getSiteSpeedData().total_blocking_time;return{id:4,score:s.score,icon:this.getStatIcon(s.score),title:W(u("Total Blocking Time - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p><strong>%2$s </strong>%3$s</p>
						<h2>%4$s</h2>
						<p>%5$s <a target="_blank" href="https://www.wpbeginner.com/wp-tutorials/how-to-fix-render-blocking-javascript-and-css-in-wordpress/" title="https://www.wpbeginner.com/wp-tutorials/how-to-fix-render-blocking-javascript-and-css-in-wordpress/">%6$s</a></p>`,u(`Total Blocking time is a measurement of the time it takes until your website visitor can interact with your website.
 When your page first loads it takes a moment before you can click/tap or scroll the page.`,"google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for a Total Blocking Time of 300ms or less.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u(`The most common reason for a long blocking time is JavaScript code that is either not efficient or is too large. A quick way to improve that
 is to use an efficient caching plugin that will improve the way scripts are loaded on your page. For an easy way to improve your Total Blocking
 Time, check out`,"google-analytics-for-wordpress"),u("this summary","google-analytics-for-wordpress"))}},getInpDescription(){const s=this.getSiteSpeedData().interaction_to_next_paint;return{id:5,score:s.score,icon:this.getStatIcon(s.score),title:W(u("Interaction to Next Paint - %s","google-analytics-for-wordpress"),s.value),details:W(`<p>%1$s</p>
						<p>%2$s</p>
						<p><strong>%3$s </strong>%4$s</p>
						<h2>%5$s</h2>
						<p>%6$s</p>
						<p>%7$s <a target="_blank" href="https://www.wpbeginner.com/wp-tutorials/what-is-google-inp-score-and-how-to-improve-it-in-wordpress/" title="https://www.wpbeginner.com/wp-tutorials/what-is-google-inp-score-and-how-to-improve-it-in-wordpress/">%8$s</a></p>`,u("Interaction to Next Paint looks at the time it takes for the next paint after the user interacts with the page.","google-analytics-for-wordpress"),u("The score you get is based on a comparison of your page's interaction to next paint with other live websites.","google-analytics-for-wordpress"),u("Goal:","google-analytics-for-wordpress"),u("You should be aiming for a time of 1.5 seconds or less.","google-analytics-for-wordpress"),u("How to Improve","google-analytics-for-wordpress"),u("Ideally, you should diagnose and find slow user interactions in the field to optimize them.","google-analytics-for-wordpress"),u("It takes time and effort to improve INP, but the reward is a better user experience. That's why we recommend to learn more about","google-analytics-for-wordpress"),u("Interaction to Next Paint ","google-analytics-for-wordpress"))}}}};var ga=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-site-speed monsterinsights-site-speed-container"},[t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-report-2-columns"},[t("div",{staticClass:"monsterinsights-site-speed-indicator"},[e.sitespeed_data?t("speed-indicator",{attrs:{percentage:e.helper.getFormattedScore(e.sitespeed_data.score),device:e.current_device}}):e._e()],1),t("div",{staticClass:"monsterinsights-site-speed-stats"},[t("div",{staticClass:"monsterinsights-report-flex monsterinsights-report-2-columns"},[t("div",{staticClass:"monsterinsights-site-speed-stat"},[e.getSiteSpeedData().total_blocking_time?t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().total_blocking_time.score)}}):e._e(),e.getSiteSpeedData().total_blocking_time?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().total_blocking_time.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_total_blocking_time))])],1),t("div",{staticClass:"monsterinsights-site-speed-stat"},[e.getSiteSpeedData().cumulative_layout_shift?t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().cumulative_layout_shift.score)}}):e._e(),e.getSiteSpeedData().cumulative_layout_shift?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().cumulative_layout_shift.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_cumulative_layout_shift))])],1),t("div",{staticClass:"monsterinsights-site-speed-stat"},[e.getSiteSpeedData().largest_contentful_paint?t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().largest_contentful_paint.score)}}):e._e(),e.getSiteSpeedData().largest_contentful_paint?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().largest_contentful_paint.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_largest_contentful_paint))])],1),t("div",{staticClass:"monsterinsights-site-speed-stat"},[e.getSiteSpeedData().server_response_time?t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().server_response_time.score)}}):e._e(),e.getSiteSpeedData().server_response_time?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().server_response_time.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_server_response_time))])],1),t("div",{staticClass:"monsterinsights-site-speed-stat"},[e.getSiteSpeedData().first_contentful_paint?t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().first_contentful_paint.score)}}):e._e(),e.getSiteSpeedData().first_contentful_paint?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().first_contentful_paint.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_first_contentful_paint))])],1),e.getSiteSpeedData().interaction_to_next_paint?t("div",{staticClass:"monsterinsights-site-speed-stat"},[t("stat-icons",{attrs:{icon:e.getStatIcon(e.getSiteSpeedData().interaction_to_next_paint.score)}}),e.getSiteSpeedData().interaction_to_next_paint?t("h2",[e._v(" "+e._s(e.getSiteSpeedData().interaction_to_next_paint.value)+" ")]):e._e(),t("p",[e._v(e._s(e.text_inp))])],1):e._e()])])]),t("div",{staticClass:"monsterinsights-how-to-improve"},[t("h2",{staticClass:"title"},[e._v(" "+e._s(e.text_how_to_improve)+" ")]),t("accordion",{attrs:{id:"monsterinsights-site-speed-accordion",content:e.getAccordionContent()}})],1)])},ua=[],ma=l(ha,ga,ua,!1,null,null,null,null);const _a=ma.exports,{__:He,sprintf:fa}=wp.i18n,va={name:"SiteSpeed",components:{ReportsPdfExport:F,Layout:_a,ReportUpsellOverlay:E},mixins:[B],computed:{...C({sitespeedmobile:"$_reports/sitespeedmobile"}),sitespeed(){return this.showUpsell()?this.$store.getters["$_reports/demo_sitespeed"]:this.$store.getters["$_reports/sitespeed"]},getSiteSpeed(){return this.sitespeed_current_device==="mobile"?this.sitespeedmobile:this.sitespeed}},data(){return{text_sitespeed:He("Site Speed","google-analytics-for-wordpress"),text_run_audit:He("Run Audit","google-analytics-for-wordpress"),sitespeed_current_device:"",componentKey:!0,runningAudit:!1}},mounted(){if(localStorage.getItem("monsterinsights_sitespeed_current_device")?this.sitespeed_current_device=localStorage.getItem("monsterinsights_sitespeed_current_device"):this.sitespeed_current_device="desktop",this.showUpsell()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.dispatchData(this.sitespeed_current_device)},methods:{dispatchData(s){s==="mobile"?this.$store.dispatch("$_reports/getReportData","sitespeedmobile"):this.$store.dispatch("$_reports/getReportData","sitespeed")},showInfoFor(s){this.showUpsell()||this.sitespeed_current_device!==s&&(localStorage.setItem("monsterinsights_sitespeed_current_device",s),this.sitespeed_current_device=s,this.dispatchData(s))},async runAudit(){if(this.showUpsell())return;let s=this,e="";this.sitespeed_current_device==="mobile"?e="site_speed_run_audit_mobile":e="site_speed_run_audit",s.componentKey=!1,s.runningAudit=!0;let t=new FormData;t.append("action",e),t.append("nonce",this.$mi.nonce),ne.post(this.$mi.ajax,t).then(()=>{s.dispatchData(s.sitespeed_current_device),s.runningAudit=!1,s.componentKey=!0}).catch(function(r){if(r.response){const o=r.response;return this.$mi_error_toast({title:fa(He("Failed to load report: %1$s, %2$s","google-analytics-for-wordpress"),o.status,o.statusText)})}})}}};var wa=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-site-speed"},[t("div",{staticClass:"monsterinsights-report-top"},[t("div",{staticClass:"monsterinsights-report-flex monsterinsights-report-2-columns"},[t("div",{staticClass:"monsterinsights-report-flex"},[t("h2",{staticClass:"monsterinsights-report-top-title",domProps:{textContent:e._s(e.text_sitespeed)}}),t("div",{staticClass:"monsterinsights-choose-device-button"},[t("button",{staticClass:"monsterinsights-button mobile",class:[e.sitespeed_current_device=="mobile"?"active":""],on:{click:function(r){return r.preventDefault(),e.showInfoFor("mobile")}}},[t("svg",{attrs:{width:"13",height:"23",viewBox:"0 0 13 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{staticClass:"svg-icons",attrs:{d:"M10.875 0H2.125C1.07031 0 0.25 0.859375 0.25 1.875V18.125C0.25 19.1797 1.07031 20 2.125 20H10.875C11.8906 20 12.75 19.1797 12.75 18.125V1.875C12.75 0.859375 11.8906 0 10.875 0ZM6.5 18.75C5.79688 18.75 5.25 18.2031 5.25 17.5C5.25 16.8359 5.79688 16.25 6.5 16.25C7.16406 16.25 7.75 16.8359 7.75 17.5C7.75 18.2031 7.16406 18.75 6.5 18.75ZM10.875 14.5312C10.875 14.8047 10.6406 15 10.4062 15H2.59375C2.32031 15 2.125 14.8047 2.125 14.5312V2.34375C2.125 2.10938 2.32031 1.875 2.59375 1.875H10.4062C10.6406 1.875 10.875 2.10938 10.875 2.34375V14.5312Z",fill:"#828282"}})])]),t("button",{staticClass:"monsterinsights-button desktop",class:[e.sitespeed_current_device=="desktop"?"active":""],on:{click:function(r){return r.preventDefault(),e.showInfoFor("desktop")}}},[t("svg",{attrs:{width:"23",height:"23",viewBox:"0 0 23 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{staticClass:"svg-icons",attrs:{d:"M20.875 0H2.125C1.07031 0 0.25 0.859375 0.25 1.875V14.375C0.25 15.4297 1.07031 16.25 2.125 16.25H9.625L9 18.125H6.1875C5.64062 18.125 5.25 18.5547 5.25 19.0625C5.25 19.6094 5.64062 20 6.1875 20H16.8125C17.3203 20 17.75 19.6094 17.75 19.0625C17.75 18.5547 17.3203 18.125 16.8125 18.125H14L13.375 16.25H20.875C21.8906 16.25 22.75 15.4297 22.75 14.375V1.875C22.75 0.859375 21.8906 0 20.875 0ZM20.25 13.75H2.75V2.5H20.25V13.75Z",fill:"#828282"}})])])])]),t("div",{staticClass:"monsterinsights-report-flex"},[t("reports-pdf-export",{attrs:{"report-title":e.text_sitespeed}}),t("div",{staticClass:"monsterinsights-run-audit-btn"},[t("button",{staticClass:"monsterinsights-button monsterinsights-run-audit",on:{click:function(r){return r.preventDefault(),e.runAudit.apply(null,arguments)}}},[e._v(" "+e._s(e.text_run_audit)+" ")])])],1)])]),t("div",{staticClass:"monsterinsights-site-speed-device"},[e.runningAudit?t("div",{staticClass:"monsterinsights-site-speed-blur"}):e._e(),e.componentKey?t("layout",{attrs:{sitespeed_data:e.getSiteSpeed,current_device:e.sitespeed_current_device}}):e._e()],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"sitespeed"}}):e._e()],1)},ya=[],Ca=l(va,wa,ya,!1,null,null,null,null);const ba=Ca.exports,{__:ie,sprintf:xa}=wp.i18n,ka={name:"ReportMedia",mixins:[B],components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportOverviewLineChartApex:J,ReportTableBox:_e,ReportsPdfExport:F},data(){return{text_media_report:ie("Media Report","google-analytics-for-wordpress"),text_graph_title:ie("Video Plays","google-analytics-for-wordpress"),text_chart_tooltip:xa(ie("Video %s Plays","google-analytics-for-wordpress"),"<br />"),text_video_plays_empty:ie("No video plays tracked during this time period.","google-analytics-for-wordpress")}},computed:{media(){return this.showUpsell()?this.$store.getters["$_reports/demo_media"]:this.$store.getters["$_reports/media"]},video_plays_rows(){return this.media.video_details_rows.map(s=>[this.tableFormatRowData(s.title),this.tableFormatRowData(s.video_start),this.tableFormatRowData(s.average_watch_time),this.tableFormatRowData(s.average_time_viewed,"%",String),this.tableFormatRowData(s.completion_rate,"%",String)])},video_plays_headers(){return[ie("Video Details","google-analytics-for-wordpress"),ie("Video Plays","google-analytics-for-wordpress"),ie("Avg. Watch Time","google-analytics-for-wordpress"),ie("Avg. % Watched","google-analytics-for-wordpress"),ie("Completion Rate","google-analytics-for-wordpress")]}},methods:{chartData(){return{labels:this.media.line_chart_report.labels,data:this.media.line_chart_report.datas,trend:this.media.line_chart_report.trends,timestamps:this.media.line_chart_report.timestamps,compare:this.media.line_compare_chart?this.media.line_compare_chart.datas:[]}}},mounted(){if(this.showUpsell()){this.$store.commit("$_reports/ENABLE_BLUR");return}this.$store.dispatch("$_reports/getReportData","media")}};var $a=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-media"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_media_report)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_media_report}})],1),t("div",{staticClass:"monsterinsights-report-box monsterinsights-report-row"},[t("div",{staticClass:"monsterinsights-report-box-icon-heading"},[t("svg",{staticStyle:{"enable-background":"new 0 0 50 50"},attrs:{x:"0px",y:"0px",viewBox:"0 0 50 50","xml:space":"preserve",width:"30",height:"30"}},[t("g",[t("path",{attrs:{fill:"#8AA5B7",d:`M23.7,2.8c0.9,0,1.7,0,2.6,0C27,2.9,27.8,3,28.5,3.1c4.6,0.8,8.7,2.8,12,6.1c5,5,7.2,11,6.5,18
						c-0.4,4.7-2.3,8.9-5.4,12.4c-4.2,4.7-9.4,7.2-15.7,7.5c-4.4,0.2-8.5-0.8-12.2-3.1C8,40.5,4.4,35.5,3.2,28.8
						C3,28,2.9,27.1,2.8,26.3c0-0.9,0-1.7,0-2.6c0-0.3,0.1-0.6,0.1-0.9c0.5-4.8,2.4-9,5.6-12.6c3.4-3.8,7.6-6.2,12.7-7
						C22,3,22.9,2.9,23.7,2.8z M19.5,25c0,2.7,0,5.4,0,8.1c0,0.4,0.1,0.8,0.5,1.1c0.4,0.2,0.8,0.1,1.2-0.2c4.2-2.7,8.3-5.4,12.5-8
						c0.9-0.6,0.9-1.3,0-1.8c-4.2-2.7-8.3-5.4-12.5-8.1c-0.4-0.2-0.7-0.4-1.2-0.1c-0.4,0.2-0.5,0.6-0.5,1C19.5,19.6,19.5,22.3,19.5,25z`}})])]),t("h3",{domProps:{textContent:e._s(e.text_graph_title)}})]),e.media.line_chart_report.labels?t("ReportOverviewLineChartApex",{attrs:{id:"media-chart",chartData:e.chartData(),tooltipDescriptor:e.text_chart_tooltip}}):e._e()],1),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{headers:e.video_plays_headers,rows:e.video_plays_rows,emptytext:e.text_video_plays_empty}})],1),e.showUpsell()?t("ReportUpsellOverlay",{attrs:{report:"media"}}):e._e()],1)},Ra=[],Pa=l(ka,$a,Ra,!1,null,null,null,null);const Da=Pa.exports,{__:ee,sprintf:gt}=wp.i18n,Sa={date:{start:Xe().subtract(29,"days").format("YYYY-MM-DD"),end:Xe().format("YYYY-MM-DD"),interval:!1,text:""},mobileTableExpanded:!1,activeReport:"user-journey",journeyReports:{items:{},pagination:{}},filterForm:{search:"",sources:{value:"",label:ee("All Sources","google-analytics-for-wordpress")},mediums:{value:"",label:ee("All Mediums","google-analytics-for-wordpress")},campaigns:{value:"",label:ee("All Campaigns","google-analytics-for-wordpress")}},noauth:!1,blur:!1,reauth:!1},Ta={date:s=>s.date,activeReport:s=>s.activeReport,mobileTableExpanded:s=>s.mobileTableExpanded,journeyReports:s=>s.journeyReports,filterForm:s=>s.filterForm,noauth:s=>s.noauth,reauth:s=>s.reauth,blur:s=>s.blur},Ma={UPDATE_INTERVAL:(s,e)=>{w.set(s.date,"interval",e)},UPDATE_DATE:(s,e)=>{e.start&&e.end&&(w.set(s.date,"start",e.start),w.set(s.date,"end",e.end))},UPDATE_DATE_TEXT:(s,e)=>{w.set(s.date,"text",e)},UPDATE_REPORT_DATA:(s,e)=>{s.journeyReports=e},updateFilterForm(s,e){s.filterForm[e.name]=e.value},ENABLE_BLUR(s){s.blur=!0},DISABLE_BLUR(s){s.blur=!1}};function Te(s,e,t,r){let o=ee(t?"activate":"install","google-analytics-for-wordpress");w.prototype.$swal({icon:"error",customClass:{container:"monsterinsights-swal"},title:ee("Report Unavailable","google-analytics-for-wordpress"),html:gt(ee("Please %1$s the <strong>%2$s</strong> addon to view User journey reports.","google-analytics-for-wordpress"),o,r),allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!1,showCancelButton:!0,confirmButtonText:gt(ee("%s Addon","google-analytics-for-wordpress"),o.charAt(0).toUpperCase()+o.slice(1)),cancelButtonText:ee("Dismiss","google-analytics-for-wordpress")}).then(function(n){n.value&&(t?as(s,s.rootState.$_addons.addons[e]):ns(s,e))})}function La(s){s.dispatch("$_addons/getAddons",null,{root:!0}).then(e=>{if(!e)return;let t=s.rootState.$_addons.addons["user-journey"],r=s.rootState.$_addons.addons.ecommerce;if(!t.installed){Te(s,"user-journey",!1,t.title);return}if(!t.active){Te(s,"user-journey",!0,t.title);return}if(!r.installed){Te(s,"ecommerce",!1,r.title);return}r.active||Te(s,"ecommerce",!0,r.title)})}const Ea={getReportData(s,e={}){return new Promise(t=>{let r=new FormData;r.append("action","monsterinsights_user_journey_report"),r.append("nonce",w.prototype.$mi.nonce),r.append("search",s.state.filterForm.search),r.append("start_date",s.state.date.start),r.append("end_date",s.state.date.end),r.append("sources",s.state.filterForm.sources.value),r.append("mediums",s.state.filterForm.mediums.value),r.append("campaigns",s.state.filterForm.campaigns.value),r.append("page",e.page?e.page:1),ne.post(w.prototype.$mi.ajax,r).then(o=>{o.data.success&&(s.commit("UPDATE_REPORT_DATA",{items:o.data.items,pagination:o.data.pagination}),t(!0)),o.data.demo&&(s.commit("UPDATE_REPORT_DATA",{items:o.data.items,pagination:{}}),t(!0),s.commit("$_app/ADD_NOTICE",{content:ee("This is a demo report. The demo report will replaced after the first sale is tracked.","google-analytics-for-wordpress"),type:"warning",dismissable:!1,id:"user_journey_report_notice"},{root:!0})),t(!1)}).catch(o=>{s.commit("ENABLE_BLUR"),o.response.status===400&&La(s)})})}},ut={namespaced:!0,state:Sa,actions:Ea,getters:Ta,mutations:Ma};const{__:q,sprintf:mt}=wp.i18n;w.directive("click-outside",{bind:function(s,e,t){s.clickOutsideEvent=function(r){s===r.target||s.contains(r.target)||t.context[e.expression](r)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}});const _t=783;let Ve=!1;const Aa={name:"ReportsDatePicker",data(){const s=this;return{config:{mode:"range",disableMobile:"true",dateFormat:"Y-m-d",disable:[function(e){let t=e,r=x(x().tz(s.$mi.timezone).format("YYYY-MM-DD")),n=x.duration(r.diff(t)).asDays();n=n+1;let d=x(e).tz(s.$mi.timezone).subtract(n,"d").tz(s.$mi.timezone),p=x(),m=d.isBetween(x("01-01-2005","MM-DD-YYYY").tz(s.$mi.timezone),p),b=x(e).isBetween(x("01-01-2005","MM-DD-YYYY").tz(s.$mi.timezone),p);return!m||!b}],static:!0,inline:!0},isMobile:window.innerWidth<_t,dropdownVisible:!1,isCalendarOpen:!1,dateInputStart:"",dateInputEnd:"",dateCompareStart:"",dateCompareEnd:"",text_compare_to_previous:q("Compare to Previous","google-analytics-for-wordpress"),text_cancel:q("Cancel","google-analytics-for-wordpress"),text_apply:q("Apply","google-analytics-for-wordpress"),text_date_range:q("Date Range","google-analytics-for-wordpress"),text_compare_to:q("Compare To","google-analytics-for-wordpress"),selectedInterval:"last30days",compareDateLocal:!1}},props:{isGetReportData:{type:Boolean,default:!0},compareOptions:{type:Boolean,default:!0},dashboardWidget:{type:Boolean,default:!1}},computed:{...C({date:"$_reports/date",activeReport:"$_reports/activeReport",mobileTableExpanded:"$_reports/mobileTableExpanded"}),selectedIntervalText(){if(this.date.compareReport)return mt(q("%1$sComparison:%2$s %3$s To %4$s","google-analytics-for-wordpress"),'<b class="monsterinsights-custom-dates-label">',"</b>",this.date.intervalText,this.date.intervalCompareText);if(this.interval){let t=this.intervals[this.interval];return this.getIntervalFormatted(t.text,t.start,t.end)}let s=x(this.date.start),e=x(this.date.end);return this.dateInputStart&&!this.date.start&&(s=x(this.dateInputStart)),this.dateInputEnd&&!this.date.end&&(e=x(this.dateInputEnd)),mt(q("%1$sCustom dates:%2$s %3$s - %4$s","google-analytics-for-wordpress"),'<b class="monsterinsights-custom-dates-label">',"</b>",s.format("MMMM D"),e.format("MMMM D, YYYY"))},text_datepicker_placeholder(){return this.isMobile?q("Custom Date Range","google-analytics-for-wordpress"):q("Set Custom Date Range","google-analytics-for-wordpress")},text_mobile_details(){return this.mobileTableExpanded?q("Hide Details","google-analytics-for-wordpress"):q("Show Details","google-analytics-for-wordpress")},interval:{set(s){this.$store.commit("$_reports/UPDATE_INTERVAL",s)},get(){let s=this.date.interval;return(s===30||s==="false")&&(s="last30days"),s===7&&(s="last7days"),s}},local_date:{set(s){this.$store.commit("$_reports/UPDATE_DATE_TEXT",s)},get(){return this.date.text}},comparePickerDate:{set(s){this.$store.commit("$_reports/UPDATE_DATE_STORE",{compareText:s})},get(){return this.date.compareText}},dropdownClass(){let s="monsterinsights-reports-intervals-dropdown";return this.dropdownVisible||(s+=" monsterinsights-hide"),s},intervals(){let s=this.$mi_intervals();return typeof this.$route<"u"&&this.$route.name==="queries"&&(delete s.today,delete s.yesterday),s},compareDate:{set(s){this.compareDateLocal=s,s||this.$store.commit("$_reports/UPDATE_DATE_STORE",{compareReport:s}),this.selectedInterval&&(this.dropdownVisible=!1,this.sendReportRequest())},get(){return this.compareDateLocal}},showDefaultButtons(){return this.dashboardWidget?this.dropdownVisible&&!this.isCalendarOpen:!0}},components:{flatPickr:Jt},methods:{updateDates(s){if(this.compareDate)return;const e=this;this.dropdownVisible=!1;let t={};s[0]&&s[1]&&(t.start=this.getFormattedDate(s[0]),t.end=this.getFormattedDate(s[1]),this.interval=!1,document.activeElement.blur(),this.$store.commit("$_reports/UPDATE_DATE",t),e.$emit("date-changed"),this.sendReportRequest())},getFormattedDate(s){if(s instanceof Date){let e=this.addLeadingZero(s.getMonth()+1),t=this.addLeadingZero(s.getDate());s=s.getFullYear()+"-"+e+"-"+t}return s},addLeadingZero(s){return s<10&&s>0?0+s.toString():s},getInterval(s){const e=this;this.dropdownVisible=!1,this.interval=s.interval,this.selectedInterval=s.interval,this.$store.commit("$_reports/UPDATE_DATE_STORE",{start:s.start.format("YYYY-MM-DD"),end:s.end.format("YYYY-MM-DD"),compareStart:s.compareStart.format("YYYY-MM-DD"),compareEnd:s.compareEnd.format("YYYY-MM-DD")}),e.$emit("date-changed",s.interval),window.blur(),this.sendReportRequest(),this.defaultSelectClear()},getIntervalText(s,e){return s.format("YYYYMMDD")===e.format("YYYYMMDD")?s.format("MMMM D, YYYY"):s.format("MMMM D")+" - "+e.format("MMMM D, YYYY")},getIntervalFormatted(s,e,t){return"<b>"+s+'<span class="monsterinsights-datepicker-colon">:</span></b> <span class="monsterinsights-datepicker-interval-dates">'+this.getIntervalText(e,t)+"</span>"},getButtonClass(s){let e="monsterinsights-button";return s===this.interval&&(e+=" monsterinsights-selected-interval"),e},hideMobileTables(){this.mobileTableExpanded&&this.$store.commit("$_reports/CONTRACT_TABLES")},showMobileTables(){this.mobileTableExpanded||this.$store.commit("$_reports/EXPAND_TABLES")},toggleMobileTables(){this.mobileTableExpanded?this.hideMobileTables():this.showMobileTables()},handleResize(){Ve||(Ve=!0,window.requestAnimationFrame?window.requestAnimationFrame(this.resizeCallback):setTimeout(this.resizeCallback,66))},resizeCallback(){this.isMobile=window.innerWidth<_t,Ve=!1},openFlatPicker(){this.isCalendarOpen=!0,this.selectedInterval=!1,this.$store.commit("$_reports/UPDATE_DATE_STORE",{start:"",end:"",compareStart:"",compareEnd:""})},showIfFlatpickr(){return!this.$refs.datePicker||this.dashboardWidget&&!this.isCalendarOpen?"monsterinsights-hide":this.isCalendarOpen||!this.interval?"":"monsterinsights-hide"},maybeHideDropdown(){this.isCalendarOpen=!1,this.dropdownVisible&&(this.dropdownVisible=!1)},intervalButtonClass(s){let e="monsterinsights-button ";return this.interval===s&&(e+=" monsterinsights-interval-active"),e},toggleDropdown(){this.dropdownVisible=!this.dropdownVisible,this.isCalendarOpen=!1},onCancelDatepicker(){this.isCalendarOpen=!1,this.dropdownVisible=!1},onApplyDatepicker(){this.isReadyToApplyDatepicker()&&(this.dropdownVisible=!1,this.interval=!1,window.blur(),this.sendReportRequest())},onComparePickerChange(s){s[0]&&(this.dateCompareStart=x(s[0]).format("MMMM D, YYYY"),this.updateDateToStore(s[0],"compareStart")),s[1]&&(this.dateCompareEnd=x(s[1]).format("MMMM D, YYYY"),this.updateDateToStore(s[1],"compareEnd"))},onDatePickerChange(s){s[0]&&(this.dateInputStart=x(s[0]).format("MMMM D, YYYY"),this.updateDateToStore(s[0],"start")),s[1]&&(this.dateInputEnd=x(s[1]).format("MMMM D, YYYY"),this.updateDateToStore(s[1],"end"))},updateDateToStore(s,e){this.$store.commit("$_reports/UPDATE_DATE_STORE",{[e]:this.getFormattedDate(s)})},sendReportRequest(){this.isGetReportData&&(this.$store.commit("$_reports/UPDATE_DATE_STORE",{compareReport:this.compareDateLocal}),this.$store.dispatch("$_reports/getReportData",this.activeReport).then(()=>{this.$emit("reports-updated")}))},isReadyToApplyDatepicker(){return this.compareDate?this.date.compareStart&&this.date.compareEnd:!0},defaultSelectClear(){this.dateInputStart="",this.dateInputEnd="",this.dateCompareStart="",this.dateCompareEnd="",this.local_date="",this.comparePickerDate=""}},mounted(){this.compareDateLocal=this.date.compareReport,this.selectedInterval=this.date.interval,this.selectedInterval===!1&&(this.date.start&&(this.dateInputStart=x(this.date.start).format("MMMM D, YYYY")),this.date.end&&(this.dateInputEnd=x(this.date.end).format("MMMM D, YYYY"))),this.date.compareReport&&(this.date.compareStart&&(this.dateCompareStart=x(this.date.compareStart).format("MMMM D, YYYY")),this.date.compareEnd&&(this.dateCompareEnd=x(this.date.compareEnd).format("MMMM D, YYYY"))),window.addEventListener("resize",this.handleResize)},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)}};var Fa=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-reports-datepicker"},[t("div",{staticClass:"monsterinsights-reports-interval-dropdown-container"},[t("button",{staticClass:"monsterinsights-reports-interval-date-info",on:{click:function(r){return r.stopPropagation(),e.toggleDropdown.apply(null,arguments)}}},[t("span",{domProps:{innerHTML:e._s(e.selectedIntervalText)}}),t("i",{staticClass:"monstericon-calendar-alt"})]),t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.maybeHideDropdown,expression:"maybeHideDropdown"}],class:e.dropdownClass},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.showDefaultButtons,expression:"showDefaultButtons"}],staticClass:"monsterinsights-datepicker-default-buttons"},[e._l(e.intervals,function(r,o){return t("div",{key:o},[t("button",{class:e.intervalButtonClass(r.interval),domProps:{innerHTML:e._s(e.getIntervalFormatted(r.text,r.start,r.end))},on:{click:function(n){return e.getInterval(r)}}})])}),e._t("beforeinterval"),t("div",[t("button",{class:e.intervalButtonClass(!1),on:{click:e.openFlatPicker}},[t("i",{staticClass:"monstericon-calendar-alt"}),e._v(" "),t("span",{domProps:{textContent:e._s(e.text_datepicker_placeholder)}})])]),e.compareOptions?t("div",[t("div",{staticClass:"monsterinsights-datepicker-input-switch"},[t("label",[t("span",{class:{"monsterinsights-styled-checkbox":!0,"monsterinsights-styled-checkbox-checked":e.compareDate}}),t("input",{directives:[{name:"model",rawName:"v-model",value:e.compareDate,expression:"compareDate"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.compareDate)?e._i(e.compareDate,null)>-1:e.compareDate},on:{change:function(r){var o=e.compareDate,n=r.target,a=!!n.checked;if(Array.isArray(o)){var d=null,p=e._i(o,d);n.checked?p<0&&(e.compareDate=o.concat([d])):p>-1&&(e.compareDate=o.slice(0,p).concat(o.slice(p+1)))}else e.compareDate=a}}}),t("span",{staticClass:"monsterinsights-checkbox-label",domProps:{textContent:e._s(e.text_compare_to_previous)}})])])]):e._e()],2),t("div",{class:["monsterinsights-datepicker-range-wrapper",e.showIfFlatpickr()]},[t("div",{staticClass:"monsterinsights-datepicker-range-inputs"},[t("div",{staticClass:"monsterinsights-datepicker-range-input"},[t("h3",{domProps:{textContent:e._s(e.text_date_range)}}),t("div",{staticClass:"monsterinsights-datepicker-range-input-fields"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.dateInputStart,expression:"dateInputStart"}],attrs:{type:"text"},domProps:{value:e.dateInputStart},on:{input:function(r){r.target.composing||(e.dateInputStart=r.target.value)}}}),t("input",{directives:[{name:"model",rawName:"v-model",value:e.dateInputEnd,expression:"dateInputEnd"}],attrs:{type:"text"},domProps:{value:e.dateInputEnd},on:{input:function(r){r.target.composing||(e.dateInputEnd=r.target.value)}}})])]),e.compareDate?t("div",{staticClass:"monsterinsights-datepicker-range-input"},[t("h3",{domProps:{textContent:e._s(e.text_compare_to)}}),t("div",{staticClass:"monsterinsights-datepicker-range-input-fields"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.dateCompareStart,expression:"dateCompareStart"}],attrs:{type:"text"},domProps:{value:e.dateCompareStart},on:{input:function(r){r.target.composing||(e.dateCompareStart=r.target.value)}}}),t("input",{directives:[{name:"model",rawName:"v-model",value:e.dateCompareEnd,expression:"dateCompareEnd"}],attrs:{type:"text"},domProps:{value:e.dateCompareEnd},on:{input:function(r){r.target.composing||(e.dateCompareEnd=r.target.value)}}})])]):e._e()]),t("div",{staticClass:"monsterinsights-datepicker-calender-wrapper"},[t("div",{staticClass:"monsterinsights-datepicker-calenders"},[t("div",{staticClass:"monsterinsights-datepicker-calender"},[t("flat-pickr",{ref:"datePicker",staticClass:"monsterinsights-datepicker",attrs:{config:e.config},on:{"on-close":e.updateDates,"on-change":e.onDatePickerChange},model:{value:e.local_date,callback:function(r){e.local_date=r},expression:"local_date"}})],1),e.compareDate?t("div",{staticClass:"monsterinsights-datepicker-calender"},[t("flat-pickr",{ref:"comparePicker",staticClass:"monsterinsights-datepicker",attrs:{config:e.config},on:{"on-change":e.onComparePickerChange},model:{value:e.comparePickerDate,callback:function(r){e.comparePickerDate=r},expression:"comparePickerDate"}})],1):e._e()]),e.compareDate?t("div",{staticClass:"monsterinsights-datepicker-calenders-bottom"},[t("button",{staticClass:"monsterinsights-button monsterinsights-button-secondary",domProps:{textContent:e._s(e.text_cancel)},on:{click:e.onCancelDatepicker}}),t("button",{class:{"monsterinsights-button":!0,"monsterinsights-button-primary":!0,"monsterinsights-button-disabled":!e.isReadyToApplyDatepicker()},attrs:{disabled:!e.isReadyToApplyDatepicker()},domProps:{textContent:e._s(e.text_apply)},on:{click:e.onApplyDatepicker}})]):e._e()])])])]),t("button",{staticClass:"monsterinsights-button monsterinsights-mobile-details-toggle",domProps:{textContent:e._s(e.text_mobile_details)},on:{click:e.toggleMobileTables}})])},Na=[],Ia=l(Aa,Fa,Na,!1,null,null,null,null);const Ba=Ia.exports,Oa={name:"MIReportsDatePicker",extends:Ba},Ua=null,Ha=null;var Va=l(Oa,Ua,Ha,!1,null,null,null,null);const Ya=Va.exports,{__:he}=wp.i18n,ja={name:"AdminTableNavigation",props:{pagination:{type:Object,default:()=>({})}},data(){return{currentPage:1,filterValues:{},keywords:"",keywordsTimeout:null}},computed:{texts(){return{currentPage:he("Current Page",this.$_textDomain),firstPage:he("First Page",this.$_textDomain),prevPage:he("Previous Page",this.$_textDomain),nextPage:he("Next Page",this.$_textDomain),lastPage:he("Last Page",this.$_textDomain),of:he("of",this.$_textDomain)}}},methods:{goToPrev(){this.$emit("page-change",Number.parseInt(this.pagination.page)-1)},goToNext(){this.$emit("page-change",Number.parseInt(this.pagination.page)+1)},goToPage(s){this.$emit("page-change",s)}},mounted(){this.currentPage=this.pagination.page,this.filters&&this.filters.forEach(s=>{this.filterValues[s.id]=0})},watch:{"pagination.page"(s){this.currentPage=s}}};var Za=function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-journey-report-pagination tablenav"},[t("div",{staticClass:"tablenav-pages",class:{"one-page":e.pagination.pages===1}},[e.pagination.pages>1?t("span",{staticClass:"pagination-links"},[e.pagination.page==1?[t("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[e._v("«")]),t("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[e._v("‹")])]:[t("a",{staticClass:"first-page button",attrs:{href:"#",role:"button"},on:{click:function(r){return r.preventDefault(),e.goToPage(1)}}},[t("span",{attrs:{"aria-hidden":"true"}},[e._v("«")])]),t("a",{staticClass:"prev-page button",attrs:{href:"#",role:"button"},on:{click:function(r){return r.preventDefault(),e.goToPrev.apply(null,arguments)}}},[t("span",{attrs:{"aria-hidden":"true"}},[e._v("‹")])])],t("span",{staticClass:"paging-input"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentPage,expression:"currentPage"}],staticClass:"current-page",attrs:{id:"current-page-selector",type:"text",name:"paged",size:"1","aria-describedby":"table-paging",max:e.pagination.pages},domProps:{value:e.currentPage},on:{keyup:function(r){return!r.type.indexOf("key")&&e._k(r.keyCode,"enter",13,r.key,"Enter")?null:e.goToPage(e.currentPage)},input:function(r){r.target.composing||(e.currentPage=r.target.value)}}}),t("span",{staticClass:"tablenav-paging-text"},[e._v(" "+e._s(e.texts.of)+" "),t("span",{staticClass:"total-pages"},[e._v(e._s(e.pagination.pages))])])]),e.pagination.page==e.pagination.pages?[t("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[e._v("›")]),t("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[e._v("»")])]:[t("a",{staticClass:"next-page button",attrs:{href:"#",role:"button"},on:{click:function(r){return r.preventDefault(),e.goToNext.apply(null,arguments)}}},[t("span",{attrs:{"aria-hidden":"true"}},[e._v("›")])]),t("a",{staticClass:"last-page button",attrs:{href:"#",role:"button"},on:{click:function(r){return r.preventDefault(),e.goToPage(e.pagination.pages)}}},[t("span",{attrs:{"aria-hidden":"true"}},[e._v("»")])])]],2):e._e()])])},Wa=[],za=l(ja,Za,Wa,!1,null,null,null,null);const Ot=za.exports,{__:Ce}=wp.i18n,Ka={name:"UserJourneyReport",components:{ReportsDatePicker:Ya,Multiselect:qe,Pagination:Ot},data(){return{parameters:{sources:[{value:"",label:"All Sources"}],mediums:[{value:"",label:"All Mediums"}],campaigns:[{value:"",label:"All Campaigns"}]},isMobileView:!1,currentExpandedRow:null,noItemText:Ce("No User Journey's matched your filters. Please remove some conditions and try again.","google-analytics-for-wordpress"),dataFetched:!1}},computed:{...C({journeyReports:"$_reports/journeyReports",filterForm:"$_reports/filterForm"}),searchJourneysField:{get(){return this.filterForm.search},set(s){this.$store.commit("$_reports/updateFilterForm",{name:"search",value:s})}},filterFieldCampaigns:{get(){return this.filterForm.campaigns},set(s){this.$store.commit("$_reports/updateFilterForm",{name:"campaigns",value:s})}},filterFieldMediums:{get(){return this.filterForm.mediums},set(s){this.$store.commit("$_reports/updateFilterForm",{name:"mediums",value:s})}},filterFieldSources:{get(){return this.filterForm.sources},set(s){this.$store.commit("$_reports/updateFilterForm",{name:"sources",value:s})}},showUpsell(){if(!this.$isPro())return!0;let s=this.$store.getters["$_license/license"];return!!(s&&s.type==="plus")}},methods:{getFilterParams(){if(this.showUpsell)return;let s=new FormData;s.append("action","monsterinsights_user_journey_report_filter_params"),s.append("nonce",this.$mi.nonce),ne.post(this.$mi.ajax,s).then(e=>{if(e.data.success){let t=e.data.data;t.campaigns.forEach(r=>{this.parameters.campaigns.push({value:r,label:r})}),t.mediums.forEach(r=>{this.parameters.mediums.push({value:r,label:r})}),t.sources.forEach(r=>{this.parameters.sources.push({value:r,label:r})})}})},searchJourneys(){this.$mi_loading_toast(Ce("Searching Journeys","google-analytics-for-wordpress")),this.$store.dispatch("$_reports/getReportData").then(()=>{this.$swal.close()})},onDateChanged(){this.$mi_loading_toast(Ce("Searching Journeys","google-analytics-for-wordpress"))},onReportsUpdated(){this.$swal.close()},onFilterClick(){this.$mi_loading_toast(Ce("Searching Journeys","google-analytics-for-wordpress")),this.$store.dispatch("$_reports/getReportData").then(()=>{this.$swal.close()})},pageChange(s){this.$mi_loading_toast(Ce("Loading Journeys","google-analytics-for-wordpress")),this.$store.dispatch("$_reports/getReportData",{page:s}).then(()=>{this.$swal.close()})},handleResize(){window.innerWidth<783?this.isMobileView=!0:this.isMobileView=!1},expandCurrentRow(s){this.currentExpandedRow===s?this.currentExpandedRow=null:this.currentExpandedRow=s}},created(){this.$isPro()&&(this.$store.dispatch("$_reports/getReportData").then(()=>{this.dataFetched=!0}),this.getFilterParams())},mounted(){window.addEventListener("resize",this.handleResize),this.handleResize()},beforeDestroy(){window.removeEventListener("resize",this.handleResize),this.$store.commit("$_app/REMOVE_NOTICE","user_journey_report_notice",{root:!0})}};var qa=function(){var e=this,t=e._self._c;return t("main",{class:{"monsterinsights-user-journey-report":!0,"monsterinsights-blur":e.showUpsell},attrs:{id:"monsterinsights-user-journey-report"}},[t("div",{staticClass:"monsterinsights-container"},[e._t("em-heading"),t("div",{staticClass:"monsterinsights-user-journey-report-filters"},[t("div",{staticClass:"monsterinsights-user-journey-report-filters-left"},[t("reports-date-picker",{on:{"date-changed":e.onDateChanged,"reports-updated":e.onReportsUpdated}}),t("multiselect",{attrs:{options:e.parameters.campaigns,multiple:!1,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"","allow-empty":!1},model:{value:e.filterFieldCampaigns,callback:function(r){e.filterFieldCampaigns=r},expression:"filterFieldCampaigns"}}),t("multiselect",{attrs:{options:e.parameters.mediums,multiple:!1,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"","allow-empty":!1},model:{value:e.filterFieldMediums,callback:function(r){e.filterFieldMediums=r},expression:"filterFieldMediums"}}),t("multiselect",{attrs:{options:e.parameters.sources,multiple:!1,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"","allow-empty":!1},model:{value:e.filterFieldSources,callback:function(r){e.filterFieldSources=r},expression:"filterFieldSources"}}),t("button",{staticClass:"monsterinsights-button monsterinsights-button-outline",on:{click:e.onFilterClick}},[e._v(" Filter ")])],1),t("div",{staticClass:"monsterinsights-user-journey-report-filters-right"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.searchJourneysField,expression:"searchJourneysField"}],staticClass:"monsterinsights-search-transaction-id",attrs:{type:"text",placeholder:"Transaction ID"},domProps:{value:e.searchJourneysField},on:{keyup:function(r){return!r.type.indexOf("key")&&e._k(r.keyCode,"enter",13,r.key,"Enter")?null:e.searchJourneys.apply(null,arguments)},input:function(r){r.target.composing||(e.searchJourneysField=r.target.value)}}}),t("button",{staticClass:"monsterinsights-button",on:{click:e.searchJourneys}},[e._v(" Search Journeys ")])])]),e.journeyReports.items.length>0?t("table",{staticClass:"monsterinsights-user-journey-report-table",class:{"monsterinsights-ujr-table-small-device":e.isMobileView}},[e._m(0),t("tbody",e._l(e.journeyReports.items,function(r,o){return t("tr",{key:String(r.transaction_id)+o,class:{"monsterinsights-ujr-table-small-device-row":e.isMobileView,"monsterinsights-ujr-table-small-device-row-expanded":e.currentExpandedRow===o}},[t("td",{staticClass:"monsterinsights-ujr-table-small-device-heading",on:{click:function(n){return e.expandCurrentRow(o)}}},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("Transaction ID")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[t("a",{attrs:{href:r.edit_order_url}},[e._v(" "+e._s(r.transaction_id)+" ")])])]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("Purchase Date")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[e._v(e._s(r.purchase_date))])]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("UTM Campaign")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[e._v(e._s(r.utm_campaign))])]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("UTM Medium")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[e._v(e._s(r.utm_medium))])]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("UTM Source")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[e._v(e._s(r.utm_source))])]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("Order Total")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value",domProps:{innerHTML:e._s(r.order_total)}})]),t("td",{staticClass:"monsterinsights-ujr-table-small-device-content"},[t("span",{staticClass:"monsterinsights-ujr-table-small-device-label"},[e._v("Steps to Purchase")]),t("span",{staticClass:"monsterinsights-ujr-table-small-device-value"},[e._v(e._s(r.steps))])])])}),0)]):e.dataFetched&&e.journeyReports.items.length==0?t("div",{staticClass:"monsterinsights-notice monsterinsights-notice-info"},[t("div",{staticClass:"monsterinsights-notice-inner"},[t("div",{staticClass:"notice-content"},[t("span",{staticClass:"monsterinsights-notice-content"},[e._v(e._s(e.noItemText))])])])]):e._e(),t("Pagination",{attrs:{pagination:e.journeyReports.pagination},on:{"page-change":e.pageChange}})],2),e._t("mi-upsell-overlay")],2)},Ga=[function(){var s=this,e=s._self._c;return e("thead",[e("tr",[e("th",[s._v("Transaction ID")]),e("th",[s._v("Purchase Date")]),e("th",[s._v("UTM Campaign")]),e("th",[s._v("UTM Medium")]),e("th",[s._v("UTM Source")]),e("th",[s._v("Order Total")]),e("th",[s._v("Steps to Purchase")])])])}],Ja=l(Ka,qa,Ga,!1,null,null,null,null);const Qa=Ja.exports,Xa={name:"MonsterInsightsUserJourneyReport",components:{UserJourneyReport:Qa,ReportUpsellOverlay:E},beforeCreate(){const s="$_reports";s in this.$store._modules.root._children?(this.$store.unregisterModule(s),this.$store.registerModule(s,ut)):this.$store.registerModule(s,ut)},beforeDestroy(){this.$store.unregisterModule("$_reports")},computed:{showUpsell(){if(!this.$isPro())return!0;let s=this.$store.getters["$_license/license"];return!!(s&&s.type==="plus")}}};var en=function(){var e=this,t=e._self._c;return t("UserJourneyReport",{scopedSlots:e._u([{key:"mi-upsell-overlay",fn:function(){return[e.showUpsell?t("ReportUpsellOverlay",{attrs:{report:"userjourney","show-sample-button":!1}}):e._e()]},proxy:!0}])})},tn=[],sn=l(Xa,en,tn,!1,null,null,null,null);const rn=sn.exports,{__:N,sprintf:ft}=wp.i18n,on={name:"ReportCountry",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,ReportTable:Q,SiteNotesOverview:te},mixins:[B],data(){return{texts:{title:N("Country Report","google-analytics-for-wordpress"),sessions:N("Sessions","google-analytics-for-wordpress"),sessions_tooltip:ft(N("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),pageviews:N("Pageviews","google-analytics-for-wordpress"),pageviews_tooltip:ft(N("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),site_notes:N("Site Notes","google-analytics-for-wordpress"),close_site_notes:N("Close Site Notes","google-analytics-for-wordpress")},current_tab:"sessions",show_overview_notes:!1,fake_headers:[N("Country","google-analytics-for-wordpress"),N("Sessions","google-analytics-for-wordpress"),N("Engaged Sessions","google-analytics-for-wordpress"),N("Bounce Rate","google-analytics-for-wordpress"),N("Purchases","google-analytics-for-wordpress")],reportTableSearch:!1,text_errors:{UNKNOWN_ERROR:N("An unknown error has occurred.","google-analytics-for-wordpress")},overviewKey:1}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","countries")},computed:{...C({date:"$_reports/date",license:"$_license/license",license_network:"$_license/license_network",addons:"$_addons/addons"}),countries(){return this.showUpsell()?this.$store.getters["$_reports/demo_countries"]:this.$store.getters["$_reports/countries"]},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){const s=this.countries.sessions_chart;return s?{labels:s.categories,data:s.sessions,timestamps:s.timestamps,trend:s.session_trendpoints,notes:s.notes,compare:this.countries.sessions_compare_chart?this.countries.sessions_compare_chart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){const s=this.countries.sessions_chart;return s?{labels:s.categories,data:s.page_views,timestamps:s.timestamps,trend:s.page_view_trendpoints,notes:s.notes,compare:this.countries.sessions_compare_chart?this.countries.sessions_compare_chart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},getChannelHeaders(){var e,t;const s=[{title:N("Country","google-analytics-for-wordpress"),key:"country",sortable:!1},{title:N("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:N("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:N("Bounce rate","google-analytics-for-wordpress"),key:"bounce_rate",sortable:!0}];return(t=(e=this.addons)==null?void 0:e.ecommerce)!=null&&t.active&&s.push({title:N("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0}),s},prepareCountriesRows(){var t,r;let s=[];if(typeof((t=this.countries)==null?void 0:t.countries)>"u")return s;const e=Object.values((r=this.countries)==null?void 0:r.countries);return e.length>0&&e.forEach(o=>{let n=this.prepareRowItem(o);typeof o.regions=="object"&&(n._extendedData={extendKey:o.iso,rows:[]},Object.values(o.regions).forEach(a=>{const d=this.prepareRowItem(a);d._extendedData={extendedBy:o.iso},n._extendedData.rows.push(d)})),s.push(n)}),s},prepareRowItem(s){var t,r;const e=[];return typeof s.name<"u"?e.push(this.tableFormatRowData(s.name)):typeof s.region<"u"&&e.push(this.tableFormatRowData(s.region)),e.push(this.tableFormatRowData(s.sessions)),e.push(this.tableFormatRowData(s.engagedSessions)),e.push(this.tableFormatRowData(s.bounceRate,"%",this.$formatNumber)),(r=(t=this.addons)==null?void 0:t.ecommerce)!=null&&r.active&&e.push(this.tableFormatRowData(s.purchases)),e}}};var an=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-country"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.texts.title)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.texts.title}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.texts.sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.texts.pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.texts.sessions_tooltip}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.texts.pageviews_tooltip}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.texts.site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.texts.close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.texts.title,rows:e.prepareCountriesRows(),columns:e.getChannelHeaders(),withExpandableData:!0}})],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"countries"}}):e._e()],1)},nn=[],ln=l(on,an,nn,!1,null,null,null,null);const cn=ln.exports,{__:D,sprintf:Ye}=wp.i18n,pn={name:"ReportTrafficOverview",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,ReportTable:Q,SiteNotesOverview:te},data(){return{text_traffic_overview:D("Traffic Overview Report","google-analytics-for-wordpress"),text_traffic_details:D("Traffic Details","google-analytics-for-wordpress"),text_sessions:D("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:Ye(D("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:D("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:Ye(D("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},show_overview_notes:!1,text_site_notes:D("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:D("Close Site Notes","google-analytics-for-wordpress"),fake_headers:[D("Channel","google-analytics-for-wordpress"),D("Sessions","google-analytics-for-wordpress"),D("Engaged Sessions","google-analytics-for-wordpress"),D("Pages / Sessions","google-analytics-for-wordpress"),D("Purchases","google-analytics-for-wordpress"),D("Conversion Rate","google-analytics-for-wordpress"),D("Revenue","google-analytics-for-wordpress")],reportTableSearch:!1,text_errors:{UNKNOWN_ERROR:D("An unknown error has occurred.","google-analytics-for-wordpress")},overviewKey:1}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","traffic_overview")},computed:{...C({date:"$_reports/date",license:"$_license/license",license_network:"$_license/license_network"}),traffic_overview(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_overview"]:this.$store.getters["$_reports/traffic_overview"]},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type},channels(){return this.traffic_overview.traffic_details_table?this.traffic_overview.traffic_details_table:[]}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},showUpsell(){return this.$isPro()?this.licenseLevel==="basic":!0},getErrorForReport(s){return this.publisher[s]&&this.publisher[s].error?Ye(this.text_errors[this.publisher[s].error.code]||this.text_errors.UNKNOWN_ERROR,this.publisher[s].error.data||""):""},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.traffic_overview.sessions_chart?{labels:this.traffic_overview.sessions_chart.categories,data:this.traffic_overview.sessions_chart.sessions,timestamps:this.traffic_overview.sessions_chart.timestamps,trend:this.traffic_overview.sessions_chart.session_trendpoints,notes:this.traffic_overview.sessions_chart.notes,compare:this.traffic_overview.sessions_compare_chart?this.traffic_overview.sessions_compare_chart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){return this.traffic_overview.sessions_chart?{labels:this.traffic_overview.sessions_chart.categories,data:this.traffic_overview.sessions_chart.page_views,timestamps:this.traffic_overview.sessions_chart.timestamps,trend:this.traffic_overview.sessions_chart.page_view_trendpoints,notes:this.traffic_overview.sessions_chart.notes,compare:this.traffic_overview.sessions_compare_chart?this.traffic_overview.sessions_compare_chart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},getChannelHeaders(){return[{title:D("Channel","google-analytics-for-wordpress"),key:"channel",sortable:!0},{title:D("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:D("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:D("Pages / Sessions","google-analytics-for-wordpress"),key:"pages_per_sessions",sortable:!0},{title:D("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0},{title:D("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0},{title:D("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0}]},prepareChannelRows(){let s=[];return this.channels.length>0&&typeof this.channels[0]<"u"&&Object.values(this.channels).forEach(e=>{const t=[];t.push(this.tableFormatRowData(e.channel)),t.push(this.tableFormatRowData(e.sessions)),t.push(this.tableFormatRowData(e.engaged_sessions)),t.push(this.tableFormatRowData(e.pages_per_sessions)),t.push(this.tableFormatRowData(e.purchases)),t.push(this.tableFormatRowData(e.conversion_rate,"%")),t.push(this.tableFormatRowData(e.revenue)),s.push(t)}),s},prepareChannelError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getChannelEmptyText(){return D("No data currently for the Traffic report","google-analytics-for-wordpress")}}};var dn=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-overview"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_traffic_overview)}}),t("ReportsDatePicker"),t("reports-pdf-export",{attrs:{"report-title":e.text_traffic_overview}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),e.channels.length>0?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_traffic_details,rows:e.prepareChannelRows(),columns:e.getChannelHeaders(),error:e.prepareChannelError(e.channels),emptytext:e.getChannelEmptyText(),"allow-search":e.reportTableSearch}})],1):e._e(),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"traffic"}}):e._e()],1)},hn=[],gn=l(pn,dn,hn,!1,null,null,null,null);const un=gn.exports,{__:R,sprintf:vt}=wp.i18n,mn={name:"ReportTrafficLandingPages",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,ReportTable:Q,SiteNotesOverview:te},mixins:[B],data(){return{text_traffic_landing_pages:R("Landing Page Details","google-analytics-for-wordpress"),text_traffic_landing_pages_details:R("Top Landing Pages","google-analytics-for-wordpress"),text_sessions:R("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:vt(R("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:R("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:vt(R("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},show_overview_notes:!1,text_site_notes:R("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:R("Close Site Notes","google-analytics-for-wordpress"),text_channels:R("Top Landing Pages","google-analytics-for-wordpress"),fake_headers:[R("Landing Page","google-analytics-for-wordpress"),R("Sessions","google-analytics-for-wordpress"),R("Engaged Sessions","google-analytics-for-wordpress"),R("Pages / Sessions","google-analytics-for-wordpress"),R("Purchases","google-analytics-for-wordpress"),R("Conversion Rate","google-analytics-for-wordpress"),R("Revenue","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:R("An unknown error has occurred.","google-analytics-for-wordpress")},overviewKey:1}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","traffic_landing_pages")},computed:{...C({date:"$_reports/date"}),traffic_landing_pages(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_landing_pages"]:this.$store.getters["$_reports/traffic_landing_pages"]},landingPages(){return this.traffic_landing_pages.landing_pages_table?this.traffic_landing_pages.landing_pages_table:[]}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.traffic_landing_pages.sessions_chart?{labels:this.traffic_landing_pages.sessions_chart.categories,data:this.traffic_landing_pages.sessions_chart.sessions,timestamps:this.traffic_landing_pages.sessions_chart.timestamps,trend:this.traffic_landing_pages.sessions_chart.session_trendpoints,notes:this.traffic_landing_pages.sessions_chart.notes,compare:this.traffic_landing_pages.sessions_compare_chart?this.traffic_landing_pages.sessions_compare_chart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){return this.traffic_landing_pages.sessions_chart?{labels:this.traffic_landing_pages.sessions_chart.categories,data:this.traffic_landing_pages.sessions_chart.page_views,timestamps:this.traffic_landing_pages.sessions_chart.timestamps,trend:this.traffic_landing_pages.sessions_chart.page_view_trendpoints,notes:this.traffic_landing_pages.sessions_chart.notes,compare:this.traffic_landing_pages.sessions_compare_chart?this.traffic_landing_pages.sessions_compare_chart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},getLandingPagesHeaders(){return[{title:R("Landing Page","google-analytics-for-wordpress"),key:"landing_page",sortable:!0},{title:R("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:R("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:R("Pages / Sessions","google-analytics-for-wordpress"),key:"pages_per_sessions",sortable:!0},{title:R("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0},{title:R("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0},{title:R("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0}]},prepareLandingPagesRows(){let s=[];return this.landingPages.length>0&&typeof this.landingPages[0]<"u"&&Object.values(this.landingPages).forEach(e=>{const t=[];t.push(this.tableFormatRowData(e.landing_page)),t.push(this.tableFormatRowData(e.sessions)),t.push(this.tableFormatRowData(e.engaged_sessions)),t.push(this.tableFormatRowData(e.pages_per_sessions)),t.push(this.tableFormatRowData(e.purchases)),t.push(this.tableFormatRowData(e.conversion_rate,"%")),t.push(this.tableFormatRowData(e.revenue)),s.push(t)}),s},prepareLandingPagesError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getLandingPagesEmptyText(){return R("No data currently for the Landing Page report","google-analytics-for-wordpress")}}};var _n=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-landing-pages"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_traffic_landing_pages)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_traffic_landing_pages}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),e.landingPages.length>0?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_traffic_landing_pages_details,rows:e.prepareLandingPagesRows(),columns:e.getLandingPagesHeaders(),error:e.prepareLandingPagesError(e.landingPages),emptytext:e.getLandingPagesEmptyText(),"allow-search":e.reportTableSearch}})],1):e._e(),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"traffic_landing_pages"}}):e._e()],1)},fn=[],vn=l(mn,_n,fn,!1,null,null,null,null);const wn=vn.exports,{__:Me}=wp.i18n,yn={name:"ReportTrafficTechnology",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewPieChartApex:Je},mixins:[B],data(){return{text_traffic_technology:Me("Technology","google-analytics-for-wordpress"),text_browser_breakdown:Me("Browser Breakdown","google-analytics-for-wordpress"),text_device_breakdown:Me("Device Breakdown","google-analytics-for-wordpress"),text_errors:{UNKNOWN_ERROR:Me("An unknown error has occurred.","google-analytics-for-wordpress")}}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","traffic_technology")},computed:{...C({date:"$_reports/date"}),traffic_technology(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_technology"]:this.$store.getters["$_reports/traffic_technology"]},browserBreakdown(){if(this.traffic_technology.browser_breakdown){let s={values:this.traffic_technology.browser_breakdown.series,labels:this.traffic_technology.browser_breakdown.labels};return this.date.compareReport&&this.traffic_technology.browser_breakdown.compare&&(s.compare={values:this.traffic_technology.browser_breakdown.compare.series,labels:this.traffic_technology.browser_breakdown.compare.labels}),s}return{values:[],labels:[]}},deviceBreakdown(){if(this.traffic_technology.device_breakdown){let s={values:this.traffic_technology.device_breakdown.series,labels:this.traffic_technology.device_breakdown.labels};return this.date.compareReport&&this.traffic_technology.device_breakdown.compare&&(s.compare={values:this.traffic_technology.device_breakdown.compare.series,labels:this.traffic_technology.device_breakdown.compare.labels}),s}return{values:[],labels:[]}}}};var Cn=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-technology"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_traffic_technology)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_traffic_technology}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex"},[t("report-overview-pie-chart-apex",{attrs:{id:"browserBreakdown",chartData:e.browserBreakdown,title:e.text_browser_breakdown}}),t("report-overview-pie-chart-apex",{attrs:{id:"deviceBreakdown",chartData:e.deviceBreakdown,title:e.text_device_breakdown}})],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"traffic_technology"}}):e._e()],1)},bn=[],xn=l(yn,Cn,bn,!1,null,null,null,null);const kn=xn.exports,{__:S,sprintf:wt}=wp.i18n,$n={name:"ReportTrafficCampaigns",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,ReportTable:Q,SiteNotesOverview:te},mixins:[B],data(){return{text_traffic_campaigns:S("Campaigns Report","google-analytics-for-wordpress"),text_traffic_campaign_details:S("Campaign Details","google-analytics-for-wordpress"),text_sessions:S("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:wt(S("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:S("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:wt(S("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},show_overview_notes:!1,text_site_notes:S("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:S("Close Site Notes","google-analytics-for-wordpress"),fake_headers:[S("Campaign","google-analytics-for-wordpress"),S("Sessions","google-analytics-for-wordpress"),S("Engaged Sessions","google-analytics-for-wordpress"),S("Pages / Sessions","google-analytics-for-wordpress"),S("Purchases","google-analytics-for-wordpress"),S("Conversion Rate","google-analytics-for-wordpress"),S("Revenue","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:S("An unknown error has occurred.","google-analytics-for-wordpress")},overviewKey:1}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","traffic_campaign")},computed:{...C({date:"$_reports/date"}),traffic_campaign(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_campaign"]:this.$store.getters["$_reports/traffic_campaign"]},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type},campaigns(){return this.traffic_campaign.campaign_details_table?this.traffic_campaign.campaign_details_table:[]}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.traffic_campaign.sessions_chart?{labels:this.traffic_campaign.sessions_chart.categories,data:this.traffic_campaign.sessions_chart.sessions,timestamps:this.traffic_campaign.sessions_chart.timestamps,trend:this.traffic_campaign.sessions_chart.session_trendpoints,notes:this.traffic_campaign.sessions_chart.notes,compare:this.traffic_campaign.sessions_compare_chart?this.traffic_campaign.sessions_compare_chart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){return this.traffic_campaign.sessions_chart?{labels:this.traffic_campaign.sessions_chart.categories,data:this.traffic_campaign.sessions_chart.page_views,timestamps:this.traffic_campaign.sessions_chart.timestamps,trend:this.traffic_campaign.sessions_chart.page_view_trendpoints,notes:this.traffic_campaign.sessions_chart.notes,compare:this.traffic_campaign.sessions_compare_chart?this.traffic_campaign.sessions_compare_chart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},getCampaignsHeaders(){return[{title:S("Campaign","google-analytics-for-wordpress"),key:"campaign",sortable:!0},{title:S("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:S("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:S("Pages / Sessions","google-analytics-for-wordpress"),key:"pages_per_sessions",sortable:!0},{title:S("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0},{title:S("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0},{title:S("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0}]},prepareCampaignsRows(){let s=[];return this.campaigns.length>0&&typeof this.campaigns[0]<"u"&&Object.values(this.campaigns).forEach(e=>{const t=[];t.push(this.tableFormatRowData(e.campaign)),t.push(this.tableFormatRowData(e.sessions)),t.push(this.tableFormatRowData(e.engaged_sessions)),t.push(this.tableFormatRowData(e.pages_per_sessions)),t.push(this.tableFormatRowData(e.purchases)),t.push(this.tableFormatRowData(e.conversion_rate,"%")),t.push(this.tableFormatRowData(e.revenue)),s.push(t)}),s},prepareCampaignsError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getCampaignsEmptyText(){return S("No data currently for the Campaigns report","google-analytics-for-wordpress")}}};var Rn=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-campaign"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_traffic_campaigns)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_traffic_campaigns}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),e.campaigns.length>0?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_traffic_campaign_details,rows:e.prepareCampaignsRows(),columns:e.getCampaignsHeaders(),error:e.prepareCampaignsError(e.campaigns),emptytext:e.getCampaignsEmptyText(),"allow-search":e.reportTableSearch}})],1):e._e(),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"traffic_campaign"}}):e._e()],1)},Pn=[],Dn=l($n,Rn,Pn,!1,null,null,null,null);const Sn=Dn.exports,{__:T,sprintf:yt}=wp.i18n,Tn={name:"ReportTrafficSourceMedium",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportOverviewLineChartApex:J,ReportTable:Q,SiteNotesOverview:te},mixins:[B],data(){return{text_traffic_source_medium:T("Source / Medium Report","google-analytics-for-wordpress"),text_traffic_source_medium_details:T("Source / Medium Details","google-analytics-for-wordpress"),text_sessions:T("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:yt(T("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:T("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:yt(T("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",chart_style:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'},show_overview_notes:!1,text_site_notes:T("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:T("Close Site Notes","google-analytics-for-wordpress"),fake_headers:[T("Source / Medium","google-analytics-for-wordpress"),T("Sessions","google-analytics-for-wordpress"),T("Engaged Sessions","google-analytics-for-wordpress"),T("Pages / Sessions","google-analytics-for-wordpress"),T("Purchases","google-analytics-for-wordpress"),T("Conversion Rate","google-analytics-for-wordpress"),T("Revenue","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:T("An unknown error has occurred.","google-analytics-for-wordpress")},overviewKey:1}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","traffic_source_medium")},computed:{...C({date:"$_reports/date"}),traffic_source_medium(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_source_medium"]:this.$store.getters["$_reports/traffic_source_medium"]},source_medium(){return this.traffic_source_medium.source_medium_table?this.traffic_source_medium.source_medium_table:[]}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.traffic_source_medium.sessions_chart?{labels:this.traffic_source_medium.sessions_chart.categories,data:this.traffic_source_medium.sessions_chart.sessions,timestamps:this.traffic_source_medium.sessions_chart.timestamps,trend:this.traffic_source_medium.sessions_chart.session_trendpoints,notes:this.traffic_source_medium.sessions_chart.notes,compare:this.traffic_source_medium.sessions_compare_chart?this.traffic_source_medium.sessions_compare_chart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){return this.traffic_source_medium.sessions_chart?{labels:this.traffic_source_medium.sessions_chart.categories,data:this.traffic_source_medium.sessions_chart.page_views,timestamps:this.traffic_source_medium.sessions_chart.timestamps,trend:this.traffic_source_medium.sessions_chart.page_view_trendpoints,notes:this.traffic_source_medium.sessions_chart.notes,compare:this.traffic_source_medium.sessions_compare_chart?this.traffic_source_medium.sessions_compare_chart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"},getSourceMediumHeaders(){return[{title:T("Source / Medium","google-analytics-for-wordpress"),key:"source_medium",sortable:!0},{title:T("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:T("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:T("Pages / Sessions","google-analytics-for-wordpress"),key:"pages_per_sessions",sortable:!0},{title:T("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0},{title:T("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0},{title:T("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0}]},prepareSourceMediumRows(){let s=[];return this.source_medium.length>0&&typeof this.source_medium[0]<"u"&&Object.values(this.source_medium).forEach(e=>{const t=[];t.push(this.tableFormatRowData(e.source_medium)),t.push(this.tableFormatRowData(e.sessions)),t.push(this.tableFormatRowData(e.engaged_sessions)),t.push(this.tableFormatRowData(e.pages_per_sessions)),t.push(this.tableFormatRowData(e.purchases)),t.push(this.tableFormatRowData(e.conversion_rate,"%")),t.push(this.tableFormatRowData(e.revenue)),s.push(t)}),s},prepareSourceMediumError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getSourceMediumEmptyText(){return T("No data currently for the Source / Medium report","google-analytics-for-wordpress")}}};var Mn=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-source-medium"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_traffic_source_medium)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_traffic_source_medium}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[t("i",{staticClass:"monstericon-user"}),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[t("i",{staticClass:"monstericon-eye"}),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chart_style}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e(),e.source_medium.length>0?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_traffic_source_medium_details,rows:e.prepareSourceMediumRows(),columns:e.getSourceMediumHeaders(),error:e.prepareSourceMediumError(e.source_medium),emptytext:e.getSourceMediumEmptyText(),"allow-search":e.reportTableSearch}})],1):e._e(),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"traffic_source_medium"}}):e._e()],1)},Ln=[],En=l(Tn,Mn,Ln,!1,null,null,null,null);const An=En.exports,{__:ge,sprintf:Ct}=wp.i18n,Fn={name:"TrafficCommonSessionChart",components:{ReportOverviewLineChartApex:J,SiteNotesOverview:te},props:{sessionsChart:{required:!0,type:Object},chartStyle:{required:!0,type:Object},isMI:{required:!0,type:Boolean},compareChart:{default:()=>{},type:Object}},data(){return{text_sessions:ge("Sessions","google-analytics-for-wordpress"),text_sessions_tooltip:Ct(ge("Unique %s Sessions","google-analytics-for-wordpress"),"<br />"),text_pageviews:ge("Pageviews","google-analytics-for-wordpress"),text_pageviews_tooltip:Ct(ge("Unique %s Pageviews","google-analytics-for-wordpress"),"<br />"),current_tab:"sessions",show_overview_notes:!1,text_site_notes:ge("Site Notes","google-analytics-for-wordpress"),text_close_site_notes:ge("Close Site Notes","google-analytics-for-wordpress"),overviewKey:1}},methods:{toggleNotes(){this.show_overview_notes=!this.show_overview_notes},changeOverviewKey(){this.overviewKey=Math.random()},switchTab(s){this.current_tab=s},activeTabButtonClass(s){return s===this.current_tab?"monsterinsights-active-tab-button":"monsterinsights-deactive-tab-button"},sessionsData(){return this.sessionsChart.categories?{labels:this.sessionsChart.categories,data:this.sessionsChart.sessions,timestamps:this.sessionsChart.timestamps,trend:this.sessionsChart.session_trendpoints,notes:this.sessionsChart.notes,compare:this.compareChart&&this.compareChart.sessions?this.compareChart.sessions:[]}:{}},sessionsKey(){return this.overviewKey+"-sessions"},pageviewsData(){return this.sessionsChart.categories?{labels:this.sessionsChart.categories,data:this.sessionsChart.page_views,timestamps:this.sessionsChart.timestamps,trend:this.sessionsChart.page_view_trendpoints,notes:this.sessionsChart.notes,compare:this.compareChart&&this.compareChart.page_views?this.compareChart.page_views:[]}:{}},pageviewsKey(){return this.overviewKey+"-pageviews"}}};var Nn=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-tabs"},[t("div",{staticClass:"monsterinsights-report-tabs-navigation"},[t("button",{class:e.activeTabButtonClass("sessions"),on:{click:function(r){return e.switchTab("sessions")}}},[e.isMI?t("i",{staticClass:"monstericon-user"}):e._e(),t("span",{domProps:{textContent:e._s(e.text_sessions)}})]),t("button",{class:e.activeTabButtonClass("pageviews"),on:{click:function(r){return e.switchTab("pageviews")}}},[e.isMI?t("i",{staticClass:"monstericon-eye"}):e._e(),t("span",{domProps:{textContent:e._s(e.text_pageviews)}})])]),e.current_tab==="sessions"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.sessionsKey(),attrs:{id:"chart-overview-sessions","chart-data":e.sessionsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chartStyle}})],1):e._e(),e.current_tab==="pageviews"?t("div",{staticClass:"monsterinsights-report-tabs-content"},[t("report-overview-line-chart-apex",{key:e.pageviewsKey(),attrs:{id:"chart-overview-pageviews","chart-data":e.pageviewsData(),tooltipDescriptor:e.text_sessions_tooltip,chartStyles:e.chartStyle}})],1):e._e(),e.show_overview_notes?e._e():t("a",{staticClass:"monsterinsights-notes-show",attrs:{href:"#"},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}},[t("span",[e._v(e._s(e.text_site_notes))])])]),e.show_overview_notes?t("div",{staticClass:"monsterinsights-overview-notes-container",attrs:{id:"monsterinsights_site_notes"}},[t("site-notes-overview",{on:{"refresh-overview-report":e.changeOverviewKey}}),e.show_overview_notes?t("a",{staticClass:"monsterinsights-notes-hide",attrs:{href:"#"},domProps:{textContent:e._s(e.text_close_site_notes)},on:{click:function(r){return r.preventDefault(),e.toggleNotes()}}}):e._e()],1):e._e()])},In=[],Bn=l(Fn,Nn,In,!1,null,null,null,null);const On=Bn.exports,{__:G}=wp.i18n,Un={name:"ReportTrafficSocial",components:{UpsellOverlay:It,ReportsDatePicker:I,ReportsPdfExport:F,CommonSessionChart:On,ReportTable:Q,ReportUpsellOverlay:E},mixins:[B],data(){return{text_social_media_report:G("Social Media Report","google-analytics-for-wordpress"),text_report_details:G("Report Details","google-analytics-for-wordpress"),isMI:!0,chartStyle:{},text_upsell_title:G("See Which Social Media Networks are Making You Money.","google-analytics-for-wordpress"),upgrade_button_url:this.$getUpgradeUrl("reports","traffic_social")}},computed:{traffic_social(){return this.showUpsell()?this.$store.getters["$_reports/demo_traffic_social"]:this.$store.getters["$_reports/traffic_social"]},social_table(){return this.traffic_social.social_table?this.traffic_social.social_table:[]}},methods:{getSocialTableHeaders(){return[{title:G("Network","google-analytics-for-wordpress"),key:"network",sortable:!0},{title:G("Sessions","google-analytics-for-wordpress"),key:"sessions",sortable:!0},{title:G("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:G("Bounce Rate","google-analytics-for-wordpress"),key:"bounce_rate",sortable:!0},{title:G("Purchases","google-analytics-for-wordpress"),key:"purchases",sortable:!0},{title:G("Revenue","google-analytics-for-wordpress"),key:"revenue",sortable:!0},{title:G("Conversion Rate","google-analytics-for-wordpress"),key:"conversion_rate",sortable:!0}]},prepareSocialTableRows(){return this.social_table.map(s=>{let e=typeof s.bounce_rate=="object"?s.bounce_rate.map(r=>r+"%"):null;e||(e=typeof s.bounce_rate<"u"?[s.bounce_rate,s.bounce_rate]:"--");let t=typeof s.conversion_rate=="object"?s.conversion_rate.map(r=>r+"%"):null;return t||(t=typeof s.conversion_rate<"u"?[s.conversion_rate,s.conversion_rate]:"--"),[['<img src="'+this.getIconUrl(s.icon)+'" alt=""><span>'+s.network+"</span>"],this.tableFormatRowData(s.sessions),this.tableFormatRowData(s.engaged_sessions),this.tableFormatRowData(s.bounce_rate,"%"),this.tableFormatRowData(s.purchases),this.tableFormatRowData(s.revenue),this.tableFormatRowData(s.conversion_rate,"%")]})},getSocialTableEmptyText(){return G("No data currently for the social media report.","google-analytics-for-wordpress")},getIconUrl(s){return s?this.$mi.pro_assets+"/img/social/icon-"+s+".svg":""}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_reports/getReportData","traffic_social")}};var Hn=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-traffic-social"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_social_media_report)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_social_media_report}})],1),t("CommonSessionChart",{attrs:{"sessions-chart":e.traffic_social.sessions_chart,"compare-chart":e.traffic_social.sessions_compare_chart?e.traffic_social.sessions_compare_chart:{},isMI:e.isMI,chartStyle:e.chartStyle}}),t("div",{staticClass:"monsterinsights-report-row"},[t("ReportTable",{attrs:{title:e.text_report_details,rows:e.prepareSocialTableRows(),columns:e.getSocialTableHeaders(),emptytext:e.getSocialTableEmptyText()}})],1),e.showUpsell()?[e.isMI?t("ReportUpsellOverlay",{attrs:{report:"traffic_social"}}):t("UpsellOverlay",{attrs:{report:"traffic_social",heading:e.text_social_media_report,title:e.text_upsell_title,upgradeHref:e.upgrade_button_url,previewClass:"monsterinsights-em-traffic-social-upsell-screen"}})]:e._e()],2)},Vn=[],Yn=l(Un,Hn,Vn,!1,null,null,null,null);const jn=Yn.exports,Zn={name:"MIReportTrafficSocial",extends:jn,data(){return{chartStyle:{foreColor:"#999",borderColor:"#f3f6fa",colors:["#3a93dd","#5CC0A5"],markersStrokeColor:["#3a93dd"],markersColors:["#ffffff"],markersStrokeColorHover:["#ffffff"],markersColorsHover:["#3a93dd"],fontSize:"12px",fontFamily:'"Helvetica Neue", Helvetica, Arial, sans-serif'}}}},Wn=null,zn=null;var Kn=l(Zn,Wn,zn,!1,null,null,null,null);const qn=Kn.exports,Gn={name:"ReportBarChart",props:{chartData:{type:Object,required:!0},compareChartColors:{type:Array,default:()=>["#88A8D4","#B9D8F3"]}},components:{apexchart:Re},computed:{...C({date:"$_reports/date"}),chartOptions(){return{chart:{type:"bar",toolbar:{show:!1}},plotOptions:{bar:{horizontal:!1}},xaxis:{categories:this.chartData.labels},yaxis:{decimalsInFloat:!1},colors:this.getColors(),dataLabels:{enabled:!1},tooltip:{y:{formatter:function(s){return Number.isInteger(s)?String(s)+"%":String(Number(s).toFixed(2))+"%"},title:{formatter:function(){return""}}}},fill:{opacity:1}}},chartSeries(){let s=[{name:this.date.intervalText,data:this.chartData.values}];return this.date.compareReport&&this.chartData.compare&&s.push({name:this.date.intervalCompareText,data:this.chartData.compare}),s},chartColors(){return me.COLORS}},methods:{getColors(){return this.date.compareReport?this.compareChartColors:[s=>this.chartColors[s.dataPointIndex]?this.chartColors[s.dataPointIndex]:this.chartColors[0]]}}};var Jn=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-apexchart-bar-chart"},[t("apexchart",{attrs:{options:e.chartOptions,series:e.chartSeries,width:"100%",height:"100%"}})],1)},Qn=[],Xn=l(Gn,Jn,Qn,!1,null,null,null,null);const el=Xn.exports,tl={name:"ReportPublisherBarChart",components:{ReportBarChart:el,SettingsInfoTooltip:Pe},props:{chartData:[Object,Boolean],title:String,tooltip:String,tooltips:Function,id:String,compareChartColors:{type:Array,default:()=>["#3D6EC2","#C1DAF9"]}},computed:{...C({date:"$_reports/date"}),tooltipId(){return"monsterinsights-chartjs-pie-"+this.id+"-tooltip"},inlineStyle(){return this.date.compareReport?{height:"470px"}:{height:"200px"}}}};var sl=function(){var e=this,t=e._self._c;return e.chartData?t("div",{staticClass:"monsterinsights-reports-pie-chart"},[t("h3",{staticClass:"monsterinsights-report-title",domProps:{textContent:e._s(e.title)}}),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e(),t("div",{staticClass:"monsterinsights-reports-bar-chart-holder"},[t("div",{staticClass:"monsterinsights-pie-chart-tooltip",attrs:{id:e.tooltipId}}),t("report-bar-chart",{style:e.inlineStyle,attrs:{"chart-data":e.chartData,"compare-chart-colors":e.compareChartColors}})],1)],1):e._e()},rl=[],ol=l(tl,sl,rl,!1,null,null,null,null);const il=ol.exports,{__:f,sprintf:bt}=wp.i18n,al={name:"ReportEngagementOverview",components:{ReportUpsellOverlay:E,ReportOverviewPieChartApex:Je,ReportPublisherBarChart:il,ReportTableBox:_e,ReportsDatePicker:I,ReportsPdfExport:F},mixins:[B],data(){return{text_engagement:f("Publishers","google-analytics-for-wordpress"),text_engagement_report:f("Publishers Report","google-analytics-for-wordpress"),text_landing_pages:f("Top Landing Pages","google-analytics-for-wordpress"),text_landing_pages_empty:f("No landing pages tracked during this time period.","google-analytics-for-wordpress"),landing_pages_headers:[f("Page Titles","google-analytics-for-wordpress"),f("Visits","google-analytics-for-wordpress"),f("Avg. Duration","google-analytics-for-wordpress"),f("Bounce Rate","google-analytics-for-wordpress")],text_exit_pages:f("Top Exit Pages","google-analytics-for-wordpress"),text_exit_pages_empty:f("No exit pages tracked during this time period.","google-analytics-for-wordpress"),exit_pages_headers:[f("Page Titles","google-analytics-for-wordpress"),f("Exits","google-analytics-for-wordpress"),f("Page Views","google-analytics-for-wordpress"),f("% of Exits","google-analytics-for-wordpress")],text_outbound_links:f("Top Outbound Links","google-analytics-for-wordpress"),text_outbound_links_empty:f("No outbound link clicks detected for this time period.","google-analytics-for-wordpress"),outbound_links_headers:[f("Links","google-analytics-for-wordpress"),f("Total Clicks","google-analytics-for-wordpress")],text_affiliate_links:f("Top Affiliate Links","google-analytics-for-wordpress"),text_affiliate_links_empty:f("No affiliate link clicks detected for this time period.","google-analytics-for-wordpress"),affiliate_links_headers:[f("Label","google-analytics-for-wordpress"),f("Link","google-analytics-for-wordpress"),f("Total Clicks","google-analytics-for-wordpress")],text_downloads:f("Top Download Links","google-analytics-for-wordpress"),text_downloads_empty:f("No download link clicks detected for this time period.","google-analytics-for-wordpress"),downloads_headers:[f("Link Label","google-analytics-for-wordpress"),f("Clicks","google-analytics-for-wordpress")],text_interest:f("Interests","google-analytics-for-wordpress"),interest_headers:[f("Categories","google-analytics-for-wordpress"),f("% of Interest","google-analytics-for-wordpress")],text_interest_empty:f("No interest groups detected for this time period.","google-analytics-for-wordpress"),text_interest_tooltip:f("This list shows the interest groups your visitors belong to.","google-analytics-for-wordpress"),text_interest_button:f("View Full Interests Report","google-analytics-for-wordpress"),text_landing_pages_button:f("View Full Top Landing Pages Report","google-analytics-for-wordpress"),text_exit_pages_button:f("View Full Top Exit Pages Report","google-analytics-for-wordpress"),text_download_links_button:f("View All Download Links Report","google-analytics-for-wordpress"),text_landing_pages_tooltip:f("This list shows the top pages users first land on when visiting your website.","google-analytics-for-wordpress"),text_exit_pages_tooltip:f("This list shows the top pages users exit your website from.","google-analytics-for-wordpress"),text_outbound_links_tooltip:f("This list shows the top links clicked on your website that go to another website.","google-analytics-for-wordpress"),text_affiliate_links_tooltip:f("This list shows the top affiliate links your visitors clicked the most.","google-analytics-for-wordpress"),text_download_links_tooltip:f("This list shows the download links your visitors clicked the most.","google-analytics-for-wordpress"),text_age_title:f("Age","google-analytics-for-wordpress"),text_age_tooltip:f("This graph shows what percent of your users are in a particular age group.","google-analytics-for-wordpress"),text_gender_title:f("Gender","google-analytics-for-wordpress"),text_gender_tooltip:f("This graph shows the gender breakdown of your website visitors.","google-analytics-for-wordpress"),text_scroll:f("Scroll","google-analytics-for-wordpress"),text_scroll_label:f("Average Scroll Depth","google-analytics-for-wordpress"),text_scroll_explainer:bt(f("Scroll Depth events are triggered at 25%%, 50%%, 75%% and 100%% scrolling. The number above represents the average scroll depth from your visitors in the selected interval for all your website's pages. %1$sYou can read more about how to read this number in our %2$sknowledge base%3$s.%4$s","google-analytics-for-wordpress"),'<span class="monsterinsights-ignore-data-from-pdf-reports">','<a href="'+this.$getUrl("publishers","scroll","https://www.monsterinsights.com/docs/scroll-tracking-and-reporting/")+'" target="_blank" rel="noopener noreferrer">',"</a>","</span>"),text_errors:{MISSING_CUSTOM_DIMENSION:f('Please set up custom dimension "%s" for this report to work.',"google-analytics-for-wordpress"),UNKNOWN_ERROR:f("An unknown error has occurred.","google-analytics-for-wordpress")}}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_reports/getReportData","publisher")},computed:{...C({date:"$_reports/date"}),publisher(){return this.showUpsell()?this.$store.getters["$_reports/demo_publisher"]:this.$store.getters["$_reports/publisher"]},available_landing_pages_headers(){return this.landing_pages_rows.length>0?this.landing_pages_headers.slice(0,this.landing_pages_rows[0].length):this.landing_pages_headers},landing_pages_rows(){let s=[];return this.publisher.landingpages&&this.publisher.landingpages.forEach(e=>{typeof e.entrances<"u"?s.push([e.title,this.$formatNumber(e.entrances)]):s.push([this.tableFormatRowData(e.title),this.tableFormatRowData(e.visits,null,this.$formatNumber),this.tableFormatRowData(e.duration),this.tableFormatRowData(e.bounce,"%",this.$formatNumber)])}),s},has_exit_pages(){return typeof this.publisher.exitpages<"u"},exit_pages_rows(){let s=[];return this.publisher.exitpages&&this.publisher.exitpages.forEach(e=>{let t=this.$formatNumber(e.exitrate),r=this.$formatNumber(e.exits),o=this.$formatNumber(e.pageviews);t+="%",s.push([e.title,r,o,t])}),s},outbound_links_rows(){let s=[];return this.publisher.outboundlinks&&this.publisher.outboundlinks.length&&this.publisher.outboundlinks.forEach(e=>{s.push([this.tableFormatRowData(e.title),this.tableFormatRowData(e.clicks,null,this.$formatNumber)])}),s},outbound_links_error(){return this.getErrorForReport("outboundlinks")},affiliate_links_rows(){let s=[];return this.publisher.affiliatelinks&&this.publisher.affiliatelinks.length&&this.publisher.affiliatelinks.forEach(e=>{s.push([this.tableFormatRowData(e.title),this.tableFormatRowData(e.link),this.tableFormatRowData(e.clicks,null,this.$formatNumber)])}),s},affiliate_links_error(){return this.getErrorForReport("affiliatelinks")},downloads_rows(){let s=[];return this.publisher.downloadlinks&&this.publisher.downloadlinks.length&&this.publisher.downloadlinks.forEach(e=>{s.push([this.tableFormatRowData(e.title),this.tableFormatRowData(e.clicks,null,this.$formatNumber)])}),s},download_error(){return this.getErrorForReport("downloadlinks")},interest_rows(){let s=[];return this.publisher.interest&&this.publisher.interest.forEach(e=>{s.push([this.tableFormatRowData(e.interest),this.tableFormatRowData(e.percent,"%",this.$formatNumber)])}),s},ageData(){if(this.publisher.age){let s={values:this.publisher.age.graph.data,labels:this.publisher.age.graph.labels};return this.date.compareReport&&this.publisher.age.graph.compare&&(s.compare=this.publisher.age.graph.compare),s}return!1},genderData(){if(this.publisher.gender){let s={values:this.publisher.gender.graph.data,labels:this.publisher.gender.graph.labels};return this.date.compareReport&&this.publisher.gender.graph.compare&&(s.compare={values:this.publisher.gender.graph.compare,labels:this.publisher.gender.graph.labels}),s}return!1},scrollPercent(){return(this.publisher.scroll.average||"0")+"%"},scrollPercentError(){return this.getErrorForReport("scroll")}},methods:{gaLinks(s){return this.showUpsell()?this.$getUpgradeUrl("reports","publisher"):typeof this.publisher.galinks<"u"&&this.publisher.galinks[s]?this.publisher.galinks[s]:!1},getErrorForReport(s){return this.publisher[s]&&this.publisher[s].error?bt(this.text_errors[this.publisher[s].error.code]||this.text_errors.UNKNOWN_ERROR,this.publisher[s].error.data||""):""}}};var nl=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-engagement-overview"},[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.text_engagement)}}),t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_engagement_report}})],1),t("div",{staticClass:"monsterinsights-report-row"},[t("ReportTableBox",{attrs:{title:e.text_landing_pages,headers:e.available_landing_pages_headers,rows:e.landing_pages_rows,emptytext:e.text_landing_pages_empty,tooltip:e.text_landing_pages_tooltip,"has-compare-change-column":!0,"compare-change-opposite":[9]}},[e.gaLinks("landingpages")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("landingpages"),target:"_blank"},domProps:{textContent:e._s(e.text_landing_pages_button)},slot:"button"}):e._e()])],1),e.has_exit_pages?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table-box",{attrs:{title:e.text_exit_pages,headers:e.exit_pages_headers,rows:e.exit_pages_rows,emptytext:e.text_exit_pages_empty,tooltip:e.text_exit_pages_tooltip}},[e.gaLinks("exitpages")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("exitpages"),target:"_blank"},domProps:{textContent:e._s(e.text_exit_pages_button)},slot:"button"}):e._e()])],1):e._e(),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex"},[t("report-table-box",{attrs:{title:e.text_outbound_links,headers:e.outbound_links_headers,rows:e.outbound_links_rows,emptytext:e.text_outbound_links_empty,tooltip:e.text_outbound_links_tooltip,error:e.outbound_links_error,"has-compare-change-column":!0}}),t("report-table-box",{attrs:{title:e.text_affiliate_links,headers:e.affiliate_links_headers,rows:e.affiliate_links_rows,emptytext:e.text_affiliate_links_empty,tooltip:e.text_affiliate_links_tooltip,error:e.affiliate_links_error,"compare-single-columns":[0,1],"has-compare-change-column":!0}})],1),t("div",{staticClass:"monsterinsights-report-row",attrs:{id:"monsterinsights-report-download-links"}},[t("report-table-box",{attrs:{title:e.text_downloads,headers:e.downloads_headers,rows:e.downloads_rows,emptytext:e.text_downloads_empty,tooltip:e.text_download_links_tooltip,error:e.download_error,"has-compare-change-column":!0}},[e.gaLinks("downloadlinks")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("downloadlinks"),target:"_blank"},domProps:{textContent:e._s(e.text_download_links_button)},slot:"button"}):e._e()])],1),e.ageData||e.genderData?t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex"},[t("report-publisher-bar-chart",{attrs:{id:"age",chartData:e.ageData,title:e.text_age_title,tooltip:e.text_age_tooltip}}),t("report-overview-pie-chart-apex",{attrs:{id:"gender",chartData:e.genderData,title:e.text_gender_title,tooltip:e.text_gender_tooltip,width:200,height:200,size:50}})],1):e._e(),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-flex monsterinsights-interests-scroll-report"},[e.publisher.interest?t("report-table-box",{attrs:{title:e.text_interest,headers:e.interest_headers,rows:e.interest_rows,emptytext:e.text_interest_empty,tooltip:e.text_interest_tooltip,"has-compare-change-column":!0}},[e.gaLinks("interest")?t("a",{staticClass:"monsterinsights-button",attrs:{slot:"button",href:e.gaLinks("interest"),target:"_blank"},domProps:{textContent:e._s(e.text_interest_button)},slot:"button"}):e._e()]):e._e(),e.publisher.scroll?t("div",{staticClass:"monsterinsights-report-box monsterinsights-report-scroll",attrs:{id:"monsterinsights-report-scroll-depth"}},[t("h3",{domProps:{textContent:e._s(e.text_scroll)}}),t("div",{staticClass:"monsterinsights-realtime-box-content"},[e.scrollPercentError!==""?t("h3",{domProps:{innerHTML:e._s(e.scrollPercentError)}}):[t("div",{staticClass:"monsterinsights-realtime-large",domProps:{textContent:e._s(e.scrollPercent)}}),t("div",{staticClass:"monsterinsights-realtime-active",domProps:{textContent:e._s(e.text_scroll_label)}})],t("p",{domProps:{innerHTML:e._s(e.text_scroll_explainer)}})],2)]):e._e()],1),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"publisher"}}):e._e()],1)},ll=[],cl=l(al,nl,ll,!1,null,null,null,null);const pl=cl.exports,{__:j}=wp.i18n,dl={name:"ReportTrafficEngagementPages",components:{ReportUpsellOverlay:E,ReportsDatePicker:I,ReportsPdfExport:F,ReportTable:Q},mixins:[B],data(){return{text_engagement_pages:j("Pages Report","google-analytics-for-wordpress"),fake_headers:[j("Page","google-analytics-for-wordpress"),j("Page Views","google-analytics-for-wordpress"),j("Engaged Sessions","google-analytics-for-wordpress"),j("New Sessions","google-analytics-for-wordpress"),j("Bounce Rate","google-analytics-for-wordpress")],reportTableSearch:!0,text_errors:{UNKNOWN_ERROR:j("An unknown error has occurred.","google-analytics-for-wordpress")}}},mounted(){if(this.showUpsell())return this.$store.commit("$_reports/ENABLE_BLUR"),!1;this.$store.dispatch("$_auth/getAuth"),this.$store.dispatch("$_reports/getReportData","engagement_pages")},computed:{engagement_pages(){return this.showUpsell()?this.$store.getters["$_reports/demo_engagement_pages"]:this.$store.getters["$_reports/engagement_pages"]},engagementPages(){return this.engagement_pages.pages_report_table?this.engagement_pages.pages_report_table:[]}},methods:{getEngagementPagesHeaders(){return[{title:j("Page","google-analytics-for-wordpress"),key:"page_path",sortable:!0},{title:j("Page Views","google-analytics-for-wordpress"),key:"pageviews",sortable:!0},{title:j("Engaged Sessions","google-analytics-for-wordpress"),key:"engaged_sessions",sortable:!0},{title:j("New Sessions","google-analytics-for-wordpress"),key:"new_sessions",sortable:!0},{title:j("Bounce","google-analytics-for-wordpress"),key:"bounce_rate",sortable:!0}]},prepareEngagementPagesRows(){let s=[];return this.engagementPages.length>0&&typeof this.engagementPages[0]<"u"&&Object.values(this.engagementPages).forEach(e=>{s.push([this.tableFormatRowData(e.page_path),this.tableFormatRowData(e.pageviews),this.tableFormatRowData(e.engaged_sessions),this.tableFormatRowData(e.new_session),this.tableFormatRowData(e.bounce_rate,"%")])}),s},prepareEngagementPagesError(s){return s.error?this.text_errors.UNKNOWN_ERROR:""},getEngagementPagesEmptyText(){return j("No data currently for the Pages report","google-analytics-for-wordpress")}}};var hl=function(){var e=this,t=e._self._c;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-engagement-pages"},[t("div",{staticClass:"monsterinsights-report-top"},[t("reports-date-picker"),t("reports-pdf-export",{attrs:{"report-title":e.text_engagement_pages}})],1),e.engagementPages.length>0?t("div",{staticClass:"monsterinsights-report-row"},[t("report-table",{attrs:{title:e.text_engagement_pages,rows:e.prepareEngagementPagesRows(),columns:e.getEngagementPagesHeaders(),error:e.prepareEngagementPagesError(e.engagementPages),emptytext:e.getEngagementPagesEmptyText(),"allow-search":e.reportTableSearch}})],1):e._e(),e.showUpsell()?t("report-upsell-overlay",{attrs:{report:"engagement_pages"}}):e._e()],1)},gl=[],ul=l(dl,hl,gl,!1,null,null,null,null);const ml=ul.exports,_l={__name:"ExceptionsStatus",props:{status:{type:String,default:"false"}},setup(s){const e=s,t=ze(()=>e.status!=="true");return{__sfc:!0,props:e,status:t}}};var fl=function(){var e=this,t=e._self._c,r=e._self._setupProxy;return r.status?t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M10 0C4.47715 0 0 4.47715 0 10C0 15.5229 4.47715 20 10 20C15.5229 20 20 15.5229 20 10C20 4.47715 15.5229 0 10 0ZM14.2908 4.68383L16.3684 6.76147L9.87915 13.252L7.81372 15.3162L5.73608 13.2385L3.63158 11.1328L5.6958 9.06858L7.8003 11.1743L14.2908 4.68383Z",fill:"#4CAF50"}})]):t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M10 0C4.47717 0 0 4.47717 0 10C0 15.5228 4.47717 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47717 15.5228 0 10 0ZM10 2.17285C14.3228 2.17285 17.8259 5.67827 17.8259 10C17.8259 14.3218 14.3228 17.8259 10 17.8259C5.67717 17.8259 2.17407 14.3218 2.17407 10C2.17408 5.67827 5.67717 2.17285 10 2.17285ZM13.8318 5.25147L8.03588 11.0486L6.156 9.1687L4.31152 11.012L6.1914 12.8919L8.04808 14.7485L9.89137 12.9041L15.6885 7.10815L13.8318 5.25147Z",fill:"#BDBDBD"}})])},vl=[],wl=l(_l,fl,vl,!1,null,null,null,null);const yl=wl.exports,Cl={__name:"ExceptionsDelete",props:{exceptionId:{type:Number,required:!0}},emits:["exception-deleted"],setup(s,{emit:e}){const t=s,{__:r}=wp.i18n;function o(){if(!w.prototype.$isPro())return;w.prototype.$mi_loading_toast(r("Deleting Exception","google-analytics-for-wordpress"));let n=new FormData;n.append("action","monsterinsights_exceptions_delete"),n.append("nonce",w.prototype.$mi.nonce),n.append("exception_id",t.exceptionId),ne.post(w.prototype.$mi.ajax,n).then(a=>{a.data.success?e("exception-deleted"):w.prototype.$swal.close()}).catch(()=>{w.prototype.$swal.close()})}return{__sfc:!0,__:r,props:t,emit:e,onClick:o}}};var bl=function(){var e=this,t=e._self._c,r=e._self._setupProxy;return t("a",{attrs:{href:"javascript:void(0);"},on:{click:function(o){return o.stopPropagation(),o.preventDefault(),r.onClick.apply(null,arguments)}}},[t("svg",{attrs:{width:"12",height:"14",viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M10.9688 2.15625H8.86133L7.99805 0.734375C7.79492 0.404297 7.33789 0.125 6.95703 0.125H4.39258C4.01172 0.125 3.55469 0.404297 3.35156 0.734375L2.48828 2.15625H0.40625C0.177734 2.15625 0 2.35938 0 2.5625V2.96875C0 3.19727 0.177734 3.375 0.40625 3.375H0.8125L1.3457 11.9824C1.37109 12.6172 1.92969 13.125 2.56445 13.125H8.78516C9.41992 13.125 9.97852 12.6172 10.0039 11.9824L10.5625 3.375H10.9688C11.1719 3.375 11.375 3.19727 11.375 2.96875V2.5625C11.375 2.35938 11.1719 2.15625 10.9688 2.15625ZM4.39258 1.34375H6.95703L7.43945 2.15625H3.91016L4.39258 1.34375ZM8.78516 11.9062H2.56445L2.03125 3.375H9.31836L8.78516 11.9062Z",fill:"#EB5757"}})]),t("span",[e._v("Delete")])])},xl=[],kl=l(Cl,bl,xl,!1,null,null,null,null);const $l=kl.exports,Rl={__name:"ExceptionsTable",props:{tableRows:{type:Array,required:!0},pagination:{type:Object,default:()=>({})},perPage:{type:String,required:!0}},emits:["per-page-change"],setup(s,{emit:e}){const t=s,{__:r}=wp.i18n,o=r("Category","google-analytics-for-wordpress"),n=r("Time","google-analytics-for-wordpress"),a=r("Recurring","google-analytics-for-wordpress"),d=r("Description","google-analytics-for-wordpress"),p=r("Action","google-analytics-for-wordpress"),m=r("No data currently for the report.","google-analytics-for-wordpress"),b=r("Items per page:","google-analytics-for-wordpress"),k=ze(()=>t.tableRows),U=ze(()=>t.pagination),H=Qt(t.perPage),O=[20,50,100,200,500];function ce(De){e("per-page-change",De.target.value)}return{__sfc:!0,__:r,text_category:o,text_time:n,text_resolved:a,text_description:d,text_action:p,text_empty:m,text_number_of_items:b,props:t,emit:e,tableRows:k,tablePagination:U,itemsPerPage:H,itemsPerPageOptions:O,onItemsPerPageChange:ce,ExceptionsStatus:yl,ExceptionsDelete:$l,Pagination:Ot}}};var Pl=function(){var e=this,t=e._self._c,r=e._self._setupProxy;return t("div",{staticClass:"monsterinsights-table-box"},[r.tableRows.length>0?t("div",{staticClass:"monsterinsights-table-box-table"},[t("table",{staticClass:"monsterinsights-has-th-border"},[t("thead",[t("tr",[t("th",[e._v(e._s(r.text_category))]),t("th",[e._v(e._s(r.text_time))]),t("th",[e._v(e._s(r.text_resolved))]),t("th",[e._v(e._s(r.text_description))]),t("th",[e._v(e._s(r.text_action))])])]),t("tbody",e._l(r.tableRows,function(o,n){return t("tr",{key:n,staticClass:"monsterinsights-table-list-item"},[t("td",[e._v(e._s(o.category))]),t("td",[e._v(e._s(o.time))]),t("td",{staticClass:"text-align-center"},[t(r.ExceptionsStatus,{attrs:{status:String(o.resolved)}})],1),t("td",[e._v(e._s(o.description))]),t("td",{staticClass:"monsterinsights-table-exceptions-action"},[t(r.ExceptionsDelete,{attrs:{"exception-id":o.id},on:{"exception-deleted":function(a){return e.$emit("exception-deleted")}}})],1)])}),0)])]):t("div",{staticClass:"monsterinsights-table-no-data"},[t("h3",{domProps:{textContent:e._s(r.text_empty)}})]),r.tableRows.length>0?t("div",{staticClass:"monsterinsights-table-box-footer"},[t("div",{staticClass:"monsterinsights-table-box-footer-items-per-page"},[t("label",{domProps:{textContent:e._s(r.text_number_of_items)}}),t("select",{directives:[{name:"model",rawName:"v-model",value:r.itemsPerPage,expression:"itemsPerPage"}],on:{change:[function(o){var n=Array.prototype.filter.call(o.target.options,function(a){return a.selected}).map(function(a){var d="_value"in a?a._value:a.value;return d});r.itemsPerPage=o.target.multiple?n:n[0]},r.onItemsPerPageChange]}},e._l(r.itemsPerPageOptions,function(o){return t("option",{key:"option-"+o,domProps:{textContent:e._s(o)}})}),0)]),r.tablePagination.pages>1?t(r.Pagination,{attrs:{pagination:r.tablePagination},on:{"page-change":function(o){return e.$emit("page-change",o)}}}):e._e()],1):e._e()])},Dl=[],Sl=l(Rl,Pl,Dl,!1,null,null,null,null);const Tl=Sl.exports,Ml=[{id:529,category:"Engagement",time:"August 21, 4:30:08 PM",resolved:!1,description:"Bounce rate decrease by 30% over past 7 days"},{id:535,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"Prior day AOV was the lowest in the last 30 days."},{id:530,category:"Engagement",time:"August 21, 4:30:08 PM",resolved:!1,description:"Avgerage time on site decrease by 30% compared to prior 7 days"},{id:525,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"In the last 5 days number of sales increase by 100%"},{id:534,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"Prior day AOV was the highest in the last 30 days."},{id:528,category:"Engagement",time:"August 21, 4:30:08 PM",resolved:!1,description:"In last 7 days if engagement rate drops 30%"},{id:533,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"Prior day transactions was lowest in last 30 days."},{id:532,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"Prior day transactions was highest in last 30 days."},{id:531,category:"Engagement",time:"August 21, 4:30:08 PM",resolved:!1,description:"Returning visits decrease by 30%"},{id:524,category:"eCommerce",time:"August 21, 4:30:08 PM",resolved:!1,description:"In the last 5 days conversion rate increase by 11%"},{id:526,category:"Marketing",time:"August 21, 4:30:08 PM",resolved:!1,description:"In last 5 days test_campign campign generated 10 sales"},{id:527,category:"Marketing",time:"August 21, 4:30:08 PM",resolved:!1,description:"In last 5 days test_source source generated 5 sales"},{id:509,category:"Engagement",time:"August 20, 5:28:36 PM",resolved:!1,description:"In last 7 days if engagement rate drops 30%"},{id:515,category:"eCommerce",time:"August 20, 5:28:36 PM",resolved:!1,description:"Prior day AOV was the highest in the last 30 days."},{id:507,category:"Marketing",time:"August 20, 5:28:36 PM",resolved:!1,description:"In last 5 days test_campign campign generated 10 sales"},{id:511,category:"Engagement",time:"August 20, 5:28:36 PM",resolved:!1,description:"Avgerage time on site decrease by 30% compared to prior 7 days"},{id:512,category:"Engagement",time:"August 20, 5:28:36 PM",resolved:!1,description:"Returning visits decrease by 30%"},{id:516,category:"eCommerce",time:"August 20, 5:28:36 PM",resolved:!1,description:"Prior day AOV was the lowest in the last 30 days."},{id:505,category:"eCommerce",time:"August 20, 5:28:36 PM",resolved:!1,description:"In the last 5 days conversion rate increase by 11%"},{id:506,category:"eCommerce",time:"August 20, 5:28:36 PM",resolved:!1,description:"In the last 5 days number of sales increase by 100%"}],Ll={__name:"monsterinsights-ExceptionsReport-Lite",setup(s){const{__:e}=wp.i18n,t=bs(),r=e("Exceptions Report","google-analytics-for-wordpress");Xt(()=>{t.commit("$_reports/ENABLE_BLUR")});function o(){return w.prototype.$isPro()?w.prototype.$getAgencyUpgradeUrl("reports","exceptions"):null}return{__sfc:!0,__:e,store:t,text_main_heading:r,getUpgradeHref:o,ExceptionsTable:Tl,ReportUpsellOverlay:E,ReportsDatePicker:I,reportData:Ml}}};var El=function(){var e=this,t=e._self._c,r=e._self._setupProxy;return t("main",{staticClass:"monsterinsights-report monsterinsights-report-exceptions"},[t("div",{staticClass:"monsterinsights-report-top monsterinsights-report-exceptions-top"},[t("h2",{domProps:{textContent:e._s(r.text_main_heading)}}),t(r.ReportsDatePicker,{attrs:{isGetReportData:!1,"compare-options":!1}})],1),t("div",{staticClass:"monsterinsights-report-row monsterinsights-report-table-wrapper"},[t(r.ExceptionsTable,{attrs:{"table-rows":r.reportData,pagination:{},"per-page":"20"}})],1),t(r.ReportUpsellOverlay,{attrs:{report:"exceptions","upgrade-href":r.getUpgradeHref(),"is-agency":!0}})],1)},Al=[],Fl=l(Ll,El,Al,!1,null,null,null,null);const Nl=Fl.exports;const Il=(s,e)=>{s.insights=e},Bl=(s,e=!0)=>{s.isLoading=e},Ol=(s,{insightId:e,score:t,feedback:r=null,skipped:o=!1})=>{let n=s.ratings.filter(a=>a.insightId!==e);n.push({insightId:e,score:t,feedback:r,skipped:o}),s.ratings=n},Ul=s=>{s.ratings=[]},Hl={UPDATE_INSIGHTS:Il,SET_LOADING:Bl,RATE_INSIGHT:Ol,RESET_RATINGS:Ul},Vl=(s=null)=>{const e=ne.create();return e.interceptors.request.use(t=>{const r=window.monsterinsights,{rest_url:o,rest_nonce:n}=r;return{...t,baseURL:o+s,headers:{"X-WP-Nonce":n}}},t=>Promise.reject(t)),e};class Ut{constructor(e="monsterinsights/v1/"){this.client=Vl(e)}get(e,t={}){return this.client.get(e,t).then(r=>Promise.resolve(r.data)).catch(r=>Promise.reject(r))}delete(e,t={}){return this.client.delete(e,t).then(r=>Promise.resolve(r.data)).catch(r=>Promise.reject(r))}head(e,t={}){return this.client.head(e,t).then(r=>Promise.resolve(r)).catch(r=>Promise.reject(r))}options(e,t={}){return this.client.options(e,t).then(r=>Promise.resolve(r)).catch(r=>Promise.reject(r))}post(e,t={},r={}){return this.client.post(e,t,r).then(o=>Promise.resolve(o.data)).catch(o=>Promise.reject(o))}put(e,t={},r={}){return this.client.put(e,t,r).then(o=>Promise.resolve(o.data)).catch(o=>Promise.reject(o))}patch(e,t={},r={}){return this.client.patch(e,t,r).then(o=>Promise.resolve(o.data)).catch(o=>Promise.reject(o))}}const z=new Ut,Yl=(s={})=>z.get("ai-insights",{params:s}).then(e=>e),jl=(s,e)=>z.post("ai-insights/rate",{insight_id:s,score:e}).then(t=>t),Zl=()=>z.get("ai-insights/ratings").then(s=>s),Wl=({insightId:s,message:e,skipped:t=!1})=>z.post("ai-insights/feedback",{insight_id:s,message:e,skipped:t}).then(r=>r),le={getChats:(s=null)=>z.get("ai-insights/chats",{params:{lists:s}}).then(e=>e.data),getChatById:s=>z.get(`ai-insights/chat/${s}`).then(e=>e.data),updateChatProperties:(s,e)=>z.put(`ai-insights/chat/${s}`,e).then(t=>t.data),deleteChat:s=>z.delete(`ai-insights/chat/${s}`).then(e=>e.data),sendMessage:(s,e=null)=>{let t="ai-insights/chat";return e&&(t+=`/${e}`),z.post(t,{message:s})},rateChatMessage:(s,e,t)=>z.post(`ai-insights/chat/${s}/rate`,{message_id:e,rating:t}).then(r=>r.data),saveChatMessageFeedback:({chatId:s,messageId:e,feedback:t,skipped_feedback:r=!1})=>z.post(`ai-insights/chat/${s}/rate`,{message_id:e,feedback:t,skipped_feedback:r}).then(o=>o.data),retryLastChatMessage:s=>z.post(`ai-insights/chat/${s}/retry`).then(e=>e.data)},zl=async({commit:s},e={})=>(s("SET_LOADING",!0),Yl(e).then(t=>{if(!t.success){w.prototype.$mi_error_toast({title:!1,html:t.error});return}s("UPDATE_INSIGHTS",t.data.insights),s("RESET_RATINGS");const{ratings:r}=t.data;for(let o=0;o<r.length;o++)s("RATE_INSIGHT",{insightId:r[o].insight_id,score:r[o].score,feedback:r[o].feedback,skipped:r[o].skipped})}).finally(()=>{s("SET_LOADING",!1)})),Kl=async({commit:s},{insightId:e,score:t})=>{s("RATE_INSIGHT",{insightId:e,score:t}),await jl(e,t)},ql=async({commit:s})=>Zl().then(e=>{if(!e.success){w.prototype.$mi_error_toast({title:!1,html:e.error});return}s("RESET_RATINGS");const t=Object.values(e.data);for(let r=0;r<t.length;r++)s("RATE_INSIGHT",{insightId:t[r].insight_id,score:t[r].score,feedback:t[r].feedback,skipped:t[r].skipped})}),Gl=async(s,e)=>Wl(e).then(t=>t.data),Jl={fetchInsights:zl,rateInsight:Kl,refreshRatings:ql,saveInsightFeedback:Gl},Ql={isLoading:!0,insights:[],ratings:[]},Xl={namespaced:!0,state:Ql,mutations:Hl,actions:Jl},ec={name:"AIInsights",computed:{...C({addons:"$_addons/addons"}),showUpsell(){if(!this.$isPro())return!0;let s=this.$store.getters["$_license/license"];return!!(s&&s.type==="plus")}},beforeCreate(){this.$store.hasModule("$_ai-insights")&&this.$store.unregisterModule("$_ai-insights"),this.$store.registerModule("$_ai-insights",Xl)}};var tc=function(){var e=this,t=e._self._c;return t("main",{ref:"container",staticClass:"monsterinsights-report monsterinsights-ai-insights"},[t("router-view")],1)},sc=[],rc=l(ec,tc,sc,!1,null,null,null,null);const oc=rc.exports,ic={};var ac=function(e,t){return e("i",{staticClass:"icon"},[e("svg",{attrs:{width:"19",height:"19",viewBox:"0 0 19 19",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M13.2226 1.65012C13.4579 0.575261 14.9892 0.568832 15.2348 1.6424L15.2451 1.69255L15.2682 1.79283C15.5511 2.99369 16.5231 3.9104 17.7394 4.12255C18.8605 4.31798 18.8605 5.92769 17.7394 6.12312C17.1413 6.22722 16.5873 6.50571 16.1469 6.92364C15.7066 7.34156 15.3995 7.88027 15.2644 8.47212L15.2335 8.60326C14.9892 9.67683 13.4592 9.6704 13.2226 8.59555L13.1982 8.4824C13.0684 7.88823 12.7645 7.3462 12.3253 6.92553C11.8861 6.50485 11.3314 6.2246 10.7322 6.12055C9.61364 5.9264 9.61364 4.31926 10.7322 4.12512C11.3293 4.0215 11.8821 3.74294 12.3207 3.32475C12.7592 2.90656 13.0638 2.36758 13.1956 1.77612L13.2136 1.69126L13.2226 1.65012ZM14.1574 10.8018C13.5447 10.7899 12.9592 10.547 12.5181 10.1217C12.4211 10.1856 12.3366 10.2667 12.2686 10.3608C11.6901 11.1271 11.0125 11.9101 10.2462 12.6751C9.66507 13.2563 9.07621 13.7847 8.49507 14.2565C7.91393 13.7847 7.32507 13.2563 6.74393 12.6751C6.18707 12.1194 5.65923 11.5353 5.1625 10.9253C5.63435 10.3428 6.16407 9.75526 6.74393 9.17412C7.46794 8.44757 8.2412 7.77182 9.05821 7.15169C9.14353 7.09057 9.21824 7.01586 9.27935 6.93055C9.03494 6.7077 8.83914 6.43681 8.70419 6.13484C8.56924 5.83286 8.49805 5.50629 8.49507 5.17555C7.23378 4.29226 5.98664 3.63783 4.87321 3.29712C3.66721 2.92812 2.25293 2.82912 1.32593 3.75483C0.725497 4.35655 0.562212 5.17426 0.608497 5.95212C0.654783 6.73126 0.915783 7.58755 1.3105 8.45669C1.71315 9.32151 2.19386 10.1478 2.74664 10.9253C2.19399 11.7019 1.71329 12.5273 1.3105 13.3913C0.915783 14.2604 0.654783 15.1167 0.608497 15.8958C0.562212 16.6737 0.724212 17.4914 1.32593 18.0931C1.92764 18.6935 2.74535 18.8568 3.52321 18.8105C4.30107 18.763 5.15864 18.5033 6.02778 18.1085C6.81207 17.7524 7.6465 17.2664 8.49635 16.6724C9.34493 17.2664 10.1781 17.7524 10.9636 18.1085C11.8315 18.5033 12.6891 18.7643 13.4682 18.8105C14.2461 18.8568 15.0625 18.6935 15.6642 18.0918C16.5912 17.1661 16.4922 15.7518 16.1232 14.5458C15.7709 13.3964 15.0856 12.1043 14.1574 10.8018ZM4.30878 5.14083C5.04935 5.36712 5.93264 5.79655 6.88921 6.41883C5.84483 7.30492 4.87473 8.27502 3.98864 9.3194C3.64043 8.78918 3.33188 8.23396 3.0655 7.65826C2.72735 6.91255 2.5615 6.2954 2.53321 5.8364C2.50621 5.37483 2.61935 5.18969 2.69007 5.11898C2.80578 5.00326 3.23264 4.81297 4.30878 5.14083ZM3.0655 14.191C3.30207 13.6703 3.61193 13.111 3.98864 12.5298C4.87516 13.5742 5.84569 14.5443 6.8905 15.4304C6.36072 15.779 5.80592 16.088 5.23064 16.3548C4.48493 16.693 3.86778 16.8588 3.40878 16.8871C2.94593 16.9141 2.76207 16.801 2.69135 16.7303C2.62064 16.6595 2.5075 16.4731 2.5345 16.0128C2.56278 15.5538 2.72735 14.9367 3.06678 14.191H3.0655ZM11.7608 16.3548C11.1857 16.0883 10.6313 15.7793 10.1022 15.4304C11.1457 14.5442 12.1149 13.5741 13.0002 12.5298C13.6212 13.4877 14.0506 14.371 14.2769 15.1115C14.6061 16.1864 14.4158 16.6145 14.3001 16.7303C14.2281 16.801 14.0429 16.9141 13.5826 16.8858C13.1224 16.8601 12.5065 16.693 11.7608 16.3548ZM7.20935 10.9253C7.20935 10.5843 7.34481 10.2572 7.58593 10.0161C7.82705 9.77501 8.15408 9.63955 8.49507 9.63955C8.83606 9.63955 9.16309 9.77501 9.40421 10.0161C9.64532 10.2572 9.78078 10.5843 9.78078 10.9253C9.78078 11.2663 9.64532 11.5933 9.40421 11.8344C9.16309 12.0755 8.83606 12.211 8.49507 12.211C8.15408 12.211 7.82705 12.0755 7.58593 11.8344C7.34481 11.5933 7.20935 11.2663 7.20935 10.9253Z",fill:"currentColor"}})])])},nc=[],lc=l(ic,ac,nc,!0,null,null,null,null);const cc=lc.exports,pc={};var dc=function(){var e=this,t=e._self._c;return t("i",{staticClass:"icon"},[t("svg",{attrs:{width:"19",height:"19",viewBox:"0 0 19 19",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M12.0342 7.27191L11.2942 7.14891C11.2763 7.25635 11.2821 7.36639 11.311 7.47138C11.34 7.57637 11.3915 7.67379 11.4619 7.75687C11.5323 7.83995 11.62 7.90669 11.7188 7.95247C11.8177 7.99824 11.9253 8.02193 12.0342 8.02191V7.27191ZM1.03418 7.27191V6.52191C0.835267 6.52191 0.644502 6.60093 0.50385 6.74158C0.363197 6.88223 0.28418 7.073 0.28418 7.27191H1.03418ZM3.03418 18.0219H14.3942V16.5219H3.03418V18.0219ZM15.5942 6.52191H12.0342V8.02191H15.5942V6.52191ZM12.7742 7.39491L13.5802 2.55991L12.1002 2.31291L11.2942 7.14891L12.7742 7.39491ZM11.8542 0.521912H11.6402V2.02191H11.8532L11.8542 0.521912ZM8.51918 2.19191L6.00418 5.96491L7.25218 6.79691L9.76718 3.02391L8.51918 2.19191ZM4.96418 6.52191H1.03418V8.02191H4.96418V6.52191ZM0.28418 7.27191V15.2719H1.78418V7.27191H0.28418ZM17.0912 15.8119L18.2912 9.81191L16.8212 9.51691L15.6212 15.5169L17.0912 15.8119ZM6.00418 5.96491C5.89008 6.1362 5.73545 6.27566 5.55403 6.37283C5.3726 6.46999 5.16999 6.52086 4.96418 6.52091V8.02091C5.88418 8.02091 6.74218 7.56191 7.25218 6.79691L6.00418 5.96491ZM13.5802 2.55991C13.6218 2.30928 13.6084 2.05158 13.5409 1.80665C13.4733 1.56173 13.3533 1.33445 13.189 1.1406C13.0248 0.94676 12.8203 0.791006 12.5898 0.684165C12.3593 0.577323 12.1083 0.521956 11.8542 0.521912L11.8532 2.02191C11.8894 2.022 11.9262 2.02997 11.9591 2.04527C11.9919 2.06057 12.0211 2.08283 12.0445 2.11051C12.0679 2.1382 12.085 2.17064 12.0946 2.20558C12.1042 2.24053 12.1061 2.27716 12.1002 2.31291L13.5802 2.55991ZM15.5942 8.02091C16.3842 8.02091 16.9742 8.74291 16.8202 9.51591L18.2912 9.81091C18.3708 9.412 18.3609 8.99941 18.2622 8.6048C18.1635 8.21018 17.9785 7.84237 17.7205 7.52787C17.4626 7.21337 17.138 6.96001 16.7703 6.78606C16.4027 6.61211 16.0009 6.52189 15.5942 6.52191V8.02091ZM14.3942 18.0219C15.0299 18.022 15.646 17.8009 16.1377 17.398C16.6294 16.9951 16.9663 16.4342 17.0912 15.8109L15.6212 15.5159C15.5645 15.7995 15.4112 16.0547 15.1875 16.2379C14.9637 16.4212 14.6834 16.5222 14.3942 16.5219V18.0219ZM11.6402 0.521912C11.0228 0.521957 10.4151 0.673407 9.8708 0.964732C9.32653 1.25606 8.86259 1.67725 8.52018 2.19091L9.76718 3.02391C9.97272 2.71557 10.2512 2.46177 10.578 2.28697C10.9047 2.11217 11.2696 2.02177 11.6402 2.02191V0.521912ZM3.03418 16.5219C2.34418 16.5219 1.78418 15.9619 1.78418 15.2719H0.28418C0.28418 16.0013 0.573911 16.7007 1.08964 17.2165C1.60536 17.7322 2.30483 18.0219 3.03418 18.0219V16.5219Z",fill:"currentColor"}}),t("path",{attrs:{d:"M5.03418 7.27191V17.2719",stroke:"currentColor","stroke-width":"1.5"}})])])},hc=[],gc=l(pc,dc,hc,!1,null,null,null,null);const Ht=gc.exports,uc={};var mc=function(){var e=this,t=e._self._c;return t("i",{staticClass:"icon"},[t("svg",{attrs:{width:"19",height:"19",viewBox:"0 0 19 19",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M7.03418 11.2719L7.77418 11.3949C7.79204 11.2875 7.78629 11.1774 7.75732 11.0724C7.72836 10.9675 7.67687 10.87 7.60645 10.787C7.53603 10.7039 7.44836 10.6371 7.34953 10.5914C7.2507 10.5456 7.14309 10.5219 7.03418 10.5219L7.03418 11.2719ZM18.0342 11.2719L18.0342 12.0219C18.2331 12.0219 18.4239 11.9429 18.5645 11.8022C18.7052 11.6616 18.7842 11.4708 18.7842 11.2719L18.0342 11.2719ZM16.0342 0.521911L4.67418 0.52191L4.67418 2.02191L16.0342 2.02191L16.0342 0.521911ZM3.47418 12.0219L7.03418 12.0219L7.03418 10.5219L3.47418 10.5219L3.47418 12.0219ZM6.29418 11.1489L5.48818 15.9839L6.96818 16.2309L7.77418 11.3949L6.29418 11.1489ZM7.21418 18.0219L7.42818 18.0219L7.42818 16.5219L7.21518 16.5219L7.21418 18.0219ZM10.5492 16.3519L13.0642 12.5789L11.8162 11.7469L9.30118 15.5199L10.5492 16.3519ZM14.1042 12.0219L18.0342 12.0219L18.0342 10.5219L14.1042 10.5219L14.1042 12.0219ZM18.7842 11.2719L18.7842 3.27191L17.2842 3.27191L17.2842 11.2719L18.7842 11.2719ZM1.97718 2.73191L0.777181 8.73191L2.24718 9.02691L3.44718 3.02691L1.97718 2.73191ZM13.0642 12.5789C13.1783 12.4076 13.3329 12.2682 13.5143 12.171C13.6958 12.0738 13.8984 12.023 14.1042 12.0229L14.1042 10.5229C13.1842 10.5229 12.3262 10.9819 11.8162 11.7469L13.0642 12.5789ZM5.48818 15.9839C5.44651 16.2345 5.45992 16.4922 5.52747 16.7372C5.59501 16.9821 5.71508 17.2094 5.87933 17.4032C6.04358 17.5971 6.24806 17.7528 6.47858 17.8597C6.70909 17.9665 6.96011 18.0219 7.21418 18.0219L7.21518 16.5219C7.17893 16.5218 7.14214 16.5139 7.10928 16.4986C7.07642 16.4833 7.04727 16.461 7.02387 16.4333C7.00047 16.4056 6.98337 16.3732 6.97375 16.3382C6.96414 16.3033 6.96223 16.2667 6.96818 16.2309L5.48818 15.9839ZM3.47418 10.5229C2.68418 10.5229 2.09418 9.80091 2.24818 9.02791L0.777181 8.73291C0.697602 9.13182 0.707507 9.54441 0.806184 9.93902C0.90486 10.3336 1.08985 10.7015 1.34782 11.016C1.60579 11.3305 1.93032 11.5838 2.29801 11.7578C2.66571 11.9317 3.06741 12.0219 3.47418 12.0219L3.47418 10.5229ZM4.67418 0.52191C4.03847 0.521786 3.42236 0.742909 2.93065 1.14583C2.43894 1.54875 2.10201 2.10958 1.97718 2.73291L3.44718 3.02791C3.50387 2.74431 3.65714 2.48914 3.88087 2.30588C4.10461 2.12263 4.38497 2.02162 4.67418 2.02191L4.67418 0.52191ZM7.42818 18.0219C8.04551 18.0219 8.65329 17.8704 9.19756 17.5791C9.74183 17.2878 10.2058 16.8666 10.5482 16.3529L9.30118 15.5199C9.09564 15.8283 8.81712 16.0821 8.49037 16.2569C8.16362 16.4317 7.79875 16.522 7.42818 16.5219L7.42818 18.0219ZM16.0342 2.02191C16.7242 2.02191 17.2842 2.58191 17.2842 3.27191L18.7842 3.27191C18.7842 2.54257 18.4944 1.84309 17.9787 1.32737C17.463 0.811643 16.7635 0.521911 16.0342 0.521911L16.0342 2.02191Z",fill:"currentColor"}}),t("path",{attrs:{d:"M14.0342 11.2719L14.0342 1.27191",stroke:"currentColor","stroke-width":"1.5"}})])])},_c=[],fc=l(uc,mc,_c,!1,null,null,null,null);const Vt=fc.exports,vc={};var wc=function(){var e=this,t=e._self._c;return t("i",{staticClass:"icon"},[t("svg",{attrs:{width:"29",height:"25",viewBox:"0 0 29 25",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M26.3519 9.01789H18.7877C18.596 9.01789 18.4503 8.8364 18.5014 8.65234C18.8082 7.56079 18.8363 6.37464 18.8363 5.25753C18.8363 4.63889 18.867 3.93846 18.7979 3.26103C18.7289 2.57338 18.5576 1.91128 18.1537 1.38724C17.7038 0.806949 17.0341 0.374939 16.2902 0.374939C14.5621 0.374939 14.2886 2.06723 14.1275 3.43998C13.9409 5.04024 13.8668 6.69419 13.1587 8.16919C12.6627 9.20195 11.9623 10.089 10.8605 10.5236C10.0144 10.8585 9.39064 11.1192 8.49848 11.293C8.36044 11.3211 8.26074 11.4387 8.26074 11.5793V22.7633C8.26074 22.9269 8.39366 23.0573 8.55471 23.0573H11.6504C11.745 23.0573 11.8319 23.1033 11.8882 23.18C12.4991 23.998 13.4731 24.5297 14.5749 24.5297H24.271C25.6873 24.5297 26.8427 23.3717 26.8427 21.958C26.8427 21.5925 26.7532 21.2448 26.5973 20.9381C26.536 20.8154 26.5666 20.6671 26.674 20.5827C27.1981 20.1635 27.538 19.21 27.538 18.4917V18.1977C27.538 17.8321 27.4486 17.4845 27.2926 17.1777C27.2313 17.055 27.262 16.9067 27.3693 16.8224C27.8934 16.4031 28.2334 15.7615 28.2334 15.0432V14.7492C28.2334 14.3836 28.1439 14.036 27.9879 13.7292C27.9266 13.6065 27.9573 13.4582 28.0646 13.3739C28.5887 12.9546 28.9287 12.313 28.9287 11.5947C28.9287 10.1785 27.7706 9.02301 26.357 9.02301L26.3519 9.01789Z",fill:"currentColor"}}),t("path",{attrs:{d:"M5.98246 10.0302H1.4143C1.02746 10.0302 0.713867 10.3439 0.713867 10.7307V23.5021C0.713867 23.889 1.02746 24.2025 1.4143 24.2025H5.98246C6.3693 24.2025 6.68289 23.889 6.68289 23.5021V10.7307C6.68289 10.3439 6.3693 10.0302 5.98246 10.0302Z",fill:"currentColor"}})])])},yc=[],Cc=l(vc,wc,yc,!1,null,null,null,null);const bc=Cc.exports,xc=Ge({__name:"IconClose",setup(s){return{__sfc:!0}}});var kc=function(){var e=this,t=e._self._c;return e._self._setupProxy,t("span",{staticClass:"icon"},[t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512"}},[t("path",{attrs:{d:"M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z",fill:"currentColor"}})])])},$c=[],Rc=l(xc,kc,$c,!1,null,null,null,null);const Pc=Rc.exports,{__:Le,sprintf:Dc}=wp.i18n,Sc={name:"FeedbackInputBox",components:{IconClose:Pc,IconThumbsUpFill:bc},props:{labels:{type:Object,default:()=>({})},hasFeedback:{type:Boolean,default:!1},isSaving:{type:Boolean,default:!1}},data(){return{message:""}},computed:{texts(){const s={title:Dc(Le("Tell us how this insight can be improved %s(Optional)%s","google-analytics-for-wordpress"),"<span>","</span>"),feedbackHelp:Le("Feedback submitted will include your analytics data & insights provided so we can improve. Thank you!","google-analytics-for-wordpress"),submit:Le("Submit","google-analytics-for-wordpress"),thanksTitle:Le("Thanks for your feedback!","google-analytics-for-wordpress")};return Object.assign(s,this.labels)}},methods:{onSubmit(){this.$emit("submit",this.message)},onClose(){this.$emit("close")}},mounted(){this.$nextTick(()=>{!this.hasFeedback&&this.$refs.textarea&&this.$refs.textarea.focus()})}};var Tc=function(){var e=this,t=e._self._c;return t("form",{staticClass:"monsterinsights-ai-feedback",class:{"is-submitted":e.hasFeedback,"is-saving":e.isSaving},on:{submit:function(r){return r.preventDefault(),e.onSubmit.apply(null,arguments)}}},[e.hasFeedback?e._e():t("div",{staticClass:"monsterinsights-ai-feedback__close"},[t("button",{attrs:{type:"button"},on:{click:e.onClose}},[t("icon-close")],1)]),e.hasFeedback?[t("icon-thumbs-up-fill"),t("h3",{domProps:{innerHTML:e._s(e.texts.thanksTitle)}})]:[t("h3",{domProps:{innerHTML:e._s(e.texts.title)}}),t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.message,expression:"message"}],ref:"textarea",attrs:{required:""},domProps:{value:e.message},on:{input:function(r){r.target.composing||(e.message=r.target.value)}}}),e.texts.feedbackHelp!==null?t("p",{domProps:{innerHTML:e._s(e.texts.feedbackHelp)}}):e._e(),t("button",{attrs:{type:"submit"},domProps:{innerHTML:e._s(e.texts.submit)}})]],2)},Mc=[],Lc=l(Sc,Tc,Mc,!1,null,null,null,null);const Yt=Lc.exports,Ec={name:"InsightsFeedback",components:{FeedbackInputBox:Yt},props:{insight:{type:Object,required:!0}},data(){return{isSaving:!1}},computed:{...pe("$_ai-insights",["ratings"]),foundRating(){return this.ratings.find(s=>s.insightId===this.insight.id)},hasFeedback(){return typeof this.foundRating<"u"?this.foundRating.hasOwnProperty("feedback")&&this.foundRating.feedback!==null:!1},hasSkippedFeedback(){return typeof this.foundRating<"u"?this.foundRating.hasOwnProperty("skipped")&&this.foundRating.skipped===!0:!1}},methods:{onSubmit(s){this.isSaving=!0,this.$store.dispatch("$_ai-insights/saveInsightFeedback",{insightId:this.insight.id,message:s}).then(async()=>{await this.$store.dispatch("$_ai-insights/refreshRatings"),this.isSaving=!1})},onClose(){this.isSaving=!0,this.$store.dispatch("$_ai-insights/saveInsightFeedback",{insightId:this.insight.id,skipped:!0}).then(async()=>{await this.$store.dispatch("$_ai-insights/refreshRatings"),this.isSaving=!1})}}};var Ac=function(){var e=this,t=e._self._c;return e.hasSkippedFeedback?e._e():t("feedback-input-box",{attrs:{"has-feedback":e.hasFeedback,"is-saving":e.isSaving},on:{submit:e.onSubmit,close:e.onClose}})},Fc=[],Nc=l(Ec,Ac,Fc,!1,null,null,null,null);const Ic=Nc.exports,{__:Bc}=wp.i18n,Oc={name:"InsightsItem",components:{InsightsFeedback:Ic,IconAI:cc,IconThumbsUp:Ht,IconThumbsDown:Vt},props:{mock:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},insight:{type:Object}},computed:{score:{get(){const s=this.$store.state["$_ai-insights"].ratings;if(s.length===0)return-1;const e=s.find(t=>t.insightId===this.insight.id);return typeof e>"u"?-1:e.score},set(s){this.$emit("rate",{id:this.insight.id,score:s})}},showFeedbackBox(){return this.score===0},mockInsight(){return{content:Bc("For the week of March 18 to March 24, 2024, your website received 6,424 visitors. Comparing this to the previous week, March 11 to March 17, 2024, when you had 6,658 visitors, there was decrease of 234 visotors (-3.51%).","google-analytics-for-wordpress")}},shownInsight(){return this.mock?this.mockInsight:this.insight}},methods:{rate(s){this.mock||(this.score=s)}}};var Uc=function(){var e=this,t=e._self._c;return t("article",{staticClass:"monsterinsights-ai-result"},[t("IconAI"),t("section",{staticClass:"monsterinsights-ai-result__content"},[e.loading?[e._m(0)]:e.insight||e.mock?[t("div",{staticClass:"monsterinsights-ai-result__message",domProps:{innerHTML:e._s(e.shownInsight.content)}}),t("div",{staticClass:"monsterinsights-ai-result__rate"},[t("button",{class:{"is-on":e.score===1},attrs:{type:"button",disabled:e.score===1||e.mock},on:{click:function(r){return r.preventDefault(),e.rate(1)}}},[t("IconThumbsUp")],1),t("button",{class:{"is-on":e.score===0},attrs:{type:"button",disabled:e.score===0||e.mock},on:{click:function(r){return r.preventDefault(),e.rate(0)}}},[t("IconThumbsDown")],1)]),e.showFeedbackBox?t("insights-feedback",{attrs:{insight:e.insight}}):e._e()]:e._e()],2)],1)},Hc=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"monsterinsights-text-skeleton"},[e("span"),e("span"),e("span")])}],Vc=l(Oc,Uc,Hc,!1,null,null,null,null);const Yc=Vc.exports,jc=new Ut,Zc=(s,e)=>jc.post("feedback",{...e,feature_key:s}).then(t=>t.data),Wc={};var zc=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"23",height:"23",viewBox:"0 0 23 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{"clip-path":"url(#clip0_26_3424)"}},[t("path",{attrs:{d:"M22.5614 1.08295C22.5246 1.16322 22.4738 1.23635 22.4114 1.29895L8.23044 15.4769L12.2354 22.1535C12.3064 22.2722 12.4088 22.369 12.5313 22.4332C12.6539 22.4973 12.7918 22.5264 12.9298 22.5171C13.0678 22.5077 13.2005 22.4604 13.3133 22.3804C13.4261 22.3003 13.5146 22.1906 13.5689 22.0634L22.5614 1.08295ZM7.17144 14.4179L0.494942 10.4099C0.376203 10.339 0.279397 10.2366 0.215228 10.1141C0.151059 9.99152 0.122027 9.85363 0.131341 9.71562C0.140656 9.57761 0.187954 9.44487 0.268006 9.33207C0.348058 9.21927 0.457746 9.1308 0.584942 9.07645L21.5699 0.0854492C21.4885 0.122481 21.4143 0.17379 21.3509 0.236949L7.17144 14.4179Z",fill:"currentColor"}})]),t("defs",[t("clipPath",{attrs:{id:"clip0_26_3424"}},[t("rect",{attrs:{width:"22.5",height:"22.5",fill:"currentColor",transform:"translate(0.130371 0.0180664)"}})])])])},Kc=[],qc=l(Wc,zc,Kc,!1,null,null,null,null);const xt=qc.exports,Gc={};var Jc=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"17",height:"18",viewBox:"0 0 17 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M15.7825 1.98804L1.2168 16.5537M1.2168 1.98804L15.7825 16.5537",stroke:"currentColor","stroke-width":"2.04109","stroke-linecap":"round","stroke-linejoin":"round"}})])},Qc=[],Xc=l(Gc,Jc,Qc,!1,null,null,null,null);const ep=Xc.exports,tp={inheritAttrs:!1,name:"FeatureFeedbackNPS",props:{value:{type:Number}},computed:{inputAttrs(){return{...this.$attrs,name:`feature-feedback-nps-${this.$attrs.name}`}},internalValue:{get(){return this.value},set(s){this.$emit("input",s)}},ratingIcons(){return{"rate-1":new URL(""+new URL("../img/rate-1.svg",import.meta.url).href,self.location).href,"rate-2":new URL(""+new URL("../img/rate-2.svg",import.meta.url).href,self.location).href,"rate-3":new URL(""+new URL("../img/rate-3.svg",import.meta.url).href,self.location).href,"rate-4":new URL(""+new URL("../img/rate-4.svg",import.meta.url).href,self.location).href,"rate-5":new URL(""+new URL("../img/rate-5.svg",import.meta.url).href,self.location).href}}}};var sp=function(){var e=this,t=e._self._c;return t("fieldset",{staticClass:"monsterinsights-feature-feedback-nps"},e._l(5,function(r){return t("label",{key:r,staticClass:"monsterinsights-nps-radio",attrs:{for:e.inputAttrs.id}},[t("input",e._b({directives:[{name:"model",rawName:"v-model",value:e.internalValue,expression:"internalValue"}],attrs:{type:"radio"},domProps:{value:r,checked:e._q(e.internalValue,r)},on:{change:function(o){e.internalValue=r}}},"input",e.inputAttrs,!1)),t("img",{attrs:{src:e.ratingIcons["rate-"+r],alt:""}})])}),0)},rp=[],op=l(tp,sp,rp,!1,null,null,null,null);const ip=op.exports,{__:be}=wp.i18n,ap={name:"FeatureFeedback",components:{IconMessage:xt,FeatureFeedbackNPS:ip},props:{featureKey:{type:String,required:!0},labels:{type:Object,default:()=>{}}},data(){return{hasSubmitted:!1,isSubmitting:!1,isVisible:!1,renderWidget:!1,rating:0,message:""}},computed:{finalLabels(){return{...{widgetTitle:be("Provide Feedback","google-analytics-for-wordpress"),textAreaLabel:be("What do you think about this feature?","google-analytics-for-wordpress"),textAreaPlaceholder:be("Tell us in your words...","google-analytics-for-wordpress"),submitButtonText:be("Submit","google-analytics-for-wordpress"),thankYouTitle:be("Thanks! We appreciate your feedback.","google-analytics-for-wordpress")},...this.labels}},toggleButtonComponent(){return this.isVisible?ep:xt},thanksImage(){return new URL(""+new URL("../img/charlie-with-heart.svg",import.meta.url).href,self.location).href}},methods:{close(){this.$destroy(),this.$el.parentNode.removeChild(this.$el)},toggleWidget(){this.isVisible?(setTimeout(()=>{this.renderWidget=!1,this.hasSubmitted&&this.close()},250),this.isVisible=!1):(this.renderWidget=!0,setTimeout(()=>{this.isVisible=!0},10))},handleSubmit(){this.isSubmitting=!0,Zc(this.featureKey,{rating:this.rating,message:this.message}).then(()=>{this.hasSubmitted=!0}).finally(()=>{this.isSubmitting=!1})}}};var np=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-feature-feedback",class:[`monsterinsights-feature-feedback-${e.featureKey}`,{"is-loading":e.isSubmitting}]},[e.renderWidget?t("div",{staticClass:"monsterinsights-feature-feedback-widget",class:{"is-open":e.isVisible}},[e.hasSubmitted?t("div",{staticClass:"monsterinsights-feature-feedback-thanks"},[t("img",{attrs:{src:e.thanksImage,alt:""}}),t("h4",{domProps:{innerHTML:e._s(e.finalLabels.thankYouTitle)}})]):[t("h4",{domProps:{innerHTML:e._s(e.finalLabels.widgetTitle)}}),t("form",{staticClass:"monsterinsights-feature-feedback-form",on:{submit:function(r){return r.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("FeatureFeedbackNPS",{attrs:{name:e.featureKey,required:""},model:{value:e.rating,callback:function(r){e.rating=r},expression:"rating"}}),t("label",{attrs:{for:`feature-feedback-message-${e.featureKey}`},domProps:{innerHTML:e._s(e.finalLabels.textAreaLabel)}}),t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.message,expression:"message"}],attrs:{id:`feature-feedback-message-${e.featureKey}`,name:`feature-feedback-message-${e.featureKey}`,placeholder:e.finalLabels.textAreaPlaceholder},domProps:{value:e.message},on:{input:function(r){r.target.composing||(e.message=r.target.value)}}}),t("button",{staticClass:"monsterinsights-button",attrs:{type:"submit",disabled:!e.rating},domProps:{innerHTML:e._s(e.finalLabels.submitButtonText)}})],1)]],2):e._e(),t("button",{staticClass:"monsterinsights-feature-feedback-toggle",attrs:{type:"button"},on:{click:e.toggleWidget}},[t("transition",{attrs:{name:"fade"}},[t(e.toggleButtonComponent,{tag:"component"})],1)],1)])},lp=[],cp=l(ap,np,lp,!1,null,null,null,null);const pp=cp.exports,dp={create(s,e={}){const{feedback:t}=window.monsterinsights;if(typeof t<"u"&&Object.keys(t.tracked_features).includes(s))return;const r={parentElement:document.body,props:{featureKey:s}},o=es(r,e),n=document.createElement("div"),{parentElement:a,props:d}=o;a.append(n),new w({render:p=>p(pp,{props:d})}).$mount(n)}},{__:xe,sprintf:hp}=wp.i18n,gp={name:"AiInsightsViewer",components:{ReportUpsellOverlay:E,InsightsItem:Yc},props:{upsell:{type:Boolean,default:!1}},data(){return{}},computed:{...pe("$_ai-insights",["isLoading","insights","ratings"]),mascotImage(){return new URL(""+new URL("../img/charlie-front.svg",import.meta.url).href,self.location).href},texts(){return{title:xe("AI Insights","google-analytics-for-wordpress"),generateInsights:xe("Generate Insights","google-analytics-for-wordpress"),feedbackTitle:hp(xe("Tell us how this insight can be improved %s(Optional)%s","google-analytics-for-wordpress"),"<span>","</span>"),upsellSubheading:xe("Our AI Insights Report includes:","google-analytics-for-wordpress")}}},methods:{fetchInsights(s=!1){this.$isPro()&&this.$store.dispatch("$_ai-insights/fetchInsights",{force_refresh:s})},handleItemRating(s){const{id:e,score:t}=s;this.$store.dispatch("$_ai-insights/rateInsight",{insightId:e,score:t})}},mounted(){this.$isPro()&&(dp.create("ai-insights",{parentElement:this.$refs.container,props:{labels:{textAreaLabel:xe("What do you think about Our AI Insights?","google-analytics-for-wordpress")}}}),this.fetchInsights())}};var up=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.texts.title)}})]),t("article",{ref:"container",staticClass:"monsterinsights-ai-insights-card",class:{"monsterinsights-blur":e.upsell}},[e.upsell?t("ReportUpsellOverlay",{attrs:{"custom-subheading":e.texts.upsellSubheading,report:"ai_insights","force-two-columns":"","show-sample-button":!1}}):e._e(),t("div",{staticClass:"monsterinsights-container"},[t("div",{staticClass:"monsterinsights-ai-insights-card-wrapper"},[t("header",{staticClass:"monsterinsights-ai-insights-card__header"},[t("figure",{staticClass:"framed-mascot"},[t("img",{attrs:{src:e.mascotImage,alt:""}})]),t("h3",{domProps:{textContent:e._s(e.texts.title)}})]),t("section",{staticClass:"monsterinsights-ai-insights-card__content"},[e.upsell?[t("insights-item",{attrs:{mock:""}}),t("insights-item",{attrs:{mock:""}}),t("insights-item",{attrs:{mock:""}})]:e.isLoading?t("insights-item",{attrs:{loading:""}}):e._l(e.insights,function(r){return t("insights-item",{key:r.id,attrs:{insight:r},on:{rate:e.handleItemRating}})})],2),t("footer",{staticClass:"monsterinsights-ai-insights-card__footer"},[t("button",{staticClass:"monsterinsights-button",domProps:{textContent:e._s(e.texts.generateInsights)},on:{click:function(r){return e.fetchInsights(!0)}}})])])])],1)])},mp=[],_p=l(gp,up,mp,!1,null,null,null,null);const fp=_p.exports,{__:vp}=wp.i18n,yp=(s,e)=>{s.chatId=e},Cp=(s,e)=>{s.currentChat=e,e.length>0&&s.currentChat[s.currentChat.length-1].role==="user"?s.aiError=vp("Looks like your last message was not sent. Please try again.","monsterinsights-ai-insights"):s.aiError=null},bp=(s,e=!0)=>{s.isLoadingChat=e},xp=(s,e=!0)=>{s.isWaitingForAI=e},kp=(s,e=!0)=>{s.isLoadingChatLists=e},$p=(s,{pinnedChats:e,recentChats:t})=>{s.pinnedChats=e,s.recentChats=t},Rp=(s,e)=>{let{currentChat:t}=s;t=[...t,...e],s.currentChat=t},Pp=(s,e)=>{if(e===null){s.aiError=null,s.retryable=!0;return}typeof e=="string"?(s.aiError=e,s.retryable=!0):(s.aiError=e.error?e.error:"",s.retryable=e.hasOwnProperty("retryable")?e.retryable:!0)},Dp=(s,{messageId:e,rating:t})=>{const r=s.currentChat.findIndex(o=>o.id===e);r!==-1&&(s.currentChat[r].rating=t),s.currentChat=[...s.currentChat]},Sp={SET_CHAT_ID:yp,SET_IS_LOADING_CHAT:bp,UPDATE_CURRENT_CHAT:Cp,SET_IS_WAITING_FOR_AI:xp,SET_IS_LOADING_CHAT_LISTS:kp,UPDATE_CHAT_LISTS:$p,APPEND_MESSAGES:Rp,SET_AI_ERROR:Pp,UPDATE_MESSAGE_RATING:Dp},Tp=({commit:s},e=!1)=>{e||s("SET_IS_LOADING_CHAT_LISTS"),le.getChats().then(t=>{s("UPDATE_CHAT_LISTS",{pinnedChats:t.pinned,recentChats:t.recent})}).finally(()=>{s("SET_IS_LOADING_CHAT_LISTS",!1)})},Mp=({state:s,commit:e,dispatch:t},{chatId:r,force:o=!1})=>{if(r===!1){e("SET_CHAT_ID",null),e("UPDATE_CURRENT_CHAT",[]);return}if(!(!o&&s.chatId===r))return e("SET_IS_LOADING_CHAT"),le.getChatById(r).then(n=>{e("SET_CHAT_ID",n.id),e("UPDATE_CURRENT_CHAT",n.chat)}).catch(()=>{throw e("SET_CHAT_ID",null),e("UPDATE_CURRENT_CHAT",[]),t("getChats"),new Error("Chat not found")}).finally(()=>{e("SET_IS_LOADING_CHAT",!1)})},Lp=(s,{chatId:e,properties:t})=>le.updateChatProperties(e,t),Ep=({state:s,commit:e,dispatch:t},r)=>{e("SET_IS_LOADING_CHAT_LISTS"),le.deleteChat(r).then(()=>{r===s.chatId&&(e("SET_CHAT_ID",null),e("UPDATE_CURRENT_CHAT",[])),t("getChats")})},Ap=({commit:s,state:e})=>{s("SET_IS_WAITING_FOR_AI"),le.retryLastChatMessage(e.chatId).then(t=>{s("SET_AI_ERROR",null),s("APPEND_MESSAGES",[t.latest_message])}).catch(t=>{const{data:r}=t.response.data;s("SET_AI_ERROR",r)}).finally(()=>{s("SET_IS_WAITING_FOR_AI",!1)})},Fp=({dispatch:s,commit:e,state:t,getters:r},o)=>{const n=!r.hasChatStarted;return e("APPEND_MESSAGES",[{role:"user",content:o}]),e("SET_IS_WAITING_FOR_AI"),(n?le.sendMessage(o):le.sendMessage(o,t.chatId)).then(p=>{if(!p.success)return e("SET_AI_ERROR",{error:p.error,retryable:!1}),w.prototype.$mi_error_toast({title:!1,html:p.error}),{isNewChat:n};const{data:m}=p;e("SET_AI_ERROR",null),n&&(e("SET_CHAT_ID",m.chat_id),s("getChats",!0));const b=m.latest_message;return e("APPEND_MESSAGES",[b]),{isNewChat:n,chatId:m.chat_id}}).catch(p=>{const{data:m}=p.response.data;e("SET_AI_ERROR",m),m.chat_id&&e("SET_CHAT_ID",m.chat_id)}).finally(()=>{e("SET_IS_WAITING_FOR_AI",!1)})},Np=({commit:s},{chatId:e,messageId:t,rating:r})=>(s("UPDATE_MESSAGE_RATING",{messageId:t,rating:r}),le.rateChatMessage(e,t,r)),Ip=(s,e)=>le.saveChatMessageFeedback(e),Bp={getChats:Tp,loadChat:Mp,deleteChat:Ep,updateChatProperties:Lp,addUserMessage:Fp,retryLastChatMessage:Ap,rateChatMessage:Np,saveChatMessageFeedback:Ip},Op=s=>s.currentChat.length>0,Up=s=>!s.isWaitingForAI,Hp=s=>s.aiError!==null,Vp=(s,e)=>e.hasAIError&&s.chatId!==null&&!s.isWaitingForAI,Yp={hasChatStarted:Op,userCanSendMessage:Up,hasAIError:Hp,canRetryLastMessage:Vp},jp={chatId:null,isWaitingForAI:!1,currentChat:[],pinnedChats:[],recentChats:[],isLoadingChatLists:!1,isLoadingChat:!1,aiError:null,retryable:!0},Zp={namespaced:!0,state:jp,getters:Yp,mutations:Sp,actions:Bp},Wp={};var zp=function(){var e=this,t=e._self._c;return t("i",{staticClass:"icon"},[t("svg",{attrs:{width:"12",height:"7",viewBox:"0 0 12 7",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M6 6.0592L0.803849 0.0592046L11.1962 0.0592055L6 6.0592Z",fill:"currentColor"}})])])},Kp=[],qp=l(Wp,zp,Kp,!1,null,null,null,null);const Gp=qp.exports,Jp={name:"SidebarBlock",components:{IconArrowDown:Gp},props:{toggleLabel:{required:!0,type:String},isLoading:{type:Boolean,default:!1}},data(){return{isOpen:!1}},methods:{open(){this.isOpen=!0},close(){this.isOpen=!1},toggle(){this.isLoading||(this.isOpen=!this.isOpen)}}};var Qp=function(){var e=this,t=e._self._c;return t("section",{staticClass:"monsterinsights-sidebar-block",class:{"is-open":e.isOpen,"is-loading":e.isLoading}},[t("button",{staticClass:"monsterinsights-sidebar-block__toggle",on:{click:e.toggle}},[t("span",{domProps:{innerHTML:e._s(e.toggleLabel)}}),t("icon-arrow-down")],1),t("transition",{attrs:{name:"slide-down"}},[e.isOpen?t("div",{staticClass:"monsterinsights-sidebar-block__content"},[e._t("default")],2):e._e()])],1)},Xp=[],ed=l(Jp,Qp,Xp,!1,null,null,null,null);const td=ed.exports,sd={};var rd=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M2.5 11.3981L3.4525 10.6362L3.45875 10.6312C3.6575 10.4718 3.75812 10.3918 3.87 10.3349C3.97 10.2837 4.07688 10.2462 4.1875 10.2237C4.31188 10.1981 4.44125 10.1981 4.70125 10.1981H10.1269C10.8256 10.1981 11.1756 10.1981 11.4425 10.0618C11.6777 9.94197 11.8689 9.75075 11.9887 9.51556C12.125 9.24806 12.125 8.89806 12.125 8.20056V3.44618C12.125 2.74743 12.125 2.39743 11.9887 2.13056C11.8687 1.8953 11.6773 1.70408 11.4419 1.58431C11.175 1.44806 10.825 1.44806 10.125 1.44806H2.875C2.175 1.44806 1.825 1.44806 1.5575 1.58431C1.32228 1.70411 1.13105 1.89534 1.01125 2.13056C0.875 2.39806 0.875 2.74806 0.875 3.44806V10.6174C0.875 11.2837 0.875 11.6168 1.01125 11.7874C1.06984 11.861 1.14427 11.9204 1.229 11.9612C1.31374 12.002 1.40658 12.0231 1.50062 12.0231C1.71937 12.0231 1.97937 11.8143 2.5 11.3981Z",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}})])},od=[],id=l(sd,rd,od,!1,null,null,null,null);const ad=id.exports,nd={};var ld=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"4",height:"15",viewBox:"0 0 4 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M2 2.3512C2.41421 2.3512 2.75 2.01541 2.75 1.6012C2.75 1.18698 2.41421 0.851196 2 0.851196C1.58579 0.851196 1.25 1.18698 1.25 1.6012C1.25 2.01541 1.58579 2.3512 2 2.3512Z",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}}),t("path",{attrs:{d:"M2 7.8512C2.41421 7.8512 2.75 7.51541 2.75 7.1012C2.75 6.68699 2.41421 6.3512 2 6.3512C1.58579 6.3512 1.25 6.68699 1.25 7.1012C1.25 7.51541 1.58579 7.8512 2 7.8512Z",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}}),t("path",{attrs:{d:"M2 13.3512C2.41421 13.3512 2.75 13.0154 2.75 12.6012C2.75 12.187 2.41421 11.8512 2 11.8512C1.58579 11.8512 1.25 12.187 1.25 12.6012C1.25 13.0154 1.58579 13.3512 2 13.3512Z",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}})])},cd=[],pd=l(nd,ld,cd,!1,null,null,null,null);const dd=pd.exports,hd={};var gd=function(){var e=this,t=e._self._c;return t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{"clip-path":"url(#clip0_93_850)"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10.7812 3.54248L12.7637 5.52748C13.3937 6.15748 13.9037 6.66811 14.2324 7.11186C14.5705 7.56811 14.7893 8.04936 14.6705 8.59748C14.5518 9.14686 14.1543 9.49373 13.658 9.76936C13.1749 10.0375 12.4993 10.2912 11.6662 10.6044L10.4305 11.0687C9.93366 11.255 9.79866 11.3131 9.69803 11.4C9.64803 11.4437 9.60241 11.4937 9.56303 11.5481C9.48553 11.6562 9.43991 11.7962 9.29928 12.3081L9.29116 12.3362C9.14866 12.8556 9.03053 13.2844 8.90116 13.6062C8.76991 13.9331 8.59303 14.2425 8.27491 14.4244C8.05408 14.5504 7.80416 14.6165 7.54991 14.6162C7.18303 14.6162 6.87616 14.4337 6.60053 14.215C6.32928 13.9987 6.01553 13.6837 5.63491 13.3025L4.64241 12.31L2.06616 14.8894C1.9783 14.9774 1.85908 15.0269 1.73472 15.027C1.61036 15.0271 1.49105 14.9778 1.40303 14.89C1.31501 14.8021 1.2655 14.6829 1.26538 14.5586C1.26526 14.4342 1.31455 14.3149 1.40241 14.2269L3.97991 11.6469L3.02053 10.6862C2.64241 10.3081 2.33053 9.99561 2.11553 9.72561C1.89741 9.45186 1.71678 9.14686 1.71428 8.78311C1.71286 8.52387 1.78038 8.26892 1.90991 8.04436C2.09116 7.72873 2.39866 7.55311 2.72366 7.42248C3.04366 7.29436 3.46928 7.17686 3.98491 7.03498L4.01303 7.02686C4.52553 6.88561 4.66616 6.83936 4.77366 6.76186C4.82991 6.72186 4.87991 6.67498 4.92491 6.62311C5.01116 6.52123 5.06866 6.38561 5.25178 5.88561L5.69928 4.66311C6.00866 3.81998 6.25866 3.13686 6.52428 2.64936C6.79741 2.14748 7.14303 1.74436 7.69428 1.62311C8.24553 1.50123 8.72866 1.72186 9.18741 2.06186C9.63241 2.39311 10.1468 2.90748 10.7812 3.54248ZM8.62803 2.81436C8.25178 2.53436 8.05241 2.50311 7.89616 2.53748C7.73991 2.57186 7.57178 2.68436 7.34741 3.09686C7.11928 3.51561 6.89178 4.13311 6.56616 5.02248L6.13178 6.20811L6.10678 6.27561C5.96116 6.67561 5.84803 6.98561 5.63803 7.23124C5.54469 7.34084 5.43851 7.43884 5.32178 7.52311C5.05928 7.71186 4.74241 7.79873 4.33116 7.91186L4.26241 7.93061C3.71116 8.08248 3.33741 8.18686 3.07303 8.29311C2.80741 8.39936 2.74366 8.47436 2.72178 8.51186C2.67539 8.59244 2.65124 8.68389 2.65178 8.77686C2.65178 8.82061 2.67053 8.91748 2.84928 9.14186C3.02678 9.36498 3.30053 9.63998 3.70428 10.0444L6.27741 12.6194C6.68366 13.0269 6.96053 13.3025 7.18491 13.4819C7.40991 13.6606 7.50741 13.6787 7.55053 13.6787C7.64172 13.679 7.73137 13.6552 7.81053 13.61C7.84803 13.5887 7.92366 13.525 8.03116 13.2575C8.13803 12.9906 8.24241 12.6137 8.39491 12.0594L8.41366 11.9906C8.52616 11.58 8.61366 11.2631 8.80116 11.0012C8.88312 10.8872 8.97814 10.7832 9.08428 10.6912C9.32803 10.4806 9.63553 10.365 10.0337 10.2162L10.1005 10.1912L11.2987 9.74123C12.1787 9.40998 12.788 9.17999 13.2024 8.94999C13.6093 8.72436 13.7205 8.55623 13.7543 8.39998C13.788 8.24311 13.7562 8.04373 13.4793 7.66998C13.1968 7.28873 12.7374 6.82686 12.0724 6.16186L10.1462 4.23311C9.47678 3.56311 9.01116 3.09936 8.62803 2.81436Z",fill:"currentColor"}})]),t("defs",[t("clipPath",{attrs:{id:"clip0_93_850"}},[t("rect",{attrs:{width:"15",height:"15",fill:"white",transform:"translate(0.484253 0.808105)"}})])])])])},ud=[],md=l(hd,gd,ud,!1,null,null,null,null);const _d=md.exports,fd=Ge({__name:"IconPencil",setup(s){return{__sfc:!0}}});var vd=function(){var e=this,t=e._self._c;return e._self._setupProxy,t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M2.46179 14.3307H13.5118M3.61237 9.1655C3.31777 9.44793 3.15234 9.83075 3.15242 10.2299V12.3474H5.3783C5.79544 12.3474 6.19531 12.1887 6.49021 11.9058L13.0511 5.62201C13.3459 5.33963 13.5114 4.9568 13.5114 4.55764C13.5114 4.15849 13.3459 3.77566 13.0511 3.49327L12.4033 2.87184C12.2572 2.73192 12.0838 2.62094 11.8928 2.54524C11.7019 2.46954 11.4973 2.4306 11.2906 2.43066C11.0839 2.43073 10.8793 2.46978 10.6884 2.54559C10.4976 2.62141 10.3242 2.73249 10.1781 2.8725L3.61237 9.1655Z",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}})])])},wd=[],yd=l(fd,vd,wd,!1,null,null,null,null);const Cd=yd.exports,bd=Ge({__name:"IconUnpin",setup(s){return{__sfc:!0}}});var xd=function(){var e=this,t=e._self._c;return e._self._setupProxy,t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{"clip-path":"url(#clip0_93_615)"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.2255 2.84497L13.208 4.82997C13.838 5.45997 14.348 5.9706 14.6767 6.41435C15.0149 6.8706 15.2336 7.35185 15.1149 7.89997C14.9961 8.44935 14.5986 8.79623 14.1024 9.07185C13.6192 9.33998 12.9436 9.59373 12.1105 9.90685L10.8749 10.3712C10.378 10.5575 10.243 10.6156 10.1424 10.7025C10.0924 10.7462 10.0467 10.7962 10.0074 10.8506C9.92987 10.9587 9.88424 11.0987 9.74362 11.6106L9.73549 11.6387C9.59299 12.1581 9.47487 12.5868 9.34549 12.9087C9.21424 13.2356 9.03737 13.545 8.71924 13.7268C8.49842 13.8529 8.2485 13.919 7.99424 13.9187C7.62737 13.9187 7.32049 13.7362 7.04487 13.5175C6.77362 13.3012 6.45987 12.9862 6.07924 12.605L5.08674 11.6125L2.51049 14.1918C2.42264 14.2799 2.30342 14.3294 2.17906 14.3295C2.0547 14.3296 1.93539 14.2803 1.84737 14.1925C1.75935 14.1046 1.70983 13.9854 1.70972 13.861C1.7096 13.7367 1.75889 13.6174 1.84674 13.5293L4.42424 10.9493L3.46487 9.98872C3.08674 9.6106 2.77487 9.2981 2.55987 9.0281C2.34174 8.75435 2.16112 8.44935 2.15862 8.0856C2.1572 7.82636 2.22471 7.57141 2.35424 7.34685C2.53549 7.03122 2.84299 6.8556 3.16799 6.72498C3.48799 6.59685 3.91362 6.47935 4.42924 6.33747L4.45737 6.32935C4.96987 6.1881 5.11049 6.14185 5.21799 6.06435C5.27424 6.02435 5.32424 5.97747 5.36924 5.9256C5.45549 5.82372 5.51299 5.6881 5.69612 5.1881L6.14362 3.9656C6.45299 3.12247 6.70299 2.43935 6.96862 1.95185C7.24174 1.44997 7.58737 1.04685 8.13862 0.9256C8.68987 0.803725 9.17299 1.02435 9.63174 1.36435C10.0767 1.6956 10.5911 2.20997 11.2255 2.84497ZM9.07237 2.11685C8.69612 1.83685 8.49674 1.8056 8.34049 1.83997C8.18424 1.87435 8.01612 1.98685 7.79174 2.39935C7.56362 2.8181 7.33612 3.4356 7.01049 4.32497L6.57612 5.5106L6.55112 5.5781C6.40549 5.9781 6.29237 6.2881 6.08237 6.53373C5.98902 6.64333 5.88285 6.74133 5.76612 6.8256C5.50362 7.01435 5.18674 7.10122 4.77549 7.21435L4.70674 7.2331C4.15549 7.38497 3.78174 7.48935 3.51737 7.5956C3.25174 7.70185 3.18799 7.77685 3.16612 7.81435C3.11973 7.89493 3.09557 7.98638 3.09612 8.07935C3.09612 8.1231 3.11487 8.21997 3.29362 8.44435C3.47112 8.66747 3.74487 8.94247 4.14862 9.34685L6.72174 11.9219C7.12799 12.3293 7.40487 12.605 7.62924 12.7843C7.85424 12.9631 7.95174 12.9812 7.99487 12.9812C8.08606 12.9814 8.17571 12.9577 8.25487 12.9125C8.29237 12.8912 8.36799 12.8275 8.47549 12.56C8.58237 12.2931 8.68674 11.9162 8.83924 11.3618L8.85799 11.2931C8.97049 10.8825 9.05799 10.5656 9.24549 10.3037C9.32745 10.1897 9.42247 10.0857 9.52862 9.99372C9.77237 9.7831 10.0799 9.66747 10.478 9.51873L10.5449 9.49372L11.743 9.04373C12.623 8.71247 13.2324 8.48248 13.6467 8.25248C14.0536 8.02685 14.1649 7.85873 14.1986 7.70248C14.2324 7.5456 14.2005 7.34622 13.9236 6.97248C13.6411 6.59123 13.1817 6.12935 12.5167 5.46435L10.5905 3.5356C9.92112 2.8656 9.45549 2.40185 9.07237 2.11685Z",fill:"currentColor"}}),t("path",{attrs:{d:"M3.35693 2.06339L13.5005 13.1579",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}})]),t("defs",[t("clipPath",{attrs:{id:"clip0_93_615"}},[t("rect",{attrs:{width:"15",height:"15",fill:"white",transform:"translate(0.928711 0.110596)"}})])])])])},kd=[],$d=l(bd,xd,kd,!1,null,null,null,null);const Rd=$d.exports,Pd={};var Dd=function(){var e=this,t=e._self._c;return t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{"clip-path":"url(#clip0_93_862)"}},[t("path",{attrs:{d:"M6.64499 3.21886V3.48672H9.3236V3.21886C9.3236 2.86365 9.1825 2.52299 8.93133 2.27183C8.68016 2.02066 8.3395 1.87955 7.9843 1.87955C7.62909 1.87955 7.28843 2.02066 7.03726 2.27183C6.78609 2.52299 6.64499 2.86365 6.64499 3.21886ZM5.57354 3.48672V3.21886C5.57354 2.57949 5.82753 1.9663 6.27964 1.5142C6.73174 1.06209 7.34492 0.808105 7.9843 0.808105C8.62367 0.808105 9.23685 1.06209 9.68896 1.5142C10.1411 1.9663 10.395 2.57949 10.395 3.21886V3.48672H14.413C14.5551 3.48672 14.6913 3.54316 14.7918 3.64363C14.8923 3.7441 14.9487 3.88036 14.9487 4.02244C14.9487 4.16453 14.8923 4.30079 14.7918 4.40126C14.6913 4.50172 14.5551 4.55817 14.413 4.55817H13.6051L12.5915 13.434C12.5168 14.0877 12.2041 14.691 11.713 15.1289C11.222 15.5668 10.5869 15.8087 9.92897 15.8084H6.03962C5.38168 15.8087 4.74664 15.5668 4.25558 15.1289C3.76453 14.691 3.45182 14.0877 3.37708 13.434L2.36349 4.55817H1.55562C1.41354 4.55817 1.27727 4.50172 1.17681 4.40126C1.07634 4.30079 1.0199 4.16453 1.0199 4.02244C1.0199 3.88036 1.07634 3.7441 1.17681 3.64363C1.27727 3.54316 1.41354 3.48672 1.55562 3.48672H5.57354ZM4.4421 13.3119C4.48682 13.704 4.67425 14.0659 4.96867 14.3287C5.26309 14.5915 5.6439 14.7368 6.03855 14.7369H9.92951C10.3242 14.7368 10.705 14.5915 10.9994 14.3287C11.2938 14.0659 11.4812 13.704 11.526 13.3119L12.5272 4.55817H3.4419L4.4421 13.3119ZM6.37713 6.4332C6.51921 6.4332 6.65547 6.48964 6.75594 6.59011C6.85641 6.69057 6.91285 6.82684 6.91285 6.96892V12.3262C6.91285 12.4682 6.85641 12.6045 6.75594 12.705C6.65547 12.8054 6.51921 12.8619 6.37713 12.8619C6.23504 12.8619 6.09878 12.8054 5.99831 12.705C5.89785 12.6045 5.8414 12.4682 5.8414 12.3262V6.96892C5.8414 6.82684 5.89785 6.69057 5.99831 6.59011C6.09878 6.48964 6.23504 6.4332 6.37713 6.4332ZM10.1272 6.96892C10.1272 6.82684 10.0707 6.69057 9.97028 6.59011C9.86981 6.48964 9.73355 6.4332 9.59146 6.4332C9.44938 6.4332 9.31312 6.48964 9.21265 6.59011C9.11218 6.69057 9.05574 6.82684 9.05574 6.96892V12.3262C9.05574 12.4682 9.11218 12.6045 9.21265 12.705C9.31312 12.8054 9.44938 12.8619 9.59146 12.8619C9.73355 12.8619 9.86981 12.8054 9.97028 12.705C10.0707 12.6045 10.1272 12.4682 10.1272 12.3262V6.96892Z",fill:"currentColor"}})]),t("defs",[t("clipPath",{attrs:{id:"clip0_93_862"}},[t("rect",{attrs:{width:"15",height:"15",fill:"white",transform:"translate(0.484253 0.808105)"}})])])])])},Sd=[],Td=l(Pd,Dd,Sd,!1,null,null,null,null);const Md=Td.exports,{__:Ee}=wp.i18n,Ld={name:"ChatItem",components:{IconUnpin:Rd,IconChatItem:ad,IconMenuDots:dd,IconPin:_d,IconPencil:Cd,IconTrash:Md},props:{eventBus:{type:Object,required:!0},chat:{type:Object,required:!0},isActive:{type:Boolean,default:!1}},data(){return{showFloatingMenu:!1,isRenaming:!1,renameValue:""}},computed:{preview(){return this.chat.title?this.chat.title:this.chat.preview},texts(){return{actions:{pin:this.chat.is_pinned?Ee("Unpin","google-analytics-for-wordpress"):Ee("Pin","google-analytics-for-wordpress"),rename:Ee("Rename","google-analytics-for-wordpress"),delete:Ee("Delete","google-analytics-for-wordpress")}}}},methods:{handleClick(){this.$emit("selected",this.chat.id)},toggleFloatingMenu(){this.showFloatingMenu=!this.showFloatingMenu,this.showFloatingMenu&&this.eventBus.$emit("chat-item-float-open",this.chat.id)},closeFloatingMenu(){this.showFloatingMenu=!1},toggleChatPin(){this.closeFloatingMenu(),this.$emit("update",this.chat.id,{is_pinned:!this.chat.is_pinned})},enableChatRename(){this.closeFloatingMenu(),this.isRenaming=!0;const{title:s,preview:e}=this.chat;s&&s.length>0?this.renameValue=s:this.renameValue=e.substring(0,50),this.$nextTick(()=>{this.$refs.renameField.focus()})},deleteChat(){this.$emit("delete",this.chat.id),this.eventBus.$emit("chat-item-float-open",null)},onItemRename(){this.$emit("update",this.chat.id,{title:this.renameValue.length>0?this.renameValue:this.chat.preview}),this.finishRename()},finishRename(){this.isRenaming=!1,this.renameValue=""}},mounted(){this.eventBus.$on("chat-item-float-open",s=>{s!==this.chat.id&&this.closeFloatingMenu()})}};var Ed=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-ai-chat-history-item",class:{"is-active":e.isActive},on:{keyup:function(r){return!r.type.indexOf("key")&&e._k(r.keyCode,"esc",27,r.key,["Esc","Escape"])?null:e.closeFloatingMenu.apply(null,arguments)}}},[t("button",{staticClass:"monsterinsights-ai-chat-history-item__action",attrs:{type:"button"},on:{click:e.handleClick}},[t("span",{staticClass:"monsterinsights-ai-chat-history-item__icon"},[t("icon-chat-item")],1),t("span",{staticClass:"monsterinsights-ai-chat-history-item__preview",domProps:{innerHTML:e._s(e.preview)}})]),e.isRenaming?t("form",{staticClass:"monsterinsights-ai-chat-history-item__rename",on:{keyup:function(r){return!r.type.indexOf("key")&&e._k(r.keyCode,"esc",27,r.key,["Esc","Escape"])?null:e.finishRename.apply(null,arguments)},submit:function(r){return r.preventDefault(),e.onItemRename.apply(null,arguments)}}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.renameValue,expression:"renameValue"}],ref:"renameField",attrs:{type:"text",maxlength:"50",placeholder:e.chat.preview},domProps:{value:e.renameValue},on:{input:function(r){r.target.composing||(e.renameValue=r.target.value)}}}),t("button",{attrs:{type:"submit"}},[t("icon-pencil")],1)]):e._e(),t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.closeFloatingMenu,expression:"closeFloatingMenu"}],staticClass:"monsterinsights-ai-chat-history-item-menu"},[t("button",{staticClass:"monsterinsights-ai-chat-history-item-menu-toggle",attrs:{type:"button"},on:{click:e.toggleFloatingMenu}},[t("icon-menu-dots")],1),e.showFloatingMenu?t("div",{staticClass:"monsterinsights-ai-chat-history-item-menu-float"},[t("button",{staticClass:"floating-action",on:{click:e.toggleChatPin}},[e.chat.is_pinned?t("icon-unpin"):t("icon-pin"),t("span",{domProps:{innerHTML:e._s(e.texts.actions.pin)}})],1),t("button",{staticClass:"floating-action",on:{click:e.enableChatRename}},[t("icon-pencil"),t("span",{domProps:{innerHTML:e._s(e.texts.actions.rename)}})],1),t("button",{staticClass:"floating-action",on:{click:e.deleteChat}},[t("icon-trash"),t("span",{domProps:{innerHTML:e._s(e.texts.actions.delete)}})],1)]):e._e()])])},Ad=[],Fd=l(Ld,Ed,Ad,!1,null,null,null,null);const Nd=Fd.exports,{__:ke}=wp.i18n,Id=new w,Bd={name:"AiAnalyticsSidebar",components:{ChatItem:Nd,SidebarBlock:td},data(){return{eventBus:Id}},computed:{...pe("$_ai-chat",["chatId","pinnedChats","recentChats","isLoadingChat","isLoadingChatLists"]),texts(){return{newConversation:ke("New Conversation","google-analytics-for-wordpress"),pinnedConversations:ke("Pinned Conversations","google-analytics-for-wordpress"),recent:ke("Recent","google-analytics-for-wordpress"),noPinnedConversations:ke("There aren't any pinned conversations","google-analytics-for-wordpress"),noRecentConversations:ke("There aren't any recent conversations","google-analytics-for-wordpress")}}},methods:{startNewChat(){this.$router.push({name:"ai-insights-new-chat"})},onSidebarChatSelected(s){this.chatId!==s&&this.$router.push({name:"ai-insights-chat",params:{chatId:s}})},async onSidebarChatUpdate(s,e){this.$store.commit("$_ai-chat/SET_IS_LOADING_CHAT_LISTS"),await this.$store.dispatch("$_ai-chat/updateChatProperties",{chatId:s,properties:e}),await this.$store.dispatch("$_ai-chat/getChats")},onSidebarChatDelete(s){this.$store.dispatch("$_ai-chat/deleteChat",s).then(()=>{s===this.chatId&&this.$router.replace("/ai-insights/chat")})}},watch:{pinnedChats(s){const{pinnedChatsBlock:e}=this.$refs;s.length>0?e.open():e.close()},recentChats(s){const{recentChatsBlock:e}=this.$refs;s.length>0?e.open():e.close()}}};var Od=function(){var e=this,t=e._self._c;return t("aside",{staticClass:"monsterinsights-ai-insights__sidebar"},[e.chatId?t("div",{staticClass:"monsterinsights-ai-insights__sidebar-actions"},[t("button",{staticClass:"monsterinsights-button",domProps:{innerHTML:e._s(e.texts.newConversation)},on:{click:e.startNewChat}})]):e._e(),t("sidebar-block",{ref:"pinnedChatsBlock",attrs:{"is-loading":e.isLoadingChatLists,"toggle-label":e.texts.pinnedConversations}},[e.pinnedChats.length===0?t("div",[t("p",{domProps:{innerHTML:e._s(e.texts.noPinnedConversations)}})]):e._l(e.pinnedChats,function(r){return t("chat-item",{key:r.id,attrs:{chat:r,"event-bus":e.eventBus,"is-active":r.id===e.chatId},on:{selected:e.onSidebarChatSelected,update:e.onSidebarChatUpdate,delete:e.onSidebarChatDelete}})})],2),t("sidebar-block",{ref:"recentChatsBlock",attrs:{"is-loading":e.isLoadingChatLists,"toggle-label":e.texts.recent}},[e.recentChats.length===0?t("div",[t("p",{domProps:{innerHTML:e._s(e.texts.noRecentConversations)}})]):e._l(e.recentChats,function(r){return t("chat-item",{key:r.id,attrs:{chat:r,"event-bus":e.eventBus,"is-active":r.id===e.chatId},on:{selected:e.onSidebarChatSelected,update:e.onSidebarChatUpdate,delete:e.onSidebarChatDelete}})})],2)],1)},Ud=[],Hd=l(Bd,Od,Ud,!1,null,null,null,null);const Vd=Hd.exports,Yd={};var jd=function(){var e=this,t=e._self._c;return t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M15.67 0.823711C15.7361 0.889929 15.7813 0.974112 15.8 1.0658C15.8186 1.15749 15.81 1.25265 15.775 1.33946L10.2174 15.2331C10.1684 15.3554 10.0866 15.4619 9.98104 15.5408C9.87545 15.6196 9.75014 15.6678 9.61892 15.6801C9.48769 15.6923 9.35564 15.6681 9.23728 15.6102C9.11892 15.5522 9.01885 15.4627 8.94808 15.3515L5.91283 10.5809L1.14218 7.54559C1.03074 7.4749 0.940995 7.3748 0.882848 7.25632C0.824701 7.13785 0.800413 7.00562 0.812664 6.87422C0.824915 6.74282 0.873229 6.61735 0.952274 6.51167C1.03132 6.40599 1.13802 6.3242 1.26061 6.27533L15.1542 0.719607C15.241 0.684663 15.3362 0.675991 15.4279 0.694669C15.5196 0.713346 15.6037 0.758551 15.67 0.824666V0.823711ZM6.866 10.302L9.50299 14.4451L14.0234 3.14457L6.866 10.302ZM13.3482 2.46932L2.0476 6.98973L6.19171 9.62577L13.3482 2.46932Z",fill:"currentColor"}})])])},Zd=[],Wd=l(Yd,jd,Zd,!1,null,null,null,null);const zd=Wd.exports,{__:ae}=wp.i18n,Kd={name:"ChatStart",components:{IconMessagePrompt:zd},computed:{mascotImage(){return new URL(""+new URL("../img/charlie-ai.svg",import.meta.url).href,self.location).href},texts(){return{title:ae("Have a conversation with Google Analytics.","google-analytics-for-wordpress")}},predefinedPrompts(){return[{title:ae("Last week's visitors","google-analytics-for-wordpress"),prompt:ae("How many visits did I get last week?","google-analytics-for-wordpress")},{title:ae("Last month's page views","google-analytics-for-wordpress"),prompt:ae("How many page views did I get last month?","google-analytics-for-wordpress")},{title:ae("Bounce rate","google-analytics-for-wordpress"),prompt:ae("What was the bounce rate from last week?","google-analytics-for-wordpress")},{title:ae("Popular marketing campaign","google-analytics-for-wordpress"),prompt:ae("What was my most popular marketing campaign last week?","google-analytics-for-wordpress")}]}},methods:{startChatWithPrompt(s){const e=this.predefinedPrompts[s];this.$emit("prompt-selected",e.prompt)}}};var qd=function(){var e=this,t=e._self._c;return t("div",[t("header",{staticClass:"monsterinsights-ai-insights-card__header"},[t("figure",[t("img",{attrs:{src:e.mascotImage,alt:""}})]),t("h3",{domProps:{textContent:e._s(e.texts.title)}})]),t("section",{staticClass:"monsterinsights-ai-insights-card__content"},[t("div",{staticClass:"monsterinsights-ai-prompts"},e._l(e.predefinedPrompts,function(r,o){return t("button",{key:o,staticClass:"monsterinsights-ai-prompt-item",attrs:{type:"button"},on:{click:function(n){return e.startChatWithPrompt(o)}}},[t("span",{staticClass:"monsterinsights-ai-prompt-item-content"},[t("span",{staticClass:"monsterinsights-ai-prompt-item__title"},[e._v(e._s(r.title))]),t("span",{staticClass:"monsterinsights-ai-prompt-item__desc"},[e._v(e._s(r.prompt))])]),t("icon-message-prompt")],1)}),0)])])},Gd=[],Jd=l(Kd,qd,Gd,!1,null,null,null,null);const Qd=Jd.exports,Xd={};var eh=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M10.4812 9.69294C12.4524 9.69294 14.0504 8.09497 14.0504 6.12377C14.0504 4.15257 12.4524 2.5546 10.4812 2.5546C8.51002 2.5546 6.91205 4.15257 6.91205 6.12377C6.91205 8.09497 8.51002 9.69294 10.4812 9.69294Z",fill:"currentColor"}}),t("path",{attrs:{d:"M17.6195 16.3852C17.6195 18.6025 17.6195 20.4005 10.4812 20.4005C3.34283 20.4005 3.34283 18.6025 3.34283 16.3852C3.34283 14.1678 6.53903 12.3698 10.4812 12.3698C14.4233 12.3698 17.6195 14.1678 17.6195 16.3852Z",fill:"currentColor"}})])},th=[],sh=l(Xd,eh,th,!1,null,null,null,null);const kt=sh.exports,rh={};var oh=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"21",height:"18",viewBox:"0 0 21 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M5.50099 15.2466L8.76075 15.2471L8.76276 17.9758L5.56254 17.9753C5.05631 17.7617 4.63931 17.4579 4.33639 17.102C4.45503 16.5136 4.89605 15.7448 5.50099 15.2466Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M5.6898 15.4741C5.25846 15.9012 4.83214 16.4184 4.68869 17.0068C4.94691 17.301 5.28446 17.5289 5.64678 17.695L8.38557 17.6954L8.38393 15.4745L5.6898 15.4741Z",fill:"#7D5DC2"}}),t("path",{attrs:{d:"M4.93651 16.4048C4.85224 16.5187 4.69382 16.9789 4.68391 17.0074C4.94213 17.3016 5.27967 17.5295 5.64198 17.6956L7.10065 17.6958L7.09978 16.5047C6.38036 16.4999 5.66093 16.4666 4.93651 16.4048Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M5.48776 17.3973C5.82521 17.5065 5.98412 17.7105 6.03393 17.9763L4.85809 17.9761C4.9373 17.7484 5.16036 17.4684 5.48776 17.3973Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M5.4826 17.5211C5.29905 17.5686 5.14533 17.7062 5.04621 17.8628L5.48781 17.8628L5.48756 17.5211L5.4826 17.5211Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M6.64877 17.3306C7.10033 17.4492 7.31883 17.7387 7.37358 17.9759L5.80582 17.9757C5.90983 17.7243 6.22711 17.3874 6.64877 17.3306Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M6.63887 17.4494C6.39082 17.492 6.14783 17.6581 6.00409 17.8621L6.6491 17.8622L6.6488 17.4494L6.63887 17.4494Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M12.6694 15.2459L9.40967 15.2455L9.41168 17.9741L12.6119 17.9746C13.1178 17.7611 13.5344 17.4574 13.8368 17.1016C13.7222 16.5131 13.2801 15.7443 12.6694 15.2459Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M12.4862 15.4744C12.9181 15.9015 13.3452 16.4188 13.4895 17.0073C13.2318 17.3015 12.8945 17.5292 12.5325 17.6952L9.79369 17.6949L9.79205 15.474L12.4862 15.4744Z",fill:"#7D5DC2"}}),t("path",{attrs:{d:"M13.2355 16.4045C13.3199 16.5184 13.479 16.9787 13.4889 17.0072C13.2312 17.3014 12.8939 17.5291 12.5319 17.6952L11.0732 17.695L11.0723 16.5039C11.7917 16.4993 12.5161 16.4661 13.2355 16.4045Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M12.6901 17.3968C12.3528 17.5059 12.1942 17.7099 12.1448 17.9756L13.3206 17.9758C13.2411 17.748 13.0176 17.468 12.6901 17.3968Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M12.6953 17.5195C12.8789 17.567 13.0328 17.7047 13.1322 17.8613L12.6906 17.8613L12.6903 17.5195L12.6953 17.5195Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M11.5294 17.33C11.078 17.4486 10.8599 17.738 10.8055 17.9752L12.3733 17.9754C12.2689 17.7239 11.9511 17.387 11.5294 17.33Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M11.5344 17.4488C11.7825 17.4915 12.0258 17.6576 12.1698 17.8617L11.5248 17.8616L11.5245 17.4488L11.5344 17.4488Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M0.372645 16.0872C0.392865 16.5856 0.512246 16.9748 0.740684 17.2169C1.09794 17.1884 1.40034 16.8088 1.53904 16.4813C1.33037 16.1348 0.923359 15.9781 0.372645 16.0872Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M0.556401 16.2249C0.566538 16.5145 0.646171 16.8135 0.810125 17.0319L0.94853 16.2439C0.824422 16.2154 0.690408 16.2154 0.556401 16.2249Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M1.27579 16.3528C1.31086 16.799 1.37069 17.1929 1.66375 17.5916C2.18964 17.4825 2.60128 17.2026 2.77462 16.7565C2.51618 16.1916 1.90083 16.1156 1.27579 16.3528Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M1.73804 17.4017L2.08967 16.4097C1.89112 16.3717 1.66782 16.4002 1.45446 16.4666C1.48945 16.8036 1.54923 17.1027 1.73804 17.4017Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M3.00209 16.7189C3.09178 17.2267 3.59312 17.4783 3.84617 17.4641C3.96016 17.2791 4.07411 17.037 4.07389 16.7475L3.85544 16.5719C3.52294 16.5101 3.17065 16.5338 3.00209 16.7189Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M3.75685 17.2928C3.85601 17.1504 3.92037 16.932 3.93014 16.7231L3.57275 16.6661L3.75685 17.2928Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M4.07582 9.91119L4.08082 16.742C2.84508 16.889 1.40571 16.8413 0.372805 16.0817C0.392192 15.4456 0.456259 14.8238 0.574942 14.2304C0.797501 13.1576 1.21866 12.1798 1.94767 11.3777C2.59237 10.6705 3.23734 10.3336 4.07582 9.91119Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M0.720111 15.925C1.3657 16.3998 2.91438 16.5472 3.7283 16.4476L3.72393 10.4617C1.6252 11.5295 0.792792 13.4993 0.720111 15.925Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M0.720327 15.9255C1.04814 16.1676 1.60425 16.3196 2.18513 16.4051C2.26844 14.8812 2.25689 12.5883 1.89903 11.9806C1.12039 13.0392 0.768882 14.3921 0.720327 15.9255Z",fill:"#A58DD4"}}),t("path",{attrs:{d:"M2.18265 14.9094C2.45557 14.9094 2.67405 15.1183 2.67425 15.3793C2.67444 15.6403 2.45627 15.8491 2.18335 15.8491C1.91044 15.8491 1.69195 15.6402 1.69176 15.3792C1.69156 15.1181 1.90974 14.9093 2.18265 14.9094Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M3.34585 4.58242C3.20128 3.69483 3.35973 3.23445 3.54769 2.42285C4.13856 2.89283 4.51594 3.22987 5.23573 3.57646C5.379 2.71739 5.69598 1.90581 6.1569 1.14645L6.4295 0.700334C7.48207 1.4599 8.38069 2.06756 9.07616 3.07389C9.77013 2.06776 10.6679 1.46036 11.7193 0.701088L11.9925 1.14729C12.4546 1.90677 12.7728 2.71844 12.9173 3.57755C13.6366 3.23117 14.0135 2.89423 14.6037 2.42443C14.7978 3.23134 14.952 3.69176 14.8087 4.58405L16.1091 4.92598C15.8535 8.17245 15.5925 10.9395 15.2625 14.186C15.2181 14.6322 15.1689 15.0831 15.1196 15.5292C15.1048 15.6574 14.9412 15.8899 14.2465 15.8945C10.8524 16.1124 7.29944 16.1119 3.91004 15.8931C3.21532 15.8835 3.05139 15.6556 3.03641 15.5275C2.98645 15.0718 2.93648 14.6162 2.89149 14.1605C2.8715 13.9754 2.85151 13.7903 2.83152 13.6004C2.54652 10.7051 2.26648 7.81928 2.03109 4.91922L3.33096 4.57767L3.34585 4.57767L3.34585 4.58242Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M6.57361 1.37927C6.01355 2.29049 5.70164 3.25396 5.64283 4.25068C5.06705 4.04176 4.41178 3.69993 3.84575 3.25844C3.73198 3.74255 3.75726 4.37382 3.9115 4.92442C3.4352 5.02877 2.99364 5.13788 2.55208 5.26122C2.79249 8.24197 3.01801 11.2227 3.32789 14.194C3.35788 14.4883 3.42286 15.1243 3.45782 15.4186C3.58192 15.485 3.83996 15.4851 3.94417 15.4898C7.35839 15.7086 10.8121 15.7091 14.226 15.4913C14.3352 15.4818 14.5883 15.4819 14.7123 15.4202C14.7468 15.1306 14.8108 14.4804 14.8404 14.1956C15.1409 11.2197 15.3669 8.23901 15.6029 5.26308C15.1612 5.13961 14.7194 5.03038 14.243 4.92589C14.3964 4.37534 14.4207 3.74407 14.3062 3.25993C13.7458 3.70601 13.0861 4.0429 12.5106 4.25166C12.4504 3.25966 12.137 2.29136 11.5756 1.37998C10.4349 2.20568 9.68602 2.80362 9.07655 3.99962C8.46033 2.80344 7.71555 2.2053 6.56864 1.37927L6.57361 1.37927Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M6.5752 1.37919C6.66054 2.70362 7.00906 4.17525 7.6647 4.79244C6.79104 4.70214 6.32439 4.60239 5.64909 4.25103C5.06821 4.04209 4.41277 3.70022 3.8466 3.25868C4.15001 4.13217 4.44331 4.75407 5.04435 5.36176C4.53309 5.3522 4.24012 5.19077 3.91233 4.92489C3.43591 5.02927 2.99423 5.13839 2.55256 5.26176C2.79297 8.24293 3.0185 11.2241 3.3284 14.1958C3.35839 14.4901 3.42338 15.1262 3.45834 15.4205C3.58247 15.487 3.84058 15.487 3.94482 15.4918C5.65237 15.6012 7.3698 15.6536 9.08719 15.6538L9.08591 13.8784C6.4355 14.0775 4.01038 10.0802 6.79304 7.45542L6.23733 7.74966C6.16754 7.32717 6.30614 6.80027 6.48948 6.37306C6.85122 5.54237 7.8137 4.92538 9.0793 4.77838L9.07874 3.99512C8.46239 2.79879 7.72238 2.20057 6.5752 1.37919Z",fill:"#A58DD4"}}),t("path",{attrs:{d:"M13.1117 5.35967C13.7068 4.75218 14.0041 4.12567 14.3062 3.25232C14.4207 3.7365 14.3963 4.3678 14.2429 4.91839C13.9155 5.18891 13.6229 5.35025 13.1117 5.35967ZM12.1041 5.11271C12.4019 5.11275 12.6402 5.34537 12.6404 5.62542C12.6407 5.91022 12.3977 6.13803 12.1049 6.13799C11.8071 6.13795 11.5687 5.90533 11.5685 5.62527C11.5683 5.34522 11.8113 5.11267 12.1041 5.11271ZM14.0652 6.27117C14.2687 6.27119 14.4375 6.4326 14.4377 6.62722C14.4378 6.82183 14.2692 6.98319 14.0658 6.98316C13.8623 6.98314 13.6934 6.82173 13.6933 6.62711C13.6931 6.42775 13.8568 6.27114 14.0652 6.27117ZM10.4959 4.78496C11.1506 4.17274 11.4969 2.70132 11.5802 1.37227C12.1417 2.2837 12.4551 3.24732 12.5154 4.24413C11.8357 4.59529 11.3693 4.6949 10.4959 4.78496Z",fill:"#7D5DC2"}}),t("path",{attrs:{d:"M4.21721 5.85968C4.7036 5.85974 5.10093 6.23955 5.10128 6.70474C5.10162 7.16993 4.70484 7.54963 4.21845 7.54957C3.73206 7.5495 3.33473 7.1697 3.33439 6.70451C3.33405 6.23931 3.73082 5.85962 4.21721 5.85968ZM3.64835 8.4514C3.84192 8.45142 4.00085 8.60335 4.00098 8.78847C4.00112 8.9736 3.84241 9.12548 3.64885 9.12546C3.45528 9.12543 3.29635 8.97351 3.29622 8.78838C3.29608 8.60325 3.45479 8.45137 3.64835 8.4514Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M9.13739 6.32119C11.2364 6.32149 12.9396 7.94968 12.9411 9.95732C12.9426 11.965 11.2418 13.5927 9.1428 13.5924C7.04384 13.5921 5.34063 11.9639 5.33913 9.95623C5.33764 7.94859 7.03842 6.32089 9.13739 6.32119Z",fill:"#D3E8EF"}}),t("path",{attrs:{d:"M5.43844 9.12992C5.76988 7.76309 6.91035 6.69063 8.34912 6.40132C9.91703 6.28764 11.1881 7.33671 11.1892 8.76529C11.1902 10.2034 9.90105 11.4799 8.31826 11.6126C6.73051 11.7452 5.44456 10.6914 5.44349 9.24858C5.4385 9.21061 5.43847 9.16789 5.43844 9.12992Z",fill:"white"}}),t("path",{attrs:{d:"M10.3096 8.56165C11.4955 8.56182 12.4539 9.47797 12.4547 10.6123C12.4556 11.7466 11.4986 12.6625 10.3126 12.6624C9.12669 12.6622 8.16833 11.746 8.16748 10.6117C8.16664 9.4821 9.12364 8.56148 10.3096 8.56165Z",fill:"#EA4E64"}}),t("path",{attrs:{d:"M10.3097 8.56165C10.6223 8.56169 10.9201 8.62344 11.1881 8.74213L11.1881 8.76586C11.1892 10.166 9.96942 11.4141 8.43129 11.6037C8.26236 11.3094 8.16287 10.9724 8.1626 10.6117C8.16175 9.47737 9.11877 8.56148 10.3097 8.56165Z",fill:"#EC6277"}}),t("path",{attrs:{d:"M10.3101 9.28247C11.0792 9.28258 11.7049 9.87594 11.7055 10.6163C11.706 11.352 11.0862 11.9499 10.3121 11.9498C9.54298 11.9497 8.91732 11.3564 8.91676 10.6159C8.91622 9.88029 9.53603 9.28236 10.3101 9.28247Z",fill:"#232323"}}),t("path",{attrs:{d:"M10.3101 9.28247C10.5929 9.28251 10.856 9.36323 11.0744 9.50091C10.8071 10.3504 10.103 11.0718 9.20015 11.4134C9.02631 11.1903 8.9219 10.9102 8.92168 10.616C8.91617 9.8803 9.53599 9.28236 10.3101 9.28247Z",fill:"#323232"}}),t("path",{attrs:{d:"M8.79199 9.07816C9.27828 9.07823 9.67056 9.45323 9.67091 9.91836C9.67125 10.3835 9.27953 10.7584 8.79324 10.7583C8.30695 10.7582 7.91467 10.3832 7.91432 9.91811C7.91398 9.45298 8.3057 9.07809 8.79199 9.07816Z",fill:"white"}}),t("path",{attrs:{d:"M4.6712 11.6239C5.75248 11.0545 7.38074 12.0514 7.26285 13.665C6.23054 13.3611 5.11341 12.4307 4.6712 11.6239Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M4.67108 11.6241C5.65354 11.6432 6.74058 12.1749 7.26269 13.6652C6.02709 13.5321 4.87527 12.6302 4.67108 11.6241Z",fill:"#A58DD4"}}),t("path",{attrs:{d:"M5.183 12.9723C5.32687 12.9249 6.00173 12.9392 6.05637 13.0104C6.12094 13.0958 5.94263 13.5325 5.75428 13.8172C5.50603 13.6273 5.30239 13.3663 5.183 12.9723Z",fill:"#7D5DC2"}}),t("path",{attrs:{d:"M4.98458 12.8156C5.12845 12.7681 5.80331 12.7825 5.85794 12.8537C5.92251 12.9391 5.7442 13.3757 5.55585 13.6605C5.30761 13.4706 5.10396 13.2095 4.98458 12.8156Z",fill:"white"}}),t("path",{attrs:{d:"M5.18743 12.5923C5.23276 13.5082 6.48376 14.3911 7.66937 14.0638C7.73407 14.3296 7.54564 14.4862 7.35216 14.5241C6.27554 14.6141 5.23806 13.9638 4.89509 13.1333C4.79569 12.9055 4.90463 12.6207 5.18743 12.5923Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M4.98523 14.3112C5.28333 14.8475 5.74992 15.0991 6.26105 15.1799C5.5319 15.493 4.88656 15.0753 4.98523 14.3112Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M5.4762 14.4391C6.06182 14.7523 6.61753 14.852 7.26243 14.7951C6.79134 15.0939 5.86861 15.127 5.4762 14.4391Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M10.4528 5.28945C9.27547 3.34366 6.18031 4.4774 6.49449 6.66034C7.26759 5.43613 8.76072 5.03771 10.4528 5.28945Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M10.4527 5.28899C9.78716 4.19303 8.38808 4.12172 7.45086 4.72412C7.44609 4.9803 7.52559 5.16533 7.66957 5.31241C8.42836 4.96143 9.42048 4.81921 10.4527 5.28899Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M16.6109 12.6912C17.8464 12.6913 18.8594 13.6407 18.8603 14.8273C18.8612 16.0138 17.8496 16.9629 16.614 16.9627C15.3785 16.9626 14.3655 16.0132 14.3646 14.8266C14.3637 13.6401 15.3753 12.691 16.6109 12.6912Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M16.6113 13.0046C17.6732 13.0047 18.5322 13.8212 18.533 14.8226C18.5337 15.8288 17.6759 16.6403 16.614 16.6401C15.5521 16.64 14.6931 15.8235 14.6923 14.8221C14.6916 13.8206 15.5544 13.0044 16.6113 13.0046Z",fill:"#9478CF"}}),t("path",{attrs:{d:"M16.6115 13.004C17.3658 13.0041 18.0161 13.4171 18.3341 14.0151C18.4042 14.9643 17.1941 15.6618 16.385 15.3342C15.7894 15.0256 15.3771 14.4228 15.3766 13.7347C15.3765 13.6208 15.3863 13.5116 15.4111 13.4025C15.7433 13.1557 16.16 13.0039 16.6115 13.004Z",fill:"#A58DD4"}}),t("path",{attrs:{d:"M16.3084 14.2781C16.3098 14.002 17.3827 14.0293 17.386 14.307L17.3894 17.3797C17.388 17.6557 16.3151 17.6285 16.3118 17.3508L16.3084 14.2781Z",fill:"#2B3039"}}),t("path",{attrs:{d:"M17.2967 14.2481C16.895 14.4854 16.5627 14.7179 16.3248 15.1498C16.6675 15.5485 17.1192 15.7669 17.6154 15.71C18.017 15.226 17.8081 14.6706 17.2967 14.2481Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M17.6101 14.9123C17.5504 14.7462 17.4412 14.5896 17.3021 14.4377C16.9947 14.6275 16.7418 14.8315 16.5485 15.1257C16.7618 15.1447 17.0942 15.0926 17.6101 14.9123Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M17.6842 15.411C17.2825 15.6482 16.9502 15.8807 16.7123 16.3126C17.055 16.7113 17.5067 16.9297 18.0029 16.8728C18.4045 16.3888 18.1956 15.8334 17.6842 15.411Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M17.9975 16.0281C17.9379 15.862 17.8286 15.7054 17.6895 15.5535C17.3821 15.7433 17.1292 15.9473 16.9359 16.2415C17.1492 16.2652 17.4816 16.2083 17.9975 16.0281Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M15.5908 15.5247C15.9728 15.4346 16.3102 15.3777 16.7221 15.4916C16.7721 15.9188 16.6483 16.308 16.3259 16.5547C15.79 16.5119 15.5614 16.0658 15.5908 15.5247Z",fill:"#5F3EA7"}}),t("path",{attrs:{d:"M15.8248 16.0422C15.7552 15.9094 15.7204 15.7575 15.7054 15.5914C16.003 15.525 16.2759 15.4966 16.5786 15.5583C16.4695 15.6959 16.2414 15.8525 15.8248 16.0422Z",fill:"#6F4BBB"}}),t("path",{attrs:{d:"M16.4433 3.80966C13.3755 3.97424 11.0296 6.49216 11.2043 9.42657C11.379 12.361 14.0135 14.607 17.0813 14.4424C20.149 14.2778 22.4949 11.7599 22.3202 8.8255C22.1455 5.89109 19.5185 3.64509 16.4433 3.80966Z",fill:"#232323"}}),t("path",{attrs:{d:"M16.4886 4.46947C13.8034 4.61257 11.7423 6.82204 11.8942 9.39771C12.0462 11.9662 14.3579 13.9395 17.0506 13.7893C19.7359 13.6462 21.797 11.4367 21.645 8.86102C21.4856 6.29251 19.1813 4.31919 16.4886 4.46947Z",fill:"white"}}),t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M18.4614 5.795C18.5474 5.38994 19.1237 5.38391 19.2186 5.78736L19.2226 5.8062L19.2316 5.84389C19.3409 6.29515 19.7088 6.63785 20.1671 6.71482C20.5895 6.78573 20.5933 7.39152 20.1718 7.46771C19.9469 7.5083 19.7391 7.61442 19.5744 7.77274C19.4096 7.93105 19.2954 8.13452 19.2459 8.35757L19.2346 8.407C19.1452 8.8116 18.5694 8.81279 18.4778 8.40883L18.4683 8.36631C18.4181 8.14301 18.3024 7.93974 18.1362 7.78246C17.9699 7.62518 17.7605 7.52102 17.5347 7.48327C17.1133 7.41284 17.1095 6.80802 17.53 6.73231C17.7545 6.69191 17.9619 6.58577 18.1259 6.42736C18.29 6.26895 18.4033 6.06539 18.4516 5.84249L18.4581 5.81051L18.4614 5.795ZM18.8348 9.23694C18.6042 9.2339 18.3833 9.14385 18.2162 8.98484C18.1799 9.00913 18.1483 9.03983 18.1229 9.07543C17.907 9.36518 17.6539 9.66145 17.3673 9.95115C17.1499 10.1712 16.9296 10.3715 16.712 10.5504C16.4922 10.3742 16.2693 10.1767 16.0492 9.95941C15.8384 9.75158 15.6383 9.53302 15.45 9.3046C15.6262 9.0843 15.8241 8.86192 16.041 8.64185C16.3117 8.36672 16.6012 8.11058 16.9072 7.87528C16.9391 7.85207 16.9671 7.82378 16.9899 7.79153C16.8974 7.70824 16.823 7.60676 16.7715 7.49343C16.72 7.3801 16.6925 7.25737 16.6906 7.13291C16.2138 6.80347 15.7429 6.56012 15.3231 6.43452C14.8684 6.2985 14.3359 6.26457 13.9892 6.61514C13.7646 6.84301 13.7051 7.15113 13.7244 7.44376C13.7436 7.73687 13.8439 8.05851 13.9945 8.38467C14.148 8.70918 14.3309 9.01901 14.5408 9.3103C14.3346 9.60389 14.1557 9.91566 14.0061 10.2417C13.8596 10.5698 13.7634 10.8926 13.7478 11.186C13.7322 11.4788 13.7951 11.7862 14.023 12.0112C14.2509 12.2357 14.559 12.2953 14.8516 12.276C15.1442 12.2563 15.4664 12.1565 15.7925 12.0059C16.0868 11.87 16.3997 11.6852 16.7182 11.4596C17.0389 11.6812 17.3536 11.8621 17.6501 11.9943C17.9776 12.1408 18.301 12.237 18.5943 12.2526C18.8872 12.2681 19.194 12.2048 19.419 11.9769C19.7657 11.6263 19.7251 11.0943 19.5834 10.6413C19.4481 10.2096 19.1872 9.72491 18.8348 9.23694ZM15.115 7.12971C15.3943 7.21313 15.7277 7.37265 16.0892 7.60459C15.6982 7.94052 15.3354 8.30789 15.0044 8.70303C14.8721 8.5043 14.7547 8.29608 14.6531 8.08005C14.5241 7.80021 14.4602 7.56834 14.4485 7.39567C14.4372 7.22203 14.4793 7.15208 14.5058 7.1253C14.5491 7.08148 14.7093 7.00887 15.115 7.12971ZM14.6685 10.5386C14.7563 10.342 14.8716 10.1308 15.012 9.91123C15.3481 10.3022 15.7156 10.665 16.1109 10.996C15.9123 11.1284 15.7043 11.246 15.4884 11.3478C15.2086 11.4768 14.9767 11.5407 14.804 11.5524C14.6299 11.5637 14.5604 11.5215 14.5337 11.4951C14.5069 11.4686 14.4639 11.3987 14.4729 11.2254C14.4825 11.0526 14.543 10.82 14.669 10.5386L14.6685 10.5386ZM17.9459 11.3324C17.7289 11.2335 17.5195 11.1185 17.3196 10.9884C17.7102 10.6524 18.0727 10.2851 18.4034 9.88998C18.6393 10.249 18.803 10.5804 18.8899 10.8586C19.0163 11.2623 18.9457 11.4239 18.9025 11.4677C18.8755 11.4945 18.8061 11.5375 18.6328 11.5279C18.4595 11.5193 18.2274 11.4579 17.9459 11.3324ZM16.2203 9.29978C16.2195 9.17145 16.2697 9.04805 16.3598 8.95674C16.45 8.86543 16.5728 8.81368 16.7011 8.81288C16.8294 8.81208 16.9528 8.86228 17.0441 8.95246C17.1354 9.04263 17.1872 9.16538 17.188 9.29371C17.1888 9.42204 17.1386 9.54543 17.0484 9.63674C16.9582 9.72805 16.8355 9.7798 16.7072 9.78061C16.5788 9.78141 16.4554 9.7312 16.3641 9.64103C16.2728 9.55086 16.2211 9.4281 16.2203 9.29978Z",fill:"#232323"}}),t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M18.4609 5.79522C18.5469 5.39015 19.1231 5.38412 19.2181 5.78757L19.2221 5.80642L19.231 5.8441C19.3403 6.29536 19.7083 6.63807 20.1665 6.71504C20.5889 6.78594 20.5927 7.39174 20.1712 7.46793C19.9464 7.50852 19.7385 7.61463 19.5738 7.77295C19.4091 7.93127 19.2948 8.13473 19.2453 8.35778L19.234 8.40721C19.1446 8.81181 18.5688 8.813 18.4772 8.40905L18.4678 8.36653C18.4175 8.14322 18.3019 7.93995 18.1356 7.78267C17.9693 7.62539 17.7599 7.52123 17.5342 7.48348C17.1127 7.41306 17.109 6.80823 17.5295 6.73253C17.7539 6.69212 17.9613 6.58599 18.1254 6.42757C18.2894 6.26916 18.4028 6.0656 18.451 5.8427L18.4576 5.81072L18.4609 5.79522Z",fill:"#EA4E64"}})])},ih=[],ah=l(rh,oh,ih,!1,null,null,null,null);const nh=ah.exports,lh={};var ch=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"21",height:"21",viewBox:"0 0 21 21",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M13.0664 2.18408H10.1544C8.83485 2.18408 7.78952 2.18408 6.97178 2.29794C6.13034 2.41552 5.44901 2.66259 4.91126 3.21924C4.37424 3.77664 4.13588 4.48287 4.02244 5.35506C3.9126 6.20343 3.9126 7.28622 3.9126 8.65404V13.1608C3.91261 13.8257 4.14158 14.4687 4.55828 14.9742C4.97497 15.4796 5.55204 15.8143 6.18562 15.918C6.28398 16.4866 6.47424 16.9718 6.85475 17.367C7.28696 17.815 7.83116 18.007 8.47732 18.0978C9.09978 18.1841 9.89167 18.1841 10.8731 18.1841H13.1059C14.0874 18.1841 14.8793 18.1841 15.5017 18.0978C16.1479 18.007 16.6921 17.815 17.1243 17.367C17.5565 16.919 17.7417 16.3549 17.8293 15.6851C17.9126 15.0399 17.9126 14.2191 17.9126 13.2018V9.39897C17.9126 8.38166 17.9126 7.56083 17.8293 6.91562C17.7417 6.24585 17.5565 5.68176 17.1243 5.23376C16.7431 4.83934 16.275 4.64213 15.7264 4.54017C15.6264 3.88344 15.3035 3.28528 14.8159 2.85336C14.3282 2.42143 13.7079 2.1841 13.0664 2.18408ZM14.5957 4.43227C14.4865 4.10185 14.2804 3.81508 14.0062 3.61212C13.732 3.40915 13.4034 3.30015 13.0664 3.30036H10.1946C8.82552 3.30036 7.85342 3.30185 7.11465 3.40455C6.39311 3.50501 5.9767 3.69404 5.67301 4.00883C5.36932 4.32362 5.18696 4.75524 5.09003 5.5039C4.99096 6.26892 4.98952 7.27655 4.98952 8.69571V13.1608C4.98932 13.5101 5.09448 13.8507 5.29029 14.1349C5.4861 14.4191 5.76275 14.6328 6.08152 14.7459C6.06644 14.292 6.06644 13.7785 6.06644 13.2018V9.39897C6.06644 8.38166 6.06644 7.56083 6.15044 6.91562C6.2366 6.24585 6.42326 5.68176 6.85475 5.23376C7.28696 4.78576 7.83116 4.59376 8.47732 4.50371C9.09978 4.41664 9.89167 4.41664 10.8731 4.41664H13.1059C13.6623 4.41664 14.1577 4.41664 14.5957 4.43227ZM7.61578 6.02408C7.81465 5.81794 8.09321 5.68399 8.62091 5.61031C9.16224 5.53515 9.88162 5.53366 10.9119 5.53366H13.0657C14.096 5.53366 14.8146 5.53515 15.3574 5.61031C15.8844 5.68399 16.163 5.81869 16.3618 6.02408C16.5607 6.23022 16.6899 6.51897 16.761 7.06594C16.8335 7.62706 16.835 8.37273 16.835 9.44064V13.1616C16.835 14.2295 16.8335 14.9744 16.761 15.537C16.6899 16.0832 16.56 16.372 16.3618 16.5781C16.163 16.7843 15.8844 16.9182 15.3567 16.9919C14.8146 17.0671 14.096 17.0685 13.0657 17.0685H10.9119C9.88162 17.0685 9.16224 17.0671 8.62019 16.9919C8.09321 16.9182 7.81465 16.7835 7.61578 16.5781C7.41691 16.372 7.28767 16.0832 7.2166 15.5363C7.14409 14.9744 7.14265 14.2295 7.14265 13.1616V9.44064C7.14265 8.37273 7.14409 7.62706 7.2166 7.0652C7.28767 6.51897 7.41762 6.23022 7.61578 6.02408Z",fill:"currentColor"}})])},ph=[],dh=l(lh,ch,ph,!1,null,null,null,null);const hh=dh.exports,{__:je,sprintf:gh}=wp.i18n,uh={name:"ChatMessageFeedback",components:{FeedbackInputBox:Yt},props:{message:{type:Object,required:!0}},data(){return{isSaving:!1}},computed:{...pe("$_ai-chat",["chatId","currentChat"]),hasFeedback(){return this.message.feedback!==null},hasSkippedFeedback(){return this.message.hasOwnProperty("skipped_feedback")?this.message.skipped_feedback:!1},feedbackBoxLabels(){return{title:gh(je("Why didn't this response meet your expectations? %s(Optional)%s","google-analytics-for-wordpress"),"<span>","</span>"),feedbackHelp:null,submit:je("Send Feedback","google-analytics-for-wordpress"),thanksTitle:je("Thanks for your feedback!","google-analytics-for-wordpress")}}},methods:{onSubmit(s){this.isSaving=!0,this.$store.dispatch("$_ai-chat/saveChatMessageFeedback",{chatId:this.chatId,messageId:this.message.id,feedback:s}).then(async()=>{await this.$store.dispatch("$_ai-chat/loadChat",{chatId:this.chatId,force:!0}),this.isSaving=!1})},onClose(){this.isSaving=!0,this.$store.dispatch("$_ai-chat/saveChatMessageFeedback",{chatId:this.chatId,messageId:this.message.id,skipped_feedback:!0}).then(async()=>{await this.$store.dispatch("$_ai-chat/loadChat",{chatId:this.chatId,force:!0}),this.isSaving=!1})}}};var mh=function(){var e=this,t=e._self._c;return e.hasSkippedFeedback?e._e():t("feedback-input-box",{attrs:{"has-feedback":e.hasFeedback,"is-saving":e.isSaving,labels:e.feedbackBoxLabels},on:{submit:e.onSubmit,close:e.onClose}})},_h=[],fh=l(uh,mh,_h,!1,null,null,null,null);const vh=fh.exports,Ae=me.COLORS,wh={options:{chart:{toolbar:{show:!1}},yaxis:{decimalsInFloat:!1},dataLabels:{enabled:!1},tooltip:{y:{title:{formatter:function(){return""}}}}}},yh={name:"ChatMessageChart",components:{ApexChart:Re},props:{messageId:{type:String,required:!0},messageChartData:{type:Object,required:!0}},computed:{chartConfig(){const{type:s,seriesName:e="",data:t}=this.messageChartData;if(!t)return null;const r=[],o=[];for(const a of t)r.push(a.value),o.push(a.label);const n=JSON.parse(JSON.stringify(wh));return n.options.chart.type=s,s==="pie"||s==="donut"?(n.options.colors=Ae,n.options.labels=o,n.options.tooltip.enabled=!1,n.series=r):(n.options.colors=[a=>Ae[a.dataPointIndex]?Ae[a.dataPointIndex]:Ae[0]],n.options.xaxis={categories:o},n.series=[{name:e,data:r}]),n}}};var Ch=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-ai-insights-chat-item__message-chart",class:`chart--${e.messageChartData.type}`},[e.chartConfig?t("ApexChart",{key:`chart-${e.messageId}`,attrs:{options:e.chartConfig.options,series:e.chartConfig.series}}):e._e()],1)},bh=[],xh=l(yh,Ch,bh,!1,null,null,null,null);const kh=xh.exports,{__:Ze,sprintf:$h}=wp.i18n,Rh={name:"ChatMessage",components:{IconUser:kt,ChatMessageChart:kh,IconClipboard:hh,IconThumbsDown:Vt,IconThumbsUp:Ht,ChatMessageFeedback:vh},props:{message:{type:Object},aiMock:{type:Boolean,default:!1}},data(){return{canCopyTextToClipboard:!1}},computed:{score:{get(){return this.aiMock||this.message.role==="user"?-1:typeof this.message.rating<"u"?this.message.rating:-1},set(s){this.aiMock||this.message.role==="user"||this.$emit("rate",{messageId:this.message.id,rating:s})}},hasChart(){if(!this.isAIMessage)return!1;const{meta:{chart:s}}=this.message;return!(typeof s>"u"||s===!1)},showFeedbackBox(){return this.score===0},isAIMessage(){return!this.aiMock&&this.message.role==="assistant"},mascotImage(){return new URL(""+new URL("../img/charlie-ai.svg",import.meta.url).href,self.location).href},senderIcon(){return!this.aiMock&&this.message.role==="user"?kt:nh},sender(){return!this.aiMock&&this.message.role==="user"?Ze("You","google-analytics-for-wordpress"):"MonsterInsights"},messageHtml(){return this.message.content},texts(){return{aiLoading:$h(Ze("Retrieving data from %s...","google-analytics-for-wordpress"),"MonsterInsights")}}},methods:{async copyToClipboard(){try{await navigator.clipboard.writeText(this.message.content),w.prototype.$mi_success_toast({title:Ze("Copied to clipboard!","google-analytics-for-wordpress")})}catch(s){console.error("Failed to copy: ",s)}},rate(s){this.aiMock||(this.score=s)}},beforeMount(){this.canCopyTextToClipboard=typeof navigator.clipboard<"u"}};var Ph=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-ai-insights-chat-item",class:{"is-ai":e.isAIMessage}},[t("div",{staticClass:"monsterinsights-ai-insights-chat-item__icon"},[!e.aiMock&&e.message.role==="user"?t("IconUser"):t("img",{attrs:{src:e.mascotImage,alt:""}})],1),t("div",{staticClass:"monsterinsights-ai-insights-chat-item__content"},[t("p",{staticClass:"monsterinsights-ai-insights-chat-item__sender"},[e._v(" "+e._s(e.sender)+" ")]),e.aiMock?t("div",{staticClass:"monsterinsights-ai-insights-chat-item__message",class:{"is-mock":e.aiMock}},[t("div",{staticClass:"ai-spinner"}),t("p",{domProps:{innerHTML:e._s(e.texts.aiLoading)}})]):t("div",{staticClass:"monsterinsights-ai-insights-chat-item__message",domProps:{innerHTML:e._s(e.messageHtml)}}),e.isAIMessage?[e.hasChart?t("ChatMessageChart",{attrs:{"message-id":e.message.id,"message-chart-data":e.message.meta.chart}}):e._e(),t("div",{staticClass:"monsterinsights-ai-insights-chat-item__actions"},[t("button",{class:{"is-on":e.score===1},attrs:{type:"button",disabled:e.score===1},on:{click:function(r){return r.preventDefault(),e.rate(1)}}},[t("IconThumbsUp")],1),t("button",{class:{"is-on":e.score===0},attrs:{type:"button",disabled:e.score===0},on:{click:function(r){return r.preventDefault(),e.rate(0)}}},[t("IconThumbsDown")],1),e.canCopyTextToClipboard?t("button",{attrs:{type:"button"},on:{click:function(r){return r.preventDefault(),e.copyToClipboard.apply(null,arguments)}}},[t("IconClipboard")],1):e._e()]),e.showFeedbackBox?t("chat-message-feedback",{attrs:{message:e.message}}):e._e()]:e._e()],2)])},Dh=[],Sh=l(Rh,Ph,Dh,!1,null,null,null,null);const Th=Sh.exports,Mh={name:"ChatScreen",components:{ChatMessage:Th},computed:{...pe("$_ai-chat",["chatId","currentChat","isWaitingForAI"])},methods:{scrollToBottom(){this.$refs.container&&this.$refs.container.scroll({top:this.$refs.container.scrollHeight,behavior:"smooth"})},handleMessageRating(s){const{messageId:e,rating:t}=s;this.$store.dispatch("$_ai-chat/rateChatMessage",{chatId:this.chatId,messageId:e,rating:t})}},created(){this.unsubscribe=this.$store.subscribe(s=>{(s.type==="$_ai-chat/APPEND_MESSAGES"||s.type==="$_ai-chat/UPDATE_CURRENT_CHAT")&&this.$nextTick(()=>{this.scrollToBottom()})})},mounted(){this.$nextTick(()=>{this.scrollToBottom()})},beforeDestroy(){this.unsubscribe()}};var Lh=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-ai-insights-chat-wrapper"},[t("div",{ref:"container",staticClass:"monsterinsights-ai-insights-chat-container"},[e._l(e.currentChat,function(r){return t("chat-message",{key:r.id,attrs:{message:r},on:{rate:e.handleMessageRating}})}),e.isWaitingForAI?t("chat-message",{attrs:{"ai-mock":""}}):e._e()],2)])},Eh=[],Ah=l(Mh,Lh,Eh,!1,null,null,null,null);const Fh=Ah.exports,Nh={};var Ih=function(){var e=this,t=e._self._c;return t("span",{staticClass:"icon"},[t("svg",{attrs:{width:"23",height:"20",viewBox:"0 0 23 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M0.5 19.1077V0.441071L22.6667 9.7744L0.5 19.1077ZM2.83333 15.6077L16.6583 9.7744L2.83333 3.94107V8.0244L9.83333 9.7744L2.83333 11.5244V15.6077Z",fill:"currentColor"}})])])},Bh=[],Oh=l(Nh,Ih,Bh,!1,null,null,null,null);const Uh=Oh.exports,{__:$t,sprintf:Hh}=wp.i18n,Vh={name:"UserInputForm",components:{IconMessageSend:Uh},props:{isFormDisabled:{type:Boolean,default:!1}},data(){return{currentMessage:""}},computed:{...C("$_ai-chat",["userCanSendMessage"]),texts(){return{inputPlaceholder:Hh($t("Start a conversation with %s","google-analytics-for-wordpress"),"MonsterInsights"),buttonLabel:$t("Send Message","google-analytics-for-wordpress")}}},methods:{clear(){this.currentMessage=""},handleSubmit(){this.userCanSendMessage&&(this.$emit("submit",this.currentMessage),this.clear())}},mounted(){this.unsubscribe=this.$store.subscribe(s=>{(s.type==="$_ai-chat/UPDATE_CURRENT_CHAT"||s.type==="$_ai-chat/APPEND_MESSAGES")&&this.$refs.field!==void 0&&this.$refs.field.focus()})},beforeDestroy(){this.unsubscribe()}};var Yh=function(){var e=this,t=e._self._c;return t("form",{staticClass:"monsterinsights-ai-user-prompt",on:{submit:function(r){return r.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.currentMessage,expression:"currentMessage"}],ref:"field",staticClass:"monsterinsights-ai-user-prompt__input",attrs:{id:"monsterinsights-ai-user-input",placeholder:e.texts.inputPlaceholder,required:"",maxlength:"300",disabled:e.isFormDisabled},domProps:{value:e.currentMessage},on:{keydown:function(r){return!r.type.indexOf("key")&&e._k(r.keyCode,"enter",13,r.key,"Enter")||r.ctrlKey||r.shiftKey||r.altKey||r.metaKey?null:(r.preventDefault(),e.handleSubmit.apply(null,arguments))},input:function(r){r.target.composing||(e.currentMessage=r.target.value)}}}),t("button",{staticClass:"monsterinsights-ai-user-prompt__submit",attrs:{disabled:e.isFormDisabled,type:"submit","aria-label":e.texts.buttonLabel}},[t("icon-message-send")],1)])},jh=[],Zh=l(Vh,Yh,jh,!1,null,null,null,null);const Wh=Zh.exports,{__:zh}=wp.i18n,Kh={name:"UserInputRetry",computed:{...pe("$_ai-chat",["aiError","retryable","isWaitingForAI"]),texts(){return{retry:zh("Retry","google-analytics-for-wordpress")}}},methods:{retry(){this.$store.dispatch("$_ai-chat/retryLastChatMessage")}}};var qh=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-ai-user-retry"},[t("div",{staticClass:"monsterinsights-ai-user-retry__message",domProps:{innerHTML:e._s(e.aiError)}}),e.retryable?t("button",{staticClass:"monsterinsights-button",attrs:{disabled:e.isWaitingForAI},domProps:{innerHTML:e._s(e.texts.retry)},on:{click:e.retry}}):e._e()])},Gh=[],Jh=l(Kh,qh,Gh,!1,null,null,null,null);const Qh=Jh.exports,{__:Rt}=wp.i18n,Xh={name:"AiAnalytics",components:{ReportUpsellOverlay:E,UserInputForm:Wh,UserInputRetry:Qh,ChatStart:Qd,ChatScreen:Fh,AiAnalyticsSidebar:Vd},props:{upsell:{type:Boolean,default:!1}},data(){return{showUpsell:!1}},computed:{...pe("$_ai-chat",["isLoadingChat"]),...C("$_ai-chat",["hasChatStarted","canRetryLastMessage"]),...C("$_license",["license","license_network"]),texts(){return{title:Rt("Conversations","google-analytics-for-wordpress"),upsellSubheading:Rt("Conversations AI lets you....","google-analytics-for-wordpress")}},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type}},methods:{handleUserInput(s){!this.$isPro()||this.licenseLevel!=="pro"?this.showUpsell=!0:this.$store.dispatch("$_ai-chat/addUserMessage",s).then(({isNewChat:e,chatId:t})=>{e&&this.$router.push({name:"ai-insights-chat",params:{chatId:t}})})}},beforeCreate(){if(this.$store.hasModule("$_ai-chat")&&this.$store.unregisterModule("$_ai-chat"),this.$store.registerModule("$_ai-chat",Zp),!this.$isPro())return;this.$store.dispatch("$_ai-chat/getChats");const{name:s,params:e}=this.$route;if(s==="ai-insights-chat"){const{chatId:t}=e;t&&this.$store.dispatch("$_ai-chat/loadChat",{chatId:t}).catch(()=>{this.$router.replace("/ai-insights/chat")})}},watch:{$route(s){if(!this.$isPro())return;const{chatId:e}=s.params;e?this.$store.dispatch("$_ai-chat/loadChat",{chatId:e}).catch(()=>{this.$router.replace("/ai-insights/chat")}):this.$store.dispatch("$_ai-chat/loadChat",{chatId:!1})}}};var eg=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"monsterinsights-report-top"},[t("h2",{domProps:{textContent:e._s(e.texts.title)}})]),t("article",{staticClass:"monsterinsights-ai-insights-card",class:{"monsterinsights-blur":e.showUpsell,"is-loading-chat":e.isLoadingChat,"has-sidebar":!e.showUpsell}},[e.showUpsell?t("ReportUpsellOverlay",{attrs:{"custom-subheading":e.texts.upsellSubheading,report:"ai_insights_chat","force-two-columns":"","show-sample-button":!1}}):t("ai-analytics-sidebar"),t("main",{staticClass:"monsterinsights-ai-insights__content monsterinsights-ai-insights__content--chat"},[t("div",{staticClass:"monsterinsights-container"},[t("div",{staticClass:"monsterinsights-ai-insights-card-wrapper"},[e.hasChatStarted?t("chat-screen"):t("chat-start",{on:{"prompt-selected":e.handleUserInput}}),t("footer",{staticClass:"monsterinsights-ai-insights-card__footer"},[e.canRetryLastMessage?t("user-input-retry"):t("user-input-form",{attrs:{isFormDisabled:e.showUpsell},on:{submit:e.handleUserInput}})],1)],1)])])],1)])},tg=[],sg=l(Xh,eg,tg,!1,null,null,null,null);const Pt=sg.exports,{__:Ke}=wp.i18n,Dt={meta:{hideNav:!0,title:Ke("AI Insights","google-analytics-for-wordpress")},props:{upsell:!xs()}},rg=()=>[{path:"/ai-insights",component:oc,...Dt,children:[{path:"",name:"ai-insights",component:fp,...Dt},{path:"chat",name:"ai-insights-new-chat",component:Pt,meta:{hideNav:!0,title:Ke("Conversations AI","google-analytics-for-wordpress")}},{path:"chat/:chatId",name:"ai-insights-chat",component:Pt,meta:{hideNav:!0,title:Ke("Conversations AI","google-analytics-for-wordpress")}}]}],{__:L}=wp.i18n;let jt={routes:[{path:"*",redirect:"/"},{path:"/",name:"overview",component:at,meta:{title:L("Overview Report","google-analytics-for-wordpress")}},{path:"/publishers",name:"publisher",component:pl,meta:{title:L("Engagement Overview Report","google-analytics-for-wordpress")}},{path:"/publishers-pages",name:"engagement_pages",component:ml,meta:{title:L("Pages Report","google-analytics-for-wordpress")}},{path:"/countries",name:"countries",component:cn,meta:{title:L("Country Report","google-analytics-for-wordpress")}},{path:"/ecommerce",name:"ecommerce",component:co,meta:{title:L("eCommerce Report","google-analytics-for-wordpress")}},{path:"/ecommerce-coupons",name:"ecommerce_coupons",component:wo,meta:{title:L("eCommerce Coupons Report","google-analytics-for-wordpress")}},{path:"/ecommerce-funnel",name:"ecommerce_funnel",component:Bo,meta:{title:L("eCommerce Funnel Report","google-analytics-for-wordpress")}},{path:"/cart-abandonment",name:"cart_abandonment",component:ko,meta:{title:L("Cart Abandonment","google-analytics-for-wordpress")}},{path:"/search-console",name:"queries",component:Xo,meta:{title:L("Search Console Report","google-analytics-for-wordpress")}},{path:"/dimensions",name:"dimensions",component:oi,meta:{title:L("Dimensions Report","google-analytics-for-wordpress")}},{path:"/forms",name:"forms",component:ci,meta:{title:L("Forms Report","google-analytics-for-wordpress")}},{path:"/real-time",name:"realtime",component:ui,meta:{title:L("Real-Time Report","google-analytics-for-wordpress")}},{path:"/site-speed",name:"sitespeed",component:ba,meta:{title:L("Site Speed Report","google-analytics-for-wordpress")}},{path:"/media",name:"media",component:Da,meta:{title:L("Media Report","google-analytics-for-wordpress")}},{path:"/user-journey-report",name:"user-journey-report",component:rn,meta:{hideNav:!0,customNav:"user-journey-navigation"}},...rg(),{path:"/traffic-overview",name:"traffic_overview",component:un,meta:{title:L("Traffic Overview","google-analytics-for-wordpress")}},{path:"/traffic-landing-pages",name:"traffic_landing_pages",component:wn,meta:{title:L("Top Landing Pages","google-analytics-for-wordpress")}},{path:"/traffic-technology",name:"traffic_technology",component:kn,meta:{title:L("Technology","google-analytics-for-wordpress")}},{path:"/traffic-campaign",name:"traffic_campaign",component:Sn,meta:{title:L("Campaigns","google-analytics-for-wordpress")}},{path:"/traffic-source-medium",name:"traffic_source_medium",component:An,meta:{title:L("Source / Medium","google-analytics-for-wordpress")}},{path:"/traffic-social",name:"traffic_social",component:qn,meta:{title:L("Social","google-analytics-for-wordpress")}},{path:"/exceptions",name:"exceptions",component:Nl,meta:{title:L("Exceptions","google-analytics-for-wordpress")}},{path:"/top-landing-pages",name:"overview-top-landing-pages",component:at,meta:{title:L("Overview Report","google-analytics-for-wordpress")}}]};window.monsterinsights.yearinreview.show_report&&jt.routes.push({path:"/year-in-review",name:"yearinreview",component:Zi,meta:{title:L("Year in Review","google-analytics-for-wordpress")}});const og=new Lt(jt),ig={name:"ReportNavigationSubmenu",props:{menu_id:String,items:Array,submenu_index:Number},inject:["sharedState"],computed:{active(){return this.sharedState.active==this.submenu_index}},methods:{closeMenu(){this.sharedState=null}}};var ag=function(){var e=this,t=e._self._c;return e.active?t("ul",{staticClass:"submenu"},e._l(e.items,function(r,o){return t("li",{key:e.menu_id+o},[t("router-link",{attrs:{to:r.to},on:{click:function(n){return e.closeMenu()}}},[t("span",{staticClass:"submenu_title"},[e._v(e._s(r.label))]),t("span",{staticClass:"submenu_text"},[e._v(e._s(r.description))])])],1)}),0):e._e()},ng=[],lg=l(ig,ag,ng,!1,null,null,null,null);const cg=lg.exports,pg={name:"ReportNavigationMenu",components:{ReportNavigationSubmenu:cg},mixins:[ts],props:{menu_id:String,menu_data:Array,nav_class:String,route_title:String,with_arrows:{default:!1,type:Boolean}},provide(){return{sharedState:this.sharedState}},data(){return{sharedState:{active:null}}},methods:{hasActiveChild(s){return s.length==0?!1:s.find(e=>e.to==this.$route.path)},itemClasses(s){return this.hasActiveChild(s.children)?"active":""},menuToggle(s){this.sharedState.active!=s?this.sharedState.active=s:this.sharedState.active=null},closeMenu(){this.sharedState.active=null}}};var dg=function(){var e=this,t=e._self._c;return t("div",[t("ul",{directives:[{name:"on-clickaway",rawName:"v-on-clickaway",value:e.closeMenu,expression:"closeMenu"}],class:e.nav_class},[e.with_arrows?t("li",{staticClass:"dashicons-before dashicons-arrow-left-alt2",attrs:{"data-direction":"left"}}):e._e(),e._l(e.menu_data,function(r,o){return t("li",{key:e.menu_id+o},[r.children.length==0?t("router-link",{class:r.class,attrs:{to:r.to},nativeOn:{click:function(n){return e.closeMenu()}}},[e._v(" "+e._s(r.label)+" ")]):t("div",{class:e.itemClasses(r),on:{click:function(n){return e.menuToggle(o)}}},[e._t("menuToggler",function(){return[t("button",[e._v(" "+e._s(r.label)+" "),t("span",{staticClass:"caret"},[t("svg",{attrs:{width:"10",height:"6",viewBox:"0 0 10 6",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M9 1L5 5L1 1",stroke:"#8EA4B4","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])])])]}),t("ReportNavigationSubmenu",{attrs:{items:r.children,menu_id:e.menu_id,submenu_index:o}})],2)],1)}),e.with_arrows?t("li",{staticClass:"dashicons-before dashicons-arrow-right-alt2",attrs:{"data-direction":"right"}}):e._e()],2)])},hg=[],gg=l(pg,dg,hg,!1,null,null,null,null);const ug=gg.exports,{__:y}=wp.i18n,mg={MENU:[{id:"overview",label:y("Overview","google-analytics-for-wordpress"),to:"/",children:[],class:"monsterinsights-navigation-tab-link"},{id:"traffic",label:y("Traffic","google-analytics-for-wordpress"),children:[{label:y("Overview","google-analytics-for-wordpress"),to:"/traffic-overview",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See how customers find your website.","google-analytics-for-wordpress")},{label:y("Technology","google-analytics-for-wordpress"),to:"/traffic-technology",children:[],class:"monsterinsights-navigation-submenu-link",description:y("Uncover the devices and browsers your visitors are utilizing.","google-analytics-for-wordpress")},{label:y("Landing Page Details","google-analytics-for-wordpress"),to:"/traffic-landing-pages",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See the first page visitors land when visiting your website.","google-analytics-for-wordpress")},{label:y("Campaigns","google-analytics-for-wordpress"),to:"/traffic-campaign",children:[],class:"monsterinsights-navigation-submenu-link",description:y("Easily measure the effectiveness of your marketing efforts.","google-analytics-for-wordpress")},{label:y("Source / Medium","google-analytics-for-wordpress"),to:"/traffic-source-medium",children:[],class:"monsterinsights-navigation-submenu-link",description:y("Details about referring traffic to your website.","google-analytics-for-wordpress")},{label:y("Social","google-analytics-for-wordpress"),to:"/traffic-social",children:[],class:"monsterinsights-navigation-submenu-link",description:y("Details about social traffic to your website.","google-analytics-for-wordpress")}],class:"monsterinsights-navigation-tab-link"},{id:"Publishers",label:y("Publishers","google-analytics-for-wordpress"),children:[{label:y("Overview","google-analytics-for-wordpress"),to:"/publishers",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See how visitors interact with your website.","google-analytics-for-wordpress")},{label:y("Pages Report","google-analytics-for-wordpress"),to:"/publishers-pages",children:[],class:"monsterinsights-navigation-submenu-link",description:y("The most popular pages on your website.","google-analytics-for-wordpress")},{label:y("Country Report","google-analytics-for-wordpress"),to:"/countries",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See which countries your visitors come from.","google-analytics-for-wordpress")}],class:"monsterinsights-navigation-tab-link"},{id:"search-console",label:y("Search Console","google-analytics-for-wordpress"),to:"search-console",children:[],class:"monsterinsights-navigation-tab-link"},{id:"ecommerce",label:y("eCommerce","google-analytics-for-wordpress"),children:[{label:y("Overview","google-analytics-for-wordpress"),to:"/ecommerce",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See how your store is performing.","google-analytics-for-wordpress")},{label:y("Coupons","google-analytics-for-wordpress"),to:"/ecommerce-coupons",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See the coupons and discounts being used on your website.","google-analytics-for-wordpress")},{label:y("Cart Abandonment","google-analytics-for-wordpress"),to:"/cart-abandonment",children:[],class:"monsterinsights-navigation-submenu-link",description:y("See which products are abandoned the most.","google-analytics-for-wordpress")},{label:y("Funnel","google-analytics-for-wordpress"),to:"/ecommerce-funnel",children:[],class:"monsterinsights-navigation-submenu-link",description:y("Visually measure how customers convert in your store.","google-analytics-for-wordpress")}],class:"monsterinsights-navigation-tab-link"},{id:"dimensions",label:y("Dimensions","google-analytics-for-wordpress"),to:"/dimensions",children:[],class:"monsterinsights-navigation-tab-link"},{id:"forms",label:y("Forms","google-analytics-for-wordpress"),to:"/forms",children:[],class:"monsterinsights-navigation-tab-link"},{id:"realtime",label:y("Realtime ","google-analytics-for-wordpress"),to:"/real-time",children:[],class:"monsterinsights-navigation-tab-link"},{id:"site-speed",label:y("Site Speed","google-analytics-for-wordpress"),to:"/site-speed",children:[],class:"monsterinsights-navigation-tab-link"},{id:"media",label:y("Media","google-analytics-for-wordpress"),to:"/media",children:[],class:"monsterinsights-navigation-tab-link"},{id:"exceptions",label:y("Exceptions","google-analytics-for-wordpress"),to:"/exceptions",children:[],class:"monsterinsights-navigation-tab-link"}]},_g=null,fg=null;var vg=l(mg,_g,fg,!1,null,null,null,null);const wg=vg.exports,{__:yg,sprintf:St}=wp.i18n,Tt=document.querySelector('a.current[href="admin.php?page=monsterinsights_reports"]'),We={"user-journey-report":document.querySelector('[href*="monsterinsights_reports#/user-journey-report"]'),"ai-insights":document.querySelector('[href*="monsterinsights_reports#/ai-insights"]'),"ai-insights-new-chat":document.querySelector('[href*="monsterinsights_reports#/ai-insights/chat"]'),"ai-insights-chat":document.querySelector('[href*="monsterinsights_reports#/ai-insights/chat"]')},Cg={name:"ReportsNavigation",components:{ReportNavigationMenu:ug,UserJourneyNavigation:us},data(){return{menu_id:"monsterinsights-top-menu",nav_open:!1}},computed:{...C({addons:"$_addons/addons",license:"$_license/license",license_network:"$_license/license_network",yearinreview_data:"$_reports/yearinreview_data"}),customNavComponent(){return this.$route.meta.customNav?this.$route.meta.customNav:""},hideReportNavigation(){return this.$route.meta.hideNav===!0},menuData(){let s=wg.MENU;const e=s.find(t=>t.to==="year-in-review");return this.isYearInReviewVisible()&&typeof e!="object"&&s.push({label:this.textYearInReview,to:"year-in-review",children:[],class:"monsterinsights-navigation-tab-link year-in-review"}),s},textYearInReview(){return St(yg("%s Year in Review","google-analytics-for-wordpress"),this.yearinreview_data.report_year)},routeTitle(){return this.$route.meta.title?this.$route.meta.title:!1},navClass(){let s="monsterinsights-main-navigation monsterinsights-reports-navigation";return this.nav_open&&(s+=" monsterinsights-main-navigation-open"),s},buttonIconClass(){let s="monstericon-arrow";return this.nav_open&&(s+=" monstericon-down"),s},triggerClass(){let s="monsterinsights-mobile-nav-trigger";return this.nav_open&&(s+=" monsterinsights-mobile-nav-trigger-open"),s},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type},routeName(){return this.$route.name}},methods:{isAddonActive(s){return this.addons[s]?this.addons[s].active:!1},isYearInReviewVisible(){return this.yearinreview_data.show_report},sprintf:St,handleRouteChange(s){for(const e in We)if(s===e){this.removeActiveClasses();const t=We[e];t&&t.parentElement.classList.add("current");break}},removeActiveClasses(){Object.values(We).forEach(s=>{s&&s.parentElement.classList.remove("current")}),Tt&&Tt.parentElement.classList.remove("current")}},watch:{$route(s){this.handleRouteChange(s.name),this.nav_open=!1}},created(){this.handleRouteChange(this.routeName)}};var bg=function(){var e=this,t=e._self._c;return t("div",[e.hideReportNavigation?e._e():t("nav",[t("button",{class:e.triggerClass,on:{click:function(r){e.nav_open=!e.nav_open}}},[e.routeTitle?t("span",{staticClass:"monsterinsights-route-title",domProps:{textContent:e._s(e.routeTitle)}}):e._e(),t("i",{class:e.buttonIconClass})]),t("ReportNavigationMenu",{class:e.navClass,attrs:{menu_id:"monsterinsights-top-menu",menu_data:e.menuData,nav_open:!1,route_title:e.routeTitle}})],1),e.customNavComponent?t("nav",[t(e.customNavComponent,{tag:"component"})],1):e._e()])},xg=[],kg=l(Cg,bg,xg,!1,null,null,null,null);const $g=kg.exports,{__:$e,sprintf:Rg}=wp.i18n,Pg={name:"ReportNoAuth",components:{LaunchWizardButton:ks},data(){return{text_no_auth:$e("You must connect with MonsterInsights before you can view reports.","google-analytics-for-wordpress"),text_auth_label:$e("MonsterInsights makes it effortless for you to connect your site with Google Analytics and see reports right here in the WordPress dashboard.","google-analytics-for-wordpress"),text_wizard:$e("Launch Setup Wizard","google-analytics-for-wordpress"),text_ask_webmaster:$e("Please ask your webmaster to connect MonsterInsights to Google Analytics.","google-analytics-for-wordpress"),update_settings:this.$mi.update_settings,text_onboarding_note:Rg($e("Note: You will be transfered to %1$s.com to complete the setup wizard.","google-analytics-for-wordpress"),"MonsterInsights")}}};var Dg=function(){var e=this,t=e._self._c;return t("div",{staticClass:"monsterinsights-not-authenticated-notice"},[t("h3",{domProps:{textContent:e._s(e.text_no_auth)}}),t("div",{staticClass:"monsterinsights-settings-input monsterinsights-settings-input-authenticate"},[t("span",{staticClass:"monsterinsights-dark",domProps:{textContent:e._s(e.text_auth_label)}}),e.update_settings?t("div",[t("launch-wizard-button",{attrs:{"button-class":"monsterinsights-button-alt monsterinsights-button","button-text":e.text_wizard}}),t("p",{staticClass:"monsterinsights-disclaimer-note",domProps:{textContent:e._s(e.text_onboarding_note)}})],1):t("div",[t("p",{staticClass:"monsterinsights-dark"},[t("strong",{domProps:{textContent:e._s(e.text_ask_webmaster)}})])])])])},Sg=[],Tg=l(Pg,Dg,Sg,!1,null,null,null,null);const Mg=Tg.exports,Lg={name:"ModuleReports",router:og,components:{TheQuickLinks:ms,ReportReAuth:ps,ReportNoAuth:Mg,TheAppNotices:_s,ReportsNavigation:$g,TheAppNavigation:fs,TheAppHeader:vs,Notifications:ws},computed:{...C({blocked:"$_app/blocked",blur:"$_reports/blur",noauth:"$_reports/noauth",reauth:"$_reports/reauth",isLoaded:"$_reports/isLoaded"}),route(){return this.$route.name},mainClass(){let s="monsterinsights-admin-page monsterinsights-reports-page";return this.blur&&(s+=" monsterinsights-blur"),s}},beforeCreate(){const s="$_reports";s in this.$store._modules.root._children||this.$store.registerModule(s,ls)},created(){this.updateSelectedReport(this.route),this.$store.dispatch("$_notifications/getNotifications")},watch:{$route(s){this.updateSelectedReport(s.name)}},methods:{updateSelectedReport(s){this.$mi.authed||(this.$store.commit("$_reports/ENABLE_BLUR"),this.$store.commit("$_reports/ENABLE_NOAUTH")),this.$store.commit("$_reports/UPDATE_ACTIVE_REPORT",s)}}};var Eg=function(){var e=this,t=e._self._c;return t("div",{class:e.mainClass},[t("the-app-header",[t("Notifications")],1),t("the-app-navigation",[t("reports-navigation")],1),t("the-app-notices"),t("router-view"),e.blocked?t("div",{staticClass:"monsterinsights-blocked"}):e._e(),e.noauth?t("report-no-auth"):e._e(),e.reauth?t("report-re-auth"):e._e(),t("the-quick-links")],1)},Ag=[],Fg=l(Lg,Eg,Ag,!1,null,null,null,null);const Ng=Fg.exports,{__:i}=wp.i18n,Ig={install(s){s.prototype.$miOverviewTooltips=function(t){if(!t.title)return document.querySelectorAll(".monsterinsights-line-chart-tooltip").forEach(function(O){O.style.opacity=0}),!1;let r=t.title[0],o=t.title[1],n=parseInt(t.title[2]),a=t.title[3],d=t.title[4],p=t.title[5]?t.title[5]:[],m=document.getElementById("monsterinsights-chartjs-line-"+d+"-tooltip");if(m===null&&(m=document.createElement("div"),document.body.appendChild(m),m.setAttribute("id","monsterinsights-chartjs-line-"+d+"-tooltip"),m.classList.add("monsterinsights-line-chart-tooltip")),!t.opacity){m.style.opacity=0;return}m.classList.remove("above"),m.classList.remove("below"),m.classList.remove("no-transform"),t.yAlign?m.classList.add(t.yAlign):m.classList.add("no-transform");let b="";n===0?b+="0%":n>0?b+='<span class="monsterinsights-green"><span class="monsterinsights-arrow monsterinsights-up"></span>'+n+"%</span>":b+='<span class="monsterinsights-red"><span class="monsterinsights-arrow monsterinsights-down"></span>'+Math.abs(n)+"%</span>";let k='<div class="monsterinsights-reports-overview-datagraph-tooltip-container monsterinsights-reports-tooltip">';k+='<div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+r+"</div>",k+='<div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+o+"</div>",k+='<div class="monsterinsights-reports-overview-datagraph-tooltip-descriptor">'+a+"</div>",k+='<div class="monsterinsights-reports-overview-datagraph-tooltip-trend">'+b+"</div>",p&&(k+="<hr>",p.forEach(H=>{H.title&&(k+=H.title+"<br>")})),k+="</div>",m.innerHTML=k;const U=this._chart.canvas.getBoundingClientRect();m.style.opacity="1",m.style.left=U.left+window.pageXOffset+t.x+"px",m.style.top=U.top+window.pageYOffset+t.y+"px",m.style.fontFamily="Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif;",m.style.fontSize=t.fontSize,m.style.fontStyle=t._fontStyle,m.style.padding=t.yPadding+"px "+t.xPadding+"px",m.style.zIndex=99999,m.style.pointerEvents="none"},s.prototype.$miPieTooltips=function(t){if(!t.title)return document.querySelectorAll(".monsterinsights-pie-chart-tooltip").forEach(function(b){b.style.opacity=0}),!1;let r=t.title[0],o=t.title[1],n=t.title[2],a=document.getElementById("monsterinsights-chartjs-pie-"+n+"-tooltip");a===null&&(a=document.createElement("div"),document.body.appendChild(a),a.setAttribute("id","monsterinsights-chartjs-pie-"+n+"-tooltip")),a.classList.remove("above"),a.classList.remove("below"),a.classList.remove("no-transform"),t.yAlign?a.classList.add(t.yAlign):a.classList.add("no-transform");let d='<div class="monsterinsights-reports-overview-datagraph-tooltip-container monsterinsights-reports-tooltip">';d+='<div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+r+"</div>",d+='<div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+o+"%</div>",d+="</div>",a.innerHTML=d;let p=0;if(t.yAlign){let m=0;t.caretHeight&&(m=t.caretHeight),t.yAlign==="above"?p=t.y-m-t.caretPadding:p=t.y+m+t.caretPadding}a.style.opacity=1,a.style.left=t.x-50+"px",a.style.top=p-40+"px",a.style.padding=t.yPadding+"px "+t.xPadding+"px",a.style.zIndex="99999"},s.prototype.$miyearInReviewTooltips=function(t){if(!t.title)return document.querySelectorAll(".monsterinsights-line-chart-tooltip").forEach(function(b){b.style.opacity=0}),!1;let r=t.title[0],o=t.title[1],n=t.title[4],a=document.getElementById("monsterinsights-chartjs-line-"+n+"-tooltip");if(a===null&&(a=document.createElement("div"),document.body.appendChild(a),a.setAttribute("id","monsterinsights-chartjs-line-"+n+"-tooltip"),a.classList.add("monsterinsights-line-chart-tooltip")),!t.opacity){a.style.opacity=0;return}a.classList.remove("above"),a.classList.remove("below"),a.classList.remove("no-transform"),t.yAlign?a.classList.add(t.yAlign):a.classList.add("no-transform");let d='<div class="monsterinsights-reports-overview-datagraph-tooltip-container monsterinsights-reports-tooltip">';d+='<div class="monsterinsights-reports-overview-datagraph-tooltip-title">'+r+"</div>",d+='<div class="monsterinsights-reports-overview-datagraph-tooltip-number">'+o+"</div>",d+="</div>",a.innerHTML=d;const p=this._chart.canvas.getBoundingClientRect();a.style.opacity="1",a.style.left=p.left+window.pageXOffset+t.x+"px",a.style.top=p.top+window.pageYOffset+t.y+"px",a.style.fontFamily="Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif;",a.style.fontSize=t.fontSize,a.style.fontStyle=t._fontStyle,a.style.padding=t.yPadding+"px "+t.xPadding+"px",a.style.zIndex=99999,a.style.pointerEvents="none"},s.prototype.$mi_loading_toast=function(t,r){s.prototype.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:t||i("Refreshing Report","google-analytics-for-wordpress"),html:r||i("Loading new report data...","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){s.prototype.$swal.showLoading()}})},s.prototype.$mi_error_toast=function(t){let{icon:r="error",customContainerClass:o="monsterinsights-swal",allowOutsideClick:n=!1,allowEscapeKey:a=!1,allowEnterKey:d=!1,title:p=i("Error","google-analytics-for-wordpress"),html:m=i("Please try again.","google-analytics-for-wordpress"),confirmButtonText:b="OK",showCancelButton:k=!1,cancelButtonText:U="Cancel",footer:H=!1,willClose:O=()=>{}}=t;return s.prototype.$swal({icon:r,customClass:{container:o},allowOutsideClick:n,allowEscapeKey:a,allowEnterKey:d,title:p,html:m,footer:H,showCancelButton:k,cancelButtonText:U,confirmButtonText:b,didOpen:function(){s.prototype.$swal.hideLoading()},willClose:O})},s.prototype.$mi_success_toast=function(t){let{toast:r=!0,position:o="top-end",showConfirmButton:n=!1,icon:a="success",timer:d=3e3,showCloseButton:p=!0,title:m=i("Settings Updated","google-analytics-for-wordpress"),showCancelButton:b=!1,confirmButtonText:k="",cancelButtonText:U="",text:H=""}=t;return s.prototype.$swal({customClass:{container:"monsterinsights-swal"},toast:r,position:o,showConfirmButton:n,icon:a,showCloseButton:p,title:m,timer:d,showCancelButton:b,confirmButtonText:k,cancelButtonText:U,text:H,showClass:{popup:""},hideClass:{popup:""}})},s.prototype.$mi_get_upsell_content=function(t){let r={};const o={overview:{mainheading:i("Publishers Report","google-analytics-for-wordpress"),title:i("Improve Your Conversion Rate With Insights Into Which Content Works Best.","google-analytics-for-wordpress"),subtitle:i("Stop guessing about what content your visitors are interested in. MonsterInsights Publisher Report shows you exactly which content gets the most visits, so you can analyze and optimize it for higher conversions.","google-analytics-for-wordpress"),features:[i("Top Landing Pages","google-analytics-for-wordpress"),i("Top Affilliate Links","google-analytics-for-wordpress"),i("Top Exit Pages","google-analytics-for-wordpress"),i("Top Download Links","google-analytics-for-wordpress"),i("Top Outbound Links","google-analytics-for-wordpress"),i("Scroll Depth","google-analytics-for-wordpress")]},publisher:{mainheading:i("Publishers Report","google-analytics-for-wordpress"),title:i("Improve Your Conversion Rate With Insights Into Which Content Works Best.","google-analytics-for-wordpress"),features:[i("Top Landing Pages","google-analytics-for-wordpress"),i("Top Affilliate Links","google-analytics-for-wordpress"),i("Top Exit Pages","google-analytics-for-wordpress"),i("Top Download Links","google-analytics-for-wordpress"),i("Top Outbound Links","google-analytics-for-wordpress"),i("Scroll Depth","google-analytics-for-wordpress")]},countries:{mainheading:i("Country / Region Report","google-analytics-for-wordpress"),title:i("View the most popular countries and regions on your website","google-analytics-for-wordpress"),features:[i("Top Page Views","google-analytics-for-wordpress"),i("Top Countries","google-analytics-for-wordpress"),i("Top Regions","google-analytics-for-wordpress"),i("Bounce Rates","google-analytics-for-wordpress"),i("Engaged Sessions","google-analytics-for-wordpress"),i("Purchases","google-analytics-for-wordpress")]},ecommerce:{mainheading:i("eCommerce Report","google-analytics-for-wordpress"),title:i("Increase Sales and Make More Money With Enhanced eCommerce Insights.","google-analytics-for-wordpress"),features:[i("10+ eCommerce Integrations","google-analytics-for-wordpress"),i("Average Order Value","google-analytics-for-wordpress"),i("Total Revenue","google-analytics-for-wordpress"),i("Sessions to Purchase","google-analytics-for-wordpress"),i("Top Conversion Sources","google-analytics-for-wordpress"),i("Top Products","google-analytics-for-wordpress"),i("Number of Transactions","google-analytics-for-wordpress"),i("Time to Purchase","google-analytics-for-wordpress")]},ecommerce_product_sales:{mainheading:i("Product Sales Report","google-analytics-for-wordpress"),title:i("See which products are the most popular with your customers.","google-analytics-for-wordpress"),features:[i("Products","google-analytics-for-wordpress"),i("Quantity Counts","google-analytics-for-wordpress"),i("Easy Filtering","google-analytics-for-wordpress"),i("PDF Exporting","google-analytics-for-wordpress")]},ecommerce_funnel:{mainheading:i("eCommerce Funnel Report","google-analytics-for-wordpress"),title:i("Visually see how customers move through your eCommerce website to make a purchase.","google-analytics-for-wordpress"),features:[i("Step by Step Funnel View","google-analytics-for-wordpress"),i("Conversion Rates","google-analytics-for-wordpress"),i("Breakdowns by Devices","google-analytics-for-wordpress"),i("Abandonment Rates","google-analytics-for-wordpress"),i("Breakdowns by Channel","google-analytics-for-wordpress"),i("Date Picker","google-analytics-for-wordpress")]},ecommerce_coupons:{mainheading:i("Coupon Report","google-analytics-for-wordpress"),title:i("Learn  which coupons are generating the most sales for your store.","google-analytics-for-wordpress"),features:[i("Coupons","google-analytics-for-wordpress"),i("Average Order Value","google-analytics-for-wordpress"),i("Total Revenue","google-analytics-for-wordpress"),i("Sessions to Purchase","google-analytics-for-wordpress"),i("Top Conversion Sources","google-analytics-for-wordpress"),i("Top Products","google-analytics-for-wordpress"),i("Number of Transactions","google-analytics-for-wordpress"),i("Time to Purchase","google-analytics-for-wordpress")]},cart_abandonment:{mainheading:i("Cart Abandonment Report","google-analytics-for-wordpress"),title:i("See which products are left the most so that that you can optimize and increase your sales.","google-analytics-for-wordpress"),features:[i("Products Abandoned","google-analytics-for-wordpress"),i("Revenue Abandoned","google-analytics-for-wordpress"),i("Quantity Abandoned","google-analytics-for-wordpress"),i("Checkout Abandonment","google-analytics-for-wordpress"),i("Cart Abandonment","google-analytics-for-wordpress"),i("Day-by-day Breakdowns","google-analytics-for-wordpress")]},dimensions:{mainheading:i("Dimensions Report","google-analytics-for-wordpress"),title:i("Increase Engagement and Unlock New Insights About Your Site.","google-analytics-for-wordpress"),features:[i("Author Tracking","google-analytics-for-wordpress"),i("User ID Tracking","google-analytics-for-wordpress"),i("Post Types","google-analytics-for-wordpress"),i("Tag Tracking","google-analytics-for-wordpress"),i("Categories","google-analytics-for-wordpress"),i("SEO Scores","google-analytics-for-wordpress"),i("Publish Times","google-analytics-for-wordpress"),i("Focus Keywords","google-analytics-for-wordpress")]},forms:{mainheading:i("Forms Report","google-analytics-for-wordpress"),title:i("Track Every Type of Web Form and Gain Visibility Into Your Customer Journey.","google-analytics-for-wordpress"),columns:1,features:[i("Conversion Counts","google-analytics-for-wordpress"),i("Impression Counts","google-analytics-for-wordpress"),i("Conversion Rates","google-analytics-for-wordpress")]},queries:{mainheading:i("Search Console Report","google-analytics-for-wordpress"),title:i("See Exactly How Visitors Find Your Website From Google.","google-analytics-for-wordpress"),columns:1,features:[i("Top Google Search Terms","google-analytics-for-wordpress"),i("Number of Clicks","google-analytics-for-wordpress"),i("Click-through Ratio","google-analytics-for-wordpress"),i("Average Results Position","google-analytics-for-wordpress")]},realtime:{mainheading:i("Realtime Report","google-analytics-for-wordpress"),title:i("See Who And What is Happening on Your Website in Realtime.","google-analytics-for-wordpress"),features:[i("Top Page Views","google-analytics-for-wordpress"),i("Current Active Users","google-analytics-for-wordpress"),i("Top Referral Sources","google-analytics-for-wordpress"),i("Pageviews Per Minute","google-analytics-for-wordpress"),i("Top Countries","google-analytics-for-wordpress"),i("Top Cities","google-analytics-for-wordpress")]},sitespeed:{mainheading:i("Site Speed Report","google-analytics-for-wordpress"),title:i("Improve Your User Experience and Improve Search Engine Rankings.","google-analytics-for-wordpress"),features:[i("Overall Site Speed Score","google-analytics-for-wordpress"),i("Server Response Times","google-analytics-for-wordpress"),i("Mobile and Desktop Scores","google-analytics-for-wordpress"),i("First Contentful Paint","google-analytics-for-wordpress"),i("Automatic Recommendations","google-analytics-for-wordpress"),i("Total Blocking Time","google-analytics-for-wordpress"),i("On-Demand Audits","google-analytics-for-wordpress"),i("Time to Interactive","google-analytics-for-wordpress")]},media:{mainheading:i("Media Report","google-analytics-for-wordpress"),title:i("Easily See Which Videos Are Most Popular.","google-analytics-for-wordpress"),features:[i("Videos Plays, Average Duration, and Completions","google-analytics-for-wordpress"),i("Works with YouTube, Vimeo, and HTML 5 Videos","google-analytics-for-wordpress"),i("Compare stats over time","google-analytics-for-wordpress")]},userjourney:{mainheading:i("User Journey Report","google-analytics-for-wordpress"),title:i("See the exact steps your customers took to purchase, and how they arrived at your website.","google-analytics-for-wordpress"),features:[i("Step-by-Step Purchase Paths","google-analytics-for-wordpress"),i("Purchase Dates","google-analytics-for-wordpress"),i("Campaign Filtering","google-analytics-for-wordpress"),i("Order Totals","google-analytics-for-wordpress"),i("Medium & Source Filtering","google-analytics-for-wordpress"),i("Average Steps to Purchase","google-analytics-for-wordpress")]},traffic:{mainheading:i("Traffic Report","google-analytics-for-wordpress"),title:i("Learn how visitors arrive to your website and which are the most engaged or profitable.","google-analytics-for-wordpress"),features:[i("Channel Breakdowns","google-analytics-for-wordpress"),i("Session Counts","google-analytics-for-wordpress"),i("Pages/Session","google-analytics-for-wordpress"),i("Conversion Rate","google-analytics-for-wordpress"),i("Revenue","google-analytics-for-wordpress"),i("Engaged Sessions","google-analytics-for-wordpress")]},traffic_landing_pages:{mainheading:i("Landing Page Report","google-analytics-for-wordpress"),title:i("Find out which pages are making your first impression.","google-analytics-for-wordpress"),features:[i("Page Name","google-analytics-for-wordpress"),i("Pages/Session","google-analytics-for-wordpress"),i("Sessions","google-analytics-for-wordpress"),i("Purchases","google-analytics-for-wordpress"),i("Session Counts","google-analytics-for-wordpress"),i("Conversion Rates","google-analytics-for-wordpress")]},traffic_technology:{mainheading:i("Technology Report","google-analytics-for-wordpress"),title:i("Optimize your website for your top devices and browsers.","google-analytics-for-wordpress"),features:[i("Browser Names","google-analytics-for-wordpress"),i("Device Types","google-analytics-for-wordpress"),i("Export to PDF","google-analytics-for-wordpress"),i("Date Filtering","google-analytics-for-wordpress")]},traffic_campaign:{mainheading:i("Campaigns Report","google-analytics-for-wordpress"),title:i("Measure how effective your marketing campaigns are performing.","google-analytics-for-wordpress"),features:[i("Campaign Names","google-analytics-for-wordpress"),i("Easy Filtering","google-analytics-for-wordpress"),i("Pages / Session","google-analytics-for-wordpress"),i("Purchases","google-analytics-for-wordpress"),i("eCommerce Conversion Rates","google-analytics-for-wordpress"),i("Revenue","google-analytics-for-wordpress")]},traffic_source_medium:{mainheading:i("Source and Medium Report","google-analytics-for-wordpress"),title:i("Uncover which traffic sources are creating engagement and sales from your website.","google-analytics-for-wordpress"),features:[i("Source Name","google-analytics-for-wordpress"),i("Pages / Sessions","google-analytics-for-wordpress"),i("Session Counts","google-analytics-for-wordpress"),i("Purchases","google-analytics-for-wordpress"),i("Easy Filtering","google-analytics-for-wordpress"),i("Conversion Rates","google-analytics-for-wordpress")]},engagement_overview:{mainheading:i("Engagement Report","google-analytics-for-wordpress"),title:i("Improve Your Conversion Rate With Insights Into Which Content Works Best.","google-analytics-for-wordpress"),features:[i("Top Landing Pages","google-analytics-for-wordpress"),i("Top Affilliate Links","google-analytics-for-wordpress"),i("Top Exit Pages","google-analytics-for-wordpress"),i("Top Download Links","google-analytics-for-wordpress"),i("Top Outbound Links","google-analytics-for-wordpress"),i("Scroll Depth","google-analytics-for-wordpress")]},engagement_pages:{mainheading:i("Pages Report","google-analytics-for-wordpress"),title:i("View the most popular pages on your website.","google-analytics-for-wordpress"),features:[i("Page Name","google-analytics-for-wordpress"),i("New Sessions","google-analytics-for-wordpress"),i("Sessions","google-analytics-for-wordpress"),i("Bounce Rate","google-analytics-for-wordpress"),i("Engaged Sessions","google-analytics-for-wordpress"),i("Exits","google-analytics-for-wordpress")]},traffic_social:{mainheading:i("Social Media Report","google-analytics-for-wordpress"),title:i("See Which Social Media Networks are Making You Money.","google-analytics-for-wordpress"),features:[i("Social Networks","google-analytics-for-wordpress"),i("Sessions","google-analytics-for-wordpress"),i("Bounce Rate","google-analytics-for-wordpress"),i("Purchases","google-analytics-for-wordpress"),i("Revenue","google-analytics-for-wordpress"),i("Conversion Rate","google-analytics-for-wordpress")]},exceptions:{mainheading:i("Exceptions Report","google-analytics-for-wordpress"),title:i("Be Notified About Important Website Events","google-analytics-for-wordpress"),features:[i("Traffic Changes","google-analytics-for-wordpress"),i("Conversion Rate Changes","google-analytics-for-wordpress"),i("Revenue Changes","google-analytics-for-wordpress"),i("Landing Page Changes","google-analytics-for-wordpress"),i("Engagement Rates","google-analytics-for-wordpress"),i("Email Notifications","google-analytics-for-wordpress"),i("Campaign Alerts","google-analytics-for-wordpress"),i("Site Notes Integration","google-analytics-for-wordpress")]},ai_insights:{mainheading:i("AI Insights","google-analytics-for-wordpress"),title:i("Get incredible new insights about your website with the power of AI.","google-analytics-for-wordpress"),features:[i("On Demand Insights","google-analytics-for-wordpress"),i("Traffic Insights","google-analytics-for-wordpress"),i("Visitor Insights","google-analytics-for-wordpress"),i("Campaign Insights","google-analytics-for-wordpress")]},ai_insights_chat:{mainheading:i("Conversations AI","google-analytics-for-wordpress"),title:i("Chat with your website’s analytics, no experience needed.","google-analytics-for-wordpress"),features:[i("Get insights about your website by simply asking","google-analytics-for-wordpress"),i("Ask for specific metrics or trends over time","google-analytics-for-wordpress"),i("Understand your marketing ROI","google-analytics-for-wordpress"),i("Create charts and graphs for easy analysis","google-analytics-for-wordpress"),i("Pin and save your conversations for later use","google-analytics-for-wordpress")]}};return o[t]&&(r=o[t]),r},s.prototype.$mi_intervals=function(){return cs},s.directive("icon",{bind(t,{value:r}){e(t,r)},update(t,{value:r,oldValue:o}){r!==o&&e(t,r)}});function e(t,r){const o=ys[r];if(o===void 0){t.innerHTML="";return}t.classList.contains("icon--tab")||t.classList.add("icon--tab"),t.innerHTML=`<svg viewBox="${o.viewBox}" fill="none">${o.content}</svg>`}}};const Mt=document.getElementById("monsterinsights-reports");w.config.productionTip=!1;Mt&&(ss({ctrl:!0}),w.use(Lt),w.use(rs),w.use(os,{defaultTemplate:'<div class="monsterinsights-tooltip" role="tooltip"><div class="monsterinsights-tooltip-arrow"></div><div class="monsterinsights-tooltip-inner"></div></div>',defaultArrowSelector:".monsterinsights-tooltip-arrow, .monsterinsights-tooltip__arrow",defaultInnerSelector:".monsterinsights-tooltip-inner, .monsterinsights-tooltip__inner"}),w.use(tt),w.use(Ig),w.use(ds),w.use(tt),w.directive("click-outside",{bind:function(s,e,t){s.clickOutsideEvent=function(r){s===r.target||s.contains(r.target)||t.context[e.expression](r)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}}),w.use(Cs,{componentPrefix:"monsterinsights",classPrefix:"monsterinsights",textDomain:"google-analytics-for-wordpress"}),new w({store:fe,mounted:()=>{fe.dispatch("$_app/init"),fe.dispatch("$_addons/getAddons"),fe.dispatch("$_settings/getSettings"),w.prototype.$isPro()&&fe.dispatch("$_license/getLicense")},render:s=>s(Ng)}).$mount(Mt));
