import{n as p,m as v,d as H,b as E,V as u,a as R,i as U,e as W,p as F}from"./chunks/vendor-0853f02f.min.js";import{c as D,b as G,a as Z,S as L,f as N,d as V,e as X,W as Q}from"./chunks/WelcomeOverlay-15ad78f3.min.js";import{S as K}from"./chunks/SettingsInfoTooltip-4f19e286.min.js";import{a as Y,S as J,M as j,s as h}from"./chunks/index-468bfca9.min.js";import{L as q}from"./chunks/LaunchWizardButton-0eeb5248.min.js";import"./chunks/SlideDownUp-22dab176.min.js";const tt={name:"OnboardingContentHeader",props:{title:String,subtitle:String}};var et=function(){var t=this,e=t._self._c;return e("header",[e("h2",{domProps:{innerHTML:t._s(t.title)}}),t.subtitle?e("p",{staticClass:"subtitle",domProps:{innerHTML:t._s(t.subtitle)}}):t._e()])},st=[],ot=p(tt,et,st,!1,null,null,null,null);const b=ot.exports,{__:w,sprintf:nt}=wp.i18n,at={name:"OnboardingStepWelcome",components:{SettingsInputRadio:D,OnboardingContentHeader:b},data(){return{text_header_title:w("Welcome to MonsterInsights!","google-analytics-for-wordpress"),text_header_subtitle:w("Let's get you set up.","google-analytics-for-wordpress"),text_save:w("Save and Continue","google-analytics-for-wordpress"),text_category_label:w("Which category best describes your website?","google-analytics-for-wordpress"),text_category_sublabel:w("We will recommend the optimal settings for MonsterInsights based on your choice.","google-analytics-for-wordpress"),options:[{value:"business",label:w("Business Website","google-analytics-for-wordpress")},{value:"publisher",label:nt(w("Publisher %1$s(Blog)%2$s","google-analytics-for-wordpress"),"<small>","</small>")},{value:"ecommerce",label:w("Ecommerce","google-analytics-for-wordpress")}]}},methods:{handleSubmit(){this.$router.push(this.$wizard_steps[1])}}};var it=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-step-welcome"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("div",{staticClass:"monsterinsights-separator"}),e("form",{on:{submit:function(o){return o.preventDefault(),t.handleSubmit.apply(null,arguments)}}},[e("div",{staticClass:"monsterinsights-form-row"},[e("div",{staticClass:"monsterinsights-form-label"},[e("label",{domProps:{textContent:t._s(t.text_category_label)}}),e("p",{staticClass:"monsterinsights-description",domProps:{textContent:t._s(t.text_category_sublabel)}})]),e("settings-input-radio",{attrs:{name:"site_type",options:t.options,auth_disabled:!1}})],1),e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-form-row monsterinsights-form-buttons"},[e("button",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",attrs:{type:"submit",name:"next_step"},domProps:{textContent:t._s(t.text_save)}})])])])],1)},rt=[],lt=p(at,it,rt,!1,null,null,null,null);const ct=lt.exports,{__:dt}=wp.i18n,ut={name:"OnboardingLicense",components:{SettingsInputLicense:G},computed:{...v({license:"$_license/license",license_network:"$_license/license_network",auth:"$_auth/auth"})},data(){return{text_license_label:dt("License Key","google-analytics-for-wordpress")}}};var gt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-license-lite"},[e("div",{staticClass:"monsterinsights-separator"}),e("label",{domProps:{textContent:t._s(t.text_license_label)}}),e("settings-input-license")],1)},pt=[],mt=p(ut,gt,pt,!1,null,null,null,null);const ht=mt.exports,{__:g,sprintf:_t}=wp.i18n,ft={name:"OnboardingAuthenticate",components:{SettingsNetworkNotice:Z},props:{label:String,description:String},data(){return{is_network:this.$mi.network,text_button_connect:g("Connect MonsterInsights","google-analytics-for-wordpress"),text_button_reconnect:g("Reconnect MonsterInsights","google-analytics-for-wordpress"),text_website_profile:g("Website profile","google-analytics-for-wordpress"),text_active_profile:g("Active profile","google-analytics-for-wordpress"),text_auth_network:g("Your website profile has been set at the network level of your WordPress Multisite.","google-analytics-for-wordpress"),text_auth_network_2:g("If you would like to use a different profile for this subsite, you can authenticate below.","google-analytics-for-wordpress"),text_skip:g("Skip and Keep Connection","google-analytics-for-wordpress"),text_manual_v4_label:g("Manually enter your GA4 Measurement ID","google-analytics-for-wordpress"),text_manual_v4_description:g("Warning: If you use a manual GA4 Measurement ID, you won't be able to use any of the reporting and some of the tracking features. Your UA code should look like G-XXXXXXXXXX where the X's are combination of numbers and letters.","google-analytics-for-wordpress"),text_v4_measurement_protocol:g("Measurement Protocol API Secret","google-analytics-for-wordpress"),text_v4_measurement_protocol_description:_t(g("The Measurement Protocol API secret allows your site to send tracking data to Google Analytics. To retrieve your Measurement Protocol API Secret, follow %1$sthis guide%2$s.","google-analytics-for-wordpress"),'<a href="'+this.$getUrl("dual-tracking","settings","https://www.monsterinsights.com/docs/how-to-create-your-measurement-protocol-api-secret-in-ga4/")+'" target="_blank">',"</a>"),has_error:!1,has_v4_error:!1}},computed:{...v({license:"$_license/license",license_network:"$_license/license_network",auth:"$_auth/auth"}),iconClass(){let s="monstericon-arrow";return this.showButtons&&(s+=" monstericon-down"),s},measurement_protocol_secret(){return this.is_network?this.auth.network_measurement_protocol_secret:this.auth.measurement_protocol_secret}},methods:{submitForm:function(){this.$emit("nextstep",!0)},doAuth:function(s){s.preventDefault();const t=this;this.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-loading"},icon:"info",title:g("Authenticating","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,loaderHtml:t.$getCustomLoaderHtml(),didOpen:function(){t.$swal.showLoading()}}),this.$store.dispatch("$_auth/doAuth",this.is_network).then(function(e){e.data.redirect?window.location=e.data.redirect:t.$swal({icon:"error",title:g("Error","google-analytics-for-wordpress"),html:e.data.message,confirmButtonText:g("Ok","google-analytics-for-wordpress")})})},doReAuth:function(s){s.preventDefault();const t=this;this.$swal({customClass:{container:"monsterinsights-swal monsterinsights-swal-loading"},icon:"info",title:g("Re-Authenticating","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,loaderHtml:t.$getCustomLoaderHtml(),didOpen:function(){t.$swal.showLoading()}}),this.$store.dispatch("$_auth/doReAuth",this.is_network).then(function(e){e.data.redirect?window.location=e.data.redirect:t.$swal({icon:"error",title:g("Error","google-analytics-for-wordpress"),html:e.data.message,confirmButtonText:g("Ok","google-analytics-for-wordpress")})})},processActionResponse:function(s){s.success===!1?(s.data.v4_error?this.has_v4_error=!0:s.data.error&&(this.has_v4_error=!1),this.has_error=s.data.error,this.$mi_error_toast({})):(this.has_v4_error=!1,this.has_error=!1,this.$mi_success_toast({}))},updateMeasurementProtocolSecret:function(s){this.$mi_saving_toast({}),this.has_error=!1,this.$store.dispatch("$_auth/updateMeasurementProtocolSecret",{value:s.target.value,network:this.is_network}).then(this.processActionResponse)}}};var wt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-settings-input monsterinsights-settings-input-authenticate"},[t.auth.network_v4&&!t.is_network&&!t.auth.v4?e("settings-network-notice",[e("strong",{domProps:{textContent:t._s(t.text_auth_network)}}),e("span",{domProps:{textContent:t._s(t.text_auth_network_2)}})]):t._e(),(t.is_network?t.auth.network_v4:t.auth.v4)?e("div",{staticClass:"monsterinsights-auth-info"},[e("span",{staticClass:"monsterinsights-dark",domProps:{textContent:t._s(t.text_website_profile)}}),e("p",[e("span",{domProps:{textContent:t._s(t.text_active_profile)}}),t._v(": "),e("span",{domProps:{textContent:t._s(t.is_network?t.auth.network_viewname:t.auth.viewname)}})]),e("div",{staticClass:"monsterinsights-separator"}),e("label",{staticClass:"monsterinsights-dark",attrs:{for:"monsterinsights-v4-measurement-protocol"},domProps:{textContent:t._s(t.text_v4_measurement_protocol)}}),e("p",{staticClass:"monsterinsights-field-description",domProps:{innerHTML:t._s(t.text_v4_measurement_protocol_description)}}),e("input",{staticClass:"monsterinsights-manual-ua",attrs:{id:"monsterinsights-v4-measurement-protocol",type:"text"},domProps:{value:t.measurement_protocol_secret},on:{change:t.updateMeasurementProtocolSecret}}),e("div",{staticClass:"monsterinsights-separator"}),e("div",[e("div",{staticClass:"monsterinsights-auth-actions"},[e("button",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",domProps:{textContent:t._s(t.text_button_reconnect)},on:{click:t.doReAuth}}),e("button",{staticClass:"monsterinsights-text-button monsterinsights-pull-right",attrs:{type:"submit"},on:{click:function(o){return o.preventDefault(),t.submitForm.apply(null,arguments)}}},[e("span",{domProps:{textContent:t._s(t.text_skip)}}),e("i",{staticClass:"monstericon-arrow-right"})])])])]):e("div",[e("span",{staticClass:"monsterinsights-dark",domProps:{textContent:t._s(t.label)}}),e("p",{domProps:{innerHTML:t._s(t.description)}}),e("button",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",domProps:{textContent:t._s(t.text_button_connect)},on:{click:t.doAuth}})])],1)},vt=[],yt=p(ft,wt,vt,!1,null,null,null,null);const bt=yt.exports,{__:_,sprintf:xt}=wp.i18n,At={name:"OnboardingStepWelcome",components:{OnboardingAuthenticate:bt,OnboardingLicense:ht,OnboardingContentHeader:b},data(){return{text_header_title:_("Connect MonsterInsights to Your Website","google-analytics-for-wordpress"),text_header_subtitle:_("MonsterInsights connects Google Analytics to WordPress and shows you stats that matter.","google-analytics-for-wordpress"),text_authenticate_label:_("Connect Google Analytics + WordPress","google-analytics-for-wordpress"),text_authenticate_description:_("You will be taken to the MonsterInsights website where you'll need to connect your Analytics account.","google-analytics-for-wordpress"),text_error_auth:_("Whoops, something went wrong and we weren't able to connect to MonsterInsights. Please enter your GA4 Measurement ID manually.","google-analytics-for-wordpress"),text_save:_("Save and Continue","google-analytics-for-wordpress"),is_network:this.$mi.network,has_error:!1,auth_error:!1,manual_valid:!0,has_v4_error:!1,text_manual_v4_label:_("Manually enter your GA4 Measurement ID","google-analytics-for-wordpress"),text_manual_v4_description:_("Warning: If you use a manual GA4 Measurement ID, you won't be able to use any of the reporting and some of the tracking features. Your Id code should look like G-XXXXXXXXXX where the X's are combination of numbers and letters.","google-analytics-for-wordpress"),text_v4_measurement_protocol:_("Measurement Protocol API Secret","google-analytics-for-wordpress"),text_v4_measurement_protocol_description:xt(_("The Measurement Protocol API secret allows your site to send tracking data to Google Analytics. To retrieve your Measurement Protocol API Secret, follow %1$sthis guide%2$s.","google-analytics-for-wordpress"),'<a href="'+this.$getUrl("dual-tracking","settings","https://www.monsterinsights.com/docs/how-to-create-your-measurement-protocol-api-secret-in-ga4/")+'" target="_blank">',"</a>")}},computed:{...v({auth:"$_auth/auth"}),manual_button_class(){let s="monsterinsights-onboarding-button monsterinsights-onboarding-button-large";return this.manual_valid||(s+=" monsterinsights-button-disabled"),s},measurement_protocol_secret(){return this.is_network?this.auth.network_measurement_protocol_secret:this.auth.measurement_protocol_secret}},methods:{handleSubmit(){if(this.auth_error&&this.auth.manual_v4===""&&this.auth.network_manual_v4===""){this.manual_valid=!1,this.has_error=_("GA4 Measurement ID can't be empty","google-analytics-for-wordpress");return}this.auth_error&&!this.manual_valid||this.$router.push(this.$wizard_steps[2])},updateManualV4:function(s){const t=this;this.$mi_saving_toast({}),t.has_error=!1,this.$store.dispatch("$_auth/updateManualV4",{v4:s.target.value,network:this.is_network}).then(function(e){e.success===!1?(t.has_error=e.data.error,t.has_v4_error=!0,t.$swal.close(),s.target.value=""):(t.has_error=!1,t.has_v4_error=!1,t.manual_valid=!0,t.$swal.close())})},updateMeasurementProtocolSecret:function(s){this.$mi_saving_toast({}),this.has_error=!1,this.$store.dispatch("$_auth/updateMeasurementProtocolSecret",{value:s.target.value,network:this.is_network}).then(function(t){t.success===!1?(self.has_error=t.data.error,self.$swal.close(),s.target.value=""):(self.has_error=!1,self.manual_valid=!0,self.$swal.close())})}},mounted(){if(typeof URLSearchParams<"u"){let s=new URLSearchParams(window.location.search);if(s){let t=s.get("mi-auth-error");(t==="1"||t==="2")&&(this.auth_error=parseInt(t),this.auth.manual_v4&&(this.manual_valid=!0))}}}};var Ct=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-step-authenticate"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("form",[e("div",{staticClass:"monsterinsights-form-row"},[e("onboarding-license")],1),e("div",{staticClass:"monsterinsights-separator"}),t.auth_error?[e("label",{staticClass:"monsterinsights-dark",domProps:{textContent:t._s(t.text_manual_v4_label)}}),e("p",{domProps:{innerHTML:t._s(t.text_manual_v4_description)}}),e("input",{staticClass:"monsterinsights-manual-ua",attrs:{id:"monsterinsights-auth-manual-v4-input",type:"text"},domProps:{value:t.is_network?t.auth.network_manual_v4:t.auth.manual_v4},on:{change:t.updateManualV4}}),t.has_v4_error?e("label",{staticClass:"monsterinsights-error"},[e("i",{staticClass:"monstericon-warning-triangle"}),e("span",{domProps:{innerHTML:t._s(t.has_error)}})]):t._e(),t.auth.manual_v4||t.auth.network_manual_v4?[e("div",{staticClass:"monsterinsights-separator"}),e("label",{staticClass:"monsterinsights-dark",attrs:{for:"monsterinsights-v4-measurement-protocol"},domProps:{textContent:t._s(t.text_v4_measurement_protocol)}}),e("p",{staticClass:"monsterinsights-field-description",domProps:{innerHTML:t._s(t.text_v4_measurement_protocol_description)}}),e("input",{staticClass:"monsterinsights-manual-ua",attrs:{id:"monsterinsights-v4-measurement-protocol",type:"text"},domProps:{value:t.measurement_protocol_secret},on:{change:t.updateMeasurementProtocolSecret}})]:t._e(),e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-form-row monsterinsights-form-buttons"},[e("button",{class:t.manual_button_class,attrs:{type:"submit",name:"next_step"},domProps:{textContent:t._s(t.text_save)},on:{click:function(o){return o.preventDefault(),t.handleSubmit.apply(null,arguments)}}})])]:[e("onboarding-authenticate",{attrs:{label:t.text_authenticate_label,description:t.text_authenticate_description},on:{nextstep:t.handleSubmit}})]],2)])],1)},kt=[],$t=p(At,Ct,kt,!1,null,null,null,null);const Pt=$t.exports,{__:x,sprintf:Mt}=wp.i18n,St={name:"OnboardingImprove",components:{SettingsInputCheckbox:L},data(){return{text_anonymous_label:x("Help Us Improve","google-analytics-for-wordpress"),text_anonymous_description:x("Help us better understand our users and their website needs.","google-analytics-for-wordpress"),text_anonymous_tooltip:Mt(x("If enabled MonsterInsights will send some information about your WordPress site like what plugins and themes you use and which MonsterInsights settings you use to us so that we can improve our product. For a complete list of what we send and what we use it for, %1$svisit our website.%2$s","google-analytics-for-wordpress"),'<a target="_blank" href="'+this.$getUrl("onboarding-wizard","usage-tracking","https://www.monsterinsights.com/docs/usage-tracking/")+'">',"</a>")}}};var It=function(){var t=this,e=t._self._c;return e("div",[e("settings-input-checkbox",{attrs:{name:"anonymous_data",label:t.text_anonymous_label,description:t.text_anonymous_description,tooltip:t.text_anonymous_tooltip}}),e("div",{staticClass:"monsterinsights-separator"})],1)},Tt=[],Et=p(St,It,Tt,!1,null,null,null,null);const Lt=Et.exports,{__:l,sprintf:M}=wp.i18n,Ot={name:"OnboardingStepRecommendedSettings",components:{OnboardingImprove:Lt,SettingsInputSelect:N,SettingsInfoTooltip:K,SettingsInputRepeater:V,SettingsInputText:X,SettingsInputCheckbox:L,OnboardingContentHeader:b},data(){return{text_header_title:l("Recommended Settings","google-analytics-for-wordpress"),text_header_subtitle:l("MonsterInsights recommends the following settings based on your configuration.","google-analytics-for-wordpress"),text_events_label:l("Events Tracking","google-analytics-for-wordpress"),text_events_description:l("Must have for all click tracking on site.","google-analytics-for-wordpress"),text_events_tooltip:l("MonsterInsights uses an advanced system to automatically detect all outbound links, download links, affiliate links, telephone links, mail links, and more automatically. We do all the work for you so you don't have to write any code.","google-analytics-for-wordpress"),text_link_attribution_label:l("Enhanced Link Attribution","google-analytics-for-wordpress"),text_link_attribution_description:l("Improves the accuracy of your In-Page Analytics.","google-analytics-for-wordpress"),text_link_attribution_tooltip:l("MonsterInsights will automatically help Google determine which links are unique and where they are on your site so that your In-Page Analytics reporting will be more accurate.","google-analytics-for-wordpress"),text_updates_label:l("Install Updates Automatically","google-analytics-for-wordpress"),text_updates_description:l("Get the latest features, bug fixes, and security updates as they are released.","google-analytics-for-wordpress"),text_updates_tooltip:l("To ensure you get the latest bug fixes and security updates and avoid needing to spend time logging into your WordPress site to update MonsterInsights, we offer the ability to automatically have MonsterInsights update itself.","google-analytics-for-wordpress"),text_badge_label:l("Show MonsterInsights Badge","google-analytics-for-wordpress"),text_badge_description:l("Build trust with website visitors by automatically placing a MonsterInsights badge in your website’s footer.","google-analytics-for-wordpress"),text_extensions_of_files_label:l("File Download Tracking","google-analytics-for-wordpress"),text_extensions_of_files_description:l("Helps you see file downloads data.","google-analytics-for-wordpress"),text_extensions_of_files_tooltip:l("MonsterInsights will automatically track downloads of common file types from links you have inserted onto your website. For example: want to know how many of your site's visitors have downloaded a PDF or other file you offer your visitors to download on your site? MonsterInsights makes this both easy, and code-free! You can customize the file types to track at any time from our settings panel.","google-analytics-for-wordpress"),repeater_structure:[{name:"path",label:M(l("Path (example: %s)","google-analytics-for-wordpress"),"/go/"),pattern:/^\/\S+$/,error:l("Path has to start with a / and have no spaces","google-analytics-for-wordpress")},{name:"label",label:M(l("Label (example: %s)","google-analytics-for-wordpress"),"aff"),pattern:/^\S+$/,error:l("Label can't contain any spaces","google-analytics-for-wordpress")}],text_affiliate_repeater_description:l("Helps you increase affiliate revenue.","google-analytics-for-wordpress"),text_affiliate_tooltip_content:l("MonsterInsights will automatically help you track affiliate links that use internal looking urls like example.com/go/ or example.com/refer/. You can add custom affiliate patterns on our settings panel when you finish the onboarding wizard.","google-analytics-for-wordpress"),text_affiliate_label:l("Affiliate Link Tracking","google-analytics-for-wordpress"),text_permissions_view_label:l("Who Can See Reports","google-analytics-for-wordpress"),text_permissions_view_description:l("These user roles will be able to access MonsterInsights' reports in the WordPress admin area.","google-analytics-for-wordpress"),text_permissions_view_tooltip:l("Enable specific user roles to access and view the ExactMetrics reports here. A user with the 'manage_options' capability and any user with at least one of these roles can view the reports.","google-analytics-for-wordpress"),text_save:l("Save and continue","google-analytics-for-wordpress"),text_events_faux_tooltip:l("Events Tracking is enabled the moment you set up MonsterInsights","google-analytics-for-wordpress"),text_link_attribution_faux_tooltip:l("Enhanced Link Attribution is enabled the moment you set up MonsterInsights","google-analytics-for-wordpress"),text_add_role:l("+ Add Role","google-analytics-for-wordpress")}},methods:{handleSubmit(){this.$router.push(this.$wizard_steps[3])}},computed:{...v({settings:"$_settings/settings"}),user_roles:function(){let s=[];for(let t in this.$mi.roles)s.push({label:this.$mi.roles[t],value:t});return s},user_roles_manage_options:function(){let s=[];for(let t in this.$mi.roles_manage_options)s.push({label:this.$mi.roles_manage_options[t],value:t});return s}}};var zt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-step-recommended-settings"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("form",{attrs:{action:"",method:"post"},on:{submit:function(o){return o.preventDefault(),t.handleSubmit.apply(null,arguments)}}},[e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-checkbox",{attrs:{label:t.text_events_label,description:t.text_events_description,tooltip:t.text_events_tooltip,faux:!0,faux_tooltip:t.text_events_faux_tooltip}}),e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-checkbox",{attrs:{label:t.text_link_attribution_label,description:t.text_link_attribution_description,tooltip:t.text_link_attribution_tooltip,faux:!0,faux_tooltip:t.text_link_attribution_faux_tooltip}}),e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-text",{attrs:{default_value:"doc,pdf,ppt,zip,xls,docx,pptx,xlsx",name:"extensions_of_files",label:t.text_extensions_of_files_label,description:t.text_extensions_of_files_description,tooltip:t.text_extensions_of_files_tooltip}}),e("div",{staticClass:"monsterinsights-separator"}),e("p",[e("label",{domProps:{textContent:t._s(t.text_affiliate_label)}}),e("span",{staticClass:"monsterinsights-sublabel",domProps:{innerHTML:t._s(t.text_affiliate_repeater_description)}}),e("settings-info-tooltip",{attrs:{content:t.text_affiliate_tooltip_content}})],1),e("settings-input-repeater",{attrs:{structure:t.repeater_structure,name:"affiliate_links",data:t.settings.affiliate_links}}),e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-select",{attrs:{options:t.user_roles,forced:t.user_roles_manage_options,multiple:!0,name:"view_reports",label:t.text_permissions_view_label,description:t.text_permissions_view_description,tooltip:t.text_permissions_view_tooltip,addtext:t.text_add_role}}),e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-checkbox",{attrs:{"value-on":"all","value-off":"none",name:"automatic_updates",label:t.text_updates_label,description:t.text_updates_description,tooltip:t.text_updates_tooltip}}),e("div",{staticClass:"monsterinsights-separator"}),e("settings-input-checkbox",{attrs:{name:"verified_automatic",label:t.text_badge_label,description:t.text_badge_description}}),e("div",{staticClass:"monsterinsights-separator"}),e("onboarding-improve"),e("div",{staticClass:"monsterinsights-form-row monsterinsights-form-buttons"},[e("button",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",attrs:{type:"submit",name:"next_step"},domProps:{textContent:t._s(t.text_save)}})])],1)])],1)},Bt=[],Ht=p(Ot,zt,Bt,!1,null,null,null,null);const Rt=Ht.exports,Ut={name:"OnboardingAddon",props:{feature:{type:Object,required:!0},allRecommendedPlugins:{type:Array,required:!0}},data(){return{group:""}},computed:{...v({addons:"$_addons/addons"}),isFeatureInstalled(){return this.feature.data.faux?!0:this.feature.id==="recommended_plugins"?this.checkArrayOfInstalled(this.allRecommendedPlugins):this.feature.addons?this.checkArrayOfInstalled(this.feature.addons):!1}},methods:{toggleCheck(s){this.$emit("item-checkbox-updated",this.feature,s.target.checked)},toggleGroup(s){this.group===""?this.group=s:this.group="",this.$emit("toggle-group",s)},checkArrayOfInstalled(s){let t=!0;return s.forEach(e=>{this.addons[e]?this.addons[e].installed===!1&&(t=!1):t=!1}),t}}};var Wt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-features monsterinsights-flex"},[e("div",{staticClass:"monsterinsights-feat-content"},[e("p",[e("span",[t._v(t._s(t.feature.data.title))]),t.feature.group_heading?e("a",{staticClass:"icon",on:{click:function(o){return t.toggleGroup(t.feature.group_heading)}}},[t.group===""?e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"8",fill:"none"}},[e("path",{attrs:{fill:"#586074","fill-rule":"evenodd",d:"M4.91 6.758a1.157 1.157 0 0 0 1.636 0l4.363-4.362A1.157 1.157 0 0 0 9.272.76L5.728 4.305 2.184.76A1.157 1.157 0 0 0 .547 2.395L4.91 6.76v-.001Z","clip-rule":"evenodd"}})]):t._e(),t.group===t.feature.group_heading?e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"8",fill:"none"}},[e("path",{attrs:{fill:"#586074","fill-rule":"evenodd",d:"M4.91.76a1.157 1.157 0 0 1 1.636 0l4.363 4.361a1.157 1.157 0 0 1-1.637 1.636L5.728 3.213 2.184 6.757A1.157 1.157 0 0 1 .547 5.122L4.91.76Z","clip-rule":"evenodd"}})]):t._e()]):t._e()]),e("p",[t._v(t._s(t.feature.data.description))]),t.feature.data.checked&&t.feature.data.installText&&!t.feature.data.faux?e("p",{staticClass:"monsterinsights-feat-content-install-text"},[t._v(" "+t._s(t.feature.data.installText)+" ")]):t._e(),t.feature.data.faux&&t.feature.data.installedText?e("p",{staticClass:"monsterinsights-feat-content-install-text"},[t._v(" "+t._s(t.feature.data.installedText)+" ")]):t._e()]),e("div",{staticClass:"monsterinsights-feat-checkbox"},[t.isFeatureInstalled?e("label",{staticClass:"monsterinsights-checkbox-round 1"},[e("input",{staticClass:"faux",attrs:{id:t.feature.id,type:"checkbox",disabled:""},domProps:{checked:!0}}),e("span",{staticClass:"checkmark",attrs:{id:"checkmark-loader-"+t.feature.id}})]):t.feature.loading?e("label",{staticClass:"monsterinsights-checkbox-round"},[e("span",{staticClass:"loader",staticStyle:{display:"block"}})]):e("label",{staticClass:"monsterinsights-checkbox-round 3"},[e("input",{staticClass:"addon-checkbox",attrs:{id:t.feature.id,type:"checkbox",name:t.feature.id},domProps:{checked:t.feature.data.checked},on:{click:t.toggleCheck}}),e("span",{class:{checkmark:!0,remove:!t.feature.data.checked,error:t.feature.error},attrs:{id:"checkmark-loader-"+t.feature.id}})])])])},Ft=[],Dt=p(Ut,Wt,Ft,!1,null,null,null,null);const Gt=Dt.exports,{__:a}=wp.i18n,Zt={name:"OnboardingStepRecommendedFeatures",components:{OnboardingContentHeader:b,OnboardingAddon:Gt},data(){return{skip_for_now:a("Skip for Now","google-analytics-for-wordpress"),text_header_title:a("Which website features would you like to enable?","google-analytics-for-wordpress"),text_header_subtitle:a("We’ve already selected our recommended features based on your site. ","google-analytics-for-wordpress"),text_other_addons:a("Other Addons","google-analytics-for-wordpress"),text_other_addons_button:a("View all MonsterInsights addons","google-analytics-for-wordpress"),text_save:a("Continue","google-analytics-for-wordpress"),features_awaiting_install:[],features_awaiting_install_text:a("The following plugins will be installed: ","google-analytics-for-wordpress"),features_list:[{id:"standard",standard:{addons:"standard"},data:{feature:"standard",title:a("Standard Analytics & Reports","google-analytics-for-wordpress"),description:a("Get the reports and stats that matter right inside your WordPress Dashboard.","google-analytics-for-wordpress"),faux:!0}},{id:"enhanced_link",enhanced_link:{addons:"enhanced-link"},data:{feature:"enhanced-link",title:a("Enhanced Link Attribution","google-analytics-for-wordpress"),description:a("Helps you see what links your users are clicking on your site.","google-analytics-for-wordpress"),faux:!0}},{id:"recommended_plugins",group_heading:"recommended-plugins",data:{feature:"recommended_plugins",title:a("Add Recommended Plugins To My Website","google-analytics-for-wordpress"),description:a("Get the best plugins to help you collect user feedback, create contact forms, improve your SEO, and convert users.","google-analytics-for-wordpress"),checked:!0},loading:!1},{id:"userfeedback_lite",addons:["userfeedback-lite"],group:"recommended-plugins",data:{feature:"userfeedback-lite",title:a("Collect User Feedback","google-analytics-for-wordpress"),description:a("Ask visitors questions about your website and content to learn how you can improve and grow your business.","google-analytics-for-wordpress"),checked:!0,installText:a("Installs UserFeedback","google-analytics-for-wordpress"),installedText:a("UserFeedback is already installed","google-analytics-for-wordpress")},loading:!1,error:!1},{id:"wpconsent",addons:["wpconsent"],group:"recommended-plugins",data:{feature:"recommended-plugins",title:a("Cookie Consent & Compliance","google-analytics-for-wordpress"),description:a("Easily create customizable cookie banners, and record user consent. Works automatically with MonsterInsights.","google-analytics-for-wordpress"),checked:!0,installText:a("Installs WPConsent","google-analytics-for-wordpress"),installedText:a("WPConsent is already installed","google-analytics-for-wordpress")},loading:!1,error:!1},{id:"aioseo",addons:["aioseo"],group:"recommended-plugins",data:{feature:"aioseo",title:a("All In One SEO Toolkit","google-analytics-for-wordpress"),description:a("The best WordPress SEO plugin that works with MonsterInsights to boost your rankings.","google-analytics-for-wordpress"),checked:!0,installText:a("Installs All In One SEO Toolkit","google-analytics-for-wordpress"),installedText:a("All In One SEO Toolkit is already installed","google-analytics-for-wordpress")},loading:!1,error:!1},{id:"duplicator",addons:["duplicator"],group:"recommended-plugins",data:{feature:"duplicator",title:a("Website Backup and Migration","google-analytics-for-wordpress"),description:a("Backup your website's files and databases for free with the #1 backup solution.","google-analytics-for-wordpress"),checked:!0,installText:a("Installs Duplicator","google-analytics-for-wordpress"),installedText:a("Duplicator is already installed","google-analytics-for-wordpress")},loading:!1,error:!1},{id:"wpforms_lite",addons:["wpforms-lite"],group:"recommended-plugins",data:{feature:"wpforms-lite",title:a("Smart Form Builder by WPForms","google-analytics-for-wordpress"),description:a("The most popular WordPress form plugin, trusted by over 6 million websites. Easily create contact forms, payment forms, surveys and more.","google-analytics-for-wordpress"),checked:!0,installText:a("Installs WPForms","google-analytics-for-wordpress"),installedText:a("WPForms is already installed","google-analytics-for-wordpress")},loading:!1,error:!1},{id:"eu_compliance",addons:["eu-compliance"],data:{feature:"eu-compliance",title:a("Privacy Compliance Addon","google-analytics-for-wordpress"),description:a("Help Google Analytics become compliant with internet privacy laws like GDPR, PECR, and CCPA.","google-analytics-for-wordpress"),checked:!1},loading:!1,error:!1},{id:"advanced_reports",addons:["dimensions","forms","ecommerce","page-insights"],data:{feature:["dimensions","forms","ecommerce","page-insights"],title:a("Advanced Reports","google-analytics-for-wordpress"),description:a("Get access to advanced reports inside WordPress including search keywords report, real-time analytics dashboard, publishers / eCommerce report, custom dimensions, and more.","google-analytics-for-wordpress"),checked:!1},loading:!1,error:!1},{id:"ecommerce",addons:["ecommerce"],data:{feature:"ecommerce",title:a("eCommerce Tracking","google-analytics-for-wordpress"),description:a("Instantly enable enhanced eCommerce tracking, so you can measure conversions, sales, and revenue stats. Works with WooCommerce, Easy Digital Downloads, MemberPress, and more.","google-analytics-for-wordpress"),checked:!1},loading:!1,error:!1},{id:"advanced_tracking",addons:["dimensions","forms"],data:{feature:["dimensions","forms"],title:a("20+ Advanced Tracking","google-analytics-for-wordpress"),description:a("Get access to advanced tracking features like form conversion tracking, author tracking, custom dimensions, scroll tracking, and more.","google-analytics-for-wordpress"),checked:!1},loading:!1,error:!1},{id:"media",addons:["media"],data:{feature:"media",title:a("Media Tracking","google-analytics-for-wordpress"),description:a("Track how your users interact with videos on your website.","google-analytics-for-wordpress"),checked:!1},loading:!1,error:!1}],allRecommendedPlugins:["userfeedback-lite","aioseo","wpforms-lite","duplicator","wpconsent"],active_group:"",recommendedPluginsGroupState:{allDisabled:!1,allChecked:!1}}},computed:{...v({settings:"$_settings/settings",addons:"$_addons/addons"}),addonList:function(){return this.addonState(this.addons)},featureAwaitingInstall(){return this.features_awaiting_install.join(", ")}},watch:{features_list:{handler:function(){this.featuresToInstall()},deep:!0}},mounted(){localStorage.removeItem("activated_addons"),this.featuresToInstall()},methods:{featuresList(){return this.features_list},skipAddons(){this.$wizard_steps[4]!==void 0?this.$router.push(this.$wizard_steps[4]):this.$router.push({name:"success"})},addonState(s){let t=this.features_list;for(var e=0;e<t.length;e++){const o=t[e].data.feature;if(o!=="standard"&&o!=="enhanced-link"&&(typeof o=="string"&&s[o]&&(s[o].active?t[e].data.faux=!0:s[o].installed&&t[e].data.checked===!0&&(t[e].data.checked=!0)),typeof o=="object")){let n=o.length,r=[],c=[];for(let f in o){let m=o[f];s[m]&&(s[m].active?(c.push(m),r.push(m)):s[m].installed&&r.push(m))}n===c.length?t[e].data.faux=!0:n===r.length&&t[e].data.checked===!0&&(t[e].data.checked=!0)}(o==="wpforms-lite"||o==="aioseo"||o==="userfeedback-lite"||o==="duplicator"||o==="wpconsent")&&s[o]&&s[o].installed&&(t[e].data.faux=!0,t[e].data.checked=!1)}if(this.$nextTick(()=>{this.updateRecommendedPluginsGroupState(),this.featuresToInstall()}),parseInt(this.$mi.allow_file_edit)===0){const o=t.findIndex(n=>n.id==="recommended_plugins");t.splice(o,1)}return t},updateRecommendedPluginsGroupState(){const s=this.features_list.filter(t=>t.group==="recommended-plugins");this.recommendedPluginsGroupState.allDisabled=s.every(t=>t.data.faux),this.recommendedPluginsGroupState.allChecked=s.every(t=>t.data.faux||t.data.checked)},async continueNextStep(s){s.target.setAttribute("disabled","disabled"),s.target.innerHTML="Please Wait...";let t=this;this.showSpinnerToAllInstallable();const e=["standard","enhanced_link","recommended_plugins"],o=[],n=[],r=["advanced_reports","advanced_tracking"];for(const c of this.features_list)if(!e.includes(c.id)&&c.data.checked&&c.addons&&Array.isArray(c.addons)){for(let f of c.addons)parseInt(this.$mi.allow_file_edit)===1&&await t.executeAddon(c,f,n,o);r.includes(c.id)||(c.loading=!1)}if(n.length===0)localStorage.getItem("activated_addons")!==""&&o.push(localStorage.getItem("activated_addons")),localStorage.setItem("activated_addons",o),this.$wizard_steps[4]!==void 0?this.$router.push(this.$wizard_steps[4]):this.$router.push({name:"success"});else{o.length>0&&(localStorage.getItem("activated_addons")!==""&&o.push(localStorage.getItem("activated_addons")),localStorage.setItem("activated_addons",o)),console.log("===================================================================="),console.log("MonsterInsights Onboarding Wizard Debug"),console.log("Following addons were not installed:"),console.log(n.toString()),console.log("===================================================================="),t.$swal({icon:"error",title:a("Error Processing","google-analytics-for-wordpress"),text:a("There was an error while processing some features. Please try again or you can skip this process for now","google-analytics-for-wordpress"),confirmButtonText:a("Ok","google-analytics-for-wordpress")}),s.target.removeAttribute("disabled"),s.target.innerHTML="Continue";let c=document.querySelector(".monsterinsights-skip-addons");c.style.visibility="visible"}},async executeAddon(s,t,e,o){let n=this,r=this.addons[t];if(!r){e.push(t);return}const c=["wpforms-lite","aioseo","userfeedback-lite","duplicator","wpconsent"];if(r.installed)r.installed&&!r.active&&(await n.activateAddon(r)===!0?o.push(r.title):(e.push(t),s.error=!0));else{let f="",m=JSON.parse(JSON.stringify(r));if(c.includes(t))f=await n.installPlugin(m);else{if(!this.$isPro())return;f=await n.installAddon(m)}if(f===!0&&await n.activateAddon(m)===!0){o.push(r.title);return}e.push(t),s.error=!0}},async installAddon(s){try{const t=await this.$store.dispatch("$_addons/installAddon",s);if(t!==void 0&&t&&t.plugin)return s.basename=t.plugin,!0}catch{return!1}return!1},async installPlugin(s){try{const t=await this.$store.dispatch("$_addons/installOnboardingPlugin",s);if(t!==void 0&&t&&"success"in t&&t.success===!0)return!0}catch{return!1}return!1},async activateAddon(s){try{return await this.$store.dispatch("$_addons/activateOnboardingAddon",s)}catch{return!1}},featuresToInstall(){const s=this.features_list;let t=[];const e=s.find(o=>o.id==="recommended_plugins");if(e&&e.data.checked){for(let o of s)if(o.group==="recommended-plugins"){const n=o.id.replace("_","-"),r=this.addons[n];if(r&&!r.active&&!r.installed){let c;o.id==="optinmonster"?c="OptinMonster":o.id==="userfeedback_lite"?c="UserFeedback":c=o.data.title,t.push(c)}}}else for(let o of s)if(o.group==="recommended-plugins"&&o.data.checked){const n=o.id.replace("_","-"),r=this.addons[n];if(r&&!r.active&&!r.installed){let c;o.id==="optinmonster"?c="OptinMonster":o.id==="userfeedback_lite"?c="UserFeedback":c=o.data.title,t.push(c)}}this.features_awaiting_install=t},handleItemCheckboxUpdated(s,t){s.data.checked=t;let e=[];for(let n=0;n<this.features_list.length;n++){const r=this.features_list[n];r.group&&s.group_heading===r.group&&(r.data.checked=t),r.group&&r.data.faux===void 0&&e.push(r.data.checked)}let o=this.features_list.find(n=>s.group===n.group_heading);e.indexOf(!1)===-1?o.data.checked=!0:o.data.checked=!1,this.updateRecommendedPluginsGroupState()},toggleGroup(s){this.active_group===""?this.active_group=s:this.active_group=""},showSpinnerToAllInstallable(){let s=["standard","enhanced_link"];this.features_list.forEach(t=>{!s.includes(t.id)&&t.data.checked&&(t.loading=!0)})}}};var Nt=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"monsterinsights-onboarding-step-recommended-addons"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("div",{staticClass:"monsterinsights-grey-area"},[e("div",{staticClass:"monsterinsights-separator"}),t._l(t.addonList,function(o,n){return e("div",{key:n},[e("div",{directives:[{name:"show",rawName:"v-show",value:!o.group||o.group&&t.active_group===o.group,expression:"!feature_data.group || (feature_data.group && active_group === feature_data.group)"}]},[e("onboarding-addon",{attrs:{feature:o,"all-recommended-plugins":t.allRecommendedPlugins},on:{"toggle-group":t.toggleGroup,"item-checkbox-updated":t.handleItemCheckboxUpdated}}),e("div",{staticClass:"monsterinsights-separator"})],1)])}),e("button",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",attrs:{type:"button",name:"next_step"},domProps:{textContent:t._s(t.text_save)},on:{click:function(o){return o.preventDefault(),t.continueNextStep(o)}}}),e("a",{staticClass:"monsterinsights-skip-addons",attrs:{href:"#",title:""},on:{click:function(o){return o.preventDefault(),t.skipAddons()}}},[e("span",{domProps:{textContent:t._s(t.skip_for_now)}}),e("svg",{attrs:{width:"16",height:"9",viewBox:"0 0 16 9",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M11.1289 3.34375H0.546875C0.300781 3.34375 0.125 3.55469 0.125 3.76562V5.73438C0.125 5.98047 0.300781 6.15625 0.546875 6.15625H11.1289V7.80859C11.1289 8.54688 12.043 8.93359 12.5703 8.40625L15.5938 5.34766C15.9453 5.03125 15.9453 4.50391 15.5938 4.1875L12.5703 1.12891C12.043 0.601562 11.1289 0.988281 11.1289 1.72656V3.34375Z",fill:"#586074"}})])])],2)])],1),t.features_awaiting_install.length?e("div",{staticClass:"monsterinsights-features-awaiting-install"},[e("span",{domProps:{textContent:t._s(t.features_awaiting_install_text)}}),e("span",{domProps:{textContent:t._s(t.featureAwaitingInstall)}})]):t._e()])},Vt=[],Xt=p(Zt,Nt,Vt,!1,null,null,null,null);const Qt=Xt.exports,{__:y}=wp.i18n,Kt={name:"OnboardingStepWpforms",components:{OnboardingContentHeader:b},data(){return{text_header_title:y("MonsterInsights Recommends WPForms","google-analytics-for-wordpress"),text_header_subtitle:y("Built by the folks behind MonsterInsights, WPForms is the most beginner friendly form plugin in the market.","google-analytics-for-wordpress"),text_wpforms_label:y("Used on over 4,000,000 websites!","google-analytics-for-wordpress"),text_wpforms_description:y("WPForms allow you to create beautiful contact forms, subscription forms, payment forms, and other types of forms for your site in minutes, not hours!","google-analytics-for-wordpress"),text_skip_step:y("Skip this Step","google-analytics-for-wordpress"),text_install_wpforms:y("Continue & Install WPForms","google-analytics-for-wordpress"),text_installing_wpforms:y("Installing...","google-analytics-for-wordpress"),button_text:"",loading:!1}},mounted(){this.button_text=this.text_install_wpforms},methods:{handleSubmit(){this.$router.push(this.$wizard_steps[5])},buttonClass(){let s="monsterinsights-onboarding-button monsterinsights-onboarding-button-large monsterinsights-install-wpforms";return this.loading&&(s+=" monsterinsights-button-disabled"),s},installPlugin(){let s=this;this.loading=!0,this.button_text=this.text_installing_wpforms,this.$store.dispatch("$_addons/installWPForms").then(function(){s.loading=!1,s.button_text=s.text_install_wpforms,s.handleSubmit()})}}};var Yt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-step-wpforms"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("form",{attrs:{action:"",method:"post"},on:{submit:function(o){return o.preventDefault(),t.handleSubmit.apply(null,arguments)}}},[e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-addon-row monsterinsights-wpforms-row"},[t._m(0),e("div",{staticClass:"monsterinsights-addon-text"},[e("label",{domProps:{textContent:t._s(t.text_wpforms_label)}}),e("p",{domProps:{textContent:t._s(t.text_wpforms_description)}})])]),e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-form-row monsterinsights-form-buttons"},[e("div",{staticClass:"monsterinsights-form-input"},[e("button",{class:t.buttonClass(),attrs:{type:"button"},domProps:{textContent:t._s(t.button_text)},on:{click:function(o){return o.preventDefault(),t.installPlugin.apply(null,arguments)}}}),t.loading?t._e():e("button",{staticClass:"monsterinsights-text-button monsterinsights-pull-right",attrs:{type:"submit",name:"next_step"}},[e("span",{domProps:{textContent:t._s(t.text_skip_step)}}),e("i",{staticClass:"monstericon-arrow-right"})])])])])])],1)},Jt=[function(){var s=this,t=s._self._c;return t("div",{staticClass:"monsterinsights-addon-icon"},[t("div",{staticClass:"monsterinsights-addon-wpforms"})])}],jt=p(Kt,Yt,Jt,!1,null,null,null,null);const qt=jt.exports,{__:d,sprintf:A}=wp.i18n,te={name:"OnboardingStepSuccess",components:{OnboardingContentHeader:b},data(){return{text_header_title:d("Congratulations, your site is now using Google Analytics!","google-analytics-for-wordpress"),text_header_subtitle:d("MonsterInsights is connected to Google Analytics and data is being collected.","google-analytics-for-wordpress"),text_notice:A(d("%1$sPlease Note:%2$s While Google Analytics is properly setup and tracking everything, it does not send the data back to WordPress immediately. Depending on the size of your website, it can take between a few hours to 24 hours for reports to populate.","google-analytics-for-wordpress"),"<strong>","</strong>"),text_newsletter:A(d("%1$sSubscribe to the MonsterInsights blog%2$s for tips on how to get more traffic and grow your business.","google-analytics-for-wordpress"),'<a target="_blank" href="https://www.monsterinsights.com/blog/">',"</a>"),text_exit:d("Finish Setup & Exit Wizard","google-analytics-for-wordpress"),text_exit_lite:d("Complete Setup without Upgrading","google-analytics-for-wordpress"),exit_url:this.$mi.exit_url,text_success:d("Success","google-analytics-for-wordpress"),text_connected:d("Connected to Google Analytics","google-analytics-for-wordpress"),text_code_installed:d("Tracking Code Installed","google-analytics-for-wordpress"),text_data_collected:d("Data Being Collected","google-analytics-for-wordpress"),icon_success:'<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" rx="8" fill="#EAFAEE"/><path d="M6.70312 10.875C6.85938 11.0312 7.125 11.0312 7.28125 10.875L11.875 6.28125C12.0312 6.125 12.0312 5.85938 11.875 5.70312L11.3125 5.14062C11.1562 4.98438 10.9062 4.98438 10.75 5.14062L7 8.89062L5.23438 7.14062C5.07812 6.98438 4.82812 6.98438 4.67188 7.14062L4.10938 7.70312C3.95312 7.85938 3.95312 8.125 4.10938 8.28125L6.70312 10.875Z" fill="#46BF40"/></svg>',icon_checkmark:'<svg width="14" height="11" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.05469 9.8125C5.28906 10.0469 5.6875 10.0469 5.92188 9.8125L12.8125 2.92188C13.0469 2.6875 13.0469 2.28906 12.8125 2.05469L11.9688 1.21094C11.7344 0.976562 11.3594 0.976562 11.125 1.21094L5.5 6.83594L2.85156 4.21094C2.61719 3.97656 2.24219 3.97656 2.00781 4.21094L1.16406 5.05469C0.929688 5.28906 0.929688 5.6875 1.16406 5.92188L5.05469 9.8125Z" fill="#46BF40"/></svg>',text_button_upgrade:d("Upgrade to MonsterInsights Pro","google-analytics-for-wordpress"),upgrade_button_url:this.$getUpgradeUrl("onboarding-lite","onboarding-lite"),activated_addons:[],text_license_label:d("Already purchased? Simply enter your license key below to connect with MonsterInsights PRO!","google-analytics-for-wordpress"),is_loading:!1,show_connect:!1,connect_key:"",text_license_placeholder:d("Paste your license key here","google-analytics-for-wordpress"),text_license_verify:d("Verify","google-analytics-for-wordpress"),text_upgrade_to_pro:d("Verify License Key","google-analytics-for-wordpress"),text_lite_user:{heading:d("Upgrade to Pro and instantly unlock these features and more","google-analytics-for-wordpress"),description:d("To unlock the selected features, please upgrade to Pro and enter your license key below.","google-analytics-for-wordpress"),bottom_text:A(d("Upgrade today to the %1$sPro plan%2$s and %1$ssave 60%% off%2$s (Discount auto applied)","google-analytics-for-wordpress"),"<strong>","</strong>"),features:["All Reports & Stats","EU & GDPR Compliance","One-Click eCommerce Tracking","Search Console Integration","Forms Tracking","No Code PPC Pixel","Advanced Integrations","Custom Dimensions","User Journeys","Growth Tools"]}}},computed:{...v({install_errors:"$_onboarding/install_errors",license:"$_license/license",license_network:"$_license/license_network",auth:"$_auth/auth"})},mounted(){const s=this;s.$swal({icon:"info",title:d("Checking your website...","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){s.$swal.showLoading()}}),this.$store.dispatch("$_onboarding/getErrors").then(function(){s.$swal.close()});let t=localStorage.getItem("activated_addons");t&&(t=t.replace(/,\s*$/,""),this.activated_addons=t.split(","))},methods:{fieldInput:H(function(){this.show_connect=this.connect_key!==""},100),startUpgradeToPro(){const s=this;this.$swal({icon:"info",title:d("Verifying License...","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,customClass:{container:"monsterinsights-swal"},didOpen:function(){s.$swal.showLoading()}}),Y.getUpgradeLink(s.connect_key).then(function(t){if(t.success&&t.data.url)return window.location=t.data.url;{let e=t.data&&t.data.message?t.data.message:d("There was an error unlocking MonsterInsights PRO please try again or install manually.","google-analytics-for-wordpress");s.$mi_error_toast({title:d("Error","google-analytics-for-wordpress"),html:e,toast:!1,position:"center",showConfirmButton:!0,showCloseButton:!1,customClass:!1,confirmButtonText:d("Ok","google-analytics-for-wordpress")}).then(function(){t.data&&t.data.reload&&(window.location=s.exit_url)})}}).catch(function(){s.$swal.close()})},exitOnboardingWizard(){window.location=this.$mi.reports_url}}};var ee=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-step-success"},[e("onboarding-content-header",{attrs:{title:t.text_header_title,subtitle:t.text_header_subtitle}}),e("div",{staticClass:"monsterinsights-onboarding-wizard-form"},[e("ol",[e("li",{domProps:{innerHTML:t._s(t.text_notice)}}),t.install_errors?e("div",t._l(t.install_errors,function(o,n){return e("div",{key:n},[e("li",{domProps:{innerHTML:t._s(o)}})])}),0):t._e()]),e("div",{staticClass:"monsterinsights-tracking-info-container"},[e("div",{staticClass:"monsterinsights-flex monsterinsights-tracking-info"},[e("div",{staticClass:"monsterinsights-tracking-info-text"},[e("p",{domProps:{innerHTML:t._s(t.text_connected)}})]),e("div",{staticClass:"monsterinsights-tracking-info-icon"},[e("span",{staticClass:"monsterinsights-success",domProps:{innerHTML:t._s(t.text_success)}}),e("span",{staticClass:"monsterinsights-success-icon",domProps:{innerHTML:t._s(t.icon_success)}})])])]),e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-tracking-info-container"},[e("div",{staticClass:"monsterinsights-flex monsterinsights-tracking-info"},[e("div",{staticClass:"monsterinsights-tracking-info-text"},[e("p",[t._v(t._s(t.text_code_installed))])]),e("div",{staticClass:"monsterinsights-tracking-info-icon"},[e("span",{staticClass:"monsterinsights-success",domProps:{innerHTML:t._s(t.text_success)}}),e("span",{staticClass:"monsterinsights-success-icon",domProps:{innerHTML:t._s(t.icon_success)}})])])]),e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-tracking-info-container"},[e("div",{staticClass:"monsterinsights-flex monsterinsights-tracking-info"},[e("div",{staticClass:"monsterinsights-tracking-info-text"},[e("p",[t._v(t._s(t.text_data_collected))])]),e("div",{staticClass:"monsterinsights-tracking-info-icon"},[e("span",{staticClass:"monsterinsights-success",domProps:{innerHTML:t._s(t.text_success)}}),e("span",{staticClass:"monsterinsights-success-icon",domProps:{innerHTML:t._s(t.icon_success)}})])])]),t.activated_addons?e("div",t._l(t.activated_addons,function(o,n){return e("div",{key:n},[e("div",{staticClass:"monsterinsights-separator"}),e("div",{staticClass:"monsterinsights-tracking-info-container"},[e("div",{staticClass:"monsterinsights-flex monsterinsights-tracking-info"},[e("div",{staticClass:"monsterinsights-tracking-info-text"},[e("p",[t._v(t._s(o+" Installed"))])]),e("div",{staticClass:"monsterinsights-tracking-info-icon"},[e("span",{staticClass:"monsterinsights-success"},[t._v(t._s(t.text_success))]),e("span",{staticClass:"monsterinsights-success-icon",domProps:{innerHTML:t._s(t.icon_success)}})])])])])}),0):t._e(),t.license.type!=="pro"&&t.license_network.type!=="pro"?e("div",[e("div",{staticClass:"monsterinsights-onboarding-upsell"},[e("h2",[t._v(t._s(t.text_lite_user.heading))]),e("p",{staticClass:"monsterinsights-onboarding-upsell-description"},[t._v(" "+t._s(t.text_lite_user.description)+" ")]),e("ul",t._l(t.text_lite_user.features,function(o,n){return e("li",{key:n},[e("span",{staticClass:"monsterinsights-icon",domProps:{innerHTML:t._s(t.icon_checkmark)}}),e("span",{staticClass:"monsterinsights-text"},[t._v(t._s(o))])])}),0),e("div",{staticClass:"monsterinsights-upsell-bottom-text"},[e("p",{domProps:{innerHTML:t._s(t.text_lite_user.bottom_text)}})]),e("div",{staticClass:"monsterinsights-upsell-upgrade-button"},[e("a",{staticClass:"monsterinsights-button monsterinsights-button-large",attrs:{target:"_blank",href:t.upgrade_button_url}},[e("span",{domProps:{textContent:t._s(t.text_button_upgrade)}}),e("svg",{attrs:{width:"18",height:"13",viewBox:"0 0 18 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M10.3753 1.33789L10.3752 1.33769L10.3689 1.34393C10.1941 1.51879 10.0976 1.72877 10.0876 1.96876C10.0774 2.21341 10.1663 2.4284 10.3439 2.60607L13.7879 6.05H1.75C1.49713 6.05 1.27866 6.1342 1.10643 6.30643C0.934205 6.47866 0.85 6.69713 0.85 6.95C0.85 7.20287 0.934205 7.42134 1.10643 7.59357C1.27866 7.7658 1.49713 7.85 1.75 7.85H13.7879L10.3689 11.2689C10.2044 11.4335 10.125 11.644 10.125 11.8875C10.125 12.1364 10.2164 12.3535 10.3939 12.5311C10.5702 12.7073 10.7826 12.8 11.025 12.8C11.2674 12.8 11.4798 12.7073 11.6561 12.5311L16.6061 7.58107C16.6995 7.48765 16.7714 7.38781 16.8143 7.28071C16.8555 7.17774 16.875 7.06691 16.875 6.95C16.875 6.83309 16.8555 6.72226 16.8143 6.61929C16.7714 6.51219 16.6995 6.41235 16.6061 6.31893L11.6311 1.34393C11.4665 1.17939 11.256 1.1 11.0125 1.1C10.769 1.1 10.5539 1.17915 10.3753 1.33789Z",fill:"white",stroke:"white","stroke-width":"0.3"}})])])])]),e("div",{staticClass:"monsterinsights-settings-license-lite"},[e("label",{attrs:{for:"monsterinsights-license-key"},domProps:{innerHTML:t._s(t.text_license_label)}}),e("div",{staticClass:"monsterinsights-inline-field"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.connect_key,expression:"connect_key"}],attrs:{id:"monsterinsights-license-key",readonly:t.is_loading,type:"text",autocomplete:"off",placeholder:t.text_license_placeholder},domProps:{value:t.connect_key},on:{input:[function(o){o.target.composing||(t.connect_key=o.target.value)},t.fieldInput]}}),t.show_connect?e("span",[e("button",{staticClass:"monsterinsights-button",domProps:{textContent:t._s(t.text_upgrade_to_pro)},on:{click:function(o){return o.preventDefault(),t.startUpgradeToPro(o)}}})]):e("span",[e("button",{staticClass:"monsterinsights-button disabled",domProps:{textContent:t._s(t.text_upgrade_to_pro)}})])])])]):t._e(),t.license.type!=="pro"&&t.license_network.type!=="pro"?e("div",[e("div",{staticClass:"monsterinsights-separator"}),e("a",{staticClass:"monsterinsights-exit-link",attrs:{href:"#",title:""},on:{click:function(o){return o.preventDefault(),t.exitOnboardingWizard.apply(null,arguments)}}},[t._v(t._s(t.text_exit_lite))])]):e("div",{staticClass:"monsterinsights-form-row monsterinsights-form-buttons"},[e("div",{staticClass:"monsterinsights-form-input"},[e("a",{staticClass:"monsterinsights-onboarding-button monsterinsights-onboarding-button-large",attrs:{href:"#"},domProps:{textContent:t._s(t.text_exit)},on:{click:function(o){return o.preventDefault(),t.exitOnboardingWizard.apply(null,arguments)}}})])])])],1)},se=[],oe=p(te,ee,se,!1,null,null,null,null);const ne=oe.exports,ae=new E({routes:[{path:"*",redirect:"/"},{path:"/",name:"welcome",component:ct},{path:"/authenticate",name:"authenticate",component:Pt},{path:"/recommended_settings",name:"recommended_settings",component:Rt},{path:"/recommended_addons",name:"recommended_addons",component:Qt},{path:"/wpforms",name:"wpforms",component:qt},{path:"/success",name:"success",component:ne}],scrollBehavior(){return{x:0,y:0}}}),{__:S,sprintf:ie}=wp.i18n,re=s=>new Promise(t=>{let e=new FormData,o=u.prototype.$addQueryArg(u.prototype.$mi.ajax,"page","monsterinsights-onboarding");e.append("action","monsterinsights_onboarding_get_errors"),R.post(o,e).then(n=>{t(n.data)}).catch(function(n){if(s.dispatch("$_app/block",!1,{root:!0}),n.response){const r=n.response;return u.prototype.$mi_error_toast({title:ie(S("Can't load errors. Error: %1$s, %2$s","google-analytics-for-wordpress"),r.status,r.statusText)})}u.prototype.$mi_error_toast({title:S("You appear to be offline.","google-analytics-for-wordpress")})})}),le={fetchErrors:re},ce=s=>{let t=le.fetchErrors(s);return t.then(e=>{s.commit("ERRORS_UPDATED",e)}).catch(e=>{console.error(e)}),t},de={getErrors:ce},ue=s=>s.install_errors,ge={install_errors:ue},pe=(s,t)=>{s.install_errors=t},me={ERRORS_UPDATED:pe},he={install_errors:[]},_e={namespaced:!0,state:he,actions:de,getters:ge,mutations:me};const{__:fe}=wp.i18n,we={name:"TheWizardHeader",data(){return{text_exit:fe("Exit Setup","google-analytics-for-wordpress"),href:this.$mi.exit_url}},methods:{exitOnboardingWizard(){window.location=this.href},showExitButton(){return this.$route.name!=="success"}}};var ve=function(){var t=this,e=t._self._c;return e("header",{staticClass:"monsterinsights-onboarding-header"},[e("nav",{staticClass:"monsterinsights-header-navigation"},[e("a",{directives:[{name:"show",rawName:"v-show",value:t.showExitButton(),expression:"showExitButton()"}],staticClass:"monsterinsights-exit-button",attrs:{href:"#"},on:{click:function(o){return o.preventDefault(),t.exitOnboardingWizard.apply(null,arguments)}}},[e("i",{staticClass:"monstericon-times-circle"}),e("span",{domProps:{textContent:t._s(t.text_exit)}})])]),t._m(0)])},ye=[function(){var s=this,t=s._self._c;return t("h1",{staticClass:"monsterinsights-onboarding-wizard-logo"},[t("div",{staticClass:"monsterinsights-logo"},[t("div",{staticClass:"monsterinsights-bg-img"})])])}],be=p(we,ve,ye,!1,null,null,null,null);const xe=be.exports,Ae={name:"TheWizardTimeline",data(){return{steps:this.$wizard_steps}},methods:{stepClass(s){let t="monsterinsights-onboarding-wizard-step";if(this.$route.name==="success")return t+" monsterinsights-onboarding-wizard-step-completed";let e=0;for(let o in this.steps)this.$route.name===this.steps[o]&&(e=o);return s<e&&(t+=" monsterinsights-onboarding-wizard-step-completed"),parseInt(s)===parseInt(e)&&(t+=" monsterinsights-onboarding-wizard-step-active"),t},lineClass(s){let t="monsterinsights-onboarding-wizard-step-line",e=0;for(let o in this.steps)this.$route.name===this.steps[o]&&(e=o);return s<=e&&(t+=" monsterinsights-onboarding-wizard-line-active"),t}}};var Ce=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-onboarding-wizard-container"},[e("div",{staticClass:"monsterinsights-onboarding-wizard-steps"},[t._l(t.steps,function(o,n){return[n>0?e("div",{key:n+"line",class:t.lineClass(n)}):t._e(),e("div",{key:n,class:t.stepClass(n)})]})],2)])},ke=[],$e=p(Ae,Ce,ke,!1,null,null,null,null);const Pe=$e.exports,Me={name:"OnboardingBottomUpsell"};var Se=function(){var t=this,e=t._self._c;return e("div")},Ie=[],Te=p(Me,Se,Ie,!1,null,null,null,null);const Ee=Te.exports;const Le={name:"WizardModuleOnboarding",components:{OnboardingBottomUpsell:Ee,TheWizardTimeline:Pe,TheWizardHeader:xe},router:ae,created(){const s="$_settings";s in this.$store._modules.root._children||this.$store.registerModule(s,J);const t="$_onboarding";t in this.$store._modules.root._children||this.$store.registerModule(t,_e)},computed:{...v({blocked:"$_app/blocked"}),route(){return this.$route.name}},mounted(){this.$mi_loading_toast(),this.$store.dispatch("$_settings/getSettings")}};var Oe=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-admin-page onboarding-wizard"},[e("the-wizard-header"),e("the-wizard-timeline"),e("div",{staticClass:"monsterinsights-onboarding-wizard-container"},[e("div",{staticClass:"monsterinsights-onboarding-wizard-content"},[e("router-view")],1)]),t.route==="success"?e("OnboardingBottomUpsell"):t._e(),t.blocked?e("div",{staticClass:"monsterinsights-blocked"}):t._e()],1)},ze=[],Be=p(Le,Oe,ze,!1,null,"1177c7ce",null,null);const He=Be.exports;const{__:i}=wp.i18n,Re={name:"WizardModuleWelcome",components:{WelcomeOverlay:Q,LaunchWizardButton:q},data(){return{text_welcome_title:i("Welcome to MonsterInsights","google-analytics-for-wordpress"),text_welcome_subtitle:i("Thank you for choosing MonsterInsights - The Most Powerful WordPress Analytics Plugin","google-analytics-for-wordpress"),text_below_buttons:i("Note: You will be transfered to MonsterInsights.com to complete the setup wizard.","google-analytics-for-wordpress"),text_above_buttons:i("MonsterInsights makes it “effortless” to setup Google Analytics in WordPress, the RIGHT Way. You can watch the video tutorial or use our 3 minute setup wizard.","google-analytics-for-wordpress"),text_wizard_button:i("Launch the Wizard!","google-analytics-for-wordpress"),text_read_more_button:i("Read the Full Guide","google-analytics-for-wordpress"),text_features_title:i("MonsterInsights Features & Addons","google-analytics-for-wordpress"),text_features_subtitle:i("Here are the features that make MonsterInsights the most powerful and user-friendly WordPress analytics plugin in the market.","google-analytics-for-wordpress"),text_view_all_features:i("See All Features","google-analytics-for-wordpress"),text_upgrade_to_pro:i("Upgrade to PRO","google-analytics-for-wordpress"),text_per_year:i("per year","google-analytics-for-wordpress"),text_upgrade_now:i("Upgrade Now","google-analytics-for-wordpress"),text_testimonials:i("Testimonials","google-analytics-for-wordpress"),wizard_url:this.$mi.wizard_url,features:[{name:i("Google Analytics Tracking","google-analytics-for-wordpress"),description:i("Setup universal website tracking across devices and campaigns with just a few clicks (without any code).","google-analytics-for-wordpress"),icon:'<svg class="" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 192 192" enable-background="new 0 0 192 192" xml:space="preserve"><rect fill="none" width="192" height="192"></rect><g><g><path fill="#509FE2" d="M130,29v132c0,14.77,10.189,23,21,23c10,0,21-7,21-23V30c0-13.54-10-22-21-22S130,17.33,130,29z"></path></g><g><path fill="#ACBDC9" d="M75,96v65c0,14.77,10.19,23,21,23c10,0,21-7,21-23V97c0-13.54-10-22-21-22S75,84.33,75,96z"></path></g><g><circle fill="#D6E2EA" cx="41" cy="163" r="21"></circle></g></g></svg>'},{name:i("Google Analytics Dashboard","google-analytics-for-wordpress"),description:i("See your website analytics report right inside your WordPress dashboard with actionable insights.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 75 76"><image width="69" height="56" xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEUAAAA4CAMAAACc78UEAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAANlBMVEUAAADU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OrU4OoAAACAdzTSAAAAEHRSTlMAEHCvv2CPMECA35/vz1Agb16hXgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAAHdElNRQfjBgcLLAR8mk7DAAAAlklEQVRIx+3XvQ7CMAwE4GuduM0f5P2fFoewICGk2ghl8C23fUpO6VBg2ymYQhFA6OYwDjvSO86/KyHlZxPPLlyvKywrkrSsuTXpQ7peVgQZM+bRNznZ6KRV2kshnbJ/uFHWrDsfV5krV9as++Wu6ylN+ym+rXtCmeSKK6644oorrrjiykqKMUsp958oKHZk/H7FZEzEA/X+Y6vtpvg9AAAAAElFTkSuQmCC"></image>    <image id="Vector_Smart_Object-2" data-name="Vector Smart Object" x="15" y="19" width="40" height="25" xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAZCAMAAAB0BpxXAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAANlBMVEUAAABQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+BQn+AAAAD+uSRLAAAAEHRSTlMAEGCAUO/fMCCfz3Cvv0CP0Y9aNgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAAHdElNRQfjBgcLLAR8mk7DAAABC0lEQVQ4y4VS0QKEIAgrTU+tcP//tQd6npAP8dYcMca27aV25717I3EdAVzx88ZLjcfM/YWYgbynApym/QJCThoKiKITuBX46WOC1vNjREOMQPHRNvNQ7nMsYGIEXDyFn9RwpoTKQkETO/vXYUDXl0bVjgFirNdEkc1ionE88RTaXED4eyY69L/M0lBEFlfWmxaMGo+ySVqIbPZN3nsqwwsZcSw8/7+nnNdR77ACJUhVXYQWDXaJacE5iEbgCJKSzctedBTdy1UlSHvWemKLTGonnXUjPPPxy4WNBxNLj8lES/OcTGSafzSio6zyNZh0tCwgS5Cmt+NI5odtxycquyH451HcEiRxnR7AF4SxERp7xl7rAAAAAElFTkSuQmCC"></image>    <image id="wordpress" x="35" y="35" width="39" height="39" xlink:href="data:img/png;base64,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"></image></svg>'},{name:i("Real-time Stats","google-analytics-for-wordpress"),description:i("Get real-time stats right inside WordPress to see who is online, what are they doing, and more.","google-analytics-for-wordpress"),icon:'<svg class="" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="14 14 32 32"><g>	<path fill="#ACBCC8" d="M31,20.5v-1c0-0.276-0.224-0.5-0.5-0.5S30,19.224,30,19.5v1c0,0.276,0.224,0.5,0.5,0.5S31,20.776,31,20.5z"></path> <path fill="#ACBCC8" d="M30,39.5v1c0,0.276,0.224,0.5,0.5,0.5s0.5-0.224,0.5-0.5v-1c0-0.276-0.224-0.5-0.5-0.5S30,39.224,30,39.5z"></path> <path fill="#ACBCC8" d="M39.5,30c-0.276,0-0.5,0.224-0.5,0.5s0.224,0.5,0.5,0.5h1c0.276,0,0.5-0.224,0.5-0.5S40.776,30,40.5,30 H39.5z"></path> <path fill="#ACBCC8" d="M19.5,30c-0.276,0-0.5,0.224-0.5,0.5s0.224,0.5,0.5,0.5h1c0.276,0,0.5-0.224,0.5-0.5S20.776,30,20.5,30 H19.5z"></path> <path fill="#D6E2EA" d="M41.284,25.958C41.739,27.223,42,28.58,42,30c0,6.617-5.383,12-12,12c-6.617,0-12-5.383-12-12 s5.383-12,12-12c2.993,0,5.727,1.108,7.831,2.927l2.826-2.827C37.824,15.56,34.096,14,30,14c-8.822,0-16,7.178-16,16 s7.178,16,16,16s16-7.178,16-16c0-2.545-0.613-4.944-1.675-7.083L41.284,25.958z"></path> <path fill="#509FE1" d="M24.975,25.146C24.877,25.049,24.749,25,24.621,25s-0.256,0.049-0.354,0.146l-2.121,2.121 c-0.195,0.195-0.195,0.512,0,0.707l6.439,6.439C28.964,34.792,29.466,35,30,35s1.036-0.208,1.414-0.586l14.5-14.5 c0.195-0.195,0.195-0.512,0-0.707l-2.121-2.121c-0.098-0.098-0.226-0.146-0.354-0.146s-0.256,0.049-0.354,0.146L30,30.172 L24.975,25.146z"></path></g></svg>'},{name:i("Enhanced Ecommerce Tracking","google-analytics-for-wordpress"),description:i("1-click Google Analytics Enhanced Ecommerce tracking for WooCommerce, Easy Digital Downloads & MemberPress.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" width="96" viewBox="0 0 96 102"><path id="package" fill="#509fe1" d="M76.513,14.5a3.346,3.346,0,0,1,2.479,1.039,3.465,3.465,0,0,1,1.021,2.523V39.431a3.466,3.466,0,0,1-1.021,2.523,3.346,3.346,0,0,1-2.479,1.039h-35a3.346,3.346,0,0,1-2.479-1.039,3.466,3.466,0,0,1-1.021-2.523V18.06a3.466,3.466,0,0,1,1.021-2.523A3.346,3.346,0,0,1,41.513,14.5h4.375a6.68,6.68,0,0,1-.729-2.968,6.337,6.337,0,0,1,1.9-4.6A6.119,6.119,0,0,1,51.575,5a6.571,6.571,0,0,1,3.938,1.261,16.639,16.639,0,0,1,3.573,3.784,16.639,16.639,0,0,1,3.573-3.784A6.571,6.571,0,0,1,66.6,5a6.119,6.119,0,0,1,4.521,1.929,6.336,6.336,0,0,1,1.9,4.6,6.68,6.68,0,0,1-.729,2.968h4.229ZM66.6,8.562a3.2,3.2,0,0,0-1.458.3,5.947,5.947,0,0,0-1.823,1.558,41.215,41.215,0,0,0-2.99,4.081H66.6a2.8,2.8,0,0,0,2.078-.853,2.9,2.9,0,0,0,.839-2.115,2.9,2.9,0,0,0-.839-2.115A2.8,2.8,0,0,0,66.6,8.562ZM48.659,11.53a2.9,2.9,0,0,0,.839,2.115,2.8,2.8,0,0,0,2.078.853h6.271a41.215,41.215,0,0,0-2.99-4.081,5.947,5.947,0,0,0-1.823-1.558,3.2,3.2,0,0,0-1.458-.3,2.8,2.8,0,0,0-2.078.853A2.9,2.9,0,0,0,48.659,11.53Zm4.375,7.717H42.679v9.5H75.346v-9.5H64.992l2.625,2.671a1.288,1.288,0,0,1,0,1.632l-0.875.891a1.232,1.232,0,0,1-1.6,0l-5.1-5.194H57.992l-5.1,5.194a1.232,1.232,0,0,1-1.6,0l-0.875-.891a1.288,1.288,0,0,1,0-1.632Zm-10.354,19H75.346V33.494H42.679v4.749Z"></path><path id="cart" fill="#d5e2ea" d="M35.868,69.2l0.958,5.132H79.791a3.479,3.479,0,0,1,2.875,1.443,3.894,3.894,0,0,1,.8,3.207l-0.958,4.009A8.924,8.924,0,1,1,72.364,97.344a8.753,8.753,0,0,1-2.635-6.415,8.369,8.369,0,0,1,2.715-6.335H38.9a8.369,8.369,0,0,1,2.715,6.335,8.753,8.753,0,0,1-2.636,6.415,8.844,8.844,0,0,1-12.618,0,8.66,8.66,0,0,1-2.635-6.335,8.864,8.864,0,0,1,1.2-4.49A9.428,9.428,0,0,1,28.2,83.151L17.021,28.145H5.84A3.825,3.825,0,0,1,2.007,24.3V21.73A3.825,3.825,0,0,1,5.84,17.881H22.291a3.489,3.489,0,0,1,2.316.882,4.075,4.075,0,0,1,1.358,2.165L27.4,28.145"></path><path id="cart-2" data-name="cart" fill="#acbdc9" d="M27.4,28.145H90.173a3.634,3.634,0,0,1,3.035,1.443,3.486,3.486,0,0,1,.639,3.207L86.34,66.152A3.749,3.749,0,0,1,84.982,68.4a3.7,3.7,0,0,1-2.316.8h-46.8"></path></svg>'},{name:i("Page Level Analytics","google-analytics-for-wordpress"),description:i("Get detailed stats for each post and page, so you can see the most popular posts, pages, and sections of your site.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" width="156" viewBox="0 0 156 156">    <rect fill="#d5e2e9" y="12" width="126" height="144" rx="8" ry="8"></rect>    <rect fill="#acbdc9" x="16" y="132" width="94" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="16" y="79" width="26" height="47" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="48" y="119" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="48" y="106" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="48" y="92" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="48" y="79" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="16" y="56" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="16" y="43" width="62" height="6" rx="3" ry="3"></rect>    <rect fill="#acbdc9" x="16" y="30" width="62" height="6" rx="3" ry="3"></rect> <circle fill="#ffffff" cx="94.5" cy="42.5" r="35.5"></circle> <path fill="#54a0de" d="M155.386,96.172A2.214,2.214,0,0,1,156,97.813a3,3,0,0,1-.616,1.846l-4.716,4.512a2.551,2.551,0,0,1-1.846.82,1.948,1.948,0,0,1-1.641-.82L122.164,79.356a2.729,2.729,0,0,1-.616-1.641V74.844a44.053,44.053,0,0,1-12.919,7.69,42.454,42.454,0,0,1-36.4-2.974A43.1,43.1,0,0,1,56.744,64.077,41.43,41.43,0,0,1,51,42.647a41.43,41.43,0,0,1,5.742-21.431A43.1,43.1,0,0,1,72.227,5.733a42.863,42.863,0,0,1,42.862,0,43.1,43.1,0,0,1,15.483,15.483,41.43,41.43,0,0,1,5.742,21.431,41.258,41.258,0,0,1-2.768,14.971,44.07,44.07,0,0,1-7.691,12.92h2.871a2.217,2.217,0,0,1,1.641.615ZM93.658,75.459a32.1,32.1,0,0,0,16.406-4.409,32.573,32.573,0,0,0,12-12,32.729,32.729,0,0,0,0-32.812,32.573,32.573,0,0,0-12-12,32.728,32.728,0,0,0-32.812,0,32.58,32.58,0,0,0-12,12,32.728,32.728,0,0,0,0,32.813,32.58,32.58,0,0,0,12,12A32.1,32.1,0,0,0,93.658,75.459Z"></path><path fill="#d4e2e8" d="M112.786,52.128H78.724V28.066a0.934,0.934,0,0,0-.937-0.937H74.661a0.934,0.934,0,0,0-.937.938V56.191a0.933,0.933,0,0,0,.938.938h38.125a0.933,0.933,0,0,0,.937-0.937V53.066A0.933,0.933,0,0,0,112.786,52.128Z"></path>    <path fill="#acbdc9" d="M102.864,34.55v0.078a0.717,0.717,0,0,1,.742-0.2,0.826,0.826,0,0,1,.586.508l7.031,14.688h-30V41.5l6.8-11.328a0.888,0.888,0,0,1,.742-0.43,0.9,0.9,0,0,1,.82.352L96.224,39Z"></path></svg>'},{name:i("Affiliate Link & Ads Tracking","google-analytics-for-wordpress"),description:i("Automatically track clicks on your affiliate links, banner ads, and other outbound links with our link tracking.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" width="96" viewBox="0 0 96 102"><rect fill="#8ba4b7" x="41" y="60" width="14" height="42"></rect><path fill="#acbdc9" d="M14,71H82a0,0,0,0,1,0,0v3a2,2,0,0,1-2,2H16a2,2,0,0,1-2-2V71A0,0,0,0,1,14,71Z"></path><rect fill="#509fe2" y="5" width="96" height="66" rx="4.129" ry="4.129"></rect><path fill="#ffffff" d="M40.866,38.665l1.266-3.727L43.4,38.665H40.866ZM54.506,37.54a1.681,1.681,0,1,1-1.2.492A1.627,1.627,0,0,1,54.506,37.54Zm7.875-13.5a3.361,3.361,0,0,1,3.375,3.375v20.25a3.361,3.361,0,0,1-3.375,3.375H33.131a3.361,3.361,0,0,1-3.375-3.375V27.415a3.361,3.361,0,0,1,3.375-3.375h29.25ZM47.4,44.29a1.01,1.01,0,0,0,.879-0.457,1.2,1.2,0,0,0,.176-1.02l-3.8-10.9a1.957,1.957,0,0,0-.633-0.809,1.6,1.6,0,0,0-.984-0.316H41.217a1.6,1.6,0,0,0-.984.316,1.955,1.955,0,0,0-.633.809l-3.8,10.9a1.2,1.2,0,0,0,.176,1.02,1.01,1.01,0,0,0,.879.457h1.2a1.185,1.185,0,0,0,.668-0.211,0.86,0.86,0,0,0,.387-0.562L39.67,42.04h4.922l0.563,1.477a0.86,0.86,0,0,0,.387.563,1.185,1.185,0,0,0,.668.211h1.2Zm12.164-1.125V31.915a1.083,1.083,0,0,0-1.125-1.125H57.319a1.083,1.083,0,0,0-1.125,1.125v2.531a5.309,5.309,0,0,0-1.687-.281,5.063,5.063,0,1,0,0,10.125,4.64,4.64,0,0,0,1.969-.422,1.064,1.064,0,0,0,.844.422h1.125A1.083,1.083,0,0,0,59.569,43.165Z"></path><path fill="#2e7fbe" d="M73,0h1a2,2,0,0,1,2,2v8H71V2A2,2,0,0,1,73,0ZM70.5,10h6a2.5,2.5,0,0,1,0,5h-6A2.5,2.5,0,0,1,70.5,10Z"></path><path fill="#2e7fbe" d="M48,0h1a2,2,0,0,1,2,2v8H46V2A2,2,0,0,1,48,0ZM45.5,10h6a2.5,2.5,0,0,1,0,5h-6A2.5,2.5,0,0,1,45.5,10Z"></path><path fill="#2e7fbe" d="M23,0h1a2,2,0,0,1,2,2v8H21V2A2,2,0,0,1,23,0ZM20.5,10h6a2.5,2.5,0,0,1,0,5h-6A2.5,2.5,0,0,1,20.5,10Z"></path></svg>'},{name:i("EU Compliance (GDPR Friendly)","google-analytics-for-wordpress"),description:i("Make Google Analytics compliant with GDPR and other privacy regulations automatically.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" width="96" viewBox="0 0 96 102"><path fill="#adbdc7" d="M28.884,78.139a0.832,0.832,0,0,0-.479-0.437,0.932,0.932,0,0,0-.629,0,0.832,0.832,0,0,0-.479.438l-1.777,3.609-3.992.574a0.83,0.83,0,0,0-.561.328,0.914,0.914,0,0,0-.191.6,0.839,0.839,0,0,0,.26.574l2.9,2.816-0.684,3.992a0.864,0.864,0,0,0,.123.615,0.8,0.8,0,0,0,.506.369,0.843,0.843,0,0,0,.629-0.082l3.582-1.859,3.582,1.859a0.843,0.843,0,0,0,.629.082,0.8,0.8,0,0,0,.506-0.369,0.863,0.863,0,0,0,.123-0.615l-0.684-3.992,2.9-2.816a0.839,0.839,0,0,0,.26-0.574,0.912,0.912,0,0,0-.191-0.6,0.83,0.83,0,0,0-.56-0.328l-3.992-.574Zm-14-14a0.832,0.832,0,0,0-.479-0.438,0.932,0.932,0,0,0-.629,0,0.832,0.832,0,0,0-.479.438l-1.777,3.609-3.992.574a0.83,0.83,0,0,0-.561.328,0.913,0.913,0,0,0-.191.6,0.839,0.839,0,0,0,.26.574l2.9,2.816L9.251,76.635a0.864,0.864,0,0,0,.123.615,0.8,0.8,0,0,0,.506.369,0.843,0.843,0,0,0,.629-0.082l3.582-1.859,3.582,1.859a0.843,0.843,0,0,0,.629.082,0.8,0.8,0,0,0,.506-0.369,0.863,0.863,0,0,0,.123-0.615l-0.684-3.992,2.9-2.816a0.839,0.839,0,0,0,.26-0.574,0.912,0.912,0,0,0-.191-0.6,0.83,0.83,0,0,0-.561-0.328l-3.992-.574Zm-5-19A0.832,0.832,0,0,0,9.406,44.7a0.932,0.932,0,0,0-.629,0,0.832,0.832,0,0,0-.479.438L6.521,48.749l-3.992.574a0.83,0.83,0,0,0-.561.328,0.913,0.913,0,0,0-.191.6,0.839,0.839,0,0,0,.26.574l2.9,2.817L4.251,57.635a0.864,0.864,0,0,0,.123.615,0.8,0.8,0,0,0,.506.369,0.843,0.843,0,0,0,.629-0.082l3.582-1.859,3.582,1.859a0.843,0.843,0,0,0,.629.082,0.8,0.8,0,0,0,.506-0.369,0.863,0.863,0,0,0,.123-0.615l-0.684-3.992,2.9-2.817a0.839,0.839,0,0,0,.26-0.574,0.912,0.912,0,0,0-.191-0.6,0.83,0.83,0,0,0-.561-0.328l-3.992-.574Zm5-20a0.832,0.832,0,0,0-.479-0.437,0.932,0.932,0,0,0-.629,0,0.832,0.832,0,0,0-.479.438l-1.777,3.609-3.992.574a0.83,0.83,0,0,0-.561.328,0.913,0.913,0,0,0-.191.6,0.839,0.839,0,0,0,.26.574l2.9,2.817L9.251,37.635a0.864,0.864,0,0,0,.123.615,0.8,0.8,0,0,0,.506.369,0.843,0.843,0,0,0,.629-0.082l3.582-1.859,3.582,1.859a0.843,0.843,0,0,0,.629.082,0.8,0.8,0,0,0,.506-0.369,0.863,0.863,0,0,0,.123-0.615l-0.684-3.992,2.9-2.817a0.839,0.839,0,0,0,.26-0.574,0.912,0.912,0,0,0-.191-0.6,0.83,0.83,0,0,0-.561-0.328l-3.992-.574Zm14-15A0.832,0.832,0,0,0,28.406,9.7a0.931,0.931,0,0,0-.629,0,0.832,0.832,0,0,0-.479.437l-1.777,3.609-3.992.574a0.83,0.83,0,0,0-.561.328,0.914,0.914,0,0,0-.191.6,0.839,0.839,0,0,0,.26.574l2.9,2.816-0.684,3.992a0.864,0.864,0,0,0,.123.615,0.8,0.8,0,0,0,.506.369,0.843,0.843,0,0,0,.629-0.082l3.582-1.859,3.582,1.859a0.843,0.843,0,0,0,.629.082,0.8,0.8,0,0,0,.506-0.369,0.863,0.863,0,0,0,.123-0.615l-0.684-3.992,2.9-2.816a0.839,0.839,0,0,0,.26-0.574,0.912,0.912,0,0,0-.191-0.6,0.83,0.83,0,0,0-.56-0.328l-3.992-.574Zm19.232,74a0.832,0.832,0,0,1,.479-0.437,0.932,0.932,0,0,1,.629,0,0.832,0.832,0,0,1,.478.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.816,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.816a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.56-0.328l3.992-.574Zm20-6a0.832,0.832,0,0,1,.479-0.437,0.932,0.932,0,0,1,.629,0,0.832,0.832,0,0,1,.479.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.816,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.816a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.56-0.328l3.992-.574Zm14-14a0.832,0.832,0,0,1,.479-0.438,0.932,0.932,0,0,1,.629,0,0.832,0.832,0,0,1,.479.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.816,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.816a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.561-0.328l3.992-.574Zm5-19a0.832,0.832,0,0,1,.479-0.437,0.932,0.932,0,0,1,.629,0,0.832,0.832,0,0,1,.479.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.817,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.817a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.561-0.328l3.992-.574Zm-5-20a0.832,0.832,0,0,1,.479-0.437,0.932,0.932,0,0,1,.629,0,0.832,0.832,0,0,1,.479.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.817,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.817a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.561-0.328l3.992-.574Zm-14-15A0.832,0.832,0,0,1,68.594,9.7a0.931,0.931,0,0,1,.629,0,0.832,0.832,0,0,1,.479.437l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.816,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.816a0.839,0.839,0,0,1-.26-0.574,0.912,0.912,0,0,1,.191-0.6,0.83,0.83,0,0,1,.56-0.328l3.992-.574Zm-20-6A0.832,0.832,0,0,1,48.594,3.7a0.931,0.931,0,0,1,.629,0,0.832,0.832,0,0,1,.478.438l1.777,3.609,3.992,0.574a0.83,0.83,0,0,1,.561.328,0.914,0.914,0,0,1,.191.6,0.839,0.839,0,0,1-.26.574l-2.9,2.816,0.684,3.992a0.864,0.864,0,0,1-.123.615,0.8,0.8,0,0,1-.506.369,0.843,0.843,0,0,1-.629-0.082l-3.582-1.859-3.582,1.859a0.843,0.843,0,0,1-.629.082,0.8,0.8,0,0,1-.506-0.369,0.863,0.863,0,0,1-.123-0.615l0.684-3.992-2.9-2.816a0.839,0.839,0,0,1-.26-0.574,0.913,0.913,0,0,1,.191-0.6,0.829,0.829,0,0,1,.56-0.328l3.992-.574Z"></path><path fill="#509fe2" d="M65.147,42.845a3.346,3.346,0,0,0-.562-1.9,3.032,3.032,0,0,0-1.547-1.2l-13.5-5.625a2.988,2.988,0,0,0-2.531,0l-13.5,5.625a3.032,3.032,0,0,0-1.547,1.2,3.346,3.346,0,0,0-.562,1.9,32.51,32.51,0,0,0,2.391,12.445,28.183,28.183,0,0,0,5.836,9.07,20.888,20.888,0,0,0,7.383,5.2,2.988,2.988,0,0,0,2.531,0,20.754,20.754,0,0,0,6.75-4.57,29.547,29.547,0,0,0,6.188-8.859A32.516,32.516,0,0,0,65.147,42.845ZM46.8,60.7a1.026,1.026,0,0,1-1.547,0l-7.312-7.312a1.106,1.106,0,0,1,0-1.617l1.547-1.547a1.106,1.106,0,0,1,1.617,0l4.922,4.922L56.569,44.6a1.106,1.106,0,0,1,1.617,0l1.547,1.547a1.106,1.106,0,0,1,0,1.617Z"></path></svg>'},{name:i("Custom Dimensions","google-analytics-for-wordpress"),description:i("Setup tracking for authors, categories, tags, searches, custom post types, users, and other events with 1-click.","google-analytics-for-wordpress"),icon:'<svg class="" xmlns="http://www.w3.org/2000/svg" width="96" viewBox="0 0 96 102"><path fill="#509fe2" d="M93.623,52.744A9.542,9.542,0,0,1,91.9,64.718a8.43,8.43,0,0,1-5.919,2.29H47.033a8.569,8.569,0,0,1-5.919-2.225,9.134,9.134,0,0,1-2.991-5.627A9.485,9.485,0,0,1,39.4,52.744l14.894-24.6V8.383H53.27a2.906,2.906,0,0,1-2.164-.916,3.072,3.072,0,0,1-.891-2.225V3.149A3.072,3.072,0,0,1,51.106.924,2.906,2.906,0,0,1,53.27.008H79.748a2.906,2.906,0,0,1,2.164.916A3.072,3.072,0,0,1,82.8,3.149V5.242a3.072,3.072,0,0,1-.891,2.225,2.906,2.906,0,0,1-2.164.916H78.729v19.76ZM55.562,41.883H77.456l-6.11-10.207a3.9,3.9,0,0,1-.764-2.355V8.383H62.436V29.32a3.9,3.9,0,0,1-.764,2.355Z"></path><path fill="#d6e2ea" d="M68,99.334a11.55,11.55,0,0,0,4.246,0l1.061,1.837a1.418,1.418,0,0,0,.863.722,2.006,2.006,0,0,0,1.128.065,14.624,14.624,0,0,0,4.246-2.492,1.388,1.388,0,0,0,.6-0.984,1.94,1.94,0,0,0-.2-1.115l-1.061-1.837A13.05,13.05,0,0,0,81,91.987h2.123a1.686,1.686,0,0,0,1.128-.394,1.307,1.307,0,0,0,.464-1.05,11.23,11.23,0,0,0,0-4.855,1.175,1.175,0,0,0-.464-0.919,1.686,1.686,0,0,0-1.128-.394H81A15.309,15.309,0,0,0,78.879,80.7l1.061-1.837a1.94,1.94,0,0,0,.2-1.115,1.388,1.388,0,0,0-.6-0.984A12.6,12.6,0,0,0,75.3,74.4a1.437,1.437,0,0,0-1.128-.066,1.419,1.419,0,0,0-.863.722l-1.061,1.968A13.838,13.838,0,0,0,68,76.9l-1.062-1.837a1.419,1.419,0,0,0-.862-0.722,1.438,1.438,0,0,0-1.128.066A12.6,12.6,0,0,0,60.7,76.766a1.389,1.389,0,0,0-.6.984,1.941,1.941,0,0,0,.2,1.115L61.363,80.7a15.312,15.312,0,0,0-2.123,3.674H57.117a1.686,1.686,0,0,0-1.128.394,1.759,1.759,0,0,0-.6.919,12.766,12.766,0,0,0,.133,4.855,1.307,1.307,0,0,0,.464,1.05,1.686,1.686,0,0,0,1.128.394H59.24a13.053,13.053,0,0,0,2.123,3.543L60.3,97.366a1.941,1.941,0,0,0-.2,1.115,1.389,1.389,0,0,0,.6.984,14.624,14.624,0,0,0,4.246,2.492,2.007,2.007,0,0,0,1.128-.065,1.419,1.419,0,0,0,.862-0.722Zm-1.327-7.741A5.082,5.082,0,0,1,65.543,86.8a4.6,4.6,0,0,1,3.185-3.215,5.074,5.074,0,0,1,4.843,1.05A5.276,5.276,0,0,1,74.7,89.494a4.591,4.591,0,0,1-3.184,3.149A5.074,5.074,0,0,1,66.671,91.593Z"></path><path fill="#acbdc9" d="M51.279,72.568a21.653,21.653,0,0,0,0-8.266l4.511-2.231a3.077,3.077,0,0,0,1.393-1.706,3.115,3.115,0,0,0-.066-2.1,30.1,30.1,0,0,0-5.573-8.66,3.226,3.226,0,0,0-1.924-1.115,2.645,2.645,0,0,0-2.057.459l-3.848,2.1a25.939,25.939,0,0,0-7.3-4.068V42.521a2.878,2.878,0,0,0-.8-2.034,3.731,3.731,0,0,0-1.858-1.115,30.03,30.03,0,0,0-10.085.131,2.84,2.84,0,0,0-1.924.984,3.026,3.026,0,0,0-.73,2.034v4.461a22.237,22.237,0,0,0-7.3,4.068L9.88,48.95a3.054,3.054,0,0,0-2.189-.459A2.7,2.7,0,0,0,5.9,49.606a32.637,32.637,0,0,0-5.573,8.66,2.5,2.5,0,0,0-.133,2.1,3.408,3.408,0,0,0,1.46,1.706L6.032,64.3a21.654,21.654,0,0,0,0,8.266L1.653,74.8a2.825,2.825,0,0,0-1.46,1.64A2.643,2.643,0,0,0,.326,78.6,32.636,32.636,0,0,0,5.9,87.263a2.909,2.909,0,0,0,1.791.984,3.477,3.477,0,0,0,2.189-.328l3.848-2.23a22.345,22.345,0,0,0,7.3,4.2v4.461a3.107,3.107,0,0,0,.73,1.968,2.744,2.744,0,0,0,1.924,1.05,27.911,27.911,0,0,0,10.085,0,2.889,2.889,0,0,0,1.858-1.05,2.957,2.957,0,0,0,.8-1.968V89.887a24.326,24.326,0,0,0,7.3-4.2l3.848,2.23a3.007,3.007,0,0,0,2.057.328,3.535,3.535,0,0,0,1.924-.984q4.379-5.248,5.573-8.66a2.643,2.643,0,0,0,.133-2.165,2.825,2.825,0,0,0-1.46-1.64ZM35.754,75.323a11.432,11.432,0,0,1-7.7,2.493,8.964,8.964,0,0,1-6.17-2.69,8.772,8.772,0,0,1-2.72-6.1,10.684,10.684,0,0,1,2.521-7.479,10.691,10.691,0,0,1,7.563-2.624,9.649,9.649,0,0,1,9.023,8.922A10.684,10.684,0,0,1,35.754,75.323Z"></path></svg>'}],pro_features:[i("Ecommerce Report","google-analytics-for-wordpress"),i("Form Conversions","google-analytics-for-wordpress"),i("Custom Dimensions","google-analytics-for-wordpress"),i("Author Tracking","google-analytics-for-wordpress"),i("Category / Tags Tracking","google-analytics-for-wordpress"),i("WooCommerce","google-analytics-for-wordpress"),i("Easy Digital Downloads","google-analytics-for-wordpress"),i("MemberPress","google-analytics-for-wordpress"),i("LifterLMS","google-analytics-for-wordpress")],testimonials:[{image:"monsterinsights-testimonial-one",text:"It just works. Really easy way to insert Google Analytics tracking code and keep it there when switching themes. No need to copy/paste code anywhere. This is the best way to handle Google Analytics in WordPress.",author:"Steven Gliebe",function:"Founder of ChurchThemes"},{image:"monsterinsights-testimonial-two",text:"Analytics for PROs! This plugin brings it all, great features and helpful info to easily see what you are doing.",author:"Frank van der Sluijs",function:"Business Consultant"}],welcome_video:!1}},computed:{welcome_title(){return this.$mi.first_name&&this.$mi.first_name.length<28?this.text_welcome_title+" "+this.$mi.first_name:this.text_welcome_title}},methods:{feature_class(s){return"monsterinsights-bg-img "+s}}};var Ue=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-admin-page monsterinsights-welcome"},[e("div",{staticClass:"monsterinsights-welcome-container"},[e("div",{staticClass:"monsterinsights-welcome-block monsterinsights-welcome-block-first"},[t._m(0),e("div",{staticClass:"monsterinsights-welcome-block-inner"},[e("h3",{domProps:{textContent:t._s(t.welcome_title)}}),e("p",{staticClass:"monsterinsights-subtitle",domProps:{textContent:t._s(t.text_welcome_subtitle)}})]),e("div",{staticClass:"monsterinsights-welcome-video"},[e("div",{staticClass:"monsterinsights-welcome-video-image monsterinsights-bg-img",on:{click:function(o){t.welcome_video=!0}}}),t.welcome_video?e("welcome-overlay",{attrs:{id:"welcome-video"},on:{close:function(o){t.welcome_video=!1}}},[e("iframe",{attrs:{width:"1280",height:"720",src:"https://www.youtube.com/embed/4Y8TGGkdcGY?autoplay=1&modestbranding=1&showinfo=0&rel=0&fs=1",frameborder:"0",allow:"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""}})]):t._e()],1),e("div",{staticClass:"monsterinsights-welcome-block-inner"},[e("p",{domProps:{textContent:t._s(t.text_above_buttons)}}),e("div",{staticClass:"monsterinsights-button-wrap"},[e("div",{staticClass:"monsterinsights-welcome-left"},[e("launch-wizard-button",{attrs:{"button-class":"monsterinsights-button monsterinsights-button-large","button-text":t.text_wizard_button}})],1),e("div",{staticClass:"monsterinsights-welcome-right"},[e("a",{staticClass:"monsterinsights-button monsterinsights-button-alt monsterinsights-button-large",attrs:{href:t.$getUrl("welcome-screen","guide","https://www.monsterinsights.com/docs/connect-google-analytics/"),target:"_blank",rel:"noopener noreferrer"},domProps:{textContent:t._s(t.text_read_more_button)}})])]),e("p",{staticClass:"monsterinsights-disclaimer-note",domProps:{textContent:t._s(t.text_below_buttons)}})])]),e("div",{staticClass:"monsterinsights-welcome-block"},[e("div",{staticClass:"monsterinsights-welcome-block-inner"},[e("h3",{domProps:{textContent:t._s(t.text_features_title)}}),e("p",{staticClass:"monsterinsights-subtitle",domProps:{textContent:t._s(t.text_features_subtitle)}})]),e("div",{staticClass:"monsterinsights-welcome-block-inner monsterinsights-welcome-features"},t._l(t.features,function(o,n){return e("div",{key:n,staticClass:"monsterinsights-welcome-feature"},[e("div",{staticClass:"monsterinsights-welcome-feature-img",domProps:{innerHTML:t._s(o.icon)}}),e("div",{staticClass:"monsterinsights-welcome-feature-text"},[e("h4",{domProps:{textContent:t._s(o.name)}}),e("p",{domProps:{textContent:t._s(o.description)}})])])}),0),e("div",{staticClass:"monsterinsights-welcome-block-inner monsterinsights-welcome-block-footer"},[e("a",{staticClass:"monsterinsights-button",attrs:{href:t.$getUrl("welcome-screen","features-button","https://monsterinsights.com/features"),target:"_blank"},domProps:{textContent:t._s(t.text_view_all_features)}})]),e("div",{staticClass:"monsterinsights-upgrade-cta"},[e("div",{staticClass:"monsterinsights-welcome-block-inner"},[e("div",{staticClass:"monsterinsights-welcome-left"},[e("h2",{domProps:{textContent:t._s(t.text_upgrade_to_pro)}}),e("ul",t._l(t.pro_features,function(o,n){return e("li",{key:n},[e("i",{staticClass:"monstericon-check"}),t._v(" "+t._s(o)+" ")])}),0)]),e("div",{staticClass:"monsterinsights-welcome-right"},[t._m(1),e("div",{staticClass:"monsterinsights-price"},[e("span",{staticClass:"monsterinsights-amount"},[t._v(" 199 ")]),e("br"),e("span",{staticClass:"monsterinsights-term",domProps:{textContent:t._s(t.text_per_year)}})]),e("a",{staticClass:"monsterinsights-button monsterinsights-button-large",attrs:{href:t.$getUpgradeUrl("welcome-screen","upgrade-features"),rel:"noopener noreferrer",target:"_blank"},domProps:{textContent:t._s(t.text_upgrade_now)}})])])]),e("div",{staticClass:"monsterinsights-welcome-testimonials monsterinsights-welcome-block-inner"},[e("h3",{domProps:{textContent:t._s(t.text_testimonials)}}),t._l(t.testimonials,function(o,n){return e("div",{key:n,staticClass:"monsterinsights-welcome-testimonial"},[e("div",{staticClass:"monsterinsights-welcome-testimonial-image"},[e("div",{class:o.image+" monsterinsights-bg-img"})]),e("div",{staticClass:"monsterinsights-welcome-testimonial-text"},[e("p",{domProps:{textContent:t._s(o.text)}}),e("p",[e("strong",{domProps:{textContent:t._s(o.author)}}),t._v(", "+t._s(o.function))])])])})],2),e("div",{staticClass:"monsterinsights-welcome-footer-upsell monsterinsights-welcome-block-inner"},[e("div",{staticClass:"monsterinsights-welcome-left"},[e("launch-wizard-button",{attrs:{"button-class":"monsterinsights-button","button-text":t.text_wizard_button}})],1),e("div",{staticClass:"monsterinsights-welcome-right"},[e("a",{staticClass:"monsterinsights-button monsterinsights-button-alt",attrs:{href:t.$getUpgradeUrl("welcome-screen","upgrade-testimonials"),rel:"noopener noreferrer",target:"_blank"},domProps:{textContent:t._s(t.text_upgrade_now)}})]),e("p",{staticClass:"monsterinsights-disclaimer-note",domProps:{textContent:t._s(t.text_below_buttons)}})])])])])},We=[function(){var s=this,t=s._self._c;return t("div",{staticClass:"monsterinsights-welcome-logo-container"},[t("div",{staticClass:"monsterinsights-welcome-logo monsterinsights-bg-img"})])},function(){var s=this,t=s._self._c;return t("h2",[t("span",[s._v("PRO")])])}],Fe=p(Re,Ue,We,!1,null,null,null,null);const De=Fe.exports,Ge={name:"WizardModuleMigration"};var Ze=function(){var t=this,e=t._self._c;return e("div")},Ne=[],Ve=p(Ge,Ze,Ne,!1,null,null,null,null);const Xe=Ve.exports,{__:C}=wp.i18n,I={install(s){s.prototype.$swal&&(s.prototype.$mi_saving_toast=function(){},s.prototype.$mi_success_toast=function(){},s.prototype.$mi_error_toast=function(t){let{icon:e="error",customContainerClass:o="monsterinsights-swal",allowOutsideClick:n=!1,allowEscapeKey:r=!1,allowEnterKey:c=!1,title:f=C("Error","google-analytics-for-wordpress"),html:m=C("Please try again.","google-analytics-for-wordpress"),confirmButtonText:P="OK",showCancelButton:O=!1,cancelButtonText:z="Cancel",footer:B=!1}=t;return s.prototype.$swal({icon:e,customClass:{container:o},allowOutsideClick:n,allowEscapeKey:r,allowEnterKey:c,title:f,html:m,footer:B,showCancelButton:O,cancelButtonText:z,confirmButtonText:P,didOpen:function(){s.prototype.$swal.hideLoading()}})},s.prototype.$mi_loading_toast=function(){s.prototype.$swal({customClass:{container:"monsterinsights-swal"},icon:"info",title:C("Loading settings","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){s.prototype.$swal.showLoading()}})}),s.prototype.$getCustomLoaderHtml=()=>{}}},T=document.getElementById("monsterinsights-vue-onboarding-wizard"),k=document.getElementById("monsterinsights-welcome"),$=document.getElementById("monsterinsights-migration-wizard");u.config.productionTip=!1;if(T||k||$)if({}.NODE_ENV!=="production"&&(u.config.devtools=!0,u.config.performance=!0),U({ctrl:!0}),u.use(E),u.use(W),u.use(F,{defaultTemplate:'<div class="monsterinsights-tooltip" role="tooltip"><div class="monsterinsights-tooltip-arrow"></div><div class="monsterinsights-tooltip-inner"></div></div>',defaultArrowSelector:".monsterinsights-tooltip-arrow, .monsterinsights-tooltip__arrow",defaultInnerSelector:".monsterinsights-tooltip-inner, .monsterinsights-tooltip__inner"}),u.use(j),k)new u({store:h,mounted:()=>{h.dispatch("$_app/init")},render:s=>s(De)}).$mount(k);else if($){const s={install(t){t.prototype.$wizard_steps=["welcome","authenticate","recommended_settings"],t.prototype.$mi&&t.prototype.$mi.had_ecommerce&&t.prototype.$wizard_steps.push("pro"),t.prototype.$wizard_steps.push("success")}};u.use(s),u.use(I),new u({store:h,mounted:()=>{h.dispatch("$_app/init"),h.dispatch("$_license/getLicense"),h.dispatch("$_auth/getAuth"),h.dispatch("$_addons/getAddons")},render:t=>t(Xe)}).$mount($)}else{const s={install(t){t.prototype.$wizard_steps=["welcome","authenticate"],t.prototype.$mi&&!t.prototype.$mi.network&&t.prototype.$wizard_steps.push("recommended_settings"),t.prototype.$mi&&!t.prototype.$mi.migrated&&t.prototype.$wizard_steps.push("recommended_addons"),t.prototype.$wizard_steps.push("success")}};u.use(s),u.use(I),new u({store:h,mounted:()=>{h.dispatch("$_app/init"),h.dispatch("$_license/getLicense"),h.dispatch("$_auth/getAuth"),h.dispatch("$_addons/getAddons")},render:t=>t(He)}).$mount(T)}
