import{V as a,a as A,h as o}from"./vendor-0853f02f.min.js";import{b as n}from"./index-468bfca9.min.js";const{__:h,sprintf:v}=wp.i18n,T=(t,e,i,l)=>new Promise(s=>{let c=new FormData;c.append("action","monsterinsights_vue_get_report_data"),c.append("nonce",a.prototype.$mi.nonce),c.append("report",e),c.append("start",i),c.append("end",l),t.getters.date.compareReport&&(c.append("compare_report",!0),c.append("compare_end",t.getters.date.compareEnd),c.append("compare_start",t.getters.date.compareStart)),A.post(a.prototype.$mi.ajax,c).then(d=>{s(d.data)}).catch(function(d){t.dispatch("$_app/block",!1,{root:!0});const f=a.prototype.$getUrl("admin-notices","error-verifying-license","https://www.monsterinsights.com/my-account/support");if(d.response){const p=d.response;return a.prototype.$mi_error_toast({title:v(h("Can't load report data. Error: %1$s, %2$s. Please try again in a few minutes. If the issue persists, please %3$scontact our support%4$s team.","google-analytics-for-wordpress"),p.status,p.statusText,'<a target="_blank" href="'+f+'">',"</a>")})}a.prototype.$swal.hideLoading(),a.prototype.$mi_error_toast({allowOutsideClick:!0,allowEscapeKey:!0,title:h("Error loading report data","google-analytics-for-wordpress"),html:d.message})})}),b=t=>{if(typeof t<"u"){const e=parseInt(t.toString().replace(".",""),10);return e.toString().length===1?parseInt(e===1?e+"00":e+"0"):e}},D=t=>{if(typeof t<"u"){let e={};return Object.keys(t).forEach(function(i){let l=i.toString().split("-").join("_");e[l]=t[i]}),e}},$={fetchReportData:T,getFormattedScore:b,keysReplaceHyphensWithUnderscores:D},{__:r,sprintf:_}=wp.i18n,R=(t,e)=>{const i=["overview","site_summary","yearinreview"];return!a.prototype.$isPro()&&!i.includes(e)?null:new Promise(l=>{if(!a.prototype.$mi.authed)return l(!1),t.commit("ENABLE_BLUR"),t.commit("ENABLE_NOAUTH"),!1;if(t.state.reload_report===!1&&t.state[e]&&t.state[e].reportcurrentrange&&!t.state.date.compareReport&&t.state[e].reportcurrentrange.startDate===t.state.date.start&&t.state[e].reportcurrentrange.endDate===t.state.date.end)return l(!1),t.commit("DISABLE_BLUR"),!1;a.prototype.$mi_loading_toast(),t.commit("ENABLE_BLUR"),t.dispatch("$_queue/add",()=>$.fetchReportData(t,e,t.state.date.start,t.state.date.end).then(function(s){if(t.commit("SET_IS_LOADED"),s.data.message==="license_level"){g(t),l(!1);return}if(s.success)g(t),t.commit("DISABLE_BLUR"),t.commit("UPDATE_REPORT_DATA",{report:e,data:s.data}),l(!0);else{if(s.data.message==="invalid_grant"){g(t),l(!1),t.commit("ENABLE_REAUTH");return}if(s.data.footer&&s.data.footer==="install_addon"){let c=t.state.required_addon?t.state.required_addon:e;L(t,c).then(function(d){t.rootState.$_widget&&(t.commit("DISABLE_BLUR"),t.commit("$_widget/UPDATE_LOADED",!0,{root:!0}));let f=r(d?"activate":"install","google-analytics-for-wordpress");a.prototype.$mi_error_toast({title:!1,html:_(s.data.message,f),footer:'<a href="'+a.prototype.$mi.addons_url+'">'+r("Visit addons page","google-analytics-for-wordpress")+"</a>",report:e}),a.prototype.$swal({icon:"error",customClass:{container:"monsterinsights-swal"},title:r("Report Unavailable","google-analytics-for-wordpress"),html:_(s.data.message,f),allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!1,showCancelButton:!0,confirmButtonText:_(r("%s Addon","google-analytics-for-wordpress"),f.charAt(0).toUpperCase()+f.slice(1)),cancelButtonText:r("Dismiss","google-analytics-for-wordpress")}).then(function(p){p.value&&(d?y(t,t.rootState.$_addons.addons[c]):E(t,c))})})}else s.data.footer&&s.data.footer.indexOf("#/ecommerce")>0?(l(!1),a.prototype.$mi_error_toast({title:!1,html:s.data.message,report:e,showCancelButton:!0,cancelButtonText:r("Go Back To Reports","google-analytics-for-wordpress"),confirmButtonText:r("Enable Enhanced eCommerce","google-analytics-for-wordpress")}).then(function(c){c.value&&(window.location=s.data.footer)})):s.data.type&&s.data.type==="expired_license"?(l(!1),a.prototype.$mi_error_toast({type:"",customClass:{container:"monsterinsights-expired-license-alert-toast"},confirmButtonText:"",title:!1,html:s.data.message,footer:s.data.footer,report:e})):(l(!1),a.prototype.$mi_error_toast({title:!1,html:s.data.message,footer:s.data.footer,report:e,onClose:()=>{if(!s.data.type||s.data.type!=="INVALID_DATE_RANGE"||t.state.date==="last30days")return;const c=a.prototype.$mi_intervals().last30days;t.commit("UPDATE_INTERVAL","last30days"),t.commit("UPDATE_DATE",{start:c.start.format("YYYY-MM-DD"),end:c.end.format("YYYY-MM-DD")}),t.dispatch("getReportData",t.state.activeReport)}}))}}),{root:!0})})};function L(t,e){return new Promise(function(i){t.dispatch("$_addons/getAddons","",{root:!0}).then(function(){t.rootState.$_addons.addons[e]&&t.rootState.$_addons.addons[e].installed?i(!0):i(!1)}).catch(function(){i(!1),w()})})}function E(t,e){a.prototype.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:r("Installing Addon","google-analytics-for-wordpress"),html:r("Please wait","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){a.prototype.$swal.showLoading(),t.dispatch("$_addons/installAddon",t.rootState.$_addons.addons[e],{root:!0}).then(function(){y(t,t.rootState.$_addons.addons[e])}).catch(function(){w()})}})}function y(t,e){a.prototype.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:r("Activating Addon","google-analytics-for-wordpress"),html:r("Please wait","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){a.prototype.$swal.showLoading()}}),t.dispatch("$_addons/activateAddon",e,{root:!0}).then(function(){a.prototype.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:r("Addon Activated","google-analytics-for-wordpress"),html:r("Loading report data","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){a.prototype.$swal.showLoading(),setTimeout(function(){window.location.reload()},1e3)}})}).catch(function(i){w(i)})}function w(t){let e=r("Please activate manually","google-analytics-for-wordpress");t.response&&(e=_(r("Error: %1$s, %2$s","google-analytics-for-wordpress"),t.response.status,t.response.statusText)),a.prototype.$swal({icon:"error",customClass:{container:"monsterinsights-swal"},title:r("Error Activating Addon","google-analytics-for-wordpress"),html:e,allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,showCancelButton:!0,confirmButtonText:r("View Addons","google-analytics-for-wordpress"),cancelButtonText:r("Dismiss","google-analytics-for-wordpress")}).then(function(i){i.value&&(window.location=a.prototype.$mi.addons_url,a.prototype.$swal({icon:"info",customClass:{container:"monsterinsights-swal"},title:r("Redirecting","google-analytics-for-wordpress"),html:r("Please wait","google-analytics-for-wordpress"),allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,didOpen:function(){a.prototype.$swal.showLoading()}}))})}function g(t){t.rootState.$_widget||typeof a.prototype.$swal=="function"&&a.prototype.$swal.close()}const S={getReportData:R,installAddon:E,activateAddon:y},O=t=>t.yearinreview_data,B=t=>t.activeReport,C=t=>t.isLoaded,U=t=>t.blur,P=t=>t.mobileTableExpanded,Y=t=>t.overview,I=t=>t.site_summary,k=t=>t.publisher,M=t=>t.ecommerce,N=t=>t.ecommerce_coupons,K=t=>t.ecommerce_funnel,q=t=>t.cart_abandonment,V=t=>t.queries,H=t=>t.dimensions,F=t=>t.forms,G=t=>t.realtime,W=t=>t.yearinreview,X=t=>t.sitespeed,j=t=>t.sitespeedmobile,Q=t=>t.noauth,z=t=>t.reauth,J=t=>t.media,Z=t=>t.countries,x=t=>t.traffic_overview,tt=t=>t.traffic_landing_pages,et=t=>t.traffic_technology,at=t=>t.traffic_campaign,ot=t=>t.traffic_source_medium,st=t=>t.traffic_social,rt=t=>t.engagement_pages,nt=()=>n("overview"),it=()=>n("ecommerce"),ct=()=>n("ecommerce_coupons"),lt=()=>n("cart_abandonment"),dt=()=>n("dimensions"),mt=()=>n("countries"),ft=()=>n("traffic_overview"),pt=()=>n("traffic_technology"),ut=()=>n("traffic_landing_pages"),_t=()=>n("traffic_campaign"),gt=()=>n("traffic_source_medium"),yt=()=>n("traffic_social"),wt=()=>n("publisher"),ht=()=>n("engagement_pages"),Et=()=>n("queries"),At=()=>n("forms"),vt=()=>n("realtime"),Tt=()=>n("sitespeed"),bt=()=>n("media"),Dt=()=>n("ecommerce_funnel");function u(t,e){return t.format("YYYYMMDD")===e.format("YYYYMMDD")?t.format("MMMM D, YYYY"):t.format("MMMM D")+" - "+e.format("MMMM D, YYYY")}function $t(t){let e=t.date,i=a.prototype.$mi_intervals();if(i[e.interval]){let l=i[e.interval];e.intervalText=u(l.start,l.end),e.intervalCompareText=u(l.compareStart,l.compareEnd)}else e.intervalText=u(o(e.start),o(e.end)),e.intervalCompareText=u(o(e.compareStart),o(e.compareEnd));return e}const Rt={yearinreview_data:O,date:$t,activeReport:B,isLoaded:C,blur:U,mobileTableExpanded:P,overview:Y,publisher:k,ecommerce:M,ecommerce_coupons:N,ecommerce_funnel:K,cart_abandonment:q,queries:V,dimensions:H,forms:F,realtime:G,noauth:Q,yearinreview:W,reauth:z,sitespeed:X,sitespeedmobile:j,site_summary:I,media:J,countries:Z,traffic_overview:x,traffic_landing_pages:tt,traffic_technology:et,traffic_campaign:at,traffic_source_medium:ot,traffic_social:st,engagement_pages:rt,demo_overview:nt,demo_ecommerce:it,demo_ecommerce_coupons:ct,demo_cart_abandonment:lt,demo_dimensions:dt,demo_countries:mt,demo_traffic_overview:ft,demo_traffic_technology:pt,demo_traffic_landing_pages:ut,demo_traffic_campaign:_t,demo_traffic_source_medium:gt,demo_traffic_social:yt,demo_publisher:wt,demo_engagement_pages:ht,demo_queries:Et,demo_forms:At,demo_realtime:vt,demo_sitespeed:Tt,demo_media:bt,demo_ecommerce_funnel:Dt},Lt=(t,e)=>{e.report&&e.data&&t[e.report]&&a.set(t,e.report,e.data)},St=(t,e)=>{e.start&&e.end&&(a.set(t.date,"start",e.start),a.set(t.date,"end",e.end))},Ot=(t,e)=>{a.set(t.date,"interval",e)},Bt=(t,e)=>{a.set(t.date,"text",e)},Ct=(t,e)=>{t.activeReport=e},Ut=t=>{t.isLoaded=!0},Pt=t=>{t.blur=!0},Yt=t=>{t.blur=!1},It=t=>{t.mobileTableExpanded=!0},kt=t=>{t.mobileTableExpanded=!1},Mt=t=>{t.noauth=!0},Nt=t=>{t.reauth=!0},Kt=t=>{t.reload_report=!0},qt=t=>{t.reload_report=!1},Vt=(t,e)=>{t.required_addon=e},Ht=(t,e)=>{t.date={...t.date,...e}},Ft={UPDATE_REPORT_DATA:Lt,UPDATE_DATE:St,UPDATE_ACTIVE_REPORT:Ct,UPDATE_INTERVAL:Ot,UPDATE_DATE_TEXT:Bt,SET_IS_LOADED:Ut,ENABLE_BLUR:Pt,DISABLE_BLUR:Yt,EXPAND_TABLES:It,CONTRACT_TABLES:kt,ENABLE_NOAUTH:Mt,ENABLE_REAUTH:Nt,UPDATE_REQUIRED_ADDON:Vt,UPDATE_DATE_STORE:Ht,ENABLE_REPORT_RELOAD:Kt,DISABLE_REPORT_RELOAD:qt},Gt={yearinreview_data:window.monsterinsights.yearinreview,date:{start:"",end:"",interval:"last30days",text:"",compareText:"",compareReport:!1,compareStart:"",compareEnd:""},blur:!1,activeReport:"overview",mobileTableExpanded:!1,overview:{},site_summary:{},publisher:{},ecommerce:{},ecommerce_coupons:{},ecommerce_funnel:{funnel_table:{none:[]},funnel_chart:[]},cart_abandonment:{},queries:{},dimensions:{},forms:{},realtime:{},yearinreview:{},sitespeed:{},sitespeedmobile:{},noauth:!1,reauth:!1,isLoaded:!1,media:{line_chart_report:{},video_details_rows:[]},countries:{},traffic_overview:{},traffic_landing_pages:{},traffic_technology:{},traffic_campaign:{},traffic_source_medium:{},traffic_social:{sessions_chart:{}},engagement_pages:{},required_addon:null,reload_report:!1},jt={namespaced:!0,state:Gt,actions:S,getters:Rt,mutations:Ft},{__:m}=wp.i18n,Qt={today:{text:m("Today","google-analytics-for-wordpress"),start:o(),end:o(),interval:"today",compareStart:o().subtract(1,"days"),compareEnd:o().subtract(1,"days")},yesterday:{text:m("Yesterday","google-analytics-for-wordpress"),start:o().subtract(1,"days"),end:o().subtract(1,"days"),interval:"yesterday",compareStart:o().subtract(2,"days"),compareEnd:o().subtract(2,"days")},lastweek:{text:m("Last Week","google-analytics-for-wordpress"),end:o().startOf("week"),start:o().startOf("week").subtract(6,"days"),interval:"lastweek",compareStart:o().startOf("week").subtract(13,"days"),compareEnd:o().startOf("week").subtract(7,"days")},lastmonth:{text:m("Last Month","google-analytics-for-wordpress"),end:o().startOf("month").subtract(1,"days"),start:o().startOf("month").subtract(1,"month"),interval:"lastmonth",compareStart:o().startOf("month").subtract(2,"month"),compareEnd:o().startOf("month").subtract(1,"month").subtract(1,"days")},last7days:{text:m("Last 7 days","google-analytics-for-wordpress"),end:o().subtract(1,"days"),start:o().subtract(7,"days"),interval:"last7days",compareStart:o().subtract(14,"days"),compareEnd:o().subtract(8,"days")},last30days:{text:m("Last 30 days","google-analytics-for-wordpress"),end:o().subtract(1,"days"),start:o().subtract(30,"days"),interval:"last30days",compareStart:o().subtract(60,"days"),compareEnd:o().subtract(31,"days")},last90days:{text:m("Last 90 days","google-analytics-for-wordpress"),end:o().subtract(1,"days"),start:o().subtract(90,"days"),interval:"last90days",compareStart:o().subtract(180,"days"),compareEnd:o().subtract(91,"days")}};export{Qt as D,jt as R,y as a,$ as h,E as i};
