import{a as o,n as s}from"./vendor-0853f02f.min.js";const{__:r}=wp.i18n,e={name:"LaunchWizardButton",props:{buttonClass:{type:[String,Object,Array],default:"monsterinsights-button"},buttonText:{type:String,default:r("Launch Wizard","google-analytics-for-wordpress")},arrowIcon:{type:String,default:""}},data(){return{launch_wizard_text:r("Launch Wizard","google-analytics-for-wordpress")}},methods:{launchSetupWizard(){this.$mi_loading_toast(r("Preparing setup wizard...","google-analytics-for-wordpress"));let n=new FormData;n.append("action","monsterinsights_generate_setup_wizard_url"),n.append("nonce",this.$mi.nonce),o.post(this.$mi.ajax,n).then(t=>{const{data:a}=t.data;t.data.success&&a.wizard_url&&(window.location.href=a.wizard_url)}).catch(t=>{console.error("Error launching wizard:",t),this.$swal.close()})}}};var i=function(){var t=this,a=t._self._c;return a("button",{class:t.buttonClass,on:{click:t.launchSetupWizard}},[a("span",[t._v(" "+t._s(t.buttonText)+" ")]),a("i",{class:t.arrowIcon})])},c=[],l=s(e,i,c,!1,null,null,null,null);const d=l.exports;export{d as L};
