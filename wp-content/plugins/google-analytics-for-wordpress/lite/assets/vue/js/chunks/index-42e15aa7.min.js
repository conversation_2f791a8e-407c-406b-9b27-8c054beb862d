import{V as o,a as p,m as A,n as r,D as F,h as H,x as T,M as U,f as N}from"./vendor-0853f02f.min.js";import{S as D}from"./SlideDownUp-22dab176.min.js";const j=function(){return new Promise((s,t)=>{let e=new FormData;e.append("action","monsterinsights_vue_install_plugin"),e.append("slug","userfeedback-lite"),e.append("nonce",o.prototype.$mi.nonce),p.post(o.prototype.$mi.ajax,e).then(i=>{s(i.data)}).catch(function(i){t(i),console.log(i)})})},G=function(){return new Promise((s,t)=>{let e=new FormData;e.append("action","monsterinsights_activate_addon"),e.append("nonce",o.prototype.$mi.activate_nonce),e.append("isnetwork",o.prototype.$mi.network),e.append("plugin","userfeedback-lite/userfeedback.php"),p.post(o.prototype.$mi.ajax,e).then(i=>{s(i.data)}).catch(function(i){t(i)})})},I={installUserFeedback:j,activatelUserFeedback:G},{__:u,sprintf:Q}=wp.i18n,O={name:"AddonBlock",props:{addon:Object,isAddon:{type:Boolean,default:!0}},data(){return{text_status:u("Status: %s","google-analytics-for-wordpress"),text_upgrade:u("Upgrade Now","google-analytics-for-wordpress"),activating:!1,deactivating:!1,installing:!1,text_expired:u("License Expired","google-analytics-for-wordpress"),text_upgrade_required:u("Status: Upgrade Required","google-analytics-for-wordpress")}},computed:{...A({license:"$_license/license",license_network:"$_license/license_network"}),addonTitle(){let s=this.addon.title;return s.indexOf("MonsterInsights")===0&&(s=s.replace("MonsterInsights ","")),s},currentRouteName(){return this.$route.name},upgradeUrl(){return this.$getUpgradeUrl("addons-page",String(this.addonTitle).toLocaleLowerCase().replaceAll(/\s+/g,"-"))}},methods:{actionsClass(){let s="monsterinsights-addon-message ";return this.addon.type==="licensed"?this.addon.active?s+="monsterinsights-addon-active":this.addon.installed===!1?s+="monsterinsights-addon-not-installed":s+="monsterinsights-addon-inactive":s+="monsterinsights-addon-not-available",s},statusText(){let s=u("Not Installed","google-analytics-for-wordpress");return this.addon.type!=="licensed"?s=u("Not Available","google-analytics-for-wordpress"):this.addon.active?s=this.$mi.network?u("Network Active","google-analytics-for-wordpress"):u("Active","google-analytics-for-wordpress"):this.addon.installed&&(s=u("Inactive","google-analytics-for-wordpress")),Q(this.text_status,"<span>"+s+"</span>")},textButtonAction(){let s=u("Install","google-analytics-for-wordpress");return this.addon.redirect&&(s=u("Visit Website","google-analytics-for-wordpress")),this.addon.type!=="licensed"?u("Upgrade Now","google-analytics-for-wordpress"):this.activating?u("Activating...","google-analytics-for-wordpress"):this.deactivating?u("Deactivating...","google-analytics-for-wordpress"):this.installing?u("Installing...","google-analytics-for-wordpress"):this.addon.active?u("Deactivate","google-analytics-for-wordpress"):this.addon.installed?u("Activate","google-analytics-for-wordpress"):s},clickAction(){if(this.activating||this.deactivating||this.installing)return!1;this.addon.installed?this.addon.active?this.deactivateAddon():this.activateAddon():this.installAddon()},installAddon(){const s=this,t=this.isAddon?"$_addons/installAddon":"$_addons/installPlugin";this.installing=!0,this.$store.dispatch(t,this.addon).then(function(){s.installing=!1}).catch(function(){s.installing=!1})},activateAddon(){const s=this;this.activating=!0,this.$store.dispatch("$_addons/activateAddon",this.addon).then(function(){s.activating=!1}).catch(function(){s.activating=!1})},deactivateAddon(){const s=this;this.deactivating=!0,this.$store.dispatch("$_addons/deactivateAddon",this.addon).then(function(){s.deactivating=!1}).catch(function(){s.deactivating=!1})},isLicenseExpired(){return!!(typeof this.license<"u"&&this.license&&this.license.is_expired)},isNetworkLicenseExpired(){return!!(typeof this.license_network<"u"&&this.license_network&&this.license_network.is_expired)}}};var P=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-addon",attrs:{id:`monsterinsights-addon-${t.addon.slug}`}},[e("div",{staticClass:"monsterinsights-addon-top"},[t.addon.icon?e("div",{staticClass:"monsterinsights-addon-image"},[e("img",{staticClass:"monsterinsights-addon-thumb",attrs:{src:t.addon.icon,alt:t.addon.title}})]):t._e(),e("div",{staticClass:"monsterinsights-addon-text"},[e("h3",{staticClass:"monsterinsights-addon-title",domProps:{textContent:t._s(t.addonTitle)}}),t.addon.excerpt?e("p",{staticClass:"monsterinsights-addon-excerpt",domProps:{textContent:t._s(t.addon.excerpt)}}):t._e()])]),e("div",{class:t.actionsClass()},[e("div",{staticClass:"monsterinsights-interior"},[t.addon.type==="unlicensed"?e("span",{staticClass:"monsterinsights-addon-status monsterinsights-green-text",domProps:{innerHTML:t._s(t.text_upgrade_required)}}):e("span",{staticClass:"monsterinsights-addon-status",domProps:{innerHTML:t._s(t.statusText())}}),e("div",{staticClass:"monsterinsights-addon-action"},[t.currentRouteName==="addons"?e("div",[t.isLicenseExpired()||t.isNetworkLicenseExpired()?e("div",[t.addon.type==="licensed"?e("button",{staticClass:"monsterinsights-button"},[e("span",{domProps:{textContent:t._s(t.text_expired)}})]):t._e()]):e("div",[t.addon.type==="licensed"?e("button",{staticClass:"monsterinsights-button",on:{click:function(i){return i.preventDefault(),t.clickAction.apply(null,arguments)}}},[e("span",{domProps:{innerHTML:t._s(t.textButtonAction())}})]):e("a",{staticClass:"monsterinsights-button",attrs:{href:t.upgradeUrl,target:"_blank"},domProps:{textContent:t._s(t.textButtonAction())}})])]):e("div",[t.addon.redirect&&!t.addon.installed?e("div",[e("a",{staticClass:"monsterinsights-button",attrs:{href:t.addon.redirect,target:"_blank"},domProps:{textContent:t._s(t.textButtonAction())}})]):e("div",[t.addon.type==="licensed"?e("button",{staticClass:"monsterinsights-button",on:{click:function(i){return i.preventDefault(),t.clickAction.apply(null,arguments)}}},[e("span",{domProps:{innerHTML:t._s(t.textButtonAction())}})]):e("a",{staticClass:"monsterinsights-button",attrs:{href:t.upgradeUrl,target:"_blank"},domProps:{textContent:t._s(t.textButtonAction())}})])])])])])])},W=[],Y=r(O,P,W,!1,null,null,null,null);const L3=Y.exports,z=function(){return new Promise((s,t)=>{let e=new FormData;e.append("action","monsterinsights_vue_install_plugin"),e.append("slug","all-in-one-seo-pack"),e.append("nonce",o.prototype.$mi.nonce),p.post(o.prototype.$mi.ajax,e).then(i=>{s(i.data)}).catch(function(i){t(i),console.log(i)})})},X=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_activate_addon"),i.append("nonce",o.prototype.$mi.activate_nonce),i.append("isnetwork",o.prototype.$mi.network),i.append("plugin",s),p.post(o.prototype.$mi.ajax,i).then(n=>{t(n.data)}).catch(function(n){e(n),console.log(n)})})},R3={installAioseo:z,activateAioseo:X},{__:d,sprintf:h}=wp.i18n,K=function(s){return new Promise(t=>{let e=new FormData;e.append("action","monsterinsights_vue_get_notes"),e.append("nonce",o.prototype.$mi.nonce),e.append("params",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,e).then(i=>{t(i.data)})})},q=function(s){return new Promise(t=>{let e=new FormData;e.append("action","monsterinsights_vue_get_note"),e.append("nonce",o.prototype.$mi.nonce),e.append("id",s),p.post(o.prototype.$mi.ajax,e).then(i=>{t(i.data)})})},$=function(s){return new Promise(t=>{let e=new FormData;e.append("action","monsterinsights_vue_get_categories"),e.append("nonce",o.prototype.$mi.nonce),e.append("params",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,e).then(i=>{t(i.data)})})},t1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_save_note"),i.append("nonce",o.prototype.$mi.nonce),i.append("note",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.published?(o.prototype.$mi_success_toast({title:s.id?d("Site note is updated successfully.","google-analytics-for-wordpress"):d("Site note is added successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("%1$s","google-analytics-for-wordpress"),n.data.message),html:d("Please add some information into your site notes.","google-analytics-for-wordpress")}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("%1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText),html:d("Please add some information into your site notes.","google-analytics-for-wordpress")})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},e1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_save_category"),i.append("nonce",o.prototype.$mi.nonce),i.append("category",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.published?(o.prototype.$mi_success_toast({title:d("Site note category is added successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("Error: %1$s","google-analytics-for-wordpress"),n.data.message)}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("Error: %1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText)})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},i1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_trash_notes"),i.append("nonce",o.prototype.$mi.nonce),i.append("ids",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.success?(o.prototype.$mi_success_toast({title:d("Trashed successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("Error: %1$s","google-analytics-for-wordpress"),n.data.message)}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("Error: %1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText)})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},s1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_restore_notes"),i.append("nonce",o.prototype.$mi.nonce),i.append("ids",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.success?(o.prototype.$mi_success_toast({title:d("Restored successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("Error: %1$s","google-analytics-for-wordpress"),n.data.message)}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("Error: %1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText)})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},n1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_delete_notes"),i.append("nonce",o.prototype.$mi.nonce),i.append("ids",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.success?(o.prototype.$mi_success_toast({title:d("Deleted successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("Error: %1$s","google-analytics-for-wordpress"),n.data.message)}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("Error: %1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText)})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},a1=function(s){return new Promise((t,e)=>{let i=new FormData;i.append("action","monsterinsights_vue_delete_categories"),i.append("nonce",o.prototype.$mi.nonce),i.append("ids",JSON.stringify(s)),p.post(o.prototype.$mi.ajax,i).then(n=>{n.data.success?(o.prototype.$mi_success_toast({title:d("Deleted successfully.","google-analytics-for-wordpress")}),t(n.data)):(o.prototype.$mi_error_toast({title:h(d("Error: %1$s","google-analytics-for-wordpress"),n.data.message)}),e(n.data))}).catch(function(n){if(n.response){const a=n.response;return o.prototype.$mi_error_toast({title:h(d("Error: %1$s, %2$s","google-analytics-for-wordpress"),a.status,a.statusText)})}o.prototype.$mi_error_toast({title:d("You appear to be offline.","google-analytics-for-wordpress")})})})},Z3={getNotes:K,getNote:q,getCategories:$,saveNote:t1,saveCategory:e1,trashNotes:i1,restoreNotes:s1,deleteNotes:n1,deleteCategories:a1};const o1={name:"SiteNotesMediasField"};var r1=function(){var t=this;return t._self._c,t._e()},l1=[],c1=r(o1,r1,l1,!1,null,null,null,null);const V3=c1.exports,d1={name:"SiteNotesDatePickerField",components:{DatePicker:F},props:{disabled:Boolean,value:{type:[String],default:""}},computed:{innerValue:{get(){return this.value?this.value:H().format("YYYY-MM-DD")},set(s){this.$emit("input",s)}}},methods:{disableAfterToday(s){const t=new Date;return t.setHours(0,0,0,0),s>t}}};var C1=function(){var t=this,e=t._self._c;return e("date-picker",{attrs:{id:"note_date",disabled:t.disabled,valueType:"format",format:"YYYY-MM-DD","disabled-date":t.disableAfterToday,clearable:!1},model:{value:t.innerValue,callback:function(i){t.innerValue=i},expression:"innerValue"}})},u1=[],p1=r(d1,C1,u1,!1,null,null,null,null);const M3=p1.exports,v=s=>(s||document.location.search).replace(/(^\?)/,"").split("&").map((function(t){return t=t.split("="),this[t[0]]=t[1],this}).bind({}))[0],E=(s,t)=>{t=t||document.location.href;const e=t.split("?");if(2>e.length)return t;const i=encodeURIComponent(s)+"=",n=e[1].split(/[&;]/g);for(var a=n.length;0<a--;)n[a].lastIndexOf(i,0)!==-1&&n.splice(a,1);const l=e[0]+(0<n.length?"?"+n.join("&"):"");window.history.replaceState(null,null,l)};o.use(T,{container:"body",duration:1e3,easing:"ease-in-out",offset:0,force:!0,cancelable:!0,onStart:!1,onDone:!1,onCancel:!1,x:!1,y:!0});const h1={mounted(){if(v()["monsterinsights-scroll"]&&setTimeout(()=>{this.$scrollTo(`#${v()["monsterinsights-scroll"]}`,{offset:-130}),E("monsterinsights-scroll")},1500),v()["monsterinsights-highlight"]){const s=v()["monsterinsights-scroll"]?2500:1500;setTimeout(()=>{const t=document.querySelector(`#${v()["monsterinsights-highlight"]}`);t&&(t.classList.add("monsterinsights-row-highlight"),setTimeout(()=>{t.classList.remove("monsterinsights-row-highlight")},2500)),E("monsterinsights-highlight")},s)}},methods:{scrollToID(s,t=30){setTimeout(()=>{this.$scrollTo(`#${s}`,{offset:t})},100)}}};const{__:g1,sprintf:m1}=wp.i18n,f1={name:"TheFloatingBar",props:{showBar:{default:!1,type:Boolean},hideBar:{type:Function}},data(){return{barLink:this.$getUpgradeUrl("floatbar","upgrade")}},computed:{barText(){return m1(g1("%1$sYou're using %2$s Lite%3$s. To unlock all reports, consider %4$supgrading to Pro%5$s.","google-analytics-for-wordpress"),"<strong>","MonsterInsights","</strong>",'<a href="'+this.barLink+'" target="_blank">',"</a>")}}};var v1=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"monsterinsights-slide"}},[t.showBar?e("div",{staticClass:"monsterinsights-floating-bar"},[e("span",{domProps:{innerHTML:t._s(t.barText)}}),e("button",{staticClass:"monsterinsights-floating-bar-close",on:{click:t.hideBar}},[e("span",{staticClass:"dashicons dashicons-before dashicons-no-alt"})])]):t._e()])},_1=[],A1=r(f1,v1,_1,!1,null,null,null,null);const b1=A1.exports;const k1={mixins:[h1],name:"TheAppHeader",components:{TheFloatingBar:b1},data:function(){return{showBar:!1}},computed:{route(){return this.$route.name},link(){return this.$getUrl("logo","header","https://www.monsterinsights.com/lite/")},logo:()=>new URL(""+new URL("../../img/logo-MonsterInsights.png",import.meta.url).href,self.location).href,logo2x:()=>new URL(""+new URL("../../img/<EMAIL>",import.meta.url).href,self.location).href+" 2x",headerClassNames(){return{"monsterinsights-header":!0,"monsterinsights-header-with-floating-bar":this.showBar}}},methods:{registerResponsiveNavigation(){const s=this.$el.querySelector(".monsterinsights-with-responsive-navigation");s.scrollWidth>s.clientWidth?s.classList.add("monsterinsights-with-responsive-navigation-with-scroll"):s.classList.remove("monsterinsights-with-responsive-navigation-with-scroll"),this.registerScrollArrows()},registerArrowsClick(s){const t=this.$el.querySelector(".monsterinsights-with-responsive-navigation"),e=t.getBoundingClientRect(),i=s.target.getAttribute("data-direction");if(!i)return;let n=i==="left"?t.children[1]:t.children[t.children.length-2];for(let a=1;a<t.children.length-1;a++){const l=t.children[a].getBoundingClientRect();if(i==="left"){if(l.right<e.left)continue;n=t.children[a-1];break}else{if(e.right>l.left)continue;n=t.children[a];break}}n.scrollIntoView({behavior:"smooth",block:"nearest"})},registerScrollArrows(){const s=this.$el.querySelector(".monsterinsights-with-responsive-navigation"),t=()=>{const e=s.scrollWidth-(s.scrollLeft+s.clientWidth);s.scrollLeft>30?s.classList.add("with-scroll-left"):s.classList.remove("with-scroll-left"),e>30?s.classList.add("with-scroll-right"):s.classList.remove("with-scroll-right")};t(),s.addEventListener("scroll",t)},hideFloatingBar(){this.showBar=!1;let s=new FormData;s.append("action","monsterinsights_hide_floatbar"),s.append("nonce",this.$mi.nonce),p.post(this.$mi.ajax,s)},getFloatingBarStatus(){const s=this;let t=new FormData;t.append("action","monsterinsights_get_floatbar"),t.append("nonce",this.$mi.nonce),p.post(this.$mi.ajax,t).then(function(e){s.showBar=e.data.show}).catch(function(){s.showBar=!1})}},mounted(){const s=this;setTimeout(function(){s.getFloatingBarStatus()},1500),this.with_responsive_nav&&(setTimeout(()=>{this.registerResponsiveNavigation(),this.registerScrollArrows(),this.$el.addEventListener("click",this.registerArrowsClick)},100),window.addEventListener("resize",this.registerResponsiveNavigation))},destroyed(){window.removeEventListener("resize",this.registerResponsiveNavigation)}};var y1=function(){var t=this,e=t._self._c;return t.route!=="tools-prettylinks-flow"&&t.route!=="woocommerce-insights"?e("header",{class:t.headerClassNames},[e("the-floating-bar",{attrs:{showBar:t.showBar,hideBar:t.hideFloatingBar}}),e("div",{staticClass:"monsterinsights-container monsterinsights-header-container"},[e("div",{staticClass:"monsterinsights-logo-area"},[e("a",{attrs:{href:t.link,target:"_blank",rel:"noopener"}},[e("img",{attrs:{src:t.logo,srcset:t.logo2x}})])]),e("div",{staticClass:"monsterinsights-float-right"},[t._t("default")],2)])],1):t._e()},w1=[],S1=r(k1,y1,w1,!1,null,"8d5ffdc1",null,null);const B3=S1.exports,{__:I1}=wp.i18n,E1={data(){return{text_heading:I1("User Journey Report","google-analytics-for-wordpress")}}};var x1=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-user-journey-navbar"},[e("h1",{domProps:{textContent:t._s(t.text_heading)}})])},L1=[],R1=r(E1,x1,L1,!1,null,null,null,null);const J3=R1.exports,Z1={name:"TheAppNavigation"};var V1=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-navigation-bar"},[e("div",{staticClass:"monsterinsights-container"},[t._t("default")],2)])},M1=[],B1=r(Z1,V1,M1,!1,null,null,null,null);const F3=B1.exports,J1={name:"TheAppNotices",components:{SlideDownUp:D},computed:{...A({notices:"$_app/notices"})},methods:{removeNotice(s){this.$store.dispatch("$_app/removeNotice",s)},getNoticeClass(s,t){return"monsterinsights-notice monsterinsights-notice-"+s+" monsterinsights-"+t},buttonClass(s){let t="monsterinsights-button";return s==="success"&&(t+=" monsterinsights-button-green"),s==="error"&&(t+=" monsterinsights-button-error"),t}}};var F1=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-notices-area"},[e("div",{staticClass:"monsterinsights-container"},[e("slide-down-up",{attrs:{group:!0}},t._l(t.notices,function(i,n){return e("div",{key:n,class:t.getNoticeClass(i.type,i.id)},[e("div",{staticClass:"monsterinsights-notice-inner"},[i.dismissable?e("button",{staticClass:"dismiss-notice",on:{click:function(a){return t.removeNotice(n)}}},[e("i",{staticClass:"monstericon-times"})]):t._e(),e("div",{staticClass:"notice-content"},[e("div",{class:i.icon?"monsterinsights-flex":""},[i.icon?e("div",{class:"monsterinsights-notice-icon monsterinsights-"+i.id+"-icon"},[e("span",{domProps:{innerHTML:t._s(i.icon)}})]):t._e(),e("div",[i.title?e("h2",{staticClass:"notice-title",domProps:{innerHTML:t._s(i.title)}}):t._e(),e("span",{staticClass:"monsterinsights-notice-content",domProps:{innerHTML:t._s(i.content)}}),i.button&&i.button.enabled?e("div",{staticClass:"monsterinsights-notice-button"},[e("a",{class:t.buttonClass(i.type),attrs:{target:"_blank",href:i.button.link},domProps:{textContent:t._s(i.button.text)}})]):t._e()])])])])])}),0)],1)])},H1=[],T1=r(J1,F1,H1,!1,null,null,null,null);const H3=T1.exports,{__:k}=wp.i18n,U1={name:"Notification",props:{notification:Object,dismissable:{type:Boolean,default:!0}},data(){return{text_dismiss:k("Dismiss","google-analytics-for-wordpress"),text_install_activated:k("Installed & Active","google-analytics-for-wordpress"),text_install_processing:k("Installing & Activating","google-analytics-for-wordpress"),installedAndActivated:!1,processing:!1}},computed:{...A({notifications:"$_notifications/notifications",addons:"$_addons/addons"}),userFeedbackInstalled(){return this.addons&&this.addons["userfeedback-lite"]&&this.addons["userfeedback-lite"].installed},userFeedbackActive(){return this.addons&&this.addons["userfeedback-lite"]&&this.addons["userfeedback-lite"].active},has_notifications(){return this.notifications&&this.notifications.length>0},icon(){const t={default:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#D3F8EA"/><path d="M21.8634 18.6429C21.8634 18.8571 21.7831 19.0268 21.6224 19.1518C21.5688 19.3482 21.542 19.6786 21.542 20.1429C21.542 20.6071 21.5688 20.9375 21.6224 21.1339C21.7831 21.2768 21.8634 21.4464 21.8634 21.6429V22.0714C21.8634 22.25 21.8009 22.4018 21.6759 22.5268C21.5509 22.6518 21.3992 22.7143 21.2206 22.7143H12.4349C11.7206 22.7143 11.1134 22.4643 10.6134 21.9643C10.1134 21.4643 9.86345 20.8571 9.86345 20.1429V11.5714C9.86345 10.8571 10.1134 10.25 10.6134 9.75C11.1134 9.25 11.7206 9 12.4349 9H21.2206C21.3992 9 21.5509 9.0625 21.6759 9.1875C21.8009 9.3125 21.8634 9.46429 21.8634 9.64286V18.6429ZM13.292 12.5893V13.125C13.292 13.2321 13.3456 13.2857 13.4527 13.2857H19.1313C19.2384 13.2857 19.292 13.2321 19.292 13.125V12.5893C19.292 12.4821 19.2384 12.4286 19.1313 12.4286H13.4527C13.3456 12.4286 13.292 12.4821 13.292 12.5893ZM13.292 14.3036V14.8393C13.292 14.9464 13.3456 15 13.4527 15H19.1313C19.2384 15 19.292 14.9464 19.292 14.8393V14.3036C19.292 14.1964 19.2384 14.1429 19.1313 14.1429H13.4527C13.3456 14.1429 13.292 14.1964 13.292 14.3036ZM20.0688 21C20.0152 20.4286 20.0152 19.8571 20.0688 19.2857H12.4349C12.2027 19.2857 11.9974 19.375 11.8188 19.5536C11.6581 19.7143 11.5777 19.9107 11.5777 20.1429C11.5777 20.375 11.6581 20.5804 11.8188 20.7589C11.9974 20.9196 12.2027 21 12.4349 21H20.0688Z" fill="#1EC185"/></svg>',star:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#D4E7F7"/><path d="M15.0867 9.48214C15.2474 9.16071 15.5063 9 15.8634 9C16.2206 9 16.4795 9.16071 16.6402 9.48214L18.3813 13.0179L22.292 13.6071C22.6492 13.6429 22.8813 13.8304 22.9884 14.1696C23.0956 14.5089 23.0242 14.8036 22.7742 15.0536L19.9349 17.8125L20.6045 21.7232C20.6581 22.0625 20.542 22.3304 20.2563 22.5268C19.9706 22.7411 19.6759 22.7679 19.3724 22.6071L15.8634 20.7857L12.3545 22.6071C12.0509 22.7857 11.7563 22.7679 11.4706 22.5536C11.1849 22.3393 11.0688 22.0625 11.1224 21.7232L11.792 17.8125L8.95274 15.0536C8.70274 14.8036 8.63131 14.5089 8.73845 14.1696C8.84559 13.8304 9.07774 13.6429 9.43488 13.6071L13.3456 13.0179L15.0867 9.48214Z" fill="#2679C1"/></svg>',warning:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#FAD1D1"/><path d="M17.3634 19.0714C17.792 19.4821 18.0063 19.9821 18.0063 20.5714C18.0063 21.1607 17.792 21.6607 17.3634 22.0714C16.9527 22.5 16.4527 22.7143 15.8634 22.7143C15.2742 22.7143 14.7652 22.5 14.3367 22.0714C13.9259 21.6607 13.7206 21.1607 13.7206 20.5714C13.7206 19.9821 13.9259 19.4821 14.3367 19.0714C14.7652 18.6429 15.2742 18.4286 15.8634 18.4286C16.4527 18.4286 16.9527 18.6429 17.3634 19.0714ZM13.9617 9.66964C13.9617 9.49107 14.0242 9.33929 14.1492 9.21429C14.2742 9.07143 14.4259 9 14.6045 9H17.1224C17.3009 9 17.4527 9.07143 17.5777 9.21429C17.7027 9.33929 17.7652 9.49107 17.7652 9.66964L17.3902 16.9554C17.3902 17.1339 17.3277 17.2857 17.2027 17.4107C17.0777 17.5179 16.9259 17.5714 16.7474 17.5714H14.9795C14.8009 17.5714 14.6492 17.5179 14.5242 17.4107C14.3992 17.2857 14.3367 17.1339 14.3367 16.9554L13.9617 9.66964Z" fill="#EB5757"/></svg>',lightning:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="16" fill="#E1DAF1"/><path d="M20.0331 13.2857C20.2831 13.2857 20.4706 13.3929 20.5956 13.6071C20.7206 13.8214 20.7206 14.0357 20.5956 14.25L15.8813 22.3929C15.7563 22.6071 15.5688 22.7143 15.3188 22.7143C15.1045 22.7143 14.9349 22.6339 14.8099 22.4732C14.6849 22.3125 14.6492 22.125 14.7027 21.9107L15.9349 16.7143H12.7474C12.6224 16.7143 12.5063 16.6786 12.3992 16.6071C12.292 16.5357 12.2117 16.4464 12.1581 16.3393C12.1045 16.2321 12.0867 16.1161 12.1045 15.9911L12.9617 9.5625C12.9795 9.45536 13.0152 9.35714 13.0688 9.26786C13.1402 9.17857 13.2206 9.11607 13.3099 9.08036C13.3992 9.02679 13.4974 9 13.6045 9H17.4617C17.6759 9 17.8456 9.08929 17.9706 9.26786C18.0956 9.42857 18.1313 9.60714 18.0777 9.80357L16.9527 13.2857H20.0331Z" fill="#6F4BBB"/></svg>',exception:'<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><circle opacity="0.1" cx="14" cy="14" r="14" fill="#C84B29" /><path d="M12.4422 19.6174H16.3814C16.3026 20.1192 16.0617 20.5748 15.7011 20.9033C15.3406 21.2319 14.8838 21.4121 14.4118 21.4121C13.9398 21.4121 13.483 21.2319 13.1224 20.9033C12.7619 20.5748 12.5209 20.1192 12.4422 19.6174ZM14.4118 7.41211C15.7362 7.41211 17.0064 7.97942 17.943 8.98925C18.8795 9.99907 19.4056 11.3687 19.4056 12.7968V15.6686L20.3498 17.9374C20.3927 18.0411 20.4109 18.1548 20.4028 18.2681C20.3946 18.3814 20.3603 18.4906 20.303 18.586C20.2457 18.6814 20.1673 18.7598 20.0747 18.8142C19.9822 18.8685 19.8785 18.8971 19.7732 18.8973H9.05306C8.94755 18.8973 8.84371 18.8688 8.75101 18.8144C8.65831 18.7601 8.5797 18.6816 8.52232 18.5861C8.46494 18.4906 8.43062 18.3812 8.42249 18.2678C8.41436 18.1543 8.43268 18.0405 8.47578 17.9366L9.41795 15.6679V12.7875L9.42128 12.608C9.46682 11.2136 10.0125 9.89271 10.9435 8.92362C11.8744 7.95453 13.1178 7.41217 14.4118 7.41211ZM21.7361 11.8972C21.8626 11.8972 21.9844 11.9491 22.0769 12.0422C22.1693 12.1353 22.2255 12.2628 22.2342 12.399C22.2428 12.5351 22.2032 12.6696 22.1233 12.7755C22.0435 12.8813 21.9293 12.9505 21.804 12.9691L21.7361 12.9741H20.4044C20.2779 12.9741 20.1561 12.9223 20.0636 12.8291C19.9712 12.736 19.9149 12.6085 19.9063 12.4724C19.8977 12.3362 19.9373 12.2017 20.0172 12.0958C20.097 11.99 20.2111 11.9208 20.3365 11.9022L20.4044 11.8972H21.7361ZM8.41918 11.8972C8.54571 11.8972 8.6675 11.9491 8.75995 12.0422C8.85241 12.1353 8.90862 12.2628 8.91725 12.399C8.92587 12.5351 8.88626 12.6696 8.80641 12.7755C8.72657 12.8813 8.61244 12.9505 8.4871 12.9691L8.41918 12.9741H7.08749C6.96096 12.9741 6.83917 12.9223 6.74671 12.8291C6.65426 12.736 6.59804 12.6085 6.58942 12.4724C6.58079 12.3362 6.62041 12.2017 6.70025 12.0958C6.7801 11.99 6.89423 11.9208 7.01957 11.9022L7.08749 11.8972H8.41918ZM21.4697 7.80483C21.5416 7.90803 21.5767 8.03561 21.5686 8.16435C21.5605 8.29309 21.5099 8.41443 21.4258 8.50628L21.3699 8.55869L20.0382 9.63562C19.9374 9.71755 19.8115 9.75527 19.686 9.74119C19.5604 9.72712 19.4445 9.66229 19.3614 9.55976C19.2784 9.45723 19.2345 9.3246 19.2385 9.18855C19.2425 9.05251 19.2941 8.92314 19.383 8.82649L19.4389 8.77407L20.7706 7.69714C20.8766 7.61145 21.0097 7.57466 21.1409 7.59486C21.272 7.61505 21.3903 7.69058 21.4697 7.80483ZM8.05296 7.69714L9.38465 8.77407C9.43712 8.8165 9.48132 8.86966 9.51473 8.9305C9.54814 8.99135 9.57011 9.0587 9.57939 9.1287C9.58866 9.1987 9.58506 9.26999 9.56878 9.33849C9.55251 9.40699 9.52388 9.47136 9.48453 9.52793C9.44518 9.5845 9.39589 9.63216 9.33946 9.66819C9.28303 9.70421 9.22057 9.72791 9.15565 9.73791C9.09073 9.74791 9.02461 9.74402 8.96109 9.72647C8.89756 9.70892 8.83786 9.67805 8.78539 9.63562L7.4537 8.55869C7.40124 8.51626 7.35704 8.46311 7.32363 8.40226C7.29021 8.34141 7.26824 8.27407 7.25897 8.20406C7.24969 8.13406 7.2533 8.06278 7.26957 7.99428C7.28585 7.92578 7.31448 7.8614 7.35383 7.80483C7.39317 7.74826 7.44247 7.7006 7.4989 7.66457C7.55533 7.62855 7.61779 7.60486 7.68271 7.59486C7.74763 7.58486 7.81374 7.58874 7.87727 7.60629C7.9408 7.62384 8.0005 7.65471 8.05296 7.69714Z" fill="#C84B29" /></svg>',"ai-insight":'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_26_4726)"><circle cx="16" cy="16" r="16" fill="#D4E7F7"/><path fill-rule="evenodd" clip-rule="evenodd" d="M19.1968 8.80096C19.4039 7.8549 20.7517 7.84925 20.9678 8.79417L20.9768 8.83831L20.9972 8.92657C21.2462 9.98353 22.1017 10.7904 23.1722 10.9771C24.159 11.1491 24.159 12.566 23.1722 12.738C22.6458 12.8296 22.1582 13.0747 21.7706 13.4426C21.3831 13.8104 21.1128 14.2846 20.9938 14.8055L20.9667 14.9209C20.7517 15.8658 19.405 15.8602 19.1968 14.9141L19.1753 14.8145C19.061 14.2916 18.7935 13.8145 18.4069 13.4442C18.0203 13.0739 17.5322 12.8273 17.0048 12.7357C16.0202 12.5648 16.0202 11.1503 17.0048 10.9794C17.5303 10.8882 18.0169 10.643 18.4029 10.2749C18.7889 9.90685 19.0569 9.43245 19.173 8.91186L19.1888 8.83717L19.1968 8.80096ZM20.0195 16.856C19.4803 16.8455 18.9649 16.6317 18.5766 16.2574C18.4913 16.3137 18.4169 16.385 18.3571 16.4679C17.8478 17.1423 17.2515 17.8315 16.577 18.5048C16.0655 19.0163 15.5472 19.4814 15.0357 19.8968C14.5242 19.4814 14.0059 19.0163 13.4944 18.5048C13.0043 18.0157 12.5397 17.5016 12.1025 16.9647C12.5178 16.452 12.984 15.9349 13.4944 15.4234C14.1317 14.7839 14.8123 14.1891 15.5314 13.6433C15.6065 13.5895 15.6722 13.5237 15.726 13.4486C15.5109 13.2525 15.3385 13.0141 15.2198 12.7483C15.101 12.4825 15.0383 12.195 15.0357 11.9039C13.9256 11.1265 12.8279 10.5505 11.8479 10.2506C10.7864 9.92582 9.54156 9.83868 8.72564 10.6535C8.19716 11.1831 8.05344 11.9028 8.09418 12.5875C8.13492 13.2732 8.36464 14.0269 8.71206 14.7919C9.06646 15.5531 9.48957 16.2804 9.97611 16.9647C9.48968 17.6483 9.06658 18.3748 8.71206 19.1352C8.36464 19.9002 8.13492 20.6538 8.09418 21.3396C8.05344 22.0243 8.19603 22.744 8.72564 23.2736C9.25525 23.8021 9.97498 23.9458 10.6596 23.9051C11.3443 23.8632 12.0991 23.6346 12.8641 23.2872C13.5544 22.9737 14.2888 22.5459 15.0368 22.0231C15.7837 22.5459 16.517 22.9737 17.2085 23.2872C17.9723 23.6346 18.7271 23.8643 19.4129 23.9051C20.0976 23.9458 20.8162 23.8021 21.3458 23.2725C22.1617 22.4577 22.0745 21.2129 21.7498 20.1514C21.4397 19.1397 20.8365 18.0024 20.0195 16.856ZM11.3511 11.8734C12.0029 12.0726 12.7803 12.4505 13.6223 12.9982C12.703 13.7782 11.8492 14.632 11.0693 15.5512C10.7628 15.0846 10.4912 14.5959 10.2568 14.0891C9.95913 13.4328 9.81315 12.8896 9.78826 12.4856C9.76449 12.0793 9.86408 11.9164 9.92632 11.8541C10.0282 11.7523 10.4039 11.5848 11.3511 11.8734ZM10.2568 19.839C10.465 19.3807 10.7377 18.8885 11.0693 18.377C11.8496 19.2962 12.7038 20.1501 13.6234 20.93C13.1571 21.2368 12.6688 21.5087 12.1625 21.7436C11.5061 22.0412 10.9629 22.1872 10.5589 22.2121C10.1515 22.2359 9.98969 22.1363 9.92745 22.0741C9.86521 22.0118 9.76562 21.8477 9.78939 21.4426C9.81428 21.0386 9.95913 20.4954 10.2579 19.839H10.2568ZM17.9101 21.7436C17.4039 21.509 16.916 21.2371 16.4503 20.93C17.3687 20.1499 18.2218 19.2961 19.001 18.377C19.5476 19.22 19.9255 19.9975 20.1247 20.6493C20.4144 21.5954 20.2469 21.9722 20.1451 22.0741C20.0817 22.1363 19.9188 22.2359 19.5136 22.211C19.1085 22.1883 18.5664 22.0412 17.9101 21.7436ZM13.9041 16.9647C13.9041 16.6645 14.0233 16.3767 14.2355 16.1645C14.4477 15.9522 14.7356 15.833 15.0357 15.833C15.3358 15.833 15.6237 15.9522 15.8359 16.1645C16.0481 16.3767 16.1673 16.6645 16.1673 16.9647C16.1673 17.2648 16.0481 17.5526 15.8359 17.7649C15.6237 17.9771 15.3358 18.0963 15.0357 18.0963C14.7356 18.0963 14.4477 17.9771 14.2355 17.7649C14.0233 17.5526 13.9041 17.2648 13.9041 16.9647Z" fill="#489BE8"/></g><defs><clipPath id="clip0_26_4726"><rect width="32" height="32" fill="white"/></clipPath></defs></svg>'};return t[this.notification.icon]?t[this.notification.icon]:t.default},userFeedbackInstalledAndActivated(){return this.installedAndActivated||this.userFeedbackInstalled&&this.userFeedbackActive},loading(){return this.processing}},methods:{buttonClass(s){return"monsterinsights-button monsterinsights-button-"+s},dismiss(s){this.$store.commit("$_notifications/NOTIFICATION_DISMISSED",s),this.$store.dispatch("$_notifications/dismissNotification",s).then(()=>{let t=document.querySelector(".monsterinsights-menu-notification-indicator");t&&!this.has_notifications?t.style.display="none":t.innerText=this.notifications.length})},buttonAction(s,t){if(s==="cta_install_user_feedback"){if(this.userFeedbackInstalledAndActivated)return!1;this.processing=!0,this.installUserFeedback()&&this.activateUserFeedback()&&setTimeout(function(){this.processing=!1,this.installedAndActivated=!0,location.href=t},3e3)}return!0},triggerButtonText(s,t){if(s==="cta_install_user_feedback"){if(this.userFeedbackInstalledAndActivated)return this.text_install_activated;if(this.loading)return this.text_install_processing}return t},installUserFeedback:async()=>{try{const s=await I.installUserFeedback();if("success"in s&&s.success)return!0}catch{return!1}},activateUserFeedback:async()=>{try{const s=await I.activatelUserFeedback();if("success"in s&&s.success)return!0}catch{return!1}}}};var N1=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-notificationsv3-single-notification"},[e("div",{staticClass:"monsterinsights-notificationsv3-notification-icon",domProps:{innerHTML:t._s(t.icon)}}),e("div",{staticClass:"monsterinsights-notificationsv3-notification-details"},[e("div",{staticClass:"monsterinsights-notificationsv3-notification-title"},[e("h5",{domProps:{innerHTML:t._s(t.notification.title)}}),t.notification.start?e("span",{domProps:{innerHTML:t._s(t.notification.start)}}):t._e()]),e("div",{staticClass:"monsterinsights-notificationsv3-notification-content"},[e("div",{domProps:{innerHTML:t._s(t.notification.content)}})]),e("div",{staticClass:"monsterinsights-notificationsv3-notification-actions"},[t._l(t.notification.btns,function(i,n){return e("a",{key:n,class:["monsterinsights-button",t.buttonClass(n)],attrs:{href:n.startsWith("cta_")?"#":i.url,target:i.is_external?"_blank":"_self"},domProps:{textContent:t._s(t.triggerButtonText(n,i.text))},on:{click:function(a){return t.buttonAction(n,i.url)}}})}),t.dismissable?e("span",{domProps:{innerHTML:t._s(t.text_dismiss)},on:{click:function(i){return t.dismiss(t.notification.id)}}}):t._e()],2)])])},D1=[],j1=r(U1,N1,D1,!1,null,null,null,null);const G1=j1.exports,{__:m}=wp.i18n,Q1={name:"Notifications",components:{Notification:G1},data(){return{isShowSidebar:!1,isShowDismissed:!1,text_inbox:m("Inbox","google-analytics-for-wordpress"),text_back_to_inbox:m("Back to Inbox","google-analytics-for-wordpress"),text_view_dismissed:m("View Dismissed","google-analytics-for-wordpress"),text_notifications:m("Notifications","google-analytics-for-wordpress"),text_dismiss_all:m("Dismiss All","google-analytics-for-wordpress"),text_dismissed:m("Dismissed","google-analytics-for-wordpress"),text_no_notifications:m("No Notifications","google-analytics-for-wordpress")}},computed:{...A({notifications:"$_notifications/notifications",dismissed:"$_notifications/dismissed"}),activeNotificationsNumber(){return this.notifications.length},dismissedNotificationsNumber(){return this.dismissed.length}},created(){const s=window.location.search;if(typeof s<"u"){const e=new URLSearchParams(s).get("open");typeof e<"u"&&e==="monsterinsights_notification_sidebar"&&(this.isShowSidebar=!0)}},methods:{closeSidebar(){this.isShowSidebar=!1,this.isShowDismissed=!1},buttonClass(s){return"monsterinsights-button monsterinsights-button-"+s},dismiss(s){this.$store.commit("$_notifications/NOTIFICATION_DISMISSED",s),this.$store.dispatch("$_notifications/dismissNotification",s).then(()=>{let t=document.querySelector(".monsterinsights-menu-notification-indicator");t&&!this.notifications.length>0?t.style.display="none":t.innerText=this.notifications.length})}}};var O1=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-notificationsv3-container"},[e("div",{staticClass:"monsterinsights-notificationsv3-inbox-button"},[e("button",{staticClass:"monsterinsights-button",on:{click:function(i){t.isShowSidebar=!0}}},[e("svg",{attrs:{width:"22",height:"14",viewBox:"0 0 22 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M21.6944 6.5625C21.8981 6.85417 22 7.18229 22 7.54687V12.25C22 12.7361 21.8218 13.1493 21.4653 13.4896C21.1088 13.8299 20.6759 14 20.1667 14H1.83333C1.32407 14 0.891204 13.8299 0.534722 13.4896C0.178241 13.1493 0 12.7361 0 12.25V7.54687C0 7.18229 0.101852 6.85417 0.305556 6.5625L4.35417 0.765625C4.45602 0.644097 4.58333 0.522569 4.73611 0.401042C4.91435 0.279514 5.10532 0.182292 5.30903 0.109375C5.51273 0.0364583 5.7037 0 5.88194 0H16.1181C16.3981 0 16.6782 0.0850694 16.9583 0.255208C17.2639 0.401042 17.4931 0.571181 17.6458 0.765625L21.6944 6.5625ZM6.1875 2.33333L2.94097 7H7.63889L8.86111 9.33333H13.1389L14.3611 7H19.059L15.8125 2.33333H6.1875Z",fill:"#2679C1"}})])]),e("span",{class:["monsterinsights-notificationsv3-inbox-number",t.activeNotificationsNumber>9?"number-greater-than-10":"number-less-than-10"],domProps:{textContent:t._s(t.activeNotificationsNumber)},on:{click:function(i){t.isShowSidebar=!0}}})]),t.isShowSidebar?e("div",{class:["monsterinsights-notificationsv3-sidebar",t.isShowSidebar?"monsterinsights-notificationsv3-sidebar-in":"monsterinsights-notificationsv3-sidebar-out"]},[e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-top"},[e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-top-title"},[e("svg",{attrs:{width:"24",height:"15",viewBox:"0 0 24 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M23.6667 7.03125C23.8889 7.34375 24 7.69531 24 8.08594V13.125C24 13.6458 23.8056 14.0885 23.4167 14.4531C23.0278 14.8177 22.5556 15 22 15H2C1.44444 15 0.972222 14.8177 0.583333 14.4531C0.194444 14.0885 0 13.6458 0 13.125V8.08594C0 7.69531 0.111111 7.34375 0.333333 7.03125L4.75 0.820312C4.86111 0.690104 5 0.559896 5.16667 0.429688C5.36111 0.299479 5.56944 0.195312 5.79167 0.117188C6.01389 0.0390625 6.22222 0 6.41667 0H17.5833C17.8889 0 18.1944 0.0911458 18.5 0.273438C18.8333 0.429688 19.0833 0.611979 19.25 0.820312L23.6667 7.03125ZM6.75 2.5L3.20833 7.5H8.33333L9.66667 10H14.3333L15.6667 7.5H20.7917L17.25 2.5H6.75Z",fill:"white"}})]),t.isShowDismissed?t._e():e("h3",{domProps:{textContent:t._s(t.text_inbox)}}),t.isShowDismissed?e("h3",{domProps:{textContent:t._s(t.text_dismissed)}}):t._e()]),e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-top-actions"},[t.isShowDismissed?t._e():e("button",{staticClass:"monsterinsights-button",domProps:{textContent:t._s(t.text_view_dismissed)},on:{click:function(i){t.isShowDismissed=!0}}}),t.isShowDismissed?e("button",{staticClass:"monsterinsights-button",domProps:{textContent:t._s(t.text_back_to_inbox)},on:{click:function(i){t.isShowDismissed=!1}}}):t._e(),e("button",{staticClass:"monsterinsights-button monsterinsights-notificationsv3-sidebar-close",on:{click:t.closeSidebar}},[e("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M8.28409 6L11.6932 9.40909C11.8977 9.61364 12 9.86364 12 10.1591C12 10.4545 11.8977 10.7159 11.6932 10.9432L10.9432 11.6932C10.7159 11.8977 10.4545 12 10.1591 12C9.86364 12 9.61364 11.8977 9.40909 11.6932L6 8.28409L2.59091 11.6932C2.38636 11.8977 2.13636 12 1.84091 12C1.54545 12 1.28409 11.8977 1.05682 11.6932L0.306818 10.9432C0.102273 10.7159 0 10.4545 0 10.1591C0 9.86364 0.102273 9.61364 0.306818 9.40909L3.71591 6L0.306818 2.59091C0.102273 2.38636 0 2.13636 0 1.84091C0 1.54545 0.102273 1.28409 0.306818 1.05682L1.05682 0.306818C1.28409 0.102273 1.54545 0 1.84091 0C2.13636 0 2.38636 0.102273 2.59091 0.306818L6 3.71591L9.40909 0.306818C9.61364 0.102273 9.86364 0 10.1591 0C10.4545 0 10.7159 0.102273 10.9432 0.306818L11.6932 1.05682C11.8977 1.28409 12 1.54545 12 1.84091C12 2.13636 11.8977 2.38636 11.6932 2.59091L8.28409 6Z",fill:"white"}})])])])]),e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-bottom"},[e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-bottom-notifications-count"},[t.isShowDismissed?t._e():e("span",{staticClass:"monsterinsights-notificationsv3-inbox-number",domProps:{textContent:t._s(t.activeNotificationsNumber)}}),t.isShowDismissed?e("span",{staticClass:"monsterinsights-notificationsv3-dismissed-number",domProps:{textContent:t._s(t.dismissedNotificationsNumber)}}):t._e(),e("h4",{domProps:{textContent:t._s(t.text_notifications)}})]),!t.isShowDismissed&&t.activeNotificationsNumber>0?e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-header-bottom-actions"},[e("span",{domProps:{textContent:t._s(t.text_dismiss_all)},on:{click:function(i){return t.dismiss("all")}}})]):t._e()]),t.isShowDismissed?t._e():e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-notifications monsterinsights-notificationsv3-notifications-active"},[t.activeNotificationsNumber<1?e("div",{staticClass:"monsterinsights-notificationsv3-no-notifications"},[e("svg",{attrs:{width:"91",height:"74",viewBox:"0 0 91 74",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M89.2969 65.4062C90.4219 66.3438 90.5625 67.4219 89.7188 68.6406L86.9062 72.1562C85.9688 73.2812 84.9375 73.4219 83.8125 72.5781L0.984375 8.59375C-0.140625 7.65625 -0.28125 6.57813 0.5625 5.35938L3.375 1.84375C4.3125 0.71875 5.34375 0.578125 6.46875 1.42188L26.8594 17.1719C30.3281 12.5781 34.9219 9.67188 40.6406 8.45312V5.5C40.6406 4.28125 41.0625 3.25 41.9062 2.40625C42.8438 1.46875 43.9219 1 45.1406 1C46.3594 1 47.3906 1.46875 48.2344 2.40625C49.1719 3.25 49.6406 4.28125 49.6406 5.5V8.45312C54.8906 9.48437 59.2031 12.0156 62.5781 16.0469C65.9531 20.0781 67.6406 24.8125 67.6406 30.25C67.6406 34.375 68.1094 38.0312 69.0469 41.2188C69.9844 44.3125 70.8281 46.4219 71.5781 47.5469C72.4219 48.6719 73.5469 49.9375 74.9531 51.3438C75.1406 51.625 75.2812 51.8125 75.375 51.9062C76.2188 52.8438 76.6406 53.875 76.6406 55C76.6406 55.0938 76.5938 55.2344 76.5 55.4219C76.5 55.5156 76.5 55.5625 76.5 55.5625L89.2969 65.4062ZM22.2188 36.4375L52.1719 59.5H18.1406C17.2969 59.5 16.5 59.3125 15.75 58.9375C15.0938 58.4688 14.5781 57.9062 14.2031 57.25C13.8281 56.5 13.6406 55.75 13.6406 55C13.6406 53.875 14.0625 52.8438 14.9062 51.9062C16.2188 50.5 17.1562 49.4688 17.7188 48.8125C18.2812 48.1562 19.0781 46.6562 20.1094 44.3125C21.1406 41.9688 21.8438 39.3438 22.2188 36.4375ZM45.1406 73C42.7031 73 40.5938 72.1094 38.8125 70.3281C37.0312 68.6406 36.1406 66.5312 36.1406 64H54.1406C54.1406 65.5938 53.7188 67.0938 52.875 68.5C52.125 69.9062 51.0469 70.9844 49.6406 71.7344C48.2344 72.5781 46.7344 73 45.1406 73Z",fill:"#E2E4E9"}})]),e("h4",{domProps:{textContent:t._s(t.text_no_notifications)}})]):t._e(),t._l(t.notifications,function(i,n){return e("notification",{key:n,attrs:{notification:i}})})],2),t.isShowDismissed?e("div",{staticClass:"monsterinsights-notificationsv3-sidebar-notifications monsterinsights-notificationsv3-notifications-dismissed"},[t.dismissedNotificationsNumber<1?e("div",{staticClass:"monsterinsights-notificationsv3-no-notifications"},[e("svg",{attrs:{width:"91",height:"74",viewBox:"0 0 91 74",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M89.2969 65.4062C90.4219 66.3438 90.5625 67.4219 89.7188 68.6406L86.9062 72.1562C85.9688 73.2812 84.9375 73.4219 83.8125 72.5781L0.984375 8.59375C-0.140625 7.65625 -0.28125 6.57813 0.5625 5.35938L3.375 1.84375C4.3125 0.71875 5.34375 0.578125 6.46875 1.42188L26.8594 17.1719C30.3281 12.5781 34.9219 9.67188 40.6406 8.45312V5.5C40.6406 4.28125 41.0625 3.25 41.9062 2.40625C42.8438 1.46875 43.9219 1 45.1406 1C46.3594 1 47.3906 1.46875 48.2344 2.40625C49.1719 3.25 49.6406 4.28125 49.6406 5.5V8.45312C54.8906 9.48437 59.2031 12.0156 62.5781 16.0469C65.9531 20.0781 67.6406 24.8125 67.6406 30.25C67.6406 34.375 68.1094 38.0312 69.0469 41.2188C69.9844 44.3125 70.8281 46.4219 71.5781 47.5469C72.4219 48.6719 73.5469 49.9375 74.9531 51.3438C75.1406 51.625 75.2812 51.8125 75.375 51.9062C76.2188 52.8438 76.6406 53.875 76.6406 55C76.6406 55.0938 76.5938 55.2344 76.5 55.4219C76.5 55.5156 76.5 55.5625 76.5 55.5625L89.2969 65.4062ZM22.2188 36.4375L52.1719 59.5H18.1406C17.2969 59.5 16.5 59.3125 15.75 58.9375C15.0938 58.4688 14.5781 57.9062 14.2031 57.25C13.8281 56.5 13.6406 55.75 13.6406 55C13.6406 53.875 14.0625 52.8438 14.9062 51.9062C16.2188 50.5 17.1562 49.4688 17.7188 48.8125C18.2812 48.1562 19.0781 46.6562 20.1094 44.3125C21.1406 41.9688 21.8438 39.3438 22.2188 36.4375ZM45.1406 73C42.7031 73 40.5938 72.1094 38.8125 70.3281C37.0312 68.6406 36.1406 66.5312 36.1406 64H54.1406C54.1406 65.5938 53.7188 67.0938 52.875 68.5C52.125 69.9062 51.0469 70.9844 49.6406 71.7344C48.2344 72.5781 46.7344 73 45.1406 73Z",fill:"#E2E4E9"}})]),e("h4",{domProps:{textContent:t._s(t.text_no_notifications)}})]):t._e(),t._l(t.dismissed,function(i,n){return e("notification",{key:n,attrs:{notification:i,dismissable:!1}})})],2):t._e()]):t._e()])},P1=[],W1=r(Q1,O1,P1,!1,null,null,null,null);const T3=W1.exports;const{__:_}=wp.i18n,Y1={name:"TheQuickLinks",data(){return{showMenu:!1,text_see_quick:_("See Quick Links","google-analytics-for-wordpress")}},computed:{...A({license:"$_license/license",license_network:"$_license/license_network"}),boxClass(){let s="monsterinsights-quick-links";return this.showMenu&&(s+=" monsterinsights-quick-links-open"),s},licenseLevel(){return this.$mi.network?this.license_network.type:this.license.type},showUpsell(){return this.licenseLevel==="plus"||this.licenseLevel==="basic"||this.licenseLevel===""},menuItems(){let s=[{icon:"monstericon-lightbulb",tooltip:_("Suggest a Feature","google-analytics-for-wordpress"),link:this.$getUrl("quick-links","suggest-feature","https://www.monsterinsights.com/customer-feedback/"),key:"suggest"},{icon:"monstericon-wpbeginner",tooltip:_("Join Our Community","google-analytics-for-wordpress"),link:"https://www.facebook.com/groups/wpbeginner/",key:"community"},{icon:"monstericon-life-ring",tooltip:_("Support & Docs","google-analytics-for-wordpress"),link:this.$getUrl("quick-links","support","https://www.monsterinsights.com/docs/"),key:"support"}];return this.showUpsell&&s.push({icon:"monstericon-star",tooltip:_("Upgrade to Pro &#187;","google-analytics-for-wordpress"),link:this.$getUpgradeUrl("quick-links","upgrade"),key:"upgrade"}),s}},methods:{enter:function(s,t){const e=s.dataset.index*50;setTimeout(function(){s.classList.add("monsterinsights-show"),t()},e)},leave:function(s,t){s.classList.remove("monsterinsights-show"),setTimeout(function(){t()},200)},linksClass(s){return"monsterinsights-quick-links-menu-item monsterinsights-quick-links-item-"+s}}};var z1=function(){var t=this,e=t._self._c;return e("div",{class:t.boxClass},[e("button",{staticClass:"monsterinsights-quick-links-label",on:{click:function(i){i.stopPropagation(),t.showMenu=!t.showMenu}}},[e("span",{staticClass:"monsterinsights-bg-img monsterinsights-quick-links-mascot"}),e("span",{staticClass:"monsterinsights-quick-link-title",domProps:{textContent:t._s(t.text_see_quick)}})]),e("transition-group",{staticClass:"monsterinsights-quick-links-menu",attrs:{tag:"div",name:"monsterinsights-staggered-fade"},on:{enter:t.enter,leave:t.leave}},[t.showMenu?t._l(t.menuItems,function(i,n){return e("a",{key:i.key,class:t.linksClass(i.key),attrs:{href:i.link,"data-index":n,target:"_blank"}},[e("span",{class:i.icon}),e("span",{staticClass:"monsterinsights-quick-link-title",domProps:{innerHTML:t._s(i.tooltip)}})])}):t._e()],2)],1)},X1=[],K1=r(Y1,z1,X1,!1,null,null,null,null);const U3=K1.exports,N3={"addon-ads":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6577)"/><defs><pattern id="pattern0_83_6577" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6577" transform="scale(0.00684932)"/></pattern><image id="image0_83_6577" width="146" height="146" xlink:href="data:image/png;base64,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" /></defs>'},"addon-ai-insights":{viewBox:"0 0 30 30",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M13.2226 1.65012C13.4579 0.575261 14.9892 0.568832 15.2348 1.6424L15.2451 1.69255L15.2682 1.79283C15.5511 2.99369 16.5231 3.9104 17.7394 4.12255C18.8605 4.31798 18.8605 5.92769 17.7394 6.12312C17.1413 6.22722 16.5873 6.50571 16.1469 6.92364C15.7066 7.34156 15.3995 7.88027 15.2644 8.47212L15.2335 8.60326C14.9892 9.67683 13.4592 9.6704 13.2226 8.59555L13.1982 8.4824C13.0684 7.88823 12.7645 7.3462 12.3253 6.92553C11.8861 6.50485 11.3314 6.2246 10.7322 6.12055C9.61364 5.9264 9.61364 4.31926 10.7322 4.12512C11.3293 4.0215 11.8821 3.74294 12.3207 3.32475C12.7592 2.90656 13.0638 2.36758 13.1956 1.77612L13.2136 1.69126L13.2226 1.65012ZM14.1574 10.8018C13.5447 10.7899 12.9592 10.547 12.5181 10.1217C12.4211 10.1856 12.3366 10.2667 12.2686 10.3608C11.6901 11.1271 11.0125 11.9101 10.2462 12.6751C9.66507 13.2563 9.07621 13.7847 8.49507 14.2565C7.91393 13.7847 7.32507 13.2563 6.74393 12.6751C6.18707 12.1194 5.65923 11.5353 5.1625 10.9253C5.63435 10.3428 6.16407 9.75526 6.74393 9.17412C7.46794 8.44757 8.2412 7.77182 9.05821 7.15169C9.14353 7.09057 9.21824 7.01586 9.27935 6.93055C9.03494 6.7077 8.83914 6.43681 8.70419 6.13484C8.56924 5.83286 8.49805 5.50629 8.49507 5.17555C7.23378 4.29226 5.98664 3.63783 4.87321 3.29712C3.66721 2.92812 2.25293 2.82912 1.32593 3.75483C0.725497 4.35655 0.562212 5.17426 0.608497 5.95212C0.654783 6.73126 0.915783 7.58755 1.3105 8.45669C1.71315 9.32151 2.19386 10.1478 2.74664 10.9253C2.19399 11.7019 1.71329 12.5273 1.3105 13.3913C0.915783 14.2604 0.654783 15.1167 0.608497 15.8958C0.562212 16.6737 0.724212 17.4914 1.32593 18.0931C1.92764 18.6935 2.74535 18.8568 3.52321 18.8105C4.30107 18.763 5.15864 18.5033 6.02778 18.1085C6.81207 17.7524 7.6465 17.2664 8.49635 16.6724C9.34493 17.2664 10.1781 17.7524 10.9636 18.1085C11.8315 18.5033 12.6891 18.7643 13.4682 18.8105C14.2461 18.8568 15.0625 18.6935 15.6642 18.0918C16.5912 17.1661 16.4922 15.7518 16.1232 14.5458C15.7709 13.3964 15.0856 12.1043 14.1574 10.8018ZM4.30878 5.14083C5.04935 5.36712 5.93264 5.79655 6.88921 6.41883C5.84483 7.30492 4.87473 8.27502 3.98864 9.3194C3.64043 8.78918 3.33188 8.23396 3.0655 7.65826C2.72735 6.91255 2.5615 6.2954 2.53321 5.8364C2.50621 5.37483 2.61935 5.18969 2.69007 5.11898C2.80578 5.00326 3.23264 4.81297 4.30878 5.14083ZM3.0655 14.191C3.30207 13.6703 3.61193 13.111 3.98864 12.5298C4.87516 13.5742 5.84569 14.5443 6.8905 15.4304C6.36072 15.779 5.80592 16.088 5.23064 16.3548C4.48493 16.693 3.86778 16.8588 3.40878 16.8871C2.94593 16.9141 2.76207 16.801 2.69135 16.7303C2.62064 16.6595 2.5075 16.4731 2.5345 16.0128C2.56278 15.5538 2.72735 14.9367 3.06678 14.191H3.0655ZM11.7608 16.3548C11.1857 16.0883 10.6313 15.7793 10.1022 15.4304C11.1457 14.5442 12.1149 13.5741 13.0002 12.5298C13.6212 13.4877 14.0506 14.371 14.2769 15.1115C14.6061 16.1864 14.4158 16.6145 14.3001 16.7303C14.2281 16.801 14.0429 16.9141 13.5826 16.8858C13.1224 16.8601 12.5065 16.693 11.7608 16.3548ZM7.20935 10.9253C7.20935 10.5843 7.34481 10.2572 7.58593 10.0161C7.82705 9.77501 8.15408 9.63955 8.49507 9.63955C8.83606 9.63955 9.16309 9.77501 9.40421 10.0161C9.64532 10.2572 9.78078 10.5843 9.78078 10.9253C9.78078 11.2663 9.64532 11.5933 9.40421 11.8344C9.16309 12.0755 8.83606 12.211 8.49507 12.211C8.15408 12.211 7.82705 12.0755 7.58593 11.8344C7.34481 11.5933 7.20935 11.2663 7.20935 10.9253Z" fill="#509fe2" />'},"addon-exceptions":{viewBox:"0 0 56 56",content:'<path d="M46.8285 21.9405C47.1537 21.9406 47.4668 22.0655 47.7045 22.2899C47.9422 22.5144 48.0867 22.8216 48.1089 23.1496C48.131 23.4776 48.0292 23.8019 47.8239 24.0569C47.6187 24.3119 47.3253 24.4787 47.0031 24.5235L46.8285 24.5356H43.4051C43.0799 24.5355 42.7668 24.4106 42.5291 24.1862C42.2914 23.9617 42.1469 23.6545 42.1247 23.3265C42.1026 22.9985 42.2044 22.6742 42.4097 22.4192C42.6149 22.1642 42.9083 21.9974 43.2305 21.9526L43.4051 21.9405H46.8285ZM12.5949 21.9405C12.9202 21.9406 13.2333 22.0655 13.4709 22.2899C13.7086 22.5144 13.8531 22.8216 13.8753 23.1496C13.8975 23.4776 13.7956 23.8019 13.5904 24.0569C13.3851 24.3119 13.0917 24.4787 12.7695 24.5235L12.5949 24.5356H9.17155C8.84629 24.5355 8.5332 24.4106 8.29553 24.1862C8.05786 23.9617 7.91334 23.6545 7.89117 23.3265C7.869 22.9985 7.97083 22.6742 8.17609 22.4192C8.38135 22.1642 8.67474 21.9974 8.99696 21.9526L9.17155 21.9405H12.5949ZM46.1438 12.0791C46.3285 12.3278 46.4186 12.6352 46.3979 12.9454C46.3772 13.2557 46.2469 13.548 46.0308 13.7694L45.8871 13.8957L42.4637 16.4908C42.2046 16.6882 41.8811 16.7791 41.5583 16.7452C41.2355 16.7112 40.9374 16.555 40.724 16.308C40.5106 16.0609 40.3976 15.7413 40.4079 15.4135C40.4182 15.0856 40.5509 14.7739 40.7794 14.541L40.9232 14.4147L44.3466 11.8196C44.6189 11.6131 44.9613 11.5244 45.2984 11.5731C45.6354 11.6218 45.9395 11.8038 46.1438 12.0791ZM11.6535 11.8196L15.0768 14.4147C15.2117 14.5169 15.3253 14.645 15.4112 14.7916C15.4971 14.9383 15.5536 15.1005 15.5774 15.2692C15.6013 15.4379 15.592 15.6097 15.5502 15.7748C15.5083 15.9398 15.4347 16.0949 15.3336 16.2313C15.2324 16.3676 15.1057 16.4824 14.9607 16.5692C14.8156 16.6561 14.655 16.7131 14.4881 16.7372C14.3212 16.7613 14.1513 16.752 13.988 16.7097C13.8247 16.6674 13.6712 16.593 13.5363 16.4908L10.113 13.8957C9.97811 13.7934 9.86448 13.6653 9.77859 13.5187C9.69269 13.3721 9.63622 13.2098 9.61237 13.0411C9.58853 12.8724 9.5978 12.7007 9.63964 12.5356C9.68147 12.3705 9.75507 12.2154 9.85622 12.0791C9.95737 11.9428 10.0841 11.8279 10.2292 11.7411C10.3742 11.6543 10.5348 11.5972 10.7017 11.5731C10.8686 11.549 11.0385 11.5584 11.2018 11.6007C11.3651 11.6429 11.5186 11.7173 11.6535 11.8196Z" fill="#9FB0BD"/><path d="M27.9996 11.1318C31.4043 11.1318 34.6696 12.4989 37.0771 14.9323C39.4846 17.3657 40.8372 20.666 40.8372 24.1074V31.0277L43.2643 36.4947C43.3747 36.7447 43.4215 37.0187 43.4005 37.2916C43.3795 37.5646 43.2913 37.8279 43.1441 38.0577C42.9968 38.2875 42.7951 38.4765 42.5572 38.6075C42.3194 38.7385 42.0529 38.8073 41.782 38.8078H14.224C13.9527 38.8077 13.6858 38.7391 13.4475 38.6082C13.2092 38.4772 13.0071 38.2881 12.8596 38.058C12.7121 37.8279 12.6239 37.5642 12.603 37.2908C12.5821 37.0175 12.6292 36.7432 12.74 36.4929L15.162 31.0259V24.0849L15.1705 23.6524C15.2876 20.2923 16.6905 17.1094 19.0836 14.7741C21.4767 12.4389 24.6732 11.132 27.9996 11.1318Z" fill="#CFD9E4"/><path d="M23.4585 40.543H33.5848C33.3825 41.7523 32.763 42.8501 31.8361 43.6417C30.9093 44.4334 29.7351 44.8677 28.5216 44.8677C27.3082 44.8677 26.134 44.4334 25.2071 43.6417C24.2803 42.8501 23.6608 41.7523 23.4585 40.543Z" fill="#509FE2"/>'},"addon-amp":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6583)"/><defs><pattern id="pattern0_83_6583" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6583" transform="scale(0.00684932)"/></pattern><image id="image0_83_6583" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-dimensions":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6594)"/><defs><pattern id="pattern0_83_6594" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6594" transform="scale(0.00684932)"/></pattern><image id="image0_83_6594" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-ecommerce":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6609)"/><defs><pattern id="pattern0_83_6609" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6609" transform="scale(0.00684932)"/></pattern><image id="image0_83_6609" width="146" height="146" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJIAAACSCAYAAACue5OOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MThBRTlCN0QxRDU2MTFFOUFCRjhERjEyRDk5REE1OUEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MThBRTlCN0UxRDU2MTFFOUFCRjhERjEyRDk5REE1OUEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoxOEFFOUI3QjFENTYxMUU5QUJGOERGMTJEOTlEQTU5QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxOEFFOUI3QzFENTYxMUU5QUJGOERGMTJEOTlEQTU5QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpIR0QgAAAl/SURBVHja7J0JjBRFFIZrjp2d6dn1CCLnIiCYKIqIxKjgSYwRFQmiEcUrUaOQGNCoUfHAhHjFA28Tj5iAQRPFCyOCKCiX4IUKIiDqcrqAAWERkF3fY2q1dpiZrpmune6u+f/kz/b0Vtf09HxT9ero6khzc7OAIK+K4hJAAAkCSBBAgiCABAEkCCBBAAmCABIEkCCABAEkCAJIEECCABIEASQIIEEACQJIEASQIIAEASQIIEEQQILKqHg532xZfUNZP9zdn+wyldUJ5FvIZ5I7kreSF5NfIb/tcuxp5NHkQfLYLeQvyc+QP27Lzz/tmm4okQKkcRKaUeSu8sd3OPl88lvk6eTaHMfFyE+T55AvU47tQL6QPIP8vEyHEsly3Uh+3CXNEPJc+XeD3OeQp0pg3PJvIo8xedKjum2UWyiRgqCuOSBaRH6R/C55r7K/nyxhasgR8us5IJotj+V06oILXO2dgRLJXt1ATimv7yA/orzuT36f3Fm+Po78MvkH8kVKukZZtb2v7BsqY6uWam2MrALRarNQpyrbK8iPZv3/a/Jg8p/KvkvJE5TX+8jDsiBivSdLtRadjua/vWqvNjizqqMW/UQeKeOcFkWU7dvIM/M1YpXtwwCSvdqgbB9VIB3HPI/l2M+tuScLHNdb2d4IkOzVImW7D/mmAmnvIS9VXm8mX5enFGOdTR6uvP4cwXb4lZDBMcOSVPZ3zkr3rEz3bZ581pD7KjHV2DzpjiCPyLr2B5EfUl7/LatNDsj3AKTgqx15lmy+u4ljn3Ol3TRQWldDpLP1I/ksckPQL6TVVdvEwSm3JI9oQuSX+rjEWQfo/87IyimReKgg7TEPrgLWezh+aAh+D0ODDpFfIF1AfoLcy1B+v8p45N18pVKBwdswNLtrgg6RHyCdQ35HmB2o7C4yg6ccY3gdTeeW2niNdGeS787a95HSDXCzOHCI5FHN87tTtuoCX535CdIE0Taj3ZznwwZA2iyD70LiaSHjsvbNlM35lqJvrvzBnKekGSNhm+2S/7Vhg8iPYLt/G+bdrwxVFbfEuKPRUfYxGMMUiIRssjNYM5R9fAwPlRgboA0KRH6AVO0HqAYv+HOi9dyjubIKa8zTEBieVcIxTM/bBpGNzf8B2TuWr1xj8sLPVba/EJnJbY0F0vP/uBNzTtZxVkHkR4y0u41Lpf65IFK/gMm/d9TO7LK6TWJqfQd111gJR3u5vUNNq0o5rlECN4m8jXy7y3uEDiJWpJwPtVlW37A4V6lhUNwV0CMXRKoUmLI//HTZPVEIjANgKyTd45R0U8iXZ39PpUB08dknWVsi8eDmh6L1VAuT4q4AHvbY4varLlQy5YLDDZhi8tIs/UJREvkVI3Hzlyd/1bfhe5xYqDRy+2JKBcaE8r130CHyo2pTi+ouIjPyrqv5IjOsUlDrNzVM37Z9x9E6Ge74J9az1a8qInYnok1b/fxC9jZFDtnXHGk1SFgT3/eL5uEcjy0hTySvsrlqU2OTtUUes0QGrQWVSiYPI5B66mRIX1Cu7olOvrZ+Yjl/2D2LyOJYkenXOllkprOg+Z8DJHc4nFSdgA4RmZ5+AZBKBKmqKs4T0v4AS/vHAwFSqSDthyke/w0ciZ0AKbe46bJOJ2EqVb0THO1fXwAg5dFirTgp7dSAI7EAIOXXVzqJ0k6qBzgSCwGSxzgpHou1i0QiaysYoj26pTdKJPeAu5JB4lumdgGk/OKuca0WmZNK7kZ8BJA8V2816dRBAAkgeQaJAm6+S6WpQkGaB5AMgRSNRmsp4K7Ejsm1ovhxzIoEiQNurSkLiaqq9RUI0gI/3jSMIPHCVqu1Am4n+Q+qNYDkuRugNu20Q4kEkApJq7PNSSWPpD+VVCrxLVDfAiTDJRIF26loNLK6gkDigdo9AKk4kLSa9omqRCXNTVro1xuHFaS/yD/rJEynUwIgAaRC0upPqnVS7dFiA0ie46RksrqXDEJt1yrh4xRj60skCrjj0Wi0EgLuhX6+eZhB+kZkVtZ3L5WqE1sqAKT5AKk08bzs5VoBt5OKAiSA5Ll6q0k7nS2HiFdF+QEgtXXAXZ3oLpQlaCzUIt1qHiDllu685GgsFluFZj9AyqfvhOZYGpVK29BiA0j5xP1D32vGSQlLIWoWrR/AA5DaMk6qcVJdLQWJn/u2FSCVKU5KJKrqgnDBbazWKqpEYsXjsTUWgrQAIJkRx0hac3BSyeq/0GIDSPnEEC3VDLgdyyDiqnoFQDInzdXcnCMsA2mR0LyjBiAZBIliJF7MdKNFIM0PyonYApL+4hJVVq3m9ilAMisesNRafcNJJndZ8pnnoUQyLx4m+U4rTqpxwr64BMdEb4jMw3Kag3JSNj1lmzsmT3ZLVJveH3APCOln5DtnePH2wI0b2gSS7r1u7Y7u3WPT8pVrKnkhLlRtLiWSrvoRTPj2USLlFHfM8eQ1nRVtjyd/EEaYdB7YA5C8iWcI8g0Bp+mUSGH9kEGF3yaQWEs0QRpMfjNEn4s7UV8XARnprxSQdHQo+ZKQfTZ+3Pto8osItgPScgtxw+gpUdwjtwBSiVpJ3m4xTDxdeBhAans1WV4qsQ4GSKjeTGgZQCqPFlsO0ucACSWSV/GqKusBUvku9lZLQfoiyE1K2zSSnLQUJJ46Mi6I35ttHZL3kB8oIj3P55lCbvT5vPkx9F000vHTsx8XmZ557lDdBZDM69oiIWJFyDzR7Rrh32oeV5CvLwG8yeQRApP/jaojeVKJxw4VmeEHv877BQl0sRpOvgoxklndSq71cPxd5Cofznu80Jv2kk/3B+U7tAWkKzwez7cpDfLh2g/3mEd38ukAyYz4YnYykM/AMp93D0PnfQpAMqM6Q/l08CE+MqEuAClYnyGG865skNYFLB9dmRrq2ACQzIjv82owkM9CH87bxCMfsD6SIfEcpCke86gnzynzeXNH4mSPefC9eZ8BJHN6UGQevVWq7hX+PGnyYeFtRud95L0AyZy4irhBlDZc8Db5tRCe9zTyq7a1eIKgqeSxQvPJklIzyVcKf8er3ijxvEeJAC0iYds0Er7L4iKNlgxXY4+Rhwj/R/7DfN7/KdLcXD6ol9U3lOut0uSr5ZfTl8xPkeTbuXnS2yzySyJzx0nQZPS8j6lrbydIkL2K4hJAAAkCSBBAgiCABAEkCCBBAAmCABIEkCCABAEkCAJIEECCABIEASQIIEEACQJIEASQIIAEASQIIEEQQIIAEhQ2/SvAAGXJ/PEK0RdbAAAAAElFTkSuQmCC"/></defs>'},"addon-eu-compliance":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6620)"/><defs><pattern id="pattern0_83_6620" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6620" transform="scale(0.00684932)"/></pattern><image id="image0_83_6620" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-forms":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6643)"/><defs><pattern id="pattern0_83_6643" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6643" transform="scale(0.00684932)"/></pattern><image id="image0_83_6643" width="146" height="146" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJIAAACSCAYAAACue5OOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6M0Q0QUJFMjIxRDU2MTFFOUFCRjhERjEyRDk5REE1OUEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6M0Q0QUJFMjMxRDU2MTFFOUFCRjhERjEyRDk5REE1OUEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDozRDRBQkUyMDFENTYxMUU5QUJGOERGMTJEOTlEQTU5QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozRDRBQkUyMTFENTYxMUU5QUJGOERGMTJEOTlEQTU5QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pn1GjqMAAAIOSURBVHja7NwxSgNBFIDhJFjYqoQUEsHCItbRC9h5JK2NF9EjaJcLCLZaWAhmmyCa1i6+VBZiJTu7M/t98EiZOPszQxhJf71e9+C/+kJCSAgJIYGQEBJCQkggJISEkBASCAkhISQQEkJCSAgJhISQEFLvefG+eRnHXMWcx+xZ+lp9xNzHXMYsJuNhMSHtx8tjzMgzTmoZM42QqrrfaJDoD7oWUSM2az4r6WjbbLW7nmsjVrEj1b72qXYkETVnJ8WbDKwzQkJICAl+2WrLB3l6eW3VwhwfHWb9Oe1IONoQEggJIVGIVHdt/umpQZPxsG9HwtGGkEBICIlMuWv7g7s2OxJCQkgICYREK7hr6wB3bTjaEBIICSGRMXdtmXPXhqMNhISQEBKFc9fWAe7acLQhJBASQiJj7toy564NRxsICSEhJIQEQkJICAkhgZAQEkICISEkhISQQEgICSEhJBASQkJICAmEhJAQEggJISEkhARCokZ+Z7sD/M42jjaEBEJCSGSsNb+zfTH/atXCzM621WFHQkgICSGBkBASQoIfbv87wO0/jjaEBEKi7JA+LXVjViWFdOd5NibJ2qf6+n8QLw8xI881qWXMNL7+V6XsSG8xJzE3qbZax1nvNuY0pkrxhkl2JMonJISEkBASCAkhISSEBEJCSAgJIYGQEBJCAiEhJISEkEBICImW+xZgADt76lgGVKSxAAAAAElFTkSuQmCC"/></defs>'},"addon-media":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6665)"/><defs><pattern id="pattern0_83_6665" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6665" transform="scale(0.00684932)"/></pattern><image id="image0_83_6665" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-page-insights":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6677)"/><defs><pattern id="pattern0_83_6677" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6677" transform="scale(0.00684932)"/></pattern><image id="image0_83_6677" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-performance":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6688)"/><defs><pattern id="pattern0_83_6688" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6688" transform="scale(0.00684932)"/></pattern><image id="image0_83_6688" width="146" height="146" xlink:href="data:image/png;base64,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"/></defs>'},"addon-user-journey":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6699)"/><defs><pattern id="pattern0_83_6699" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6699" transform="scale(0.00684932)"/></pattern><image id="image0_83_6699" width="146" height="146" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJIAAACSCAYAAAHZfKMYAAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAkqADAAQAAAABAAAAkgAAAACyC+sVAAAdq0lEQVR4Ae1dC5RcRZmue293T8/7nUwm05NkSDKZV1ZRQDiLq4uKr/jYVRRwPWcFlV11jxF5CBIGiMszCUfOkeNBXF1EFkHERTnKsoqKHkFWY5IhCYRJ0j2PzHTPe6bf3Xf///ZUd9337cdMZpKqc7pvVf1//VX13br1rr8I4SZfBASjACenp+uN/Fm/9z9IJmbH/ELg/u1ZGS6W4aA/KKN7djZBBl4/oZB6tmxU7PS5trmBeD1uIsg1xL+vj7STAzIVmJV6ZDjYmUiRI4oEh3/v3ztKqCAMkk1ZMkUepjL62puzkVA/J09VIJpNDIjZpFnD52vHA2RNYz2pq6kiwclp0txQp8hnI1YJQyorUOG2+ROIcLi3vanbkK1798S8IUHjeWhoYidGzEauSlnvv08rbxPDzYz7ybNfWacRoXf2+ppEQRCUcCIld++ejKH94NdqSU+LRL0tn5CSU1QQMhqmTJZlMhsMqF67pVQzYvftQdn35QPZ7JrxcX+OwHIhoCrxGGkgIJenq2e8dgn4wLfIJH6vtDLMChoYn29JRyOjKEBbbxlV16fCtWTnU2Fy6KY6RUZWEFtl2KUG6Zf/wI3f6RzU9zXozlbN6EDD1o4ZH2f/pinC7LStbSa1NZXk6GCAdHb4yGBghHT4WrNPUXS9uaetfj9GlRWEjnyzh2EMc9B5t1zt23ngUWSwM6ZVcSHVMEZGU6QCG6tghXhnOzwSit3qjwpBHpWgvjtnlHBY0Prac/0BK2GGNMwer3oNoeGeHIEViYCqtmRTODAuV6WjoTnWrxR2bECoSaeSZG5iJNu8Uf9sd5N64BNr1XwTlEilsiKi8TgJR5Xeq+LH2rNMYImFZ4lbEs9n/dCuqjPR45A/9KpM1B1NtpJFnlIYqFLHoQOwxkiW7vVBP1o6FAgljZhL6WeVUd3rg95/yioANuv4QzMzu5AdcKIbm3/aozkxdCprR9pRGKFRGrq1DS/6UaNDihKW6nkQ3wK8DVa+FgRdmaLMqp4CDItAkEJyMnCjMgyfi3IMaYueOqQ67zi1yS15B5FeBkl+5Tra85gh6XSKzIWGsyNK+jpwAI2GdbN2SnO5XKRzU5vCq/xhUfE16YDReQgp8jRZBDemKe5x+IQVIwr/is+ezk0Z9+I/62btLK8oux/v8dVOqgJqHDqkKJ2+PuxSsYb241m/Zbd39o++3LNzoGHZI+YRcgQ4AhwBjgBHoDAETBtcVpyT9Q+W38qOE3CR2QkSjy7gzFIysG97bvgCAXXdEioM+uAp6IsrXWFcQymFoUOn8ppGgj/oXeji1/W9MWLsF9PE0IRgZ4x2yNCPdbN2Sjt87KQSVEtDT5yKevCyCoWu/dO9MugXPwzd2c9oGa3cqXSapKHr6pYyPT4cu5WXlREBpNNxXIW3jLx0Ik3uf7EsK4qdO6We+gQBOpS4FM/LH5HITHBIEW3UAdS9Q20i2NlNLa1Qd883JpIDNzcaxm1YhlQRyckvqtwlcJglBkUv+ytT8iMIT8NI5KNGedMjJJB3GjGi38xcZkRLh17sFzQ9O58d+SIvTmzjKJcadJ8cHss4Zfkj+CVTGvvUJajP1/wCy8DaPR4XccGXhF+P1kTjCZJkZk0i0ThZiESzbOieD0eybrQcDATDKg9wGIjOsABzP0ya3KoNUGq3dqhtmqBSR0zlaV+VNkGGnx4N3Hnr6O/Cs8E1kOov+Pdtf576L+XTEKGu2075JbfXhxEvTI+TZDxTFp76Ugvxug2DFJxGLUK6Qt25a/gqmhiMpbJuDald0640hv/wQO6rKTgFNgF12aXjfAxH12OGptPkfQ/OKevs+IX9fGdmrR0/ezR0iwG149OKhnQ0WnTQz7IMIQOatrockLQOQn86RaO1a90sH9LQGCUG/S0TlEhhCy6QW5/VVBeL0zQoIB8jpIXB3vbGX1qF0b2ybf2nDrs83m3aQHSqxqiF1vIW4869i0UpR/pbuqBzlqZCMSE0MaIo/j31X6qnLkEY0cDN9VIyFv4tG+k5dX3uk3t7f836nRa7b+fB3LT+aUkBj5QjwBHgCHAEOAIcAY4AR4AjwBE48xDQjR7PvCwS0nXb2AnJXbYBx6Czi/PUMHHxBMw7XeYkvwWD9Opw6F0w1fgk7G7JbChxEtsy8/zbj1MkGNFv1MZ5MJwPQyOIwqP+vX2fskqa5cSINuBAIHQBLHf8Ef1TMGliZlKpNKFb3DwuicB0QZYVphTopiHwL/gdZeVZWViAzmuXyHevrFLY/+VxF3lx0KtMZ8hp+UrwLA1IOLeK60FOzJFBv4qNnc56dXFRizKwNDonlw8NjwmNT0zTIKR93RpSXaVfGHvFn5vA+MPxZJbficXRq9ROPjsRvFJ4PvPDNImkM4t4qUScpFMJEpmbgtIMc2QC+SOswV5ol1ZbkGAV8hGQaFkclUhEcQ9E+ie7CE8X/ZPfFx9LhOeEyPw0rOcIUTiXUe40LQ5AgoUdmVgKNJsJdpqIlc5nW3HDCb4HoKq93iojK/VzhLSPQLr/JBJpb3d7vWq61yo/WpptScIAKxUEbWacuqXyitbu5krlDJaTMI5AQkGwdyQEzXejE6GUZ3gsRHAxmZpzNqxXzuai+zgsKIeZNdyuczZkuwSvnxgi8USuBWJbQGwdsRtBDUtDP6sWkoahTzhN6ob6KRcRJWiejkGi4Q75g1FIYm7PByWs0qckeLZ2+2pft0p+3iCxwg4NB3eQNPkuvNgm1n+12e0anqJAWg1gDIxM/m06mfqdVVrtQLJt3bTCu3YNv1vyVj7H+tPVSupXVyH89rFr1n2Vuk/XUxaFTwBA1xYbv+OS1HV7cERyuTPr/RaxhmdCJBHLLYLjFgGjzTgWIpaVBGl7rNfXfIVVpI5AYvdLsMJcMG59+rNVpL1eJA++GFN+lD4bGiIy7P9D87UP1pGLt6r7o1atkFOaJIlkW0c7jVLVspWXeUhHe2uWZmwRnu9rb3q3MS3nawvStv6xN1yeso5ckIzt9ztrSI1XH/wt98yQ+OJYkv0MteeEcEtXEmYL0JTBkRuYssgIhv9ILAazDBkabqpEMKjBvWu4+RINDmTpBkx042A3vdg7aKiuJG5QBWJm7OohNpxtnSS53ZvYANS+fyhJ3r5ZnwgKEOUze+KeOPwZGQTGzNRC5s0MVRpiSBfIgocInZ2+pmFDuoVn7vVZMJl9bhiko1Ek5zSJ5H+OMn0yeJszQX9OogB7+/Zt357zWF02RyBhlnp2TyQF0eTVAz2VjJHI7BQ84yoEJK9704m7uk6oPFeZwzFINF/dd0zEACsPdS9AawZ7hagz+3S5hL85fl/fgazHKrbkDRLNa9fNUxukSuEEumkFDeOgn8E8zQ7Kw58cAY4AR4AjwBHgCHAEOAIcAY4AR4AjwBHgCHAEOAIcAY4AR4AjwBHgCHAEOAJnAgIFLymtlsxvvWX0PE95+cuY3hlGh2k+i6YlA6mUSsFK9QL+8cGFq6Ny5T1UHl0fpG7YlXvJyft7fkXdZk/bDRNmAWGj6aOwwTO7r6dUisrM4ivEHwBSBUN9L2hQbzTqj06T1P+C07ag5Pa0KMHt/wYCwe/glmUWIPtQy89x13O5syQ09oaKDB7Vja2ATMa+Yeehr1O62TOvkgQ7b0dh/0+LmTDqjyqlqMHrgliTphuIwHMpTykdGFfHe+DGGjwuoSQF7z+obFhD5ifHYD9T+vPguZtNo9buGCQ4YzIEpccWoImpGXIqNJWNp60FbrtY3FOE/kinpqNtHSkvz+xFGjoVVJSLURrqUUZ9ymi0e77Zvdumu+JwMxfzIVGAFIHwJy7u+QAW0IZobRx9blD/3A07zNdbi8pQBeZsG/rA3UTZYAKzSR095RxJURuYZVQsOSK8HDXJgeufzs2VZmR/5lBmS9AvD2f8E6gpEozoEq5RLBZ/uZRYMC3XsQl6kBBvfHLBYULWUB2LWCJwPyQ1CbiQK5HMbCArg09bYl7S5Y/AJ2eSQ6WlE4R0YF+fOiIqmHnalqSBwMS9DP+SWnH/I/60AGGkqIASfyxA6O8GMCmNBQhpj35KXZoSsQicdZvMbhWqqRXVu10xkIGxBQkqttO+H9sg3Y68cK/qtRdn9M7hIcDwTJDEI5mzLqIonTvQ36Pelmci1XHFbRI+4y2QoEiEr6RFQf3qLAMtD/GtHSJ505Hwjv3BiiuzMQrCpSf39vwl67axlAQk0D65xiae001+vKd/+Eei5PqpRNLnD+7pzeuEp0m1lsuTbaUtCAdBueiq3Vmby6m5zbZOMg+6SJHlPlueVc5QfElaqQDARR5wdvgv0E97obut4QboOmSOERSQXluQoCM5AZ25M+JKC5dY1tnVVvNavjjZfm6iIH46X6ErlT+Zjh2Fl67MLeWTRtuShMJsK2+TGF89dgJGMxki9oY3wxlcag4f8+PgUnHiTYZ4oyE1r8EZXFBnoTibGmpJY10NJZHjgVH4ijI0vIy2qkLdH0QF0dS0gkztABtp0Gmfh+Nb1ZTP7mlbklAAnPbpsBNkRKcAIS3GzAygmwKEdrwHijUJOKScOcUEClkYFRvIg8MTVEyNv7FgbiCNtHgCtEcs0vAZ0oRFHjSQriooUb/OuOz/HYG0raXuOAxcv2IvTs2Bb5GeRsLrh1kDn3HWiSXJzBjpHaG8FYszCNTtXpw1oG46w0Dd7BPq2XccCQZbWT8zu6PPjQaGCbcr4Ct4lLpX/ROOu0MfTz3xZJCpvEDC8KdOyZXBRGgMqgXz128Q0Ur1cnI4MFfmHeaipUVYgGFIVZWvqRwmi/7DYbAVyzYwNGFbjeRdklZsbg0SBgB8PJ1O/8iAxHjZn8PNuyQx0le8taet8QkHiXyLHU/BswBwgHkX9DeuSiZiFdH5mUNwgfxLzdv6bvm/z6+86RJrEOScGi8Txrw/N9APMAr6AbILAuyFCjSOKy+q/uSn3lY9SN2n5SnBokWKPAHdTvNTz5AwGNM9CYpdPm6VRscgval/qi7pEdS9N5CMihJiCzNw9lY933bRZi/5+ofqreJeETRY1rqkp63pV1aJcQQSdLwEWKvKjCEspNFbdSnL+R1e0v+RlQ1UyboAfXfN6pdDKRLME2b+lKtDqNfLg1EyMOxoGpkGWean8IqTCG1L0tZdgVs93up+I2Gfu6iMfPHtZQSvMPnwQ/OgW3KRCwZHyg6ORadWu8QYjKlCoA2Cmk2+dcqKB7pHQDHVFKOYqrPDl1Wy4B8ZJ3MLuZPj7CLloH8ENFPkXghLo/Fon05KEYax7QJ4ymtu1QpH9zOfqyJf+juvsnTsq5fIn6+vJeW0g88sSCLvVJiihy4wi2o0Mg74Z0bCKWYZPMMKVe+iYW9jQi8mGLwg29qAilGeAhHfq/KwcNiWJCPtEnC7C9l/Y61ObDgukwv2zCr+rPabi7d6QamLum6y2i/AXj3FToVAx5BMg64SNLhcVFeTm+1IwCzDJEOzVMlBhDtBkctNiiAHfwX1kzaC6g0jU+HJYQ7aKLIsh0fVLR8SjOZ5aAAWGOqHT1QP21CbA4alofKWtY11rJehXXRJF/e0NrxoSDTxLAikN0LGRfulEzl9Jcl47iKy6bAxv0malsZbEL4LI/6rChFeEEgY0deeCZM7d1Rk48Rblq5+LDd5ht0BajY1u/8A38cPqHs5nmJaiMIiwCtdvoaDxcaX+z5MJHXfHpwTXW71jBnD+65OFzkxkSbHmNKl7YUv9Q1QTHKWxGpcuTBRvbrLei74eVAJxAKEFTa9921RzGOMuFVptQUJcxWLz7/ZMnfQFscWZpXdGqw+NwgThFJ0hWXYVUC0/dxoHjbvClzl9VZ/h7rxqR2GsDQYOs4H7u8zborUjCve5agkYS6O3e57OBqevZTNEQ5DDI0gBM4UgDB/jkFC5mO725/r2lzrWlz2Qi+dgaJ5P+wea9cRVrGH489Nm0e40/A43Gm4MXtTNTB4XBVr37hv87iW96x2b+wfb+nePZFu33ng22c1EDzzHAGOAEeAI8AR4AhwBDgCHAGOAEeAI8AR4AhwBDgCHAGOAEeAI8AR4AhwBDgCHAGOAEeAI8AR4AhwBDgCHAGOAEeAI8AR4AhwBDgCHAGOAEeAI8AR4AisCAQKPk2yIlLPE6FCYNvXhz4ouKTrBcn1JlAtXhmdDaXgSGYIdIL44Vz3c25ZemJwX1fRB2xVkS46eEEyQmUV+W3dNfwFd1n5NwWNDv04qKrHI2NGBpRZTAmy9DEn948YhTfyO20FaUCWPXJg8p1ElD8EOji3QEJaQcEGaOeU7TUeGOXkLPPDE/M3PeuGexWMz3GmQTUTFibUwpFKRJWjv3qIhAl3mXju4N09fj0tP59lK0hHApPbk3L6XtDA+p78ksi5tQgMjKTJ7l9Z6hPTBlHcqPF/YWoc9H/llAHB+XFZkuTuE3u2HzEM5NDT5PC1w9A2bAMjk+3pVOoZOMa8PSFrlAHZhOVkcwT2/MachpQdvW7y1Uu8pKFCJKgVZt8LUfLUXxOgtEkkVQ0tJDo/TWLhjC4iaAGEdEp4CIJdbC3VmrokBemQP3SeLMi/SSdTaqXP1mkxpUZiMeVeIKprmmXEy5dQX7VWEzDyhCNR4h+FL9BA25TX4yEd7esUjVisPLTPh6NkZDwE4dSFH2+kqq6qJOvXNmqDKO6FhQgZA3VirDpoJGC4etBfZKTDKAm30AyPT8JtNAa6kuDGmvVrmnRpjKSo6i59Mr7xwXLyob7cTTd1UJhue38FuXBTnFz3dEbtkLeylsQiUJCosi6BvFUvKT8f4wY2PxlZbuz3gE7r30Pz9TIksiSFCIWH4cUaFSKk4Y1e4UgMrTozMx82LETIGI2j/m7jcFPTswT1f6Ns9pdCrV+zc9kriLQRYiFCmbFYQvWLgl650XHjji/qqpsHnXTaMOhGneTzUDi1ptnLNk1q6l+G1IWfUvcz/ilU0UQLETDAqO4nlK/QZ8lqpIPDEz45EHoN0uctNDFm4Wqrq8hcOELi8ZyOKsrrha+2usq4zKJO9DlQ6UbvrKJh8FlZ7iXlXuOkVsOtahgfAMwGUex4RZNR7YfECpBpVjgxPiODYcwM6nAvh/i05psfk8jV/xUjC0k97cn9cYK/bWtFsrlJIsehUz5wKle4lH7SdE7fByhnfcW/r69oJUQl6WwfHAl1gUbZg4B8Tq+dNvfcXXIEdv08SV6fMv6ItJHJUJtG5qEJjeY0copE3Hvy/t5rtbyFuEtSkA4Fgkfh491aSAJ4mOIQ8E+lyA0/gz6TkPuGrbV8AasgnPK4vecdu2fLUHGx50IXXZAOBoLXQXt7T04kt50OBJ4/kiQPv4LNZOaVajUxYpqgAEUFWfxAKSciaV6L7iPBiORSejsPFVrIE2Zb/1rhKb+0Y23VWCHheZgMAp23DH/G5a14yFtdL85PjC7CIsxDM/bhk/vsb9IuFMfiayQ/3v9b3Gw0fCkB0MZ/RqnEK/SFlCrc1v7R85PR8L3J2MI/+/dtHyyVXDM5JShIwTdAeIdZBE78oVaLC4J7XY+vdtIJP+dZeQgU37QJwp9hmFxUQYKm0SPL8QmYg3oKVEDfW1YhHE5GXeazbisPx2VN0dZ11dNQi+vnQpY1FerIiq6RDgem/i4pJ19Qi+Wu04IAXF4NL/SnRJS+17u+4ffLmYaiCxIm9mAg9AOYQ7pyORPO43KAQKbWerCONN7g8wn6KXIHIpyylKQgYWTQLP0RmrgLnEZcCj5cstCth8FktAvu37DKmDJfbTBrDc1FKZK1MmUIwpAoSDt62ur3L0UCS4ocFKZfQGG6dCkSqpU5DHcqTTN3Kmnpm9vXk7IyfTfr5PApZVFWy0/dXZvb4XYQ/RLk6yeHYYlGv7CK4fCaWrzTycgcGfSbrvfhcgveE2VmkskUOXo8YEYm9XDDT6vJArJZIPhYTrolz1s7W2tCZjyF+OsRK0TKYhgYwr8XVuPfBTNfucWdIuRZBbWrPQzKgiJOv3pmFUuOZhXOah4NF33NjBUNw2h3EWjl4Ka1fA186BviyVjw0FDojnzDWvGXtEZiI4Jlk9ug9djF+q0Ue6FNGy7+4poVa7BAmy3iIh+8OGUhV1uccPtLeZl+0ZWVjXYsbFHYRqMNL0kiXMmV2y6iDefMLX67r73xGme81lxLVpBotIeHJi6EW+uehK+2lfrx58pBAD6Eu6ElubHYFC15QWITiNfKJ6OkH/wuhw/V9BYvNgy3Lz0CouDZApPBx4qJaVkLkllCT05P189HUq1CmrSmZblVILKzvRFmAs94f6Eetl+/A2r5kux/h0ngR3p8TZ8uBrYVUZCKycDZHvaQP7gX+k87i8EBlqhe7W1v6ilGRklHbcUkhIctEAHR9f0CQzLBilviQkFFr7UxqbG0bts18nlBEq8T3Z4OuMpZVRMmovMkPKtfr4UB0Qz0pZ4VJOlb/j09L1pGcBYSB0YneuRk6ndFZx3WS4uVoXqhxQpjw8OwV+i6bew5qcx7iSBbTjST2dAwDKvtp56w+F3+tmpy5YW8n85iXawdJmDv7fE1Xl+MnJIXpJ7++Za0GH5NcnkKvrQVrxDGW3ITcFIU52GMzDlr3OSeyxpJOXMVuhEf97NDQJju9TU2wzRAUbsJcht97eJzQO/sH31B8kgPiKJkP9NmIQ8vzYXz7KQMzl/hGSw82JeEY8esmVpIkx+9PE8icZmcu7Go6FixZ51dlIRPrqmpPFxsxktWI3XfHpoSXa6Czu27Frv8SfWksS5veAoiHp7X+W9b5yZ7L2/S+Ws94okEGYI1umRC34zisaC2FmMZMVhjG5uYMlwzq6msII31NdqoFDeGm4AzctqlEJjeILVwVKqqwniWA9M5DWfatLUxzqLX1VQSj1u/hmiYABtPSRLe3b2+6XkbNkfkkhSk7tuDk6LLXe8oRmDa1CiSez9cQTrXGleIb4RS5Pqnw+S1oL5k4QnR6Ny0Lqru9R5y3yeMT8BS5rHgJAnBizUzm9palLNpWnpgNEhm5xe03ln3lo1t8HL145bBwKjpOTcM3L15I9S2WTFZy9HjQwRP4BoZl8tFOje1GZHy8BOmRMF9frGTkGyERQ//O/tHvpVPIbr2nV7y35+rNi1EmLhz4GDfj6+uJje9R394sKy8hkhu/RrTq8Nx8ouDuTNbbCapvbKy3PDFIR1X8L0ma1/lXn18VKbLJcFam/EHUQbbWcyMG7e6GBQi5DeTZ0czi0vlL5Dr+9qbGkpZiFC+SVZUUVs6oDaCJs3tqEnz1Yvk2Wvy64N/9KE5ciykrpkioAQhnlWCkEteZ4ub7LvCuHnKcZ19NtwTT0Ty2d62pv9cqtzr6+M8Y4J+Ua3TIBPQQU7CarZLPY1kGhz7CMEFg1Ebnl03MG+MG+8XMmA9870EEoE+1R5XmXzPtuamuaXOcNEFCdTWxGCUpW+DDFIeBt0HO749T358VRWpsBm2x5Iy+fjD82Qmoi5IeIo0DlMDRqa5xriJMeI9k/ygwExC//0ZOGz7k5p043NLva3WCLviC1I8cp1YXv2AkXAjv6HpNLlgzywoOBDJHaCCpXedOglHxlLklp+HyZExdXOGstIwaTk3OWokVvEbnyHv62tv/oUpAycsGQJF95EwZdt2DX/J5a385pKlEiolZegf0Q/9aZySIL3vxL4eXogoIMv8LElBwjS/5YbJ2nBlakhyuUu2foGz29GFGYL6EM2NcKDe1XzRgftazMfn5oE5pUQIlKwg0fRsvWXo025v5feg3XYkW1F2kACFVyZLIVSu9gnio5IoXXp8T/dvtTTuXn4EHL3sQpLV2T/0MclV/jgsl1jOVc0Gh6AM6ftDZnFC8ZwSJWjG7ut5yYyH+y8/AktWkGhWum4e2SB7xJddbu8a6sc+45E5EpkDPRS2RnihrNx72bE7twRtWTnDsiOw5AWJzdHWXf5r3Z7KuwRclWXMHGwjwRGZgQnCgu1V/n29zxjQuNcKQmBZCxKb7y23+O/wlFXeiIVKo6U+CCdCvwy6fH7I8nM7R8AWga03DV3ScePR82wZOQNHgCPAEeAIcAQ4AhwBjgBHgCPAEeAIcAQ4AhwBjgBHgCPAEeAIcAQ4AhwBjgBHgCPAEeAIcAQ4AhwBjsASIfD/s31pjFMCaw8AAAAASUVORK5CYII="/></defs>'},"addon-ppc-tracking":{viewBox:"0 0 56 56",content:'<rect width="56" height="56" fill="url(#pattern0_83_6577)"/><defs><pattern id="pattern0_83_6577" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_83_6577" transform="scale(0.00684932)"/></pattern><image id="image0_83_6577" width="146" height="146" xlink:href="data:image/png;base64,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" /></defs>'},"addon-site-notes-important-events":{viewBox:"0 0 146 146",content:'<rect x="14" y="23.1475" width="115.957" height="106.017" rx="10" fill="#CDDCE9"/><rect x="30.5652" y="51.8613" width="58" height="9" rx="4.5" fill="#9DAFBF"/><rect x="30.5652" y="68.8613" width="76" height="9" rx="4.5" fill="#9DAFBF"/><rect x="30.5652" y="85.8613" width="57" height="9" rx="4.5" fill="#9DAFBF"/><path d="M132.736 15.8642L132.735 15.8632C129.302 12.4266 122.595 12.5716 117.699 17.4598L132.736 15.8642ZM132.736 15.8642L136.135 19.2618L132.736 15.8642ZM48.1736 86.9698L117.698 17.4608L136.136 19.2629C139.573 22.7043 139.43 29.4088 134.541 34.2918L134.54 34.2927L64.821 103.99L42.6248 112.107L42.6209 112.108C40.9551 112.72 39.9482 111.894 39.7399 110.937C39.6428 110.418 39.6917 109.883 39.8813 109.391L39.8873 109.375L39.8931 109.36L48.1736 86.9698Z" fill="#509FE2" stroke="white" stroke-width="5"/>'},"report-sessions":{viewBox:"0 0 17 16",content:'<rect width="17" height="16" fill="#8EA4B4"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-190 -273.992)" fill="#8EA4B4"/><rect width="158" height="3039" transform="translate(-190 -273.992)" fill="#8EA4B4"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z"/></mask><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z" fill="white"/><path d="M-14.5 -16.7299V-17.7299H-15.5V-16.7299H-14.5ZM196.99 -16.7299H197.99V-17.7299H196.99V-16.7299ZM196.99 57.9115V58.9115H197.99V57.9115H196.99ZM-14.5 57.9115H-15.5V58.9115H-14.5V57.9115ZM-14.5 -15.7299H196.99V-17.7299H-14.5V-15.7299ZM195.99 -16.7299V57.9115H197.99V-16.7299H195.99ZM196.99 56.9115H-14.5V58.9115H196.99V56.9115ZM-13.5 57.9115V-16.7299H-15.5V57.9115H-13.5Z" fill="#8EA4B4" mask="url(#path-1-inside-1_45_7568)"/><mask id="path-3-inside-2_45_7568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z" stroke="#8EA4B4" stroke-width="2" mask="url(#path-3-inside-2_45_7568)"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-190 -273.992)"/></clipPath></defs>'},"report-sessions-active":{viewBox:"0 0 17 16",content:'<rect width="17" height="16" fill="#8EA4B4"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-190 -273.992)" fill="#8EA4B4"/><rect width="158" height="3039" transform="translate(-190 -273.992)" fill="#8EA4B4"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z"/></mask><path d="M-14.5 -16.7299H196.99V57.9115H-14.5V-16.7299Z" fill="white"/><path d="M-14.5 -16.7299V-17.7299H-15.5V-16.7299H-14.5ZM196.99 -16.7299H197.99V-17.7299H196.99V-16.7299ZM196.99 57.9115V58.9115H197.99V57.9115H196.99ZM-14.5 57.9115H-15.5V58.9115H-14.5V57.9115ZM-14.5 -15.7299H196.99V-17.7299H-14.5V-15.7299ZM195.99 -16.7299V57.9115H197.99V-16.7299H195.99ZM196.99 56.9115H-14.5V58.9115H196.99V56.9115ZM-13.5 57.9115V-16.7299H-15.5V57.9115H-13.5Z" fill="#8EA4B4" mask="url(#path-1-inside-1_45_7568)"/><mask id="path-3-inside-2_45_7568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3282 7.99988C16.3282 8.54222 16.2735 9.08456 16.1654 9.61189C16.0603 10.1254 15.9036 10.6311 15.6994 11.114C15.4988 11.5879 15.2507 12.0455 14.9619 12.4731C14.6754 12.8966 14.3469 13.2948 13.9853 13.6569C13.8478 13.7939 13.7054 13.926 13.5589 14.0533C13.3181 14.2617 13.0646 14.4557 12.8016 14.6335C12.3739 14.9224 11.9163 15.1704 11.4424 15.371C10.9596 15.5752 10.4539 15.732 9.94037 15.8371C9.41306 15.9452 8.87073 15.9999 8.3284 15.9999C7.78608 15.9999 7.24375 15.9452 6.71643 15.8371C6.20293 15.732 5.69724 15.5752 5.21437 15.371C4.74051 15.1704 4.28286 14.9224 3.85525 14.6335C3.59219 14.4557 3.33874 14.2617 3.09791 14.0533C2.95137 13.926 2.80903 13.7939 2.67149 13.6569C2.30994 13.2948 1.98142 12.8966 1.69494 12.4731C1.40606 12.0455 1.15802 11.5879 0.957426 11.114C0.753227 10.6311 0.596474 10.1254 0.491372 9.61189C0.383266 9.08456 0.328613 8.54222 0.328613 7.99988C0.328613 7.45754 0.383266 6.9152 0.491372 6.38787C0.596474 5.87436 0.753227 5.36865 0.957426 4.88577C1.15802 4.4119 1.40606 3.95424 1.69494 3.52662C1.98142 3.10319 2.30994 2.705 2.67149 2.34284C3.03365 1.98128 3.43183 1.65275 3.85525 1.36626C4.28286 1.07738 4.74051 0.829328 5.21437 0.628728C5.69724 0.424524 6.20293 0.267768 6.71643 0.162663C7.24375 0.0545552 7.78608 -9.91821e-05 8.3284 -9.91821e-05C8.87073 -9.91821e-05 9.41306 0.0545552 9.94037 0.162663C10.4539 0.267768 10.9596 0.424524 11.4424 0.628728C11.9163 0.829328 12.3739 1.07738 12.8016 1.36626C13.225 1.65275 13.6232 1.98128 13.9853 2.34284C14.3469 2.705 14.6754 3.10319 14.9619 3.52662C15.2507 3.95424 15.4988 4.4119 15.6994 4.88577C15.9036 5.36865 16.0603 5.87436 16.1654 6.38787C16.2735 6.9152 16.3282 7.45754 16.3282 7.99988ZM15.3204 7.99988C15.3204 4.14403 12.1842 1.00771 8.3284 1.00771C4.47265 1.00771 1.33639 4.14403 1.33639 7.99988C1.33639 9.8323 2.04448 11.5026 3.20181 12.7506C3.53273 10.9128 4.6252 9.65993 6.09183 9.05153C6.29843 8.96624 6.53386 8.98306 6.72484 9.09897C6.85157 9.17585 6.98429 9.24372 7.12243 9.30198C7.5044 9.46354 7.9104 9.54522 8.3284 9.54522C8.74641 9.54522 9.15241 9.46354 9.53438 9.30198C9.67251 9.24372 9.80524 9.17585 9.93196 9.09897C10.123 8.98306 10.3584 8.96624 10.565 9.05153C12.0316 9.65993 13.1241 10.9128 13.455 12.7506C14.6123 11.5026 15.3204 9.8323 15.3204 7.99988ZM11.5211 5.72361C11.5211 7.48697 10.0917 8.91639 8.3284 8.91639C6.56509 8.91639 5.13569 7.48697 5.13569 5.72361C5.13569 3.96025 6.56509 2.53082 8.3284 2.53082C10.0917 2.53082 11.5211 3.96025 11.5211 5.72361Z" stroke="#2C9EFF" stroke-width="2" mask="url(#path-3-inside-2_45_7568)"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-190 -273.992)"/></clipPath></defs>'},"report-pageviews":{viewBox:"0 0 21 11",content:'<rect width="21" height="11" fill="#4F4F4F"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-400 -279.992)" fill="#F3F6FF"/><rect width="158" height="3039" transform="translate(-400 -279.992)" fill="#23282D"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14 -19.2244H197.49V55.417H-14V-19.2244Z"/></mask><path d="M-14 -19.2244H197.49V55.417H-14V-19.2244Z" fill="white"/><path d="M-14 -19.2244V-20.2244H-15V-19.2244H-14ZM197.49 -19.2244H198.49V-20.2244H197.49V-19.2244ZM197.49 55.417V56.417H198.49V55.417H197.49ZM-14 55.417H-15V56.417H-14V55.417ZM-14 -18.2244H197.49V-20.2244H-14V-18.2244ZM196.49 -19.2244V55.417H198.49V-19.2244H196.49ZM197.49 54.417H-14V56.417H197.49V54.417ZM-13 55.417V-19.2244H-15V55.417H-13Z" fill="#D6E2ED" mask="url(#path-1-inside-1_45_7568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M6.82422 8.20993C6.93865 8.37876 7.06426 8.54138 7.19941 8.69532C5.38222 8.06085 3.61201 6.97263 2.05118 5.50514C3.612 4.03766 5.38219 2.94944 7.19937 2.31497C7.06424 2.4689 6.93864 2.6315 6.82422 2.80031C6.64862 3.06088 6.49851 3.33561 6.37672 3.62167C6.12465 4.21928 5.9972 4.8537 5.9972 5.50512C5.9972 6.15654 6.12465 6.79097 6.37672 7.38858C6.49851 7.67464 6.64862 7.94937 6.82422 8.20993ZM19.6061 5.50514C18.0479 6.97011 16.2811 8.05711 14.4672 8.69204C14.6013 8.53907 14.7259 8.37757 14.8395 8.20993C15.0151 7.94937 15.1652 7.67464 15.287 7.38858C15.5391 6.79097 15.6665 6.15654 15.6665 5.50512C15.6665 4.8537 15.5391 4.21928 15.287 3.62167C15.1652 3.33561 15.0151 3.06088 14.8395 2.80031C14.7259 2.6327 14.6013 2.4712 14.4672 2.31825C16.2811 2.95318 18.0479 4.04018 19.6061 5.50514ZM14.6639 5.50512C14.6639 3.39878 12.9553 1.68369 10.8514 1.67312L10.8286 1.6731L10.816 1.67311C8.71042 1.68167 6.99982 3.39754 6.99982 5.50512C6.99982 7.61799 8.719 9.33717 10.8319 9.33717C12.9447 9.33717 14.6639 7.61799 14.6639 5.50512ZM13.5079 0.990522C12.6217 0.780177 11.7271 0.672584 10.8544 0.670503L10.8319 0.670452L10.8123 0.670491C9.93662 0.671812 9.03877 0.779415 8.1493 0.990522C7.28263 1.19728 6.41313 1.50316 5.56345 1.89968C4.73643 2.28487 3.92357 2.76069 3.14753 3.31298C2.38382 3.85323 1.65401 4.47258 0.977845 5.14845L0.975183 5.1511C0.877467 5.24881 0.828611 5.37698 0.828613 5.50514C0.828611 5.6333 0.877467 5.76147 0.975183 5.85918L0.977877 5.86185C1.65403 6.53772 2.38383 7.15706 3.14753 7.6973C3.92357 8.24959 4.73643 8.72541 5.56345 9.1106C6.41313 9.50711 7.28263 9.813 8.1493 10.0198C9.0443 10.2322 9.94779 10.3398 10.8286 10.3398C11.7095 10.3398 12.6129 10.2322 13.5079 10.0198C14.3746 9.813 15.2441 9.50711 16.0938 9.1106C16.9208 8.72541 17.7337 8.24959 18.5097 7.6973C19.2734 7.15707 20.0032 6.53775 20.6793 5.86191L20.6821 5.85918C20.7065 5.83475 20.7279 5.80842 20.7462 5.78066C20.8744 5.58635 20.8531 5.32211 20.6821 5.15112L20.6794 5.14845C20.0032 4.47258 19.2734 3.85323 18.5097 3.31298C17.7337 2.76069 16.9208 2.28487 16.0938 1.89968C15.2441 1.50316 14.3746 1.19728 13.5079 0.990522ZM12.5834 3.75192C12.8128 3.9785 12.9913 4.24474 13.1159 4.53929C13.2462 4.84518 13.3113 5.17089 13.3113 5.50509C13.3113 5.8393 13.2462 6.16501 13.1159 6.47089C12.9913 6.76545 12.8128 7.03168 12.5834 7.25826C12.3568 7.48768 12.0906 7.66611 11.796 7.79073C11.4902 7.92101 11.1644 7.98615 10.8302 7.98615C10.496 7.98615 10.1703 7.92101 9.86443 7.79073C9.56988 7.66611 9.30365 7.48768 9.07706 7.25826C8.84765 7.03168 8.66922 6.76545 8.5446 6.47089C8.41431 6.16501 8.34917 5.8393 8.34917 5.50509C8.34917 5.21904 8.39732 4.94147 8.49362 4.67524C8.52194 4.76587 8.56726 4.85084 8.63523 4.92165C8.93545 5.23603 9.51606 5.17372 9.93241 4.7772C10.3487 4.38352 10.4422 3.80574 10.1448 3.49136C10.0315 3.3724 9.87859 3.30726 9.70866 3.2931C9.75964 3.26666 9.81156 3.24212 9.86443 3.21946C10.1703 3.08917 10.496 3.02403 10.8302 3.02403C11.1644 3.02403 11.4902 3.08917 11.796 3.21946C12.0906 3.34408 12.3568 3.52251 12.5834 3.75192Z" fill="#8EA4B4"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-400 -279.992)"/></clipPath></defs>'},"report-pageviews-active":{viewBox:"0 0 21 11",content:'<rect width="21" height="11" fill="#4F4F4F"/><g clip-path="url(#clip0_45_7568)"><rect width="1726" height="3039" transform="translate(-400 -279.992)" fill="#F3F6FF"/><rect width="158" height="3039" transform="translate(-400 -279.992)" fill="#23282D"/><mask id="path-1-inside-1_45_7568" fill="white"><path d="M-14 -19.2244H197.49V55.417H-14V-19.2244Z"/></mask><path d="M-14 -19.2244H197.49V55.417H-14V-19.2244Z" fill="white"/><path d="M-14 -19.2244V-20.2244H-15V-19.2244H-14ZM197.49 -19.2244H198.49V-20.2244H197.49V-19.2244ZM197.49 55.417V56.417H198.49V55.417H197.49ZM-14 55.417H-15V56.417H-14V55.417ZM-14 -18.2244H197.49V-20.2244H-14V-18.2244ZM196.49 -19.2244V55.417H198.49V-19.2244H196.49ZM197.49 54.417H-14V56.417H197.49V54.417ZM-13 55.417V-19.2244H-15V55.417H-13Z" fill="#D6E2ED" mask="url(#path-1-inside-1_45_7568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M6.82422 8.20993C6.93865 8.37876 7.06426 8.54138 7.19941 8.69532C5.38222 8.06085 3.61201 6.97263 2.05118 5.50514C3.612 4.03766 5.38219 2.94944 7.19937 2.31497C7.06424 2.4689 6.93864 2.6315 6.82422 2.80031C6.64862 3.06088 6.49851 3.33561 6.37672 3.62167C6.12465 4.21928 5.9972 4.8537 5.9972 5.50512C5.9972 6.15654 6.12465 6.79097 6.37672 7.38858C6.49851 7.67464 6.64862 7.94937 6.82422 8.20993ZM19.6061 5.50514C18.0479 6.97011 16.2811 8.05711 14.4672 8.69204C14.6013 8.53907 14.7259 8.37757 14.8395 8.20993C15.0151 7.94937 15.1652 7.67464 15.287 7.38858C15.5391 6.79097 15.6665 6.15654 15.6665 5.50512C15.6665 4.8537 15.5391 4.21928 15.287 3.62167C15.1652 3.33561 15.0151 3.06088 14.8395 2.80031C14.7259 2.6327 14.6013 2.4712 14.4672 2.31825C16.2811 2.95318 18.0479 4.04018 19.6061 5.50514ZM14.6639 5.50512C14.6639 3.39878 12.9553 1.68369 10.8514 1.67312L10.8286 1.6731L10.816 1.67311C8.71042 1.68167 6.99982 3.39754 6.99982 5.50512C6.99982 7.61799 8.719 9.33717 10.8319 9.33717C12.9447 9.33717 14.6639 7.61799 14.6639 5.50512ZM13.5079 0.990522C12.6217 0.780177 11.7271 0.672584 10.8544 0.670503L10.8319 0.670452L10.8123 0.670491C9.93662 0.671812 9.03877 0.779415 8.1493 0.990522C7.28263 1.19728 6.41313 1.50316 5.56345 1.89968C4.73643 2.28487 3.92357 2.76069 3.14753 3.31298C2.38382 3.85323 1.65401 4.47258 0.977845 5.14845L0.975183 5.1511C0.877467 5.24881 0.828611 5.37698 0.828613 5.50514C0.828611 5.6333 0.877467 5.76147 0.975183 5.85918L0.977877 5.86185C1.65403 6.53772 2.38383 7.15706 3.14753 7.6973C3.92357 8.24959 4.73643 8.72541 5.56345 9.1106C6.41313 9.50711 7.28263 9.813 8.1493 10.0198C9.0443 10.2322 9.94779 10.3398 10.8286 10.3398C11.7095 10.3398 12.6129 10.2322 13.5079 10.0198C14.3746 9.813 15.2441 9.50711 16.0938 9.1106C16.9208 8.72541 17.7337 8.24959 18.5097 7.6973C19.2734 7.15707 20.0032 6.53775 20.6793 5.86191L20.6821 5.85918C20.7065 5.83475 20.7279 5.80842 20.7462 5.78066C20.8744 5.58635 20.8531 5.32211 20.6821 5.15112L20.6794 5.14845C20.0032 4.47258 19.2734 3.85323 18.5097 3.31298C17.7337 2.76069 16.9208 2.28487 16.0938 1.89968C15.2441 1.50316 14.3746 1.19728 13.5079 0.990522ZM12.5834 3.75192C12.8128 3.9785 12.9913 4.24474 13.1159 4.53929C13.2462 4.84518 13.3113 5.17089 13.3113 5.50509C13.3113 5.8393 13.2462 6.16501 13.1159 6.47089C12.9913 6.76545 12.8128 7.03168 12.5834 7.25826C12.3568 7.48768 12.0906 7.66611 11.796 7.79073C11.4902 7.92101 11.1644 7.98615 10.8302 7.98615C10.496 7.98615 10.1703 7.92101 9.86443 7.79073C9.56988 7.66611 9.30365 7.48768 9.07706 7.25826C8.84765 7.03168 8.66922 6.76545 8.5446 6.47089C8.41431 6.16501 8.34917 5.8393 8.34917 5.50509C8.34917 5.21904 8.39732 4.94147 8.49362 4.67524C8.52194 4.76587 8.56726 4.85084 8.63523 4.92165C8.93545 5.23603 9.51606 5.17372 9.93241 4.7772C10.3487 4.38352 10.4422 3.80574 10.1448 3.49136C10.0315 3.3724 9.87859 3.30726 9.70866 3.2931C9.75964 3.26666 9.81156 3.24212 9.86443 3.21946C10.1703 3.08917 10.496 3.02403 10.8302 3.02403C11.1644 3.02403 11.4902 3.08917 11.796 3.21946C12.0906 3.34408 12.3568 3.52251 12.5834 3.75192Z" fill="#2C9EFF"/></g><defs><clipPath id="clip0_45_7568"><rect width="1726" height="3039" fill="white" transform="translate(-400 -279.992)"/></clipPath></defs>'},"report-totalusers":{viewBox:"0 0 21 11",content:'<path d="M1.11963 14.8276V13.2987C1.11963 12.4877 1.4418 11.7099 2.01527 11.1364C2.58874 10.5629 3.36654 10.2408 4.17755 10.2408H7.23547C8.04648 10.2408 8.82427 10.5629 9.39774 11.1364C9.97121 11.7099 10.2934 12.4877 10.2934 13.2987V14.8276M11.0579 1.16639C11.7156 1.33481 12.2986 1.71735 12.715 2.25372C13.1313 2.79008 13.3573 3.44976 13.3573 4.12875C13.3573 4.80774 13.1313 5.46741 12.715 6.00378C12.2986 6.54015 11.7156 6.92269 11.0579 7.09111M14.8803 14.8276V13.2987C14.8764 12.6238 14.6493 11.9691 14.2345 11.4367C13.8196 10.9043 13.2403 10.5242 12.5868 10.3554M2.64859 4.12493C2.64859 4.93594 2.97076 5.71373 3.54423 6.2872C4.1177 6.86067 4.8955 7.18285 5.70651 7.18285C6.51752 7.18285 7.29531 6.86067 7.86878 6.2872C8.44225 5.71373 8.76443 4.93594 8.76443 4.12493C8.76443 3.31392 8.44225 2.53612 7.86878 1.96265C7.29531 1.38918 6.51752 1.06701 5.70651 1.06701C4.8955 1.06701 4.1177 1.38918 3.54423 1.96265C2.97076 2.53612 2.64859 3.31392 2.64859 4.12493Z" stroke="#8EA4B4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-totalusers-active":{viewBox:"0 0 21 11",content:'<path d="M1.11963 14.8276V13.2987C1.11963 12.4877 1.4418 11.7099 2.01527 11.1364C2.58874 10.5629 3.36654 10.2408 4.17755 10.2408H7.23547C8.04648 10.2408 8.82427 10.5629 9.39774 11.1364C9.97121 11.7099 10.2934 12.4877 10.2934 13.2987V14.8276M11.0579 1.16639C11.7156 1.33481 12.2986 1.71735 12.715 2.25372C13.1313 2.79008 13.3573 3.44976 13.3573 4.12875C13.3573 4.80774 13.1313 5.46741 12.715 6.00378C12.2986 6.54015 11.7156 6.92269 11.0579 7.09111M14.8803 14.8276V13.2987C14.8764 12.6238 14.6493 11.9691 14.2345 11.4367C13.8196 10.9043 13.2403 10.5242 12.5868 10.3554M2.64859 4.12493C2.64859 4.93594 2.97076 5.71373 3.54423 6.2872C4.1177 6.86067 4.8955 7.18285 5.70651 7.18285C6.51752 7.18285 7.29531 6.86067 7.86878 6.2872C8.44225 5.71373 8.76443 4.93594 8.76443 4.12493C8.76443 3.31392 8.44225 2.53612 7.86878 1.96265C7.29531 1.38918 6.51752 1.06701 5.70651 1.06701C4.8955 1.06701 4.1177 1.38918 3.54423 1.96265C2.97076 2.53612 2.64859 3.31392 2.64859 4.12493Z" stroke="#2C9EFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-pageviews_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_266)"><path d="M4.8 13.255H4.26667C4.26667 13.3964 4.32286 13.5321 4.42288 13.6321C4.5229 13.7321 4.65855 13.7883 4.8 13.7883V13.255ZM11.2 13.255V13.7883C11.3414 13.7883 11.4771 13.7321 11.5771 13.6321C11.6771 13.5321 11.7333 13.3964 11.7333 13.255H11.2ZM4.8 13.7883H11.2V12.7217H4.8V13.7883ZM11.7333 13.255V11.1217H10.6667V13.255H11.7333ZM4.26667 11.1217V13.255H5.33333V11.1217H4.26667ZM8 7.38832C7.00986 7.38832 6.06027 7.78165 5.36014 8.48179C4.66 9.18192 4.26667 10.1315 4.26667 11.1217H5.33333C5.33333 10.4144 5.61428 9.73613 6.11438 9.23604C6.61448 8.73594 7.29276 8.45499 8 8.45499V7.38832ZM11.7333 11.1217C11.7333 10.1315 11.34 9.18192 10.6399 8.48179C9.93973 7.78165 8.99014 7.38832 8 7.38832V8.45499C8.70724 8.45499 9.38552 8.73594 9.88562 9.23604C10.3857 9.73613 10.6667 10.4144 10.6667 11.1217H11.7333ZM14.9333 13.255C14.9333 13.5163 14.8117 13.8097 14.5003 14.1254C14.1867 14.4443 13.7056 14.7547 13.0699 15.0278C11.7995 15.5718 10.0075 15.9217 8 15.9217V16.9883C10.1163 16.9883 12.0576 16.6214 13.4901 16.007C14.2048 15.7009 14.8181 15.3222 15.2597 14.8753C15.7035 14.4251 16 13.8769 16 13.255H14.9333ZM8 15.9217C5.99253 15.9217 4.20053 15.5718 2.93013 15.0278C2.2944 14.7547 1.81333 14.4443 1.49973 14.1254C1.18827 13.8097 1.06667 13.5185 1.06667 13.255H0C0 13.8769 0.296533 14.4251 0.740267 14.8753C1.18187 15.3233 1.7952 15.7009 2.51093 16.0081C3.94133 16.6214 5.88267 16.9883 8 16.9883V15.9217ZM1.06667 13.255C1.06667 12.9969 1.184 12.7078 1.48693 12.3963C1.792 12.0827 2.26027 11.7755 2.88 11.5035L2.45333 10.5265C1.7536 10.8326 1.15413 11.2091 0.7232 11.6529C0.288 12.0987 0 12.6417 0 13.255H1.06667ZM13.12 11.5035C13.7397 11.7755 14.208 12.0827 14.512 12.3963C14.8149 12.7078 14.9333 12.9969 14.9333 13.255H16C16 12.6417 15.712 12.0987 15.2768 11.6529C14.8459 11.2091 14.2464 10.8326 13.5467 10.5265L13.12 11.5035ZM8 5.25499C7.57565 5.25499 7.16869 5.08642 6.86863 4.78636C6.56857 4.4863 6.4 4.07933 6.4 3.65499H5.33333C5.33333 4.36223 5.61428 5.04051 6.11438 5.5406C6.61448 6.0407 7.29276 6.32165 8 6.32165V5.25499ZM9.6 3.65499C9.6 4.07933 9.43143 4.4863 9.13137 4.78636C8.83131 5.08642 8.42435 5.25499 8 5.25499V6.32165C8.70724 6.32165 9.38552 6.0407 9.88562 5.5406C10.3857 5.04051 10.6667 4.36223 10.6667 3.65499H9.6ZM8 2.05499C8.42435 2.05499 8.83131 2.22356 9.13137 2.52362C9.43143 2.82367 9.6 3.23064 9.6 3.65499H10.6667C10.6667 2.94774 10.3857 2.26947 9.88562 1.76937C9.38552 1.26927 8.70724 0.988319 8 0.988319V2.05499ZM8 0.988319C7.29276 0.988319 6.61448 1.26927 6.11438 1.76937C5.61428 2.26947 5.33333 2.94774 5.33333 3.65499H6.4C6.4 3.23064 6.56857 2.82367 6.86863 2.52362C7.16869 2.22356 7.57565 2.05499 8 2.05499V0.988319Z" fill="#8EA4B4"/></g><defs><clipPath id="clip0_51_266"><rect width="16" height="16" fill="white" transform="translate(0 0.988319)"/></clipPath></defs>'},"report-pageviews_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_266)"><path d="M4.8 13.255H4.26667C4.26667 13.3964 4.32286 13.5321 4.42288 13.6321C4.5229 13.7321 4.65855 13.7883 4.8 13.7883V13.255ZM11.2 13.255V13.7883C11.3414 13.7883 11.4771 13.7321 11.5771 13.6321C11.6771 13.5321 11.7333 13.3964 11.7333 13.255H11.2ZM4.8 13.7883H11.2V12.7217H4.8V13.7883ZM11.7333 13.255V11.1217H10.6667V13.255H11.7333ZM4.26667 11.1217V13.255H5.33333V11.1217H4.26667ZM8 7.38832C7.00986 7.38832 6.06027 7.78165 5.36014 8.48179C4.66 9.18192 4.26667 10.1315 4.26667 11.1217H5.33333C5.33333 10.4144 5.61428 9.73613 6.11438 9.23604C6.61448 8.73594 7.29276 8.45499 8 8.45499V7.38832ZM11.7333 11.1217C11.7333 10.1315 11.34 9.18192 10.6399 8.48179C9.93973 7.78165 8.99014 7.38832 8 7.38832V8.45499C8.70724 8.45499 9.38552 8.73594 9.88562 9.23604C10.3857 9.73613 10.6667 10.4144 10.6667 11.1217H11.7333ZM14.9333 13.255C14.9333 13.5163 14.8117 13.8097 14.5003 14.1254C14.1867 14.4443 13.7056 14.7547 13.0699 15.0278C11.7995 15.5718 10.0075 15.9217 8 15.9217V16.9883C10.1163 16.9883 12.0576 16.6214 13.4901 16.007C14.2048 15.7009 14.8181 15.3222 15.2597 14.8753C15.7035 14.4251 16 13.8769 16 13.255H14.9333ZM8 15.9217C5.99253 15.9217 4.20053 15.5718 2.93013 15.0278C2.2944 14.7547 1.81333 14.4443 1.49973 14.1254C1.18827 13.8097 1.06667 13.5185 1.06667 13.255H0C0 13.8769 0.296533 14.4251 0.740267 14.8753C1.18187 15.3233 1.7952 15.7009 2.51093 16.0081C3.94133 16.6214 5.88267 16.9883 8 16.9883V15.9217ZM1.06667 13.255C1.06667 12.9969 1.184 12.7078 1.48693 12.3963C1.792 12.0827 2.26027 11.7755 2.88 11.5035L2.45333 10.5265C1.7536 10.8326 1.15413 11.2091 0.7232 11.6529C0.288 12.0987 0 12.6417 0 13.255H1.06667ZM13.12 11.5035C13.7397 11.7755 14.208 12.0827 14.512 12.3963C14.8149 12.7078 14.9333 12.9969 14.9333 13.255H16C16 12.6417 15.712 12.0987 15.2768 11.6529C14.8459 11.2091 14.2464 10.8326 13.5467 10.5265L13.12 11.5035ZM8 5.25499C7.57565 5.25499 7.16869 5.08642 6.86863 4.78636C6.56857 4.4863 6.4 4.07933 6.4 3.65499H5.33333C5.33333 4.36223 5.61428 5.04051 6.11438 5.5406C6.61448 6.0407 7.29276 6.32165 8 6.32165V5.25499ZM9.6 3.65499C9.6 4.07933 9.43143 4.4863 9.13137 4.78636C8.83131 5.08642 8.42435 5.25499 8 5.25499V6.32165C8.70724 6.32165 9.38552 6.0407 9.88562 5.5406C10.3857 5.04051 10.6667 4.36223 10.6667 3.65499H9.6ZM8 2.05499C8.42435 2.05499 8.83131 2.22356 9.13137 2.52362C9.43143 2.82367 9.6 3.23064 9.6 3.65499H10.6667C10.6667 2.94774 10.3857 2.26947 9.88562 1.76937C9.38552 1.26927 8.70724 0.988319 8 0.988319V2.05499ZM8 0.988319C7.29276 0.988319 6.61448 1.26927 6.11438 1.76937C5.61428 2.26947 5.33333 2.94774 5.33333 3.65499H6.4C6.4 3.23064 6.56857 2.82367 6.86863 2.52362C7.16869 2.22356 7.57565 2.05499 8 2.05499V0.988319Z" fill="#2C9EFF"/></g><defs><clipPath id="clip0_51_266"><rect width="16" height="16" fill="white" transform="translate(0 0.988319)"/></clipPath></defs>'},"report-average_session_duration":{viewBox:"0 0 16 16",content:'<g clip-path="url(#clip0_51_268)"><g clip-path="url(#clip1_51_268)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.14997 1.20821C7.19365 1.40112 7.15892 1.60348 7.05344 1.77079C6.94796 1.93811 6.78035 2.05669 6.58747 2.10046C6.44107 2.13377 6.29599 2.17259 6.15253 2.21685C6.05892 2.24575 5.96053 2.25593 5.86299 2.2468C5.76544 2.23768 5.67065 2.20944 5.58402 2.16368C5.49739 2.11792 5.42062 2.05555 5.3581 1.98013C5.29557 1.9047 5.24852 1.8177 5.21962 1.72409C5.19071 1.63048 5.18053 1.53209 5.18966 1.43455C5.19878 1.337 5.22702 1.24221 5.27278 1.15558C5.31854 1.06895 5.38091 0.992185 5.45633 0.92966C5.53176 0.867136 5.61876 0.82008 5.71237 0.791178C5.89142 0.735475 6.07295 0.686982 6.25697 0.645702C6.35255 0.623968 6.45146 0.621279 6.54807 0.637789C6.64469 0.654299 6.7371 0.689684 6.82003 0.741923C6.90296 0.794162 6.97478 0.86223 7.0314 0.942237C7.08801 1.02224 7.1283 1.11262 7.14997 1.20821ZM8.67934 1.20821C8.72311 1.01532 8.84169 0.847716 9.00901 0.742234C9.17633 0.636752 9.37868 0.60203 9.57159 0.645702C12.8944 1.39919 15.3757 4.36989 15.3757 7.921C15.3757 12.0413 12.035 15.3813 7.9154 15.3813C4.36355 15.3813 1.39285 12.9008 0.639354 9.57794C0.600679 9.38694 0.638274 9.18839 0.744098 9.02476C0.849923 8.86112 1.01558 8.74538 1.20563 8.70231C1.39568 8.65923 1.59505 8.69222 1.76109 8.79424C1.92713 8.89625 2.04666 9.05919 2.09412 9.24819C2.33151 10.288 2.84321 11.2452 3.57596 12.0201C4.30871 12.7951 5.23574 13.3596 6.26061 13.6548C7.28548 13.95 8.37074 13.9652 9.40347 13.6988C10.4362 13.4324 11.3787 12.8941 12.1328 12.1399C12.887 11.3858 13.4253 10.4433 13.6917 9.41057C13.9581 8.37783 13.9429 7.29258 13.6477 6.26771C13.3525 5.24284 12.788 4.3158 12.013 3.58305C11.2381 2.8503 10.2809 2.33861 9.2411 2.10121C9.04819 2.05728 8.88063 1.93851 8.77528 1.77105C8.66994 1.60358 8.63542 1.40112 8.67934 1.20821ZM3.89653 2.48989C3.96325 2.56173 4.01515 2.64601 4.04928 2.73792C4.0834 2.82983 4.09908 2.92756 4.0954 3.02553C4.09173 3.1235 4.06879 3.21979 4.02788 3.30889C3.98697 3.39798 3.9289 3.47814 3.85699 3.54478C3.74658 3.64674 3.64039 3.75268 3.53843 3.86259C3.40291 4.00361 3.21743 4.08576 3.02193 4.09137C2.82643 4.09698 2.63654 4.02559 2.49316 3.89257C2.34978 3.75955 2.26436 3.57555 2.25531 3.38018C2.24625 3.18481 2.31428 2.99369 2.44475 2.84799C2.57158 2.71072 2.70387 2.57817 2.84164 2.45035C2.91348 2.38363 2.99776 2.33173 3.08967 2.2976C3.18158 2.26348 3.27931 2.24781 3.37728 2.25148C3.47525 2.25515 3.57154 2.2781 3.66063 2.31901C3.74973 2.35991 3.82989 2.41798 3.89653 2.48989ZM7.91466 3.44481C8.11252 3.44481 8.30227 3.52341 8.44218 3.66332C8.58209 3.80323 8.66069 3.99299 8.66069 4.19085V7.61215L10.6802 9.63165C10.8161 9.77236 10.8913 9.96081 10.8896 10.1564C10.8879 10.352 10.8094 10.5391 10.6711 10.6775C10.5328 10.8158 10.3457 10.8942 10.1501 10.8959C9.95446 10.8976 9.76601 10.8224 9.62531 10.6865L7.38721 8.44845C7.24729 8.30857 7.16867 8.11885 7.16862 7.921V4.19085C7.16862 3.99299 7.24722 3.80323 7.38713 3.66332C7.52704 3.52341 7.7168 3.44481 7.91466 3.44481ZM1.71737 5.22634C1.90641 5.28463 2.06455 5.41562 2.15703 5.5905C2.2495 5.76538 2.26874 5.96982 2.2105 6.15888C2.16625 6.30234 2.12743 6.44742 2.09412 6.59381C2.04666 6.78282 1.92713 6.94575 1.76109 7.04777C1.59505 7.14978 1.39568 7.18278 1.20563 7.1397C1.01558 7.09662 0.849923 6.98089 0.744098 6.81725C0.638274 6.65361 0.600679 6.45507 0.639354 6.26407C0.681132 6.08005 0.729624 5.89851 0.784831 5.71946C0.843125 5.53043 0.974114 5.37228 1.14899 5.2798C1.32387 5.18733 1.52832 5.1681 1.71737 5.22634Z" fill="#8EA4B4"/></g></g><defs><clipPath id="clip0_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath><clipPath id="clip1_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath></defs>'},"report-average_session_duration-active":{viewBox:"0 0 16 16",content:'<g clip-path="url(#clip0_51_268)"><g clip-path="url(#clip1_51_268)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.14997 1.20821C7.19365 1.40112 7.15892 1.60348 7.05344 1.77079C6.94796 1.93811 6.78035 2.05669 6.58747 2.10046C6.44107 2.13377 6.29599 2.17259 6.15253 2.21685C6.05892 2.24575 5.96053 2.25593 5.86299 2.2468C5.76544 2.23768 5.67065 2.20944 5.58402 2.16368C5.49739 2.11792 5.42062 2.05555 5.3581 1.98013C5.29557 1.9047 5.24852 1.8177 5.21962 1.72409C5.19071 1.63048 5.18053 1.53209 5.18966 1.43455C5.19878 1.337 5.22702 1.24221 5.27278 1.15558C5.31854 1.06895 5.38091 0.992185 5.45633 0.92966C5.53176 0.867136 5.61876 0.82008 5.71237 0.791178C5.89142 0.735475 6.07295 0.686982 6.25697 0.645702C6.35255 0.623968 6.45146 0.621279 6.54807 0.637789C6.64469 0.654299 6.7371 0.689684 6.82003 0.741923C6.90296 0.794162 6.97478 0.86223 7.0314 0.942237C7.08801 1.02224 7.1283 1.11262 7.14997 1.20821ZM8.67934 1.20821C8.72311 1.01532 8.84169 0.847716 9.00901 0.742234C9.17633 0.636752 9.37868 0.60203 9.57159 0.645702C12.8944 1.39919 15.3757 4.36989 15.3757 7.921C15.3757 12.0413 12.035 15.3813 7.9154 15.3813C4.36355 15.3813 1.39285 12.9008 0.639354 9.57794C0.600679 9.38694 0.638274 9.18839 0.744098 9.02476C0.849923 8.86112 1.01558 8.74538 1.20563 8.70231C1.39568 8.65923 1.59505 8.69222 1.76109 8.79424C1.92713 8.89625 2.04666 9.05919 2.09412 9.24819C2.33151 10.288 2.84321 11.2452 3.57596 12.0201C4.30871 12.7951 5.23574 13.3596 6.26061 13.6548C7.28548 13.95 8.37074 13.9652 9.40347 13.6988C10.4362 13.4324 11.3787 12.8941 12.1328 12.1399C12.887 11.3858 13.4253 10.4433 13.6917 9.41057C13.9581 8.37783 13.9429 7.29258 13.6477 6.26771C13.3525 5.24284 12.788 4.3158 12.013 3.58305C11.2381 2.8503 10.2809 2.33861 9.2411 2.10121C9.04819 2.05728 8.88063 1.93851 8.77528 1.77105C8.66994 1.60358 8.63542 1.40112 8.67934 1.20821ZM3.89653 2.48989C3.96325 2.56173 4.01515 2.64601 4.04928 2.73792C4.0834 2.82983 4.09908 2.92756 4.0954 3.02553C4.09173 3.1235 4.06879 3.21979 4.02788 3.30889C3.98697 3.39798 3.9289 3.47814 3.85699 3.54478C3.74658 3.64674 3.64039 3.75268 3.53843 3.86259C3.40291 4.00361 3.21743 4.08576 3.02193 4.09137C2.82643 4.09698 2.63654 4.02559 2.49316 3.89257C2.34978 3.75955 2.26436 3.57555 2.25531 3.38018C2.24625 3.18481 2.31428 2.99369 2.44475 2.84799C2.57158 2.71072 2.70387 2.57817 2.84164 2.45035C2.91348 2.38363 2.99776 2.33173 3.08967 2.2976C3.18158 2.26348 3.27931 2.24781 3.37728 2.25148C3.47525 2.25515 3.57154 2.2781 3.66063 2.31901C3.74973 2.35991 3.82989 2.41798 3.89653 2.48989ZM7.91466 3.44481C8.11252 3.44481 8.30227 3.52341 8.44218 3.66332C8.58209 3.80323 8.66069 3.99299 8.66069 4.19085V7.61215L10.6802 9.63165C10.8161 9.77236 10.8913 9.96081 10.8896 10.1564C10.8879 10.352 10.8094 10.5391 10.6711 10.6775C10.5328 10.8158 10.3457 10.8942 10.1501 10.8959C9.95446 10.8976 9.76601 10.8224 9.62531 10.6865L7.38721 8.44845C7.24729 8.30857 7.16867 8.11885 7.16862 7.921V4.19085C7.16862 3.99299 7.24722 3.80323 7.38713 3.66332C7.52704 3.52341 7.7168 3.44481 7.91466 3.44481ZM1.71737 5.22634C1.90641 5.28463 2.06455 5.41562 2.15703 5.5905C2.2495 5.76538 2.26874 5.96982 2.2105 6.15888C2.16625 6.30234 2.12743 6.44742 2.09412 6.59381C2.04666 6.78282 1.92713 6.94575 1.76109 7.04777C1.59505 7.14978 1.39568 7.18278 1.20563 7.1397C1.01558 7.09662 0.849923 6.98089 0.744098 6.81725C0.638274 6.65361 0.600679 6.45507 0.639354 6.26407C0.681132 6.08005 0.729624 5.89851 0.784831 5.71946C0.843125 5.53043 0.974114 5.37228 1.14899 5.2798C1.32387 5.18733 1.52832 5.1681 1.71737 5.22634Z" fill="#2C9EFF"/></g></g><defs><clipPath id="clip0_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath><clipPath id="clip1_51_268"><rect width="16" height="16" fill="white" transform="translate(0 0.00422668)"/></clipPath></defs>'},"report-bounce_rate":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6103)"><g clip-path="url(#clip1_65_6103)"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4811 4.48853C11.5431 4.42892 11.6162 4.3821 11.6963 4.35075C11.7763 4.31941 11.8618 4.30415 11.9477 4.30585C12.0337 4.30755 12.1185 4.32618 12.1972 4.36067C12.276 4.39516 12.3472 4.44483 12.4067 4.50685L15.4513 7.67965C15.5681 7.80146 15.6332 7.96367 15.6332 8.13241C15.6332 8.30114 15.5681 8.46335 15.4513 8.58517L12.4067 11.758C12.3472 11.8199 12.2761 11.8696 12.1974 11.9041C12.1187 11.9386 12.0339 11.9572 11.948 11.9589C11.8621 11.9607 11.7767 11.9455 11.6967 11.9142C11.6167 11.883 11.5436 11.8362 11.4816 11.7767C11.4196 11.7172 11.37 11.6461 11.3355 11.5674C11.301 11.4887 11.2823 11.4039 11.2806 11.318C11.2789 11.2321 11.2941 11.1467 11.3253 11.0667C11.3566 10.9866 11.4033 10.9136 11.4628 10.8516L13.444 8.78668H7.36619C7.19266 8.78668 7.02625 8.71775 6.90355 8.59505C6.78085 8.47235 6.71191 8.30593 6.71191 8.13241C6.71191 7.95888 6.78085 7.79246 6.90355 7.66976C7.02625 7.54706 7.19266 7.47813 7.36619 7.47813H13.4448L11.4628 5.41324C11.3428 5.28803 11.2774 5.1203 11.281 4.94691C11.2846 4.77351 11.3569 4.60864 11.482 4.48853" fill="#8EA4B4"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0.366943 1.1533C0.366943 0.979774 0.435876 0.813357 0.558576 0.690656C0.681277 0.567956 0.847694 0.499023 1.02122 0.499023H9.26945C9.44298 0.499023 9.60939 0.567956 9.73209 0.690656C9.8548 0.813357 9.92373 0.979774 9.92373 1.1533V3.7704C9.92373 3.94393 9.8548 4.11034 9.73209 4.23304C9.60939 4.35574 9.44298 4.42468 9.26945 4.42468C9.09593 4.42468 8.92951 4.35574 8.80681 4.23304C8.68411 4.11034 8.61518 3.94393 8.61518 3.7704V1.80757H1.67549V14.4569H8.6143V12.4941C8.6143 12.3205 8.68324 12.1541 8.80594 12.0314C8.92864 11.9087 9.09506 11.8398 9.26858 11.8398C9.44211 11.8398 9.60852 11.9087 9.73122 12.0314C9.85392 12.1541 9.92286 12.3205 9.92286 12.4941V15.1112C9.92286 15.2847 9.85392 15.4511 9.73122 15.5738C9.60852 15.6965 9.44211 15.7655 9.26858 15.7655H1.02122C0.847694 15.7655 0.681277 15.6965 0.558576 15.5738C0.435876 15.4511 0.366943 15.2847 0.366943 15.1112V1.1533Z" fill="#8EA4B4"/></g></g><defs><clipPath id="clip0_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath><clipPath id="clip1_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath></defs>'},"report-bounce_rate-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6103)"><g clip-path="url(#clip1_65_6103)"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4811 4.48853C11.5431 4.42892 11.6162 4.3821 11.6963 4.35075C11.7763 4.31941 11.8618 4.30415 11.9477 4.30585C12.0337 4.30755 12.1185 4.32618 12.1972 4.36067C12.276 4.39516 12.3472 4.44483 12.4067 4.50685L15.4513 7.67965C15.5681 7.80146 15.6332 7.96367 15.6332 8.13241C15.6332 8.30114 15.5681 8.46335 15.4513 8.58517L12.4067 11.758C12.3472 11.8199 12.2761 11.8696 12.1974 11.9041C12.1187 11.9386 12.0339 11.9572 11.948 11.9589C11.8621 11.9607 11.7767 11.9455 11.6967 11.9142C11.6167 11.883 11.5436 11.8362 11.4816 11.7767C11.4196 11.7172 11.37 11.6461 11.3355 11.5674C11.301 11.4887 11.2823 11.4039 11.2806 11.318C11.2789 11.2321 11.2941 11.1467 11.3253 11.0667C11.3566 10.9866 11.4033 10.9136 11.4628 10.8516L13.444 8.78668H7.36619C7.19266 8.78668 7.02625 8.71775 6.90355 8.59505C6.78085 8.47235 6.71191 8.30593 6.71191 8.13241C6.71191 7.95888 6.78085 7.79246 6.90355 7.66976C7.02625 7.54706 7.19266 7.47813 7.36619 7.47813H13.4448L11.4628 5.41324C11.3428 5.28803 11.2774 5.1203 11.281 4.94691C11.2846 4.77351 11.3569 4.60864 11.482 4.48853" fill="#8EA4B4"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0.366943 1.1533C0.366943 0.979774 0.435876 0.813357 0.558576 0.690656C0.681277 0.567956 0.847694 0.499023 1.02122 0.499023H9.26945C9.44298 0.499023 9.60939 0.567956 9.73209 0.690656C9.8548 0.813357 9.92373 0.979774 9.92373 1.1533V3.7704C9.92373 3.94393 9.8548 4.11034 9.73209 4.23304C9.60939 4.35574 9.44298 4.42468 9.26945 4.42468C9.09593 4.42468 8.92951 4.35574 8.80681 4.23304C8.68411 4.11034 8.61518 3.94393 8.61518 3.7704V1.80757H1.67549V14.4569H8.6143V12.4941C8.6143 12.3205 8.68324 12.1541 8.80594 12.0314C8.92864 11.9087 9.09506 11.8398 9.26858 11.8398C9.44211 11.8398 9.60852 11.9087 9.73122 12.0314C9.85392 12.1541 9.92286 12.3205 9.92286 12.4941V15.1112C9.92286 15.2847 9.85392 15.4511 9.73122 15.5738C9.60852 15.6965 9.44211 15.7655 9.26858 15.7655H1.02122C0.847694 15.7655 0.681277 15.6965 0.558576 15.5738C0.435876 15.4511 0.366943 15.2847 0.366943 15.1112V1.1533Z" fill="#2C9EFF"/></g></g><defs><clipPath id="clip0_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath><clipPath id="clip1_65_6103"><rect width="16" height="16" fill="white" transform="translate(0 0.132233)"/></clipPath></defs>'},"report-revenue_sales":{viewBox:"0 0 16 17",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0637 0.771255C9.48809 0.28068 8.7565 0.0112305 8.00017 0.0112305C7.24384 0.0112305 6.51225 0.28068 5.9366 0.771255L5.63754 1.02577C5.38285 1.24303 5.06611 1.37441 4.73241 1.40118L4.3403 1.433C3.58627 1.493 2.87829 1.8197 2.34335 2.35449C1.80841 2.88929 1.48152 3.59718 1.42131 4.35119L1.39029 4.74331C1.36374 5.07691 1.23265 5.39364 1.01568 5.64843L0.760363 5.94749C0.269577 6.52319 0 7.25495 0 8.01146C0 8.76797 0.269577 9.49972 0.760363 10.0754L1.01488 10.3745C1.23214 10.6292 1.36351 10.9459 1.39029 11.2796L1.42211 11.6717C1.48211 12.4258 1.80881 13.1337 2.3436 13.6687C2.87839 14.2036 3.58629 14.5305 4.3403 14.5907L4.73241 14.6217C5.06602 14.6483 5.38275 14.7794 5.63754 14.9964L5.9366 15.2509C6.5123 15.7417 7.24406 16.0112 8.00057 16.0112C8.75708 16.0112 9.48883 15.7417 10.0645 15.2509L10.3636 14.9964C10.6184 14.7794 10.9351 14.6483 11.2687 14.6217L11.6608 14.5899C12.4149 14.5299 13.1228 14.2032 13.6578 13.6684C14.1927 13.1336 14.5196 12.4257 14.5798 11.6717L14.61 11.2796C14.6368 10.9459 14.7682 10.6292 14.9855 10.3745L15.24 10.0746C15.7306 9.49898 16 8.76739 16 8.01106C16 7.25473 15.7306 6.52314 15.24 5.94749L14.9855 5.64843C14.7682 5.39374 14.6368 5.077 14.61 4.74331L14.579 4.35119C14.519 3.59716 14.1923 2.88918 13.6575 2.35424C13.1227 1.8193 12.4148 1.49241 11.6608 1.4322L11.2687 1.40118C10.9351 1.37463 10.6184 1.24354 10.3636 1.02657L10.0637 0.771255ZM6.96898 1.9826C7.25678 1.73741 7.62249 1.60275 8.00057 1.60275C8.37864 1.60275 8.74436 1.73741 9.03216 1.9826L9.33201 2.23791C9.84149 2.67214 10.475 2.9346 11.1423 2.98794L11.5344 3.01975C11.9112 3.04992 12.2649 3.21329 12.5322 3.48059C12.7995 3.74789 12.9629 4.10164 12.9931 4.47845L13.0249 4.87056C13.0782 5.53786 13.3407 6.17133 13.7749 6.68081L14.0294 6.98067C14.2746 7.26846 14.4093 7.63418 14.4093 8.01225C14.4093 8.39033 14.2746 8.75605 14.0294 9.04384L13.7749 9.34369C13.3407 9.85318 13.0782 10.4866 13.0249 11.1539L12.9931 11.5461C12.9629 11.9229 12.7995 12.2766 12.5322 12.5439C12.2649 12.8112 11.9112 12.9746 11.5344 13.0048L11.1423 13.0366C10.475 13.0899 9.84149 13.3524 9.33201 13.7866L9.03216 14.0411C8.74436 14.2863 8.37864 14.421 8.00057 14.421C7.62249 14.421 7.25678 14.2863 6.96898 14.0411L6.66913 13.7866C6.15965 13.3524 5.52617 13.0899 4.85888 13.0366L4.46676 13.0048C4.08981 12.9748 3.73587 12.8115 3.46841 12.5442C3.20095 12.2768 3.03746 11.923 3.00727 11.5461L2.97545 11.1539C2.92235 10.4867 2.66017 9.85328 2.22622 9.34369L1.9717 9.04384C1.72652 8.75605 1.59186 8.39033 1.59186 8.01225C1.59186 7.63418 1.72652 7.26846 1.9717 6.98067L2.22622 6.68081C2.66045 6.17133 2.92292 5.53786 2.97625 4.87056L3.00806 4.47845C3.03805 4.1015 3.20135 3.74756 3.46866 3.4801C3.73598 3.21264 4.08983 3.04915 4.46676 3.01896L4.85888 2.98794C5.52617 2.9346 6.15965 2.67214 6.66913 2.23791L6.96898 1.9826ZM10.949 6.18848C11.0249 6.11511 11.0855 6.02735 11.1272 5.93031C11.1689 5.83327 11.1909 5.72891 11.1918 5.6233C11.1927 5.51769 11.1726 5.41296 11.1326 5.31521C11.0926 5.21746 11.0335 5.12866 10.9588 5.05398C10.8842 4.9793 10.7954 4.92024 10.6976 4.88025C10.5999 4.84026 10.4951 4.82013 10.3895 4.82105C10.2839 4.82197 10.1795 4.84391 10.0825 4.8856C9.98547 4.92728 9.89771 4.98787 9.82434 5.06384L5.05215 9.83603C4.97619 9.9094 4.91559 9.99716 4.87391 10.0942C4.83222 10.1912 4.81028 10.2956 4.80937 10.4012C4.80845 10.5068 4.82857 10.6116 4.86856 10.7093C4.90856 10.807 4.96761 10.8959 5.04229 10.9705C5.11697 11.0452 5.20577 11.1043 5.30352 11.1443C5.40127 11.1842 5.506 11.2044 5.61161 11.2035C5.71722 11.2025 5.82159 11.1806 5.91862 11.1389C6.01566 11.0972 6.10343 11.0366 6.1768 10.9607L10.949 6.18848ZM7.2052 6.02305C7.2052 6.33946 7.07951 6.64292 6.85577 6.86666C6.63203 7.0904 6.32857 7.21609 6.01216 7.21609C5.69574 7.21609 5.39228 7.0904 5.16854 6.86666C4.9448 6.64292 4.81911 6.33946 4.81911 6.02305C4.81911 5.70663 4.9448 5.40318 5.16854 5.17944C5.39228 4.9557 5.69574 4.83 6.01216 4.83C6.32857 4.83 6.63203 4.9557 6.85577 5.17944C7.07951 5.40318 7.2052 5.70663 7.2052 6.02305ZM9.98898 11.1929C10.3054 11.1929 10.6089 11.0672 10.8326 10.8435C11.0563 10.6197 11.182 10.3163 11.182 9.99987C11.182 9.68345 11.0563 9.38 10.8326 9.15626C10.6089 8.93252 10.3054 8.80682 9.98898 8.80682C9.67256 8.80682 9.36911 8.93252 9.14537 9.15626C8.92163 9.38 8.79593 9.68345 8.79593 9.99987C8.79593 10.3163 8.92163 10.6197 9.14537 10.8435C9.36911 11.0672 9.67256 11.1929 9.98898 11.1929Z" fill="#8EA4B4"/>'},"report-revenue_sales-active":{viewBox:"0 0 16 17",content:'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0637 0.771255C9.48809 0.28068 8.7565 0.0112305 8.00017 0.0112305C7.24384 0.0112305 6.51225 0.28068 5.9366 0.771255L5.63754 1.02577C5.38285 1.24303 5.06611 1.37441 4.73241 1.40118L4.3403 1.433C3.58627 1.493 2.87829 1.8197 2.34335 2.35449C1.80841 2.88929 1.48152 3.59718 1.42131 4.35119L1.39029 4.74331C1.36374 5.07691 1.23265 5.39364 1.01568 5.64843L0.760363 5.94749C0.269577 6.52319 0 7.25495 0 8.01146C0 8.76797 0.269577 9.49972 0.760363 10.0754L1.01488 10.3745C1.23214 10.6292 1.36351 10.9459 1.39029 11.2796L1.42211 11.6717C1.48211 12.4258 1.80881 13.1337 2.3436 13.6687C2.87839 14.2036 3.58629 14.5305 4.3403 14.5907L4.73241 14.6217C5.06602 14.6483 5.38275 14.7794 5.63754 14.9964L5.9366 15.2509C6.5123 15.7417 7.24406 16.0112 8.00057 16.0112C8.75708 16.0112 9.48883 15.7417 10.0645 15.2509L10.3636 14.9964C10.6184 14.7794 10.9351 14.6483 11.2687 14.6217L11.6608 14.5899C12.4149 14.5299 13.1228 14.2032 13.6578 13.6684C14.1927 13.1336 14.5196 12.4257 14.5798 11.6717L14.61 11.2796C14.6368 10.9459 14.7682 10.6292 14.9855 10.3745L15.24 10.0746C15.7306 9.49898 16 8.76739 16 8.01106C16 7.25473 15.7306 6.52314 15.24 5.94749L14.9855 5.64843C14.7682 5.39374 14.6368 5.077 14.61 4.74331L14.579 4.35119C14.519 3.59716 14.1923 2.88918 13.6575 2.35424C13.1227 1.8193 12.4148 1.49241 11.6608 1.4322L11.2687 1.40118C10.9351 1.37463 10.6184 1.24354 10.3636 1.02657L10.0637 0.771255ZM6.96898 1.9826C7.25678 1.73741 7.62249 1.60275 8.00057 1.60275C8.37864 1.60275 8.74436 1.73741 9.03216 1.9826L9.33201 2.23791C9.84149 2.67214 10.475 2.9346 11.1423 2.98794L11.5344 3.01975C11.9112 3.04992 12.2649 3.21329 12.5322 3.48059C12.7995 3.74789 12.9629 4.10164 12.9931 4.47845L13.0249 4.87056C13.0782 5.53786 13.3407 6.17133 13.7749 6.68081L14.0294 6.98067C14.2746 7.26846 14.4093 7.63418 14.4093 8.01225C14.4093 8.39033 14.2746 8.75605 14.0294 9.04384L13.7749 9.34369C13.3407 9.85318 13.0782 10.4866 13.0249 11.1539L12.9931 11.5461C12.9629 11.9229 12.7995 12.2766 12.5322 12.5439C12.2649 12.8112 11.9112 12.9746 11.5344 13.0048L11.1423 13.0366C10.475 13.0899 9.84149 13.3524 9.33201 13.7866L9.03216 14.0411C8.74436 14.2863 8.37864 14.421 8.00057 14.421C7.62249 14.421 7.25678 14.2863 6.96898 14.0411L6.66913 13.7866C6.15965 13.3524 5.52617 13.0899 4.85888 13.0366L4.46676 13.0048C4.08981 12.9748 3.73587 12.8115 3.46841 12.5442C3.20095 12.2768 3.03746 11.923 3.00727 11.5461L2.97545 11.1539C2.92235 10.4867 2.66017 9.85328 2.22622 9.34369L1.9717 9.04384C1.72652 8.75605 1.59186 8.39033 1.59186 8.01225C1.59186 7.63418 1.72652 7.26846 1.9717 6.98067L2.22622 6.68081C2.66045 6.17133 2.92292 5.53786 2.97625 4.87056L3.00806 4.47845C3.03805 4.1015 3.20135 3.74756 3.46866 3.4801C3.73598 3.21264 4.08983 3.04915 4.46676 3.01896L4.85888 2.98794C5.52617 2.9346 6.15965 2.67214 6.66913 2.23791L6.96898 1.9826ZM10.949 6.18848C11.0249 6.11511 11.0855 6.02735 11.1272 5.93031C11.1689 5.83327 11.1909 5.72891 11.1918 5.6233C11.1927 5.51769 11.1726 5.41296 11.1326 5.31521C11.0926 5.21746 11.0335 5.12866 10.9588 5.05398C10.8842 4.9793 10.7954 4.92024 10.6976 4.88025C10.5999 4.84026 10.4951 4.82013 10.3895 4.82105C10.2839 4.82197 10.1795 4.84391 10.0825 4.8856C9.98547 4.92728 9.89771 4.98787 9.82434 5.06384L5.05215 9.83603C4.97619 9.9094 4.91559 9.99716 4.87391 10.0942C4.83222 10.1912 4.81028 10.2956 4.80937 10.4012C4.80845 10.5068 4.82857 10.6116 4.86856 10.7093C4.90856 10.807 4.96761 10.8959 5.04229 10.9705C5.11697 11.0452 5.20577 11.1043 5.30352 11.1443C5.40127 11.1842 5.506 11.2044 5.61161 11.2035C5.71722 11.2025 5.82159 11.1806 5.91862 11.1389C6.01566 11.0972 6.10343 11.0366 6.1768 10.9607L10.949 6.18848ZM7.2052 6.02305C7.2052 6.33946 7.07951 6.64292 6.85577 6.86666C6.63203 7.0904 6.32857 7.21609 6.01216 7.21609C5.69574 7.21609 5.39228 7.0904 5.16854 6.86666C4.9448 6.64292 4.81911 6.33946 4.81911 6.02305C4.81911 5.70663 4.9448 5.40318 5.16854 5.17944C5.39228 4.9557 5.69574 4.83 6.01216 4.83C6.32857 4.83 6.63203 4.9557 6.85577 5.17944C7.07951 5.40318 7.2052 5.70663 7.2052 6.02305ZM9.98898 11.1929C10.3054 11.1929 10.6089 11.0672 10.8326 10.8435C11.0563 10.6197 11.182 10.3163 11.182 9.99987C11.182 9.68345 11.0563 9.38 10.8326 9.15626C10.6089 8.93252 10.3054 8.80682 9.98898 8.80682C9.67256 8.80682 9.36911 8.93252 9.14537 9.15626C8.92163 9.38 8.79593 9.68345 8.79593 9.99987C8.79593 10.3163 8.92163 10.6197 9.14537 10.8435C9.36911 11.0672 9.67256 11.1929 9.98898 11.1929Z" fill="#2C9EFF"/>'},"report-average_revenue_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_291)"><g clip-path="url(#clip1_51_291)"><path d="M15.8183 3.22621V2.10932H14.143V0.992432H13.0261V2.10932H12.4677C11.8517 2.10932 11.3508 2.6108 11.3508 3.22621V4.3431C11.3508 4.95906 11.8517 5.45998 12.4677 5.45998H14.7014V6.57687H11.3508V7.69376H13.0261V8.81065H14.143V7.69376H14.7014C15.3174 7.69376 15.8183 7.19283 15.8183 6.57687V5.45998C15.8183 4.84458 15.3174 4.3431 14.7014 4.3431H12.4677V3.22621H15.8183ZM12.4677 11.0444V12.1613H13.9118L11.9092 14.1639L10.6293 12.8834C10.5247 12.7788 10.3829 12.7199 10.235 12.7198H10.2339C10.086 12.7199 9.94418 12.7788 9.83961 12.8834L6.88321 15.8392L7.67285 16.6289L10.2344 14.0678L11.5144 15.3483C11.6191 15.453 11.7611 15.5119 11.9092 15.5119C12.0573 15.5119 12.1993 15.453 12.304 15.3483L14.7014 12.9509V14.3951H15.8183V11.0444H12.4677ZM1.29877 16.6289H0.181885V13.8366C0.181885 11.681 1.9354 9.92753 4.09099 9.92753H7.44166C8.5524 9.92753 9.614 10.4022 10.3551 11.2304L9.52298 11.9753C9.26108 11.6825 8.94031 11.4482 8.58166 11.2878C8.22301 11.1273 7.83455 11.0444 7.44166 11.0444H4.09099C2.55136 11.0444 1.29877 12.297 1.29877 13.8366V16.6289ZM5.76632 8.81065C6.80308 8.81065 7.79738 8.3988 8.53048 7.6657C9.26358 6.9326 9.67543 5.9383 9.67543 4.90154C9.67543 3.86478 9.26358 2.87048 8.53048 2.13738C7.79738 1.40428 6.80308 0.992432 5.76632 0.992432C4.72956 0.992432 3.73527 1.40428 3.00217 2.13738C2.26907 2.87048 1.85722 3.86478 1.85722 4.90154C1.85722 5.9383 2.26907 6.9326 3.00217 7.6657C3.73527 8.3988 4.72956 8.81065 5.76632 8.81065ZM5.76632 2.10932C6.50687 2.10932 7.21708 2.4035 7.74072 2.92714C8.26436 3.45078 8.55854 4.161 8.55854 4.90154C8.55854 5.64208 8.26436 6.35229 7.74072 6.87594C7.21708 7.39958 6.50687 7.69376 5.76632 7.69376C5.02578 7.69376 4.31557 7.39958 3.79193 6.87594C3.26828 6.35229 2.9741 5.64208 2.9741 4.90154C2.9741 4.161 3.26828 3.45078 3.79193 2.92714C4.31557 2.4035 5.02578 2.10932 5.76632 2.10932Z" fill="#8EA4B4"/></g></g><defs><clipPath id="clip0_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath><clipPath id="clip1_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath></defs>'},"report-average_revenue_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_51_291)"><g clip-path="url(#clip1_51_291)"><path d="M15.8183 3.22621V2.10932H14.143V0.992432H13.0261V2.10932H12.4677C11.8517 2.10932 11.3508 2.6108 11.3508 3.22621V4.3431C11.3508 4.95906 11.8517 5.45998 12.4677 5.45998H14.7014V6.57687H11.3508V7.69376H13.0261V8.81065H14.143V7.69376H14.7014C15.3174 7.69376 15.8183 7.19283 15.8183 6.57687V5.45998C15.8183 4.84458 15.3174 4.3431 14.7014 4.3431H12.4677V3.22621H15.8183ZM12.4677 11.0444V12.1613H13.9118L11.9092 14.1639L10.6293 12.8834C10.5247 12.7788 10.3829 12.7199 10.235 12.7198H10.2339C10.086 12.7199 9.94418 12.7788 9.83961 12.8834L6.88321 15.8392L7.67285 16.6289L10.2344 14.0678L11.5144 15.3483C11.6191 15.453 11.7611 15.5119 11.9092 15.5119C12.0573 15.5119 12.1993 15.453 12.304 15.3483L14.7014 12.9509V14.3951H15.8183V11.0444H12.4677ZM1.29877 16.6289H0.181885V13.8366C0.181885 11.681 1.9354 9.92753 4.09099 9.92753H7.44166C8.5524 9.92753 9.614 10.4022 10.3551 11.2304L9.52298 11.9753C9.26108 11.6825 8.94031 11.4482 8.58166 11.2878C8.22301 11.1273 7.83455 11.0444 7.44166 11.0444H4.09099C2.55136 11.0444 1.29877 12.297 1.29877 13.8366V16.6289ZM5.76632 8.81065C6.80308 8.81065 7.79738 8.3988 8.53048 7.6657C9.26358 6.9326 9.67543 5.9383 9.67543 4.90154C9.67543 3.86478 9.26358 2.87048 8.53048 2.13738C7.79738 1.40428 6.80308 0.992432 5.76632 0.992432C4.72956 0.992432 3.73527 1.40428 3.00217 2.13738C2.26907 2.87048 1.85722 3.86478 1.85722 4.90154C1.85722 5.9383 2.26907 6.9326 3.00217 7.6657C3.73527 8.3988 4.72956 8.81065 5.76632 8.81065ZM5.76632 2.10932C6.50687 2.10932 7.21708 2.4035 7.74072 2.92714C8.26436 3.45078 8.55854 4.161 8.55854 4.90154C8.55854 5.64208 8.26436 6.35229 7.74072 6.87594C7.21708 7.39958 6.50687 7.69376 5.76632 7.69376C5.02578 7.69376 4.31557 7.39958 3.79193 6.87594C3.26828 6.35229 2.9741 5.64208 2.9741 4.90154C2.9741 4.161 3.26828 3.45078 3.79193 2.92714C4.31557 2.4035 5.02578 2.10932 5.76632 2.10932Z" fill="#2C9EFF"/></g></g><defs><clipPath id="clip0_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath><clipPath id="clip1_51_291"><rect width="16" height="16" fill="white" transform="translate(0 0.810638)"/></clipPath></defs>'},"report-average_revenue_per_session":{viewBox:"0 0 16 17",content:'<path d="M11.5999 2.18463C11.3877 2.18463 11.1842 2.10034 11.0342 1.95031C10.8842 1.80028 10.7999 1.5968 10.7999 1.38463C10.7999 1.17245 10.8842 0.968969 11.0342 0.81894C11.1842 0.668911 11.3877 0.584625 11.5999 0.584625H14.7999C15.0121 0.584625 15.2156 0.668911 15.3656 0.81894C15.5156 0.968969 15.5999 1.17245 15.5999 1.38463V4.58462C15.5999 4.7968 15.5156 5.00028 15.3656 5.15031C15.2156 5.30034 15.0121 5.38462 14.7999 5.38462C14.5877 5.38462 14.3842 5.30034 14.2342 5.15031C14.0842 5.00028 13.9999 4.7968 13.9999 4.58462V3.31582L9.3655 7.95022C9.21548 8.1002 9.01203 8.18445 8.7999 8.18445C8.58777 8.18445 8.38432 8.1002 8.2343 7.95022L5.9999 5.71582L1.7655 9.95022C1.61462 10.096 1.41254 10.1766 1.20278 10.1748C0.993024 10.1729 0.792374 10.0888 0.644048 9.94048C0.495721 9.79215 0.411586 9.5915 0.409763 9.38174C0.40794 9.17199 0.488576 8.96991 0.634302 8.81902L5.4343 4.01902C5.58432 3.86905 5.78777 3.7848 5.9999 3.7848C6.21203 3.7848 6.41548 3.86905 6.5655 4.01902L8.7999 6.25342L12.8687 2.18463H11.5999ZM1.9999 13.3846V15.7846C1.9999 15.9968 1.91562 16.2003 1.76559 16.3503C1.61556 16.5003 1.41208 16.5846 1.1999 16.5846C0.987729 16.5846 0.784246 16.5003 0.634217 16.3503C0.484188 16.2003 0.399902 15.9968 0.399902 15.7846V13.3846C0.399902 13.1725 0.484188 12.969 0.634217 12.8189C0.784246 12.6689 0.987729 12.5846 1.1999 12.5846C1.41208 12.5846 1.61556 12.6689 1.76559 12.8189C1.91562 12.969 1.9999 13.1725 1.9999 13.3846ZM5.9999 10.1846C5.9999 9.97245 5.91562 9.76897 5.76559 9.61894C5.61556 9.46891 5.41208 9.38462 5.1999 9.38462C4.98773 9.38462 4.78425 9.46891 4.63422 9.61894C4.48419 9.76897 4.3999 9.97245 4.3999 10.1846V15.7846C4.3999 15.9968 4.48419 16.2003 4.63422 16.3503C4.78425 16.5003 4.98773 16.5846 5.1999 16.5846C5.41208 16.5846 5.61556 16.5003 5.76559 16.3503C5.91562 16.2003 5.9999 15.9968 5.9999 15.7846V10.1846ZM9.1999 10.9846C9.41208 10.9846 9.61556 11.0689 9.76559 11.2189C9.91562 11.369 9.9999 11.5725 9.9999 11.7846V15.7846C9.9999 15.9968 9.91562 16.2003 9.76559 16.3503C9.61556 16.5003 9.41208 16.5846 9.1999 16.5846C8.98773 16.5846 8.78425 16.5003 8.63422 16.3503C8.48419 16.2003 8.3999 15.9968 8.3999 15.7846V11.7846C8.3999 11.5725 8.48419 11.369 8.63422 11.2189C8.78425 11.0689 8.98773 10.9846 9.1999 10.9846ZM13.9999 7.78462C13.9999 7.57245 13.9156 7.36897 13.7656 7.21894C13.6156 7.06891 13.4121 6.98462 13.1999 6.98462C12.9877 6.98462 12.7842 7.06891 12.6342 7.21894C12.4842 7.36897 12.3999 7.57245 12.3999 7.78462V15.7846C12.3999 15.9968 12.4842 16.2003 12.6342 16.3503C12.7842 16.5003 12.9877 16.5846 13.1999 16.5846C13.4121 16.5846 13.6156 16.5003 13.7656 16.3503C13.9156 16.2003 13.9999 15.9968 13.9999 15.7846V7.78462Z" fill="#8EA4B4"/>'},"report-average_revenue_per_session-active":{viewBox:"0 0 16 17",content:'<path d="M11.5999 2.18463C11.3877 2.18463 11.1842 2.10034 11.0342 1.95031C10.8842 1.80028 10.7999 1.5968 10.7999 1.38463C10.7999 1.17245 10.8842 0.968969 11.0342 0.81894C11.1842 0.668911 11.3877 0.584625 11.5999 0.584625H14.7999C15.0121 0.584625 15.2156 0.668911 15.3656 0.81894C15.5156 0.968969 15.5999 1.17245 15.5999 1.38463V4.58462C15.5999 4.7968 15.5156 5.00028 15.3656 5.15031C15.2156 5.30034 15.0121 5.38462 14.7999 5.38462C14.5877 5.38462 14.3842 5.30034 14.2342 5.15031C14.0842 5.00028 13.9999 4.7968 13.9999 4.58462V3.31582L9.3655 7.95022C9.21548 8.1002 9.01203 8.18445 8.7999 8.18445C8.58777 8.18445 8.38432 8.1002 8.2343 7.95022L5.9999 5.71582L1.7655 9.95022C1.61462 10.096 1.41254 10.1766 1.20278 10.1748C0.993024 10.1729 0.792374 10.0888 0.644048 9.94048C0.495721 9.79215 0.411586 9.5915 0.409763 9.38174C0.40794 9.17199 0.488576 8.96991 0.634302 8.81902L5.4343 4.01902C5.58432 3.86905 5.78777 3.7848 5.9999 3.7848C6.21203 3.7848 6.41548 3.86905 6.5655 4.01902L8.7999 6.25342L12.8687 2.18463H11.5999ZM1.9999 13.3846V15.7846C1.9999 15.9968 1.91562 16.2003 1.76559 16.3503C1.61556 16.5003 1.41208 16.5846 1.1999 16.5846C0.987729 16.5846 0.784246 16.5003 0.634217 16.3503C0.484188 16.2003 0.399902 15.9968 0.399902 15.7846V13.3846C0.399902 13.1725 0.484188 12.969 0.634217 12.8189C0.784246 12.6689 0.987729 12.5846 1.1999 12.5846C1.41208 12.5846 1.61556 12.6689 1.76559 12.8189C1.91562 12.969 1.9999 13.1725 1.9999 13.3846ZM5.9999 10.1846C5.9999 9.97245 5.91562 9.76897 5.76559 9.61894C5.61556 9.46891 5.41208 9.38462 5.1999 9.38462C4.98773 9.38462 4.78425 9.46891 4.63422 9.61894C4.48419 9.76897 4.3999 9.97245 4.3999 10.1846V15.7846C4.3999 15.9968 4.48419 16.2003 4.63422 16.3503C4.78425 16.5003 4.98773 16.5846 5.1999 16.5846C5.41208 16.5846 5.61556 16.5003 5.76559 16.3503C5.91562 16.2003 5.9999 15.9968 5.9999 15.7846V10.1846ZM9.1999 10.9846C9.41208 10.9846 9.61556 11.0689 9.76559 11.2189C9.91562 11.369 9.9999 11.5725 9.9999 11.7846V15.7846C9.9999 15.9968 9.91562 16.2003 9.76559 16.3503C9.61556 16.5003 9.41208 16.5846 9.1999 16.5846C8.98773 16.5846 8.78425 16.5003 8.63422 16.3503C8.48419 16.2003 8.3999 15.9968 8.3999 15.7846V11.7846C8.3999 11.5725 8.48419 11.369 8.63422 11.2189C8.78425 11.0689 8.98773 10.9846 9.1999 10.9846ZM13.9999 7.78462C13.9999 7.57245 13.9156 7.36897 13.7656 7.21894C13.6156 7.06891 13.4121 6.98462 13.1999 6.98462C12.9877 6.98462 12.7842 7.06891 12.6342 7.21894C12.4842 7.36897 12.3999 7.57245 12.3999 7.78462V15.7846C12.3999 15.9968 12.4842 16.2003 12.6342 16.3503C12.7842 16.5003 12.9877 16.5846 13.1999 16.5846C13.4121 16.5846 13.6156 16.5003 13.7656 16.3503C13.9156 16.2003 13.9999 15.9968 13.9999 15.7846V7.78462Z" fill="#2C9EFF"/>'},"report-new_users":{viewBox:"0 0 16 17",content:'<path d="M12.6742 14.6295H14.3495C14.4969 14.6318 14.6426 14.5984 14.7743 14.5321C14.9059 14.4659 15.0196 14.3687 15.1055 14.249C15.1914 14.1293 15.2471 13.9905 15.2677 13.8446C15.2884 13.6986 15.2734 13.5499 15.224 13.411C14.7968 12.4967 14.1205 11.7213 13.2727 11.1738C12.4249 10.6263 11.4399 10.329 10.4308 10.3159M10.4308 8.15394C10.8079 8.15404 11.1813 8.07986 11.5298 7.93562C11.8782 7.79138 12.1948 7.57992 12.4615 7.3133C12.7282 7.04669 12.9397 6.73016 13.084 6.38177C13.2284 6.03339 13.3027 5.65998 13.3027 5.28289C13.3037 4.90513 13.2302 4.53088 13.0863 4.18159C12.9424 3.8323 12.731 3.51484 12.4643 3.2474C12.1975 2.97996 11.8805 2.76779 11.5316 2.62306C11.1826 2.47834 10.8086 2.40389 10.4308 2.404M5.9448 8.24405C6.80811 8.24198 7.63534 7.89752 8.24498 7.28626C8.85462 6.675 9.19689 5.84687 9.19668 4.98356C9.19668 4.1209 8.85399 3.29357 8.244 2.68358C7.63401 2.07359 6.80668 1.7309 5.94402 1.7309C5.08136 1.7309 4.25403 2.07359 3.64404 2.68358C3.03405 3.29357 2.69136 4.1209 2.69136 4.98356C2.69136 5.84687 3.03382 6.67492 3.64361 7.28603C4.2534 7.89714 5.08071 8.2414 5.94402 8.24327M9.6159 15.5643C9.8919 15.5639 10.1624 15.4872 10.3975 15.3425C10.6325 15.1978 10.823 14.9909 10.9477 14.7447C11.0724 14.4985 11.1265 14.2225 11.104 13.9474C11.0815 13.6723 10.9833 13.4088 10.8203 13.1861C10.2491 12.428 9.51387 11.8089 8.66955 11.3752C7.82523 10.9415 6.89373 10.7044 5.9448 10.6818C4.99581 10.7045 4.06427 10.9417 3.21995 11.3755C2.37562 11.8094 1.6404 12.4286 1.06934 13.1869C0.906866 13.4096 0.809109 13.6729 0.786862 13.9477C0.764614 14.2225 0.818741 14.4981 0.943265 14.7441C1.06779 14.9901 1.25787 15.1968 1.49251 15.3416C1.72715 15.4863 1.99723 15.5634 2.27292 15.5643H9.6159Z" stroke="#8EA4B4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-new_users-active":{viewBox:"0 0 16 17",content:'<path d="M12.6742 14.6295H14.3495C14.4969 14.6318 14.6426 14.5984 14.7743 14.5321C14.9059 14.4659 15.0196 14.3687 15.1055 14.249C15.1914 14.1293 15.2471 13.9905 15.2677 13.8446C15.2884 13.6986 15.2734 13.5499 15.224 13.411C14.7968 12.4967 14.1205 11.7213 13.2727 11.1738C12.4249 10.6263 11.4399 10.329 10.4308 10.3159M10.4308 8.15394C10.8079 8.15404 11.1813 8.07986 11.5298 7.93562C11.8782 7.79138 12.1948 7.57992 12.4615 7.3133C12.7282 7.04669 12.9397 6.73016 13.084 6.38177C13.2284 6.03339 13.3027 5.65998 13.3027 5.28289C13.3037 4.90513 13.2302 4.53088 13.0863 4.18159C12.9424 3.8323 12.731 3.51484 12.4643 3.2474C12.1975 2.97996 11.8805 2.76779 11.5316 2.62306C11.1826 2.47834 10.8086 2.40389 10.4308 2.404M5.9448 8.24405C6.80811 8.24198 7.63534 7.89752 8.24498 7.28626C8.85462 6.675 9.19689 5.84687 9.19668 4.98356C9.19668 4.1209 8.85399 3.29357 8.244 2.68358C7.63401 2.07359 6.80668 1.7309 5.94402 1.7309C5.08136 1.7309 4.25403 2.07359 3.64404 2.68358C3.03405 3.29357 2.69136 4.1209 2.69136 4.98356C2.69136 5.84687 3.03382 6.67492 3.64361 7.28603C4.2534 7.89714 5.08071 8.2414 5.94402 8.24327M9.6159 15.5643C9.8919 15.5639 10.1624 15.4872 10.3975 15.3425C10.6325 15.1978 10.823 14.9909 10.9477 14.7447C11.0724 14.4985 11.1265 14.2225 11.104 13.9474C11.0815 13.6723 10.9833 13.4088 10.8203 13.1861C10.2491 12.428 9.51387 11.8089 8.66955 11.3752C7.82523 10.9415 6.89373 10.7044 5.9448 10.6818C4.99581 10.7045 4.06427 10.9417 3.21995 11.3755C2.37562 11.8094 1.6404 12.4286 1.06934 13.1869C0.906866 13.4096 0.809109 13.6729 0.786862 13.9477C0.764614 14.2225 0.818741 14.4981 0.943265 14.7441C1.06779 14.9901 1.25787 15.1968 1.49251 15.3416C1.72715 15.4863 1.99723 15.5634 2.27292 15.5643H9.6159Z" stroke="#2C9EFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>'},"report-ecommerce_purchases":{viewBox:"0 0 16 16",content:'<path d="M15.5541 3.52685C15.5006 3.46289 15.4338 3.41144 15.3583 3.37614C15.2827 3.34084 15.2004 3.32256 15.117 3.32257H3.63441L3.20164 0.94305C3.1778 0.811844 3.10867 0.693165 3.0063 0.607704C2.90393 0.522244 2.77482 0.475422 2.64146 0.475403H0.881201C0.730178 0.475403 0.58534 0.535396 0.478551 0.642186C0.371761 0.748975 0.311768 0.893813 0.311768 1.04484C0.311768 1.19586 0.371761 1.3407 0.478551 1.44749C0.58534 1.55428 0.730178 1.61427 0.881201 1.61427H2.16243L3.98176 11.6C4.03536 11.8961 4.16623 12.1729 4.36115 12.4022C4.09213 12.6535 3.89795 12.9743 3.80016 13.3292C3.70236 13.6841 3.70477 14.0592 3.80711 14.4128C3.90945 14.7664 4.10773 15.0847 4.37996 15.3325C4.65219 15.5803 4.98773 15.7479 5.34937 15.8166C5.71102 15.8854 6.08463 15.8526 6.4288 15.722C6.77296 15.5914 7.07422 15.368 7.29917 15.0766C7.52412 14.7852 7.66397 14.4372 7.70322 14.0712C7.74246 13.7051 7.67957 13.3354 7.5215 13.0029H10.7545C10.6271 13.2696 10.5611 13.5615 10.5616 13.8571C10.5616 14.2513 10.6785 14.6366 10.8974 14.9643C11.1164 15.2921 11.4277 15.5475 11.7919 15.6984C12.1561 15.8492 12.5568 15.8887 12.9434 15.8118C13.33 15.7349 13.6851 15.5451 13.9639 15.2664C14.2426 14.9876 14.4324 14.6325 14.5093 14.2459C14.5862 13.8593 14.5467 13.4586 14.3959 13.0944C14.245 12.7302 13.9896 12.4189 13.6618 12.2C13.3341 11.981 12.9488 11.8641 12.5546 11.8641H5.6623C5.52895 11.864 5.39983 11.8172 5.29746 11.7318C5.19509 11.6463 5.12596 11.5276 5.10212 11.3964L4.87649 10.1558H13.1333C13.5333 10.1557 13.9207 10.0152 14.2278 9.75886C14.5349 9.50248 14.7423 9.14645 14.8138 8.75283L15.6793 3.99379C15.694 3.91153 15.6904 3.82706 15.6687 3.74637C15.6471 3.66567 15.6079 3.59073 15.5541 3.52685ZM6.57553 13.8571C6.57553 14.026 6.52544 14.1912 6.43158 14.3316C6.33773 14.4721 6.20433 14.5816 6.04825 14.6462C5.89218 14.7109 5.72044 14.7278 5.55475 14.6948C5.38906 14.6619 5.23686 14.5805 5.11741 14.4611C4.99795 14.3416 4.9166 14.1894 4.88365 14.0237C4.85069 13.858 4.8676 13.6863 4.93225 13.5302C4.9969 13.3741 5.10638 13.2407 5.24684 13.1469C5.38731 13.053 5.55245 13.0029 5.72138 13.0029C5.94792 13.0029 6.16517 13.0929 6.32536 13.2531C6.48554 13.4133 6.57553 13.6306 6.57553 13.8571ZM13.4087 13.8571C13.4087 14.026 13.3586 14.1912 13.2648 14.3316C13.1709 14.4721 13.0375 14.5816 12.8814 14.6462C12.7254 14.7109 12.5536 14.7278 12.3879 14.6948C12.2223 14.6619 12.0701 14.5805 11.9506 14.4611C11.8312 14.3416 11.7498 14.1894 11.7168 14.0237C11.6839 13.858 11.7008 13.6863 11.7655 13.5302C11.8301 13.3741 11.9396 13.2407 12.08 13.1469C12.2205 13.053 12.3856 13.0029 12.5546 13.0029C12.7811 13.0029 12.9984 13.0929 13.1586 13.2531C13.3187 13.4133 13.4087 13.6306 13.4087 13.8571ZM13.6934 8.54925C13.6695 8.68082 13.6001 8.79978 13.4973 8.88529C13.3945 8.97079 13.2649 9.01738 13.1311 9.0169H4.66936L3.84154 4.46144H14.4344L13.6934 8.54925Z" fill="#8EA4B4"/>'},"report-ecommerce_purchases-active":{viewBox:"0 0 16 16",content:'<path d="M15.5541 3.52685C15.5006 3.46289 15.4338 3.41144 15.3583 3.37614C15.2827 3.34084 15.2004 3.32256 15.117 3.32257H3.63441L3.20164 0.94305C3.1778 0.811844 3.10867 0.693165 3.0063 0.607704C2.90393 0.522244 2.77482 0.475422 2.64146 0.475403H0.881201C0.730178 0.475403 0.58534 0.535396 0.478551 0.642186C0.371761 0.748975 0.311768 0.893813 0.311768 1.04484C0.311768 1.19586 0.371761 1.3407 0.478551 1.44749C0.58534 1.55428 0.730178 1.61427 0.881201 1.61427H2.16243L3.98176 11.6C4.03536 11.8961 4.16623 12.1729 4.36115 12.4022C4.09213 12.6535 3.89795 12.9743 3.80016 13.3292C3.70236 13.6841 3.70477 14.0592 3.80711 14.4128C3.90945 14.7664 4.10773 15.0847 4.37996 15.3325C4.65219 15.5803 4.98773 15.7479 5.34937 15.8166C5.71102 15.8854 6.08463 15.8526 6.4288 15.722C6.77296 15.5914 7.07422 15.368 7.29917 15.0766C7.52412 14.7852 7.66397 14.4372 7.70322 14.0712C7.74246 13.7051 7.67957 13.3354 7.5215 13.0029H10.7545C10.6271 13.2696 10.5611 13.5615 10.5616 13.8571C10.5616 14.2513 10.6785 14.6366 10.8974 14.9643C11.1164 15.2921 11.4277 15.5475 11.7919 15.6984C12.1561 15.8492 12.5568 15.8887 12.9434 15.8118C13.33 15.7349 13.6851 15.5451 13.9639 15.2664C14.2426 14.9876 14.4324 14.6325 14.5093 14.2459C14.5862 13.8593 14.5467 13.4586 14.3959 13.0944C14.245 12.7302 13.9896 12.4189 13.6618 12.2C13.3341 11.981 12.9488 11.8641 12.5546 11.8641H5.6623C5.52895 11.864 5.39983 11.8172 5.29746 11.7318C5.19509 11.6463 5.12596 11.5276 5.10212 11.3964L4.87649 10.1558H13.1333C13.5333 10.1557 13.9207 10.0152 14.2278 9.75886C14.5349 9.50248 14.7423 9.14645 14.8138 8.75283L15.6793 3.99379C15.694 3.91153 15.6904 3.82706 15.6687 3.74637C15.6471 3.66567 15.6079 3.59073 15.5541 3.52685ZM6.57553 13.8571C6.57553 14.026 6.52544 14.1912 6.43158 14.3316C6.33773 14.4721 6.20433 14.5816 6.04825 14.6462C5.89218 14.7109 5.72044 14.7278 5.55475 14.6948C5.38906 14.6619 5.23686 14.5805 5.11741 14.4611C4.99795 14.3416 4.9166 14.1894 4.88365 14.0237C4.85069 13.858 4.8676 13.6863 4.93225 13.5302C4.9969 13.3741 5.10638 13.2407 5.24684 13.1469C5.38731 13.053 5.55245 13.0029 5.72138 13.0029C5.94792 13.0029 6.16517 13.0929 6.32536 13.2531C6.48554 13.4133 6.57553 13.6306 6.57553 13.8571ZM13.4087 13.8571C13.4087 14.026 13.3586 14.1912 13.2648 14.3316C13.1709 14.4721 13.0375 14.5816 12.8814 14.6462C12.7254 14.7109 12.5536 14.7278 12.3879 14.6948C12.2223 14.6619 12.0701 14.5805 11.9506 14.4611C11.8312 14.3416 11.7498 14.1894 11.7168 14.0237C11.6839 13.858 11.7008 13.6863 11.7655 13.5302C11.8301 13.3741 11.9396 13.2407 12.08 13.1469C12.2205 13.053 12.3856 13.0029 12.5546 13.0029C12.7811 13.0029 12.9984 13.0929 13.1586 13.2531C13.3187 13.4133 13.4087 13.6306 13.4087 13.8571ZM13.6934 8.54925C13.6695 8.68082 13.6001 8.79978 13.4973 8.88529C13.3945 8.97079 13.2649 9.01738 13.1311 9.0169H4.66936L3.84154 4.46144H14.4344L13.6934 8.54925Z" fill="#2C9EFF"/>'},"report-engagement_rate":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6074)"><path d="M7.95758 0.979736H9.11628C9.26431 0.979736 9.40627 1.03854 9.51094 1.14321C9.61561 1.24788 9.67442 1.38985 9.67442 1.53788C9.67442 1.6859 9.61561 1.82787 9.51094 1.93254C9.40627 2.03721 9.26431 2.09602 9.11628 2.09602H8C6.23033 2.09602 4.95926 2.0975 3.99181 2.22699C3.04 2.35499 2.46623 2.59909 2.04205 3.02253C1.6186 3.44671 1.37526 4.01974 1.24726 4.97155C1.11777 5.93899 1.11628 7.21006 1.11628 8.97974C1.11628 10.7494 1.11777 12.0205 1.24726 12.9879C1.37526 13.9397 1.61935 14.5135 2.04279 14.9377C2.46698 15.3611 3.04 15.6045 3.99181 15.7325C4.95926 15.862 6.23033 15.8635 8 15.8635C9.76967 15.8635 11.0407 15.862 12.0082 15.7325C12.96 15.6045 13.5338 15.3604 13.958 14.9369C14.3814 14.5128 14.6247 13.9397 14.7527 12.9879C14.8822 12.0205 14.8837 10.7494 14.8837 8.97974V7.86346C14.8837 7.71543 14.9425 7.57346 15.0472 7.46879C15.1519 7.36412 15.2938 7.30532 15.4419 7.30532C15.5899 7.30532 15.7319 7.36412 15.8365 7.46879C15.9412 7.57346 16 7.71543 16 7.86346V9.02215C16 10.7405 16 12.0867 15.8586 13.1375C15.7142 14.2121 15.4121 15.0605 14.7468 15.7265C14.0807 16.3926 13.2324 16.694 12.157 16.8383C11.107 16.9797 9.76074 16.9797 8.04242 16.9797H7.95758C6.23926 16.9797 4.89302 16.9797 3.84223 16.8383C2.76763 16.694 1.91926 16.3918 1.25321 15.7265C0.587163 15.0605 0.285767 14.2121 0.141395 13.1368C4.43569e-08 12.0867 0 10.7405 0 9.02215V8.93732C0 7.21899 4.43569e-08 5.87276 0.141395 4.82197C0.285767 3.74736 0.587907 2.89899 1.25321 2.23295C1.91926 1.5669 2.76763 1.2655 3.84298 1.12113C4.89302 0.979736 6.23926 0.979736 7.95758 0.979736Z" fill="#8EA4B4"/><path d="M12.0781 7.06273C12.1918 7.1575 12.2631 7.29351 12.2765 7.44088C12.2899 7.58824 12.2443 7.73489 12.1496 7.8486L10.7885 9.48208C10.5444 9.77529 10.3241 10.0402 10.1187 10.2263C9.89394 10.4272 9.61264 10.608 9.24055 10.608C8.86845 10.608 8.58641 10.4279 8.36241 10.2255C8.15701 10.0395 7.93673 9.77529 7.6919 9.48134L7.47459 9.22087C7.1985 8.88971 7.02883 8.68804 6.88892 8.56227C6.85252 8.52696 6.81175 8.49645 6.76762 8.47148L6.76241 8.46925L6.75943 8.46776L6.75199 8.47148C6.70759 8.4964 6.66657 8.52691 6.62994 8.56227C6.49078 8.68878 6.3211 8.88971 6.04501 9.22087L4.70771 10.8253C4.61177 10.935 4.47675 11.0028 4.33149 11.0143C4.18623 11.0257 4.04226 10.9799 3.93032 10.8866C3.81839 10.7933 3.74736 10.66 3.73243 10.515C3.7175 10.3701 3.75984 10.2251 3.85041 10.1109L5.21152 8.47743C5.45562 8.18422 5.6759 7.91929 5.88129 7.73325C6.10603 7.53232 6.38734 7.35148 6.75943 7.35148C7.13152 7.35148 7.41357 7.53157 7.63757 7.73399C7.84296 7.92004 8.06324 8.18422 8.30808 8.47818L8.52538 8.73864C8.80148 9.0698 8.97115 9.27148 9.11106 9.39725C9.17506 9.45529 9.21376 9.47911 9.23236 9.48804L9.2398 9.49176L9.24352 9.49027L9.24873 9.48804C9.29287 9.46306 9.33363 9.43255 9.37003 9.39725C9.5092 9.27073 9.67887 9.0698 9.95496 8.73864L11.2923 7.13418C11.387 7.02053 11.523 6.94916 11.6704 6.93577C11.8178 6.92237 11.9644 6.96804 12.0781 7.06273Z" fill="#8EA4B4"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.2094 0.979736C12.4693 0.979736 11.7594 1.27376 11.2361 1.79711C10.7127 2.32047 10.4187 3.03029 10.4187 3.77043C10.4187 4.51057 10.7127 5.2204 11.2361 5.74376C11.7594 6.26711 12.4693 6.56113 13.2094 6.56113C13.9495 6.56113 14.6594 6.26711 15.1827 5.74376C15.7061 5.2204 16.0001 4.51057 16.0001 3.77043C16.0001 3.03029 15.7061 2.32047 15.1827 1.79711C14.6594 1.27376 13.9495 0.979736 13.2094 0.979736ZM11.535 3.77043C11.535 3.32635 11.7114 2.90046 12.0254 2.58644C12.3394 2.27243 12.7653 2.09602 13.2094 2.09602C13.6535 2.09602 14.0794 2.27243 14.3934 2.58644C14.7074 2.90046 14.8838 3.32635 14.8838 3.77043C14.8838 4.21452 14.7074 4.64041 14.3934 4.95443C14.0794 5.26844 13.6535 5.44485 13.2094 5.44485C12.7653 5.44485 12.3394 5.26844 12.0254 4.95443C11.7114 4.64041 11.535 4.21452 11.535 3.77043Z" fill="#8EA4B4"/></g><defs><clipPath id="clip0_65_6074"><rect width="16" height="16" fill="white" transform="translate(0 0.979736)"/></clipPath></defs>'},"report-engagement_rate-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6074)"><path d="M7.95758 0.979736H9.11628C9.26431 0.979736 9.40627 1.03854 9.51094 1.14321C9.61561 1.24788 9.67442 1.38985 9.67442 1.53788C9.67442 1.6859 9.61561 1.82787 9.51094 1.93254C9.40627 2.03721 9.26431 2.09602 9.11628 2.09602H8C6.23033 2.09602 4.95926 2.0975 3.99181 2.22699C3.04 2.35499 2.46623 2.59909 2.04205 3.02253C1.6186 3.44671 1.37526 4.01974 1.24726 4.97155C1.11777 5.93899 1.11628 7.21006 1.11628 8.97974C1.11628 10.7494 1.11777 12.0205 1.24726 12.9879C1.37526 13.9397 1.61935 14.5135 2.04279 14.9377C2.46698 15.3611 3.04 15.6045 3.99181 15.7325C4.95926 15.862 6.23033 15.8635 8 15.8635C9.76967 15.8635 11.0407 15.862 12.0082 15.7325C12.96 15.6045 13.5338 15.3604 13.958 14.9369C14.3814 14.5128 14.6247 13.9397 14.7527 12.9879C14.8822 12.0205 14.8837 10.7494 14.8837 8.97974V7.86346C14.8837 7.71543 14.9425 7.57346 15.0472 7.46879C15.1519 7.36412 15.2938 7.30532 15.4419 7.30532C15.5899 7.30532 15.7319 7.36412 15.8365 7.46879C15.9412 7.57346 16 7.71543 16 7.86346V9.02215C16 10.7405 16 12.0867 15.8586 13.1375C15.7142 14.2121 15.4121 15.0605 14.7468 15.7265C14.0807 16.3926 13.2324 16.694 12.157 16.8383C11.107 16.9797 9.76074 16.9797 8.04242 16.9797H7.95758C6.23926 16.9797 4.89302 16.9797 3.84223 16.8383C2.76763 16.694 1.91926 16.3918 1.25321 15.7265C0.587163 15.0605 0.285767 14.2121 0.141395 13.1368C4.43569e-08 12.0867 0 10.7405 0 9.02215V8.93732C0 7.21899 4.43569e-08 5.87276 0.141395 4.82197C0.285767 3.74736 0.587907 2.89899 1.25321 2.23295C1.91926 1.5669 2.76763 1.2655 3.84298 1.12113C4.89302 0.979736 6.23926 0.979736 7.95758 0.979736Z" fill="#2C9EFF"/><path d="M12.0781 7.06273C12.1918 7.1575 12.2631 7.29351 12.2765 7.44088C12.2899 7.58824 12.2443 7.73489 12.1496 7.8486L10.7885 9.48208C10.5444 9.77529 10.3241 10.0402 10.1187 10.2263C9.89394 10.4272 9.61264 10.608 9.24055 10.608C8.86845 10.608 8.58641 10.4279 8.36241 10.2255C8.15701 10.0395 7.93673 9.77529 7.6919 9.48134L7.47459 9.22087C7.1985 8.88971 7.02883 8.68804 6.88892 8.56227C6.85252 8.52696 6.81175 8.49645 6.76762 8.47148L6.76241 8.46925L6.75943 8.46776L6.75199 8.47148C6.70759 8.4964 6.66657 8.52691 6.62994 8.56227C6.49078 8.68878 6.3211 8.88971 6.04501 9.22087L4.70771 10.8253C4.61177 10.935 4.47675 11.0028 4.33149 11.0143C4.18623 11.0257 4.04226 10.9799 3.93032 10.8866C3.81839 10.7933 3.74736 10.66 3.73243 10.515C3.7175 10.3701 3.75984 10.2251 3.85041 10.1109L5.21152 8.47743C5.45562 8.18422 5.6759 7.91929 5.88129 7.73325C6.10603 7.53232 6.38734 7.35148 6.75943 7.35148C7.13152 7.35148 7.41357 7.53157 7.63757 7.73399C7.84296 7.92004 8.06324 8.18422 8.30808 8.47818L8.52538 8.73864C8.80148 9.0698 8.97115 9.27148 9.11106 9.39725C9.17506 9.45529 9.21376 9.47911 9.23236 9.48804L9.2398 9.49176L9.24352 9.49027L9.24873 9.48804C9.29287 9.46306 9.33363 9.43255 9.37003 9.39725C9.5092 9.27073 9.67887 9.0698 9.95496 8.73864L11.2923 7.13418C11.387 7.02053 11.523 6.94916 11.6704 6.93577C11.8178 6.92237 11.9644 6.96804 12.0781 7.06273Z" fill="#2C9EFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.2094 0.979736C12.4693 0.979736 11.7594 1.27376 11.2361 1.79711C10.7127 2.32047 10.4187 3.03029 10.4187 3.77043C10.4187 4.51057 10.7127 5.2204 11.2361 5.74376C11.7594 6.26711 12.4693 6.56113 13.2094 6.56113C13.9495 6.56113 14.6594 6.26711 15.1827 5.74376C15.7061 5.2204 16.0001 4.51057 16.0001 3.77043C16.0001 3.03029 15.7061 2.32047 15.1827 1.79711C14.6594 1.27376 13.9495 0.979736 13.2094 0.979736ZM11.535 3.77043C11.535 3.32635 11.7114 2.90046 12.0254 2.58644C12.3394 2.27243 12.7653 2.09602 13.2094 2.09602C13.6535 2.09602 14.0794 2.27243 14.3934 2.58644C14.7074 2.90046 14.8838 3.32635 14.8838 3.77043C14.8838 4.21452 14.7074 4.64041 14.3934 4.95443C14.0794 5.26844 13.6535 5.44485 13.2094 5.44485C12.7653 5.44485 12.3394 5.26844 12.0254 4.95443C11.7114 4.64041 11.535 4.21452 11.535 3.77043Z" fill="#2C9EFF"/></g><defs><clipPath id="clip0_65_6074"><rect width="16" height="16" fill="white" transform="translate(0 0.979736)"/></clipPath></defs>'},"report-sessions_per_user":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6090)"><path d="M14.6873 16.0506C14.2638 7.72924 1.73674 7.72924 1.31323 16.0506M10.6751 4.45973C10.6751 5.16913 10.3933 5.84948 9.89165 6.35111C9.39003 6.85273 8.70968 7.13454 8.00027 7.13454C7.29087 7.13454 6.61052 6.85273 6.10889 6.35111C5.60727 5.84948 5.32546 5.16913 5.32546 4.45973C5.32546 3.75032 5.60727 3.06997 6.10889 2.56835C6.61052 2.06672 7.29087 1.78491 8.00027 1.78491C8.70968 1.78491 9.39003 2.06672 9.89165 2.56835C10.3933 3.06997 10.6751 3.75032 10.6751 4.45973Z" stroke="#8EA4B4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M6.44702 14.0189L7.36894 15.0719C7.45364 15.169 7.60611 15.161 7.681 15.0576L9.55337 12.4657" stroke="#8EA4B4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_65_6090"><rect width="16" height="16" fill="white" transform="translate(0 0.917725)"/></clipPath></defs>'},"report-sessions_per_user-active":{viewBox:"0 0 16 17",content:'<g clip-path="url(#clip0_65_6090)"><path d="M14.6873 16.0506C14.2638 7.72924 1.73674 7.72924 1.31323 16.0506M10.6751 4.45973C10.6751 5.16913 10.3933 5.84948 9.89165 6.35111C9.39003 6.85273 8.70968 7.13454 8.00027 7.13454C7.29087 7.13454 6.61052 6.85273 6.10889 6.35111C5.60727 5.84948 5.32546 5.16913 5.32546 4.45973C5.32546 3.75032 5.60727 3.06997 6.10889 2.56835C6.61052 2.06672 7.29087 1.78491 8.00027 1.78491C8.70968 1.78491 9.39003 2.06672 9.89165 2.56835C10.3933 3.06997 10.6751 3.75032 10.6751 4.45973Z" stroke="#2C9EFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M6.44702 14.0189L7.36894 15.0719C7.45364 15.169 7.60611 15.161 7.681 15.0576L9.55337 12.4657" stroke="#2C9EFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_65_6090"><rect width="16" height="16" fill="white" transform="translate(0 0.917725)"/></clipPath></defs>'},"report-settings":{viewBox:"0 0 27 27",content:'<path d="M13.3923 7.61566C12.2095 7.61566 11.0532 7.96641 10.0697 8.62357C9.08617 9.28073 8.31963 10.2148 7.86697 11.3076C7.41431 12.4004 7.29588 13.6029 7.52664 14.763C7.7574 15.9231 8.327 16.9888 9.1634 17.8252C9.9998 18.6616 11.0654 19.2312 12.2256 19.4619C13.3857 19.6927 14.5882 19.5743 15.681 19.1216C16.7738 18.6689 17.7078 17.9024 18.365 16.9189C19.0222 15.9354 19.3729 14.7791 19.3729 13.5962C19.3713 12.0106 18.7406 10.4904 17.6194 9.36915C16.4982 8.24793 14.978 7.61731 13.3923 7.61566ZM13.3923 17.5833C12.6037 17.5833 11.8329 17.3495 11.1772 16.9114C10.5216 16.4733 10.0105 15.8506 9.70875 15.122C9.40698 14.3935 9.32802 13.5918 9.48186 12.8184C9.63571 12.045 10.0154 11.3346 10.573 10.777C11.1306 10.2194 11.8411 9.83964 12.6145 9.6858C13.3879 9.53196 14.1896 9.61091 14.9181 9.91268C15.6466 10.2145 16.2693 10.7255 16.7074 11.3812C17.1455 12.0368 17.3794 12.8077 17.3794 13.5962C17.3794 14.6537 16.9593 15.6678 16.2116 16.4155C15.4639 17.1632 14.4497 17.5833 13.3923 17.5833ZM24.3567 13.8654C24.3617 13.686 24.3617 13.5065 24.3567 13.3271L26.2157 11.0047C26.3132 10.8827 26.3806 10.7396 26.4127 10.5868C26.4447 10.434 26.4404 10.2758 26.4001 10.125C26.0954 8.97948 25.6395 7.87961 25.0445 6.85438C24.9666 6.72021 24.8584 6.60608 24.7287 6.52107C24.5989 6.43607 24.451 6.38253 24.2969 6.36472L21.3415 6.03579C21.2186 5.90621 21.094 5.78161 20.9677 5.662L20.6189 2.69911C20.6009 2.54488 20.5472 2.39697 20.462 2.26718C20.3767 2.13739 20.2624 2.0293 20.128 1.95154C19.1023 1.35764 18.0026 0.902224 16.8573 0.597185C16.7064 0.557062 16.5482 0.552958 16.3954 0.585205C16.2426 0.617451 16.0995 0.685146 15.9777 0.782833L13.6614 2.63183H13.1232L10.8007 0.776603C10.6788 0.67913 10.5357 0.611661 10.3829 0.579631C10.2301 0.547602 10.0719 0.551907 9.92108 0.592201C8.77573 0.897495 7.67594 1.35334 6.65044 1.9478C6.51628 2.02571 6.40215 2.13385 6.31714 2.26364C6.23213 2.39342 6.17859 2.54126 6.16078 2.69538L5.83185 5.65577C5.70227 5.77953 5.57768 5.90413 5.45806 6.02955L2.49518 6.3697C2.34095 6.38765 2.19304 6.44137 2.06324 6.5266C1.93345 6.61183 1.82537 6.72621 1.74761 6.86061C1.1537 7.88623 0.698291 8.98601 0.393252 10.1312C0.353128 10.2822 0.349024 10.4404 0.381271 10.5932C0.413518 10.746 0.481213 10.8891 0.578899 11.0109L2.4279 13.3271V13.8654L0.572669 16.1878C0.475196 16.3098 0.407727 16.4529 0.375698 16.6057C0.343668 16.7585 0.347974 16.9167 0.388268 17.0675C0.693002 18.213 1.14887 19.3129 1.74387 20.3381C1.82177 20.4723 1.92992 20.5864 2.0597 20.6714C2.18949 20.7564 2.33732 20.81 2.49144 20.8278L5.44685 21.1567C5.57062 21.2863 5.69521 21.4109 5.82064 21.5305L6.16577 24.4934C6.18371 24.6476 6.23744 24.7955 6.32267 24.9253C6.4079 25.0551 6.52227 25.1632 6.65667 25.241C7.68229 25.8349 8.78207 26.2903 9.92731 26.5953C10.0782 26.6354 10.2365 26.6395 10.3893 26.6073C10.542 26.575 10.6851 26.5074 10.807 26.4097L13.1232 24.5607C13.3026 24.5656 13.482 24.5656 13.6614 24.5607L15.9839 26.4196C16.1058 26.5171 16.249 26.5846 16.4018 26.6166C16.5546 26.6486 16.7127 26.6443 16.8636 26.604C18.0091 26.2993 19.109 25.8434 20.1342 25.2484C20.2684 25.1705 20.3825 25.0624 20.4675 24.9326C20.5525 24.8028 20.606 24.655 20.6238 24.5009L20.9528 21.5455C21.0824 21.4225 21.207 21.2979 21.3266 21.1717L24.2895 20.8228C24.4437 20.8049 24.5916 20.7511 24.7214 20.6659C24.8512 20.5807 24.9593 20.4663 25.037 20.3319C25.6309 19.3063 26.0863 18.2065 26.3914 17.0613C26.4315 16.9103 26.4356 16.7521 26.4034 16.5993C26.3711 16.4465 26.3034 16.3034 26.2057 16.1816L24.3567 13.8654ZM22.3507 13.0555C22.3719 13.4157 22.3719 13.7768 22.3507 14.137C22.3359 14.3836 22.4132 14.6269 22.5675 14.8198L24.3356 17.0289C24.1327 17.6736 23.8729 18.299 23.5593 18.8978L20.7435 19.2168C20.4982 19.244 20.2718 19.3612 20.108 19.5457C19.8682 19.8155 19.6128 20.0709 19.343 20.3107C19.1585 20.4745 19.0413 20.7009 19.0141 20.9461L18.7013 23.7595C18.1027 24.0733 17.4772 24.333 16.8324 24.5357L14.6221 22.7677C14.4452 22.6264 14.2255 22.5495 13.9991 22.5497H13.9393C13.5791 22.5709 13.218 22.5709 12.8578 22.5497C12.6112 22.5348 12.3679 22.6121 12.175 22.7665L9.9597 24.5357C9.31497 24.3329 8.68953 24.0731 8.09077 23.7595L7.77181 20.9474C7.74459 20.7022 7.62738 20.4758 7.44287 20.312C7.17311 20.0721 6.91767 19.8167 6.67786 19.5469C6.51405 19.3624 6.28764 19.2452 6.04242 19.218L3.22905 18.904C2.91529 18.3053 2.65552 17.6799 2.45282 17.0351L4.22083 14.8248C4.37521 14.6319 4.45247 14.3886 4.43763 14.142C4.41647 13.7818 4.41647 13.4207 4.43763 13.0605C4.45247 12.8139 4.37521 12.5706 4.22083 12.3777L2.45282 10.1636C2.65571 9.5189 2.91547 8.89347 3.22905 8.2947L6.04117 7.97574C6.2864 7.94852 6.51281 7.83132 6.67661 7.64681C6.91642 7.37705 7.17187 7.1216 7.44163 6.88179C7.62687 6.71788 7.74456 6.49095 7.77181 6.24511L8.08454 3.43298C8.68322 3.11923 9.30867 2.85946 9.95348 2.65675L12.1638 4.42476C12.3567 4.57914 12.6 4.6564 12.8466 4.64156C13.2068 4.6204 13.5679 4.6204 13.9281 4.64156C14.1747 4.6564 14.418 4.57914 14.6109 4.42476L16.8249 2.65675C17.4697 2.85964 18.0951 3.11941 18.6939 3.43298L19.0128 6.24511C19.04 6.49033 19.1572 6.71674 19.3418 6.88054C19.6115 7.12036 19.867 7.3758 20.1068 7.64556C20.2706 7.83007 20.497 7.94727 20.7422 7.97449L23.5556 8.28723C23.8693 8.88591 24.1291 9.51136 24.3318 10.1562L22.5638 12.3665C22.4079 12.561 22.3306 12.8068 22.347 13.0555H22.3507Z" fill="#393F4C"/>'},"report-settings-close":{viewBox:"0 0 16 16",content:'<path d="M14.3651 15.7956L8.0051 9.42563L1.6451 15.7956L0.225098 14.3756L6.5951 8.01563L0.225098 1.65563L1.6451 0.235626L8.0051 6.60563L14.3651 0.245626L15.7751 1.65563L9.4151 8.01563L15.7751 14.3756L14.3651 15.7956Z" fill="black" />'},"report-forms-impressions":{viewBox:"0 0 17 17",content:'<path d="M2.56976 7.33951C2.56976 6.8573 2.76132 6.39484 3.10229 6.05386C3.44327 5.71289 3.90573 5.52133 4.38794 5.52133C4.87015 5.52133 5.33261 5.71289 5.67359 6.05386C6.01456 6.39484 6.20612 6.8573 6.20612 7.33951C6.20612 7.82172 6.01456 8.28418 5.67359 8.62516C5.33261 8.96614 4.87015 9.15769 4.38794 9.15769C3.90573 9.15769 3.44327 8.96614 3.10229 8.62516C2.76132 8.28418 2.56976 7.82172 2.56976 7.33951ZM4.38794 6.61224C4.19505 6.61224 4.01007 6.68886 3.87368 6.82525C3.73729 6.96164 3.66067 7.14663 3.66067 7.33951C3.66067 7.5324 3.73729 7.71738 3.87368 7.85377C4.01007 7.99016 4.19505 8.06678 4.38794 8.06678C4.58082 8.06678 4.76581 7.99016 4.9022 7.85377C5.03859 7.71738 5.11521 7.5324 5.11521 7.33951C5.11521 7.14663 5.03859 6.96164 4.9022 6.82525C4.76581 6.68886 4.58082 6.61224 4.38794 6.61224ZM4.38794 10.6122C3.90573 10.6122 3.44327 10.8038 3.10229 11.1448C2.76132 11.4857 2.56976 11.9482 2.56976 12.4304C2.56976 12.9126 2.76132 13.3751 3.10229 13.7161C3.44327 14.057 3.90573 14.2486 4.38794 14.2486C4.87015 14.2486 5.33261 14.057 5.67359 13.7161C6.01456 13.3751 6.20612 12.9126 6.20612 12.4304C6.20612 11.9482 6.01456 11.4857 5.67359 11.1448C5.33261 10.8038 4.87015 10.6122 4.38794 10.6122ZM3.66067 12.4304C3.66067 12.2375 3.73729 12.0526 3.87368 11.9162C4.01007 11.7798 4.19505 11.7031 4.38794 11.7031C4.58082 11.7031 4.76581 11.7798 4.9022 11.9162C5.03859 12.0526 5.11521 12.2375 5.11521 12.4304C5.11521 12.6233 5.03859 12.8083 4.9022 12.9447C4.76581 13.0811 4.58082 13.1577 4.38794 13.1577C4.19505 13.1577 4.01007 13.0811 3.87368 12.9447C3.73729 12.8083 3.66067 12.6233 3.66067 12.4304ZM7.66067 7.15769C7.66067 7.01303 7.71813 6.87429 7.82043 6.772C7.92272 6.66971 8.06146 6.61224 8.20612 6.61224H13.6607C13.8053 6.61224 13.9441 6.66971 14.0464 6.772C14.1487 6.87429 14.2061 7.01303 14.2061 7.15769C14.2061 7.30236 14.1487 7.44109 14.0464 7.54339C13.9441 7.64568 13.8053 7.70315 13.6607 7.70315H8.20612C8.06146 7.70315 7.92272 7.64568 7.82043 7.54339C7.71813 7.44109 7.66067 7.30236 7.66067 7.15769ZM8.20612 11.7031C8.06146 11.7031 7.92272 11.7606 7.82043 11.8629C7.71813 11.9652 7.66067 12.1039 7.66067 12.2486C7.66067 12.3933 7.71813 12.532 7.82043 12.6343C7.92272 12.7366 8.06146 12.7941 8.20612 12.7941H13.6607C13.8053 12.7941 13.9441 12.7366 14.0464 12.6343C14.1487 12.532 14.2061 12.3933 14.2061 12.2486C14.2061 12.1039 14.1487 11.9652 14.0464 11.8629C13.9441 11.7606 13.8053 11.7031 13.6607 11.7031H8.20612ZM2.56976 3.52133C2.56976 3.37667 2.62722 3.23793 2.72952 3.13563C2.83181 3.03334 2.97055 2.97587 3.11521 2.97587H13.6607C13.8053 2.97587 13.9441 3.03334 14.0464 3.13563C14.1487 3.23793 14.2061 3.37667 14.2061 3.52133C14.2061 3.66599 14.1487 3.80473 14.0464 3.90702C13.9441 4.00932 13.8053 4.06678 13.6607 4.06678H3.11521C2.97055 4.06678 2.83181 4.00932 2.72952 3.90702C2.62722 3.80473 2.56976 3.66599 2.56976 3.52133ZM3.11521 0.43042C2.39189 0.43042 1.6982 0.717757 1.18674 1.22922C0.675276 1.74068 0.387939 2.43438 0.387939 3.15769V13.7031C0.387939 14.4265 0.675276 15.1202 1.18674 15.6316C1.6982 16.1431 2.39189 16.4304 3.11521 16.4304H13.6607C14.384 16.4304 15.0777 16.1431 15.5891 15.6316C16.1006 15.1202 16.3879 14.4265 16.3879 13.7031V3.15769C16.3879 2.43438 16.1006 1.74068 15.5891 1.22922C15.0777 0.717757 14.384 0.43042 13.6607 0.43042H3.11521ZM1.47885 3.15769C1.47885 2.7237 1.65125 2.30749 1.95813 2.00061C2.26501 1.69373 2.68122 1.52133 3.11521 1.52133H13.6607C14.0947 1.52133 14.5109 1.69373 14.8178 2.00061C15.1246 2.30749 15.297 2.7237 15.297 3.15769V13.7031C15.297 14.1371 15.1246 14.5534 14.8178 14.8602C14.5109 15.1671 14.0947 15.3395 13.6607 15.3395H3.11521C2.68122 15.3395 2.26501 15.1671 1.95813 14.8602C1.65125 14.5534 1.47885 14.1371 1.47885 13.7031V3.15769Z" fill="#8EA4B4"/>'},"report-forms-completions":{viewBox:"0 0 17 17",content:'<g clip-path="url(#clip0_20_1874)"><path d="M9.90867 5.76374H3.68645C3.56857 5.76374 3.45553 5.71692 3.37218 5.63357C3.28883 5.55022 3.242 5.43717 3.242 5.3193V3.54152C3.242 3.42365 3.28883 3.3106 3.37218 3.22725C3.45553 3.1439 3.56857 3.09708 3.68645 3.09708H9.90867C10.0265 3.09708 10.1396 3.1439 10.2229 3.22725C10.3063 3.3106 10.3531 3.42365 10.3531 3.54152V5.3193C10.3531 5.43717 10.3063 5.55022 10.2229 5.63357C10.1396 5.71692 10.0265 5.76374 9.90867 5.76374ZM4.13089 4.87485H9.46423V3.9593H4.13089V4.87485Z" fill="#8EA4B4"/><path d="M9.90867 6.6882H3.68645C3.56857 6.6882 3.45553 6.73503 3.37218 6.81838C3.28883 6.90173 3.242 7.01477 3.242 7.13265V8.87487C3.242 8.99274 3.28883 9.10579 3.37218 9.18914C3.45553 9.27249 3.56857 9.31931 3.68645 9.31931H8.73534L10.3531 7.67487V7.13265C10.3531 7.01477 10.3063 6.90173 10.2229 6.81838C10.1396 6.73503 10.0265 6.6882 9.90867 6.6882ZM9.46423 8.43042H4.13089V7.54154H9.46423V8.43042Z" fill="#8EA4B4"/><path d="M5.4909 14.4349V14.4082L5.63312 13.7904H2.35312V2.20819H11.242V6.76375L12.1309 5.92375V1.76375C12.1309 1.64588 12.0841 1.53283 12.0007 1.44948C11.9174 1.36613 11.8043 1.31931 11.6865 1.31931H1.90868C1.7908 1.31931 1.67776 1.36613 1.59441 1.44948C1.51106 1.53283 1.46423 1.64588 1.46423 1.76375V14.2082C1.46423 14.3261 1.51106 14.4391 1.59441 14.5225C1.67776 14.6058 1.7908 14.6526 1.90868 14.6526H5.46423C5.46783 14.5795 5.47675 14.5067 5.4909 14.4349Z" fill="#8EA4B4"/><path d="M10.3531 8.95041L10.0064 9.30152C10.0924 9.28379 10.1712 9.24098 10.2329 9.17849C10.2946 9.116 10.3364 9.03663 10.3531 8.95041Z" fill="#8EA4B4"/><path d="M3.242 12.4037C3.242 12.5216 3.28883 12.6347 3.37218 12.718C3.45553 12.8014 3.56857 12.8482 3.68645 12.8482H5.83756L5.97089 12.2704L6.02867 12.026V12.0037H4.13089V11.0971H6.94867L7.83756 10.2082H3.68645C3.56857 10.2082 3.45553 10.255 3.37218 10.3384C3.28883 10.4217 3.242 10.5348 3.242 10.6526V12.4037Z" fill="#8EA4B4"/><path d="M15.4598 7.83931L13.962 6.34153C13.8955 6.27488 13.8166 6.22199 13.7296 6.18591C13.6427 6.14983 13.5495 6.13126 13.4553 6.13126C13.3612 6.13126 13.268 6.14983 13.181 6.18591C13.0941 6.22199 13.0151 6.27488 12.9487 6.34153L6.85533 12.4704L6.35311 14.6082C6.3343 14.7004 6.33388 14.7955 6.35186 14.8878C6.36985 14.9802 6.40588 15.0682 6.4579 15.1466C6.50992 15.2251 6.5769 15.2925 6.65501 15.345C6.73313 15.3975 6.82083 15.4341 6.91311 15.4526C6.95893 15.4571 7.00507 15.4571 7.05089 15.4526C7.10537 15.4611 7.16084 15.4611 7.21533 15.4526L9.37089 14.9771L15.4598 8.87486C15.5263 8.80877 15.5791 8.73018 15.6151 8.64361C15.6511 8.55703 15.6697 8.46419 15.6697 8.37042C15.6697 8.27665 15.6511 8.18381 15.6151 8.09723C15.5791 8.01066 15.5263 7.93207 15.4598 7.86598V7.83931ZM8.91755 14.1682L7.29088 14.5282L7.68644 12.9149L12.2553 8.29709L13.5087 9.55042L8.91755 14.1682ZM14.0109 9.0482L12.7576 7.79486L13.4642 7.09709L14.7264 8.35931L14.0109 9.0482Z" fill="#8EA4B4"/></g><defs><clipPath id="clip0_20_1874"><rect width="16" height="16" fill="white" transform="translate(0.575317 0.43042)"/></clipPath></defs>'}};const{__:x}=wp.i18n,q1={name:"PageNavigator",props:{currentPage:{type:Number,required:!0},totalPages:{type:Number,required:!0}},computed:{canGoBack(){return this.currentPage>1},canGoNext(){return this.currentPage<this.totalPages},texts(){return{previous:x("Previous",this.$_textDomain),next:x("Next",this.$_textDomain)}}},methods:{goToPrev(){this.$emit("prev")},goToNext(){this.$emit("next")}}};var $1=function(){var t=this,e=t._self._c;return e("nav",{staticClass:"page-navigator"},[e("div",{staticClass:"page-navigator__desc"},[t._t("desc")],2),e("div",{staticClass:"page-navigator__widget"},[e("button",{staticClass:"button page-navigator__button",attrs:{role:"button",disabled:!t.canGoBack},on:{click:t.goToPrev}},[t._t("prev-button",function(){return[e("span",[t._v(t._s(t.texts.previous))])]})],2),e("span",{staticClass:"page-navigator__current"},[t._v(t._s(t.currentPage))]),e("button",{staticClass:"button page-navigator__button",attrs:{role:"button",disabled:!t.canGoNext},on:{click:t.goToNext}},[t._t("next-button",function(){return[e("span",[t._v(t._s(t.texts.next))])]})],2)])])},t2=[],e2=r(q1,$1,t2,!1,null,null,null,null);const i2=e2.exports,s2=Object.freeze(Object.defineProperty({__proto__:null,default:i2},Symbol.toStringTag,{value:"Module"})),n2={name:"ScreenHeader"};var a2=function(){var t=this,e=t._self._c;return e("div",{staticClass:"screen-header"},[e("div",{staticClass:"screen-header__start"},[t._t("header-start")],2),e("div",{staticClass:"screen-header__end"},[t._t("header-end")],2),e("div",{staticClass:"screen-header__bottom"},[t._t("header-bottom")],2)])},o2=[],r2=r(n2,a2,o2,!1,null,null,null,null);const l2=r2.exports,c2=Object.freeze(Object.defineProperty({__proto__:null,default:l2},Symbol.toStringTag,{value:"Module"})),d2={name:"ComparisonTable",props:{columns:{type:Array,required:!0},rows:{type:Array,required:!0}}};var C2=function(){var t=this,e=t._self._c;return e("section",{staticClass:"comparison-table"},[e("header",{staticClass:"comparison-table__header"},[e("div",{staticClass:"content"},[t._t("header")],2)]),e("section",{staticClass:"comparison-table__table"},[e("table",[e("thead",[e("tr",t._l(t.columns,function(i){return e("th",{key:i.key,attrs:{scope:"col"},domProps:{innerHTML:t._s(i.label)}})}),0)]),e("tbody",t._l(t.rows,function(i,n){return e("tr",{key:n},[t._t("rows",null,{row:i})],2)}),0)])]),e("footer",{staticClass:"comparison-table__footer"},[e("div",{staticClass:"content"},[t._t("footer")],2)])])},u2=[],p2=r(d2,C2,u2,!1,null,null,null,null);const h2=p2.exports,g2=Object.freeze(Object.defineProperty({__proto__:null,default:h2},Symbol.toStringTag,{value:"Module"})),m2={name:"ButtonDropdown",props:{buttonClass:{type:[String]},choices:{type:Array,required:!0,validator(s){return s.every(t=>t.hasOwnProperty("text")&&t.hasOwnProperty("handle"))}},selectedChoice:{type:Object,default:()=>({id:null,name:"",background_color:null,text_color:null})}},data(){return{isOpen:!1,selected:null}},methods:{toggle(){this.isOpen=!this.isOpen},open(){this.isOpen=!0},close(){this.isOpen=!1},onChoiceClick(s){this.isOpen=!1,s.handle(s),this.selected=s},getButtonStyles(s){let t={};return s.background_color&&(t.backgroundColor=s.background_color),s.text_color&&(t.color=s.text_color),t}},computed:{getSelectedStyles(){let s={};return this.selected&&this.selected.background_color&&(s.backgroundColor=this.selected.background_color),this.selected&&this.selected.text_color&&(s.color=this.selected.text_color),s}},mounted(){this.selectedChoice&&(this.selected=this.selectedChoice)}};var f2=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:t.close,expression:"close"}],staticClass:"button-dropdown"},[e("button",{class:"button "+t.buttonClass+(!t.selected||!t.selected.id?" default":""),style:t.getSelectedStyles,attrs:{type:"button"},on:{keydown:function(i){return!i.type.indexOf("key")&&t._k(i.keyCode,"esc",27,i.key,["Esc","Escape"])?null:t.close.apply(null,arguments)},click:t.toggle}},[t._t("button")],2),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}],staticClass:"button-dropdown__list"},t._l(t.choices,function(i,n){return e("button",{key:n,class:"button button-dropdown__item"+(t.selected&&t.selected.id===i.id?" selected":""),attrs:{type:"button",disabled:i.disabled},on:{click:function(a){return t.onChoiceClick(i)}}},[t._t("item-content",function(){return[e("span",{style:t.getButtonStyles(i)},[t._v(t._s(i.text))])]},{item:i})],2)}),0)])},v2=[],_2=r(m2,f2,v2,!1,null,null,null,null);const A2=_2.exports,b2=Object.freeze(Object.defineProperty({__proto__:null,default:A2},Symbol.toStringTag,{value:"Module"})),S={props:{disabled:{type:Boolean,default:!1},inputValue:{type:[String,Number]},value:{type:[Boolean,Array,String,Number],default:!1},faux:{type:Boolean,default:!1}},computed:{innerValue:{get(){return this.faux?this.inputValue!==void 0?this.inputValue:!0:this.value},set(s){this.faux||this.$emit("input",s)}},fieldId(){return this.$attrs.id?this.$attrs.id:`field-toggle-${this._uid}`},fieldBindings(){return{...this.$attrs,id:this.fieldId}}}},k2={inheritAttrs:!1,name:"Checkbox",mixins:[S]};var y2=function(){var t=this,e=t._self._c;return e("label",{staticClass:"checkbox",class:{"is-disabled":t.disabled},attrs:{for:t.fieldId}},[e("input",t._b({directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"checkbox",disabled:t.disabled},domProps:{value:t.inputValue,checked:Array.isArray(t.innerValue)?t._i(t.innerValue,t.inputValue)>-1:t.innerValue},on:{change:function(i){var n=t.innerValue,a=i.target,l=!!a.checked;if(Array.isArray(n)){var c=t.inputValue,C=t._i(n,c);a.checked?C<0&&(t.innerValue=n.concat([c])):C>-1&&(t.innerValue=n.slice(0,C).concat(n.slice(C+1)))}else t.innerValue=l}}},"input",t.fieldBindings,!1)),e("span",{staticClass:"checkbox__skin"},[e("svg",{attrs:{width:"11",height:"8",viewBox:"0 0 11 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M3.9627 7.84921L0.831445 4.72042C0.743945 4.63246 0.700195 4.51937 0.700195 4.38115C0.700195 4.24293 0.743945 4.12984 0.831445 4.04188L1.5252 3.36335C1.6127 3.26283 1.71895 3.21256 1.84395 3.21256C1.98145 3.21256 2.1002 3.26283 2.2002 3.36335L4.3002 5.47434L8.8002 0.950785C8.9002 0.850261 9.0127 0.799999 9.1377 0.799999C9.2752 0.799999 9.3877 0.850261 9.4752 0.950785L10.1689 1.62932C10.2564 1.71728 10.3002 1.83037 10.3002 1.96859C10.3002 2.10681 10.2564 2.21989 10.1689 2.30785L4.6377 7.84921C4.5502 7.94974 4.4377 8 4.3002 8C4.1627 8 4.0502 7.94974 3.9627 7.84921Z",fill:"currentColor"}})])]),e("span",[t._t("default"),t._t("after-label")],2)])},w2=[],S2=r(k2,y2,w2,!1,null,null,null,null);const I2=S2.exports,E2=Object.freeze(Object.defineProperty({__proto__:null,default:I2},Symbol.toStringTag,{value:"Module"})),x2={inheritAttrs:!1,name:"ColorPicker",props:{value:{type:String}},computed:{skinStyles(){return{backgroundColor:this.value}},innerValue:{get(){return this.value},set(s){this.$emit("input",s)}}}};var L2=function(){var t=this,e=t._self._c;return e("label",{staticClass:"color-picker"},[e("input",t._b({directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"color"},domProps:{value:t.innerValue},on:{input:function(i){i.target.composing||(t.innerValue=i.target.value)}}},"input",t.$attrs,!1)),e("span",{staticClass:"color-picker__skin",style:t.skinStyles}),e("input",{directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],staticClass:"color-picker__desc",attrs:{type:"text",autocomplete:"false",spellcheck:"false",disabled:t.$attrs.disabled},domProps:{value:t.innerValue},on:{input:function(i){i.target.composing||(t.innerValue=i.target.value)}}})])},R2=[],Z2=r(x2,L2,R2,!1,null,null,null,null);const V2=Z2.exports,M2=Object.freeze(Object.defineProperty({__proto__:null,default:V2},Symbol.toStringTag,{value:"Module"}));const B2={inheritAttrs:!1,name:"ColorPickerPredefined",props:{value:{type:String},predefined:{type:Array,default:()=>["#E9AF00","#FF893A","#F072B2","#8D87E5","#1EC185","#26A69A","#754FF6","#1230DF","#8BB1FA","#5195D8","#497FB2","#55B9D1","#E432E3","#D32D1F","#8D0F03","#A8803D","#A8AFB6","#697683"]}},data(){return{isOpen:!1}},computed:{innerValue:{get(){return this.value},set(s){this.$emit("input",s)}}},methods:{chooseColor(s){this.innerValue=s,this.isOpen=!1},itemClass(s){return this.innerValue===s?"selected":""},toggleList(){this.isOpen=!this.isOpen},showList(){this.isOpen=!0},hideList(){this.isOpen=!1}}};var J2=function(){var t=this,e=t._self._c;return e("div",{staticClass:"color-picker-predefined"},[e("input",t._b({directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"hidden"},domProps:{value:t.innerValue},on:{input:function(i){i.target.composing||(t.innerValue=i.target.value)}}},"input",t.$attrs,!1)),e("a",{class:"color-picker-predefined-"+(t.isOpen?"hide":"show"),attrs:{href:"#"},on:{click:function(i){return i.preventDefault(),t.toggleList.apply(null,arguments)}}}),e("div",{staticClass:"color-picker-predefined__list main-list"},t._l(t.predefined.slice(0,6),function(i,n){return e("a",{key:n,class:t.itemClass(i),style:{backgroundColor:i},attrs:{href:"#"},on:{click:function(a){return a.preventDefault(),t.chooseColor(i)}}})}),0),t.isOpen?e("div",{staticClass:"color-picker-predefined__list additional-list"},t._l(t.predefined.slice(6,18),function(i,n){return e("a",{key:n,class:t.itemClass(i),style:{backgroundColor:i},attrs:{href:"#"},on:{click:function(a){return a.preventDefault(),t.chooseColor(i)}}})}),0):t._e()])},F2=[],H2=r(B2,J2,F2,!1,null,null,null,null);const T2=H2.exports,U2=Object.freeze(Object.defineProperty({__proto__:null,default:T2},Symbol.toStringTag,{value:"Module"})),{__:L}=wp.i18n,N2={inheritAttrs:!1,name:"EditableText",data(){return{originalValue:"",isEditing:!1}},props:{value:{type:String},tag:{type:String,default:"p"},elementClass:{type:[String,Array,Object]},editButtonClass:{type:[String,Array,Object],default(){return["button",`${this.$_classPrefix}-button`]}},saveButtonClass:{type:[String,Array,Object],default(){return["button",`${this.$_classPrefix}-button`]}}},computed:{texts(){return{edit:L("Edit",this.$_textDomain),save:L("Save",this.$_textDomain)}}},methods:{focusElement(){const{element:s}=this.$refs;s.focus();const t=document.createRange();t.selectNodeContents(s),t.collapse(!1);const e=window.getSelection();e.removeAllRanges(),e.addRange(t)},enableEdit(){this.isEditing=!0,this.$nextTick(()=>{this.focusElement()})},cancelEdit(){this.isEditing=!1,this.$refs.element.innerText=this.originalValue},saveEdit(){this.isEditing=!1;const s=this.$refs.element.innerText;s.length>0?(this.originalValue=s,this.$emit("input",s)):this.$refs.element.innerText=this.originalValue}},mounted(){this.originalValue=this.$refs.element.innerText}};var D2=function(){var t=this,e=t._self._c;return e("div",{staticClass:"editable-text",class:{"is-editing":t.isEditing}},[e(t.tag,{ref:"element",tag:"component",staticClass:"editable-text__content",class:t.elementClass,attrs:{contenteditable:t.isEditing},on:{keydown:[function(i){return!i.type.indexOf("key")&&t._k(i.keyCode,"enter",13,i.key,"Enter")?null:(i.preventDefault(),t.saveEdit.apply(null,arguments))},function(i){return!i.type.indexOf("key")&&t._k(i.keyCode,"esc",27,i.key,["Esc","Escape"])?null:(i.preventDefault(),t.cancelEdit.apply(null,arguments))}]}},[t._v(" "+t._s(t.value)+" ")]),t.isEditing?t._e():e("button",{staticClass:"editable-text__button",class:t.editButtonClass,on:{click:t.enableEdit}},[t._t("edit-button",function(){return[t._v(" "+t._s(t.texts.edit)+" ")]})],2),t.isEditing?e("button",{staticClass:"editable-text__button",class:t.saveButtonClass,on:{click:t.saveEdit}},[t._t("save-button",function(){return[t._v(" "+t._s(t.texts.save)+" ")]})],2):t._e()],1)},j2=[],G2=r(N2,D2,j2,!1,null,null,null,null);const Q2=G2.exports,O2=Object.freeze(Object.defineProperty({__proto__:null,default:Q2},Symbol.toStringTag,{value:"Module"}));const{__:b}=wp.i18n,P2={name:"ImageUploader",props:{value:{type:Object,default:()=>({})},uploaderSettings:{type:Object,default:()=>({})}},data(){return{}},computed:{texts(){return{uploadImage:b("Select Media",this.$textDomain),chooseAnother:b("Change Media",this.$_textDomain)}},hasValue(){return this.value&&Object.keys(this.value).length!==0},uploadedBtnText(){return!Object.values(this.value)[0]||!Object.values(this.value)[0].name?this.texts.chooseAnother:Object.values(this.value)[0].name.slice(0,13)}},methods:{reset(){this.$emit("input",[])},uploadImage(){const s={uploaderTitle:b("Select or upload image",this.$_textDomain),uploaderButton:b("Set Image",this.$_textDomain),multiple:!1,...this.uploaderSettings},t=wp.media({title:s.uploaderTitle,button:{text:s.uploaderButton},multiple:s.multiple}).on("select",()=>{const e=t.state().get("selection");let i={};e.forEach(n=>{i[n.id]={url:n.changed.url,name:n.changed.filename}}),this.$emit("input",i)}).open()}}};var W2=function(){var t=this,e=t._self._c;return e("div",[t.hasValue?t._e():e("div",{staticClass:"inline-fields"},[e("div",{staticClass:"field"},[e("a",{staticClass:"monsterinsights-upload-button",attrs:{href:"#"},domProps:{textContent:t._s(t.texts.uploadImage)},on:{click:function(i){return i.preventDefault(),t.uploadImage.apply(null,arguments)}}})])]),t._t("before-preview"),t.hasValue?e("div",{staticClass:"inline-fields"},[e("div",{staticClass:"field"},[e("a",{staticClass:"monsterinsights-upload-button",attrs:{href:"#"},domProps:{textContent:t._s(t.uploadedBtnText)},on:{click:function(i){return i.preventDefault(),t.uploadImage.apply(null,arguments)}}})])]):t._e()],2)},Y2=[],z2=r(P2,W2,Y2,!1,null,null,null,null);const X2=z2.exports,K2=Object.freeze(Object.defineProperty({__proto__:null,default:X2},Symbol.toStringTag,{value:"Module"})),q2={name:"InputSelect",components:{Multiselect:U},props:{options:{type:Array,required:!0},forced:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},value:{type:[Object,Array,String]}},computed:{innerValue:{get(){return this.value},set(s){const t=[...s];for(const e of this.forced)t.findIndex(i=>i.value===e.value)===-1&&t.unshift(e);this.$emit("input",t)}}},methods:{isForced(s){return this.forced.findIndex(t=>t.value===s)>-1},tagClass(s){const t=["multiselect__tag"];return this.isForced(s)&&t.push("is-forced"),t}}};var $2=function(){var t=this,e=t._self._c;return e("div",{staticClass:"input-select field"},[e("div",{staticClass:"input-select__label"},[t._t("label")],2),e("div",{staticClass:"input-select__field"},[e("multiselect",{attrs:{options:t.options,multiple:t.multiple,"track-by":"value",label:"label",searchable:!1,selectLabel:"",selectedLabel:"",deselectLabel:"",disabled:t.disabled},scopedSlots:t._u([{key:"selection",fn:function({remove:i,values:n}){return[e("div",{staticClass:"multiselect__tags-wrap"},t._l(n,function(a,l){return e("span",{key:l,class:t.tagClass(a.value),on:{mousedown:function(c){c.preventDefault()}}},[e("span",{domProps:{textContent:t._s(a.label)}}),e("i",{staticClass:"multiselect__tag-icon",attrs:{"aria-hidden":"true",tabindex:"0",role:"button"},on:{keypress:function(c){return!c.type.indexOf("key")&&t._k(c.keyCode,"enter",13,c.key,"Enter")?null:(c.preventDefault(),i(a))},mousedown:function(c){return c.preventDefault(),i(a)}}})])}),0)]}}]),model:{value:t.innerValue,callback:function(i){t.innerValue=i},expression:"innerValue"}})],1)])},tt=[],et=r(q2,$2,tt,!1,null,null,null,null);const it=et.exports,st=Object.freeze(Object.defineProperty({__proto__:null,default:it},Symbol.toStringTag,{value:"Module"})),{__:y}=wp.i18n,nt={name:"OptionsCreator",props:{fieldsPlaceholder:{type:String},name:{type:String,required:!0},value:{type:Array},minOptions:{type:Number,default:2},maxOptions:{type:Number,default:-1}},computed:{canAddMore(){return this.maxOptions===-1||this.value&&this.value.length<this.maxOptions},canRemove(){return this.value.length>this.minOptions},texts(){return{possibleAnswers:y("Possible Answers",this.$_textDomain),addAnswer:y("Add Answer",this.$_textDomain),remove:y("Remove",this.$_textDomain)}}},methods:{addAnswer(){if(!this.canAddMore)return;const s=this.value;s.push(""),this.$emit("input",s)},removeAnswerByIndex(s){if(!this.canRemove)return;const t=this.value;t.splice(s,1),this.$emit("input",t)},updateAnswerByIndex(s,t){const e=this.value;e[s]=t.trim(),this.$emit("input",e)}},mounted(){if(!this.value||!this.value.length){const s=Array(this.minOptions).fill("",0);this.$emit("input",s)}}};var at=function(){var t=this,e=t._self._c;return e("div",{staticClass:"options-creator"},[e("div",{staticClass:"field"},[e("label",[t._t("label",function(){return[t._v(t._s(t.texts.possibleAnswers))]})],2)]),t._l(t.value,function(i,n){return e("div",{key:n,staticClass:"field"},[e("input",{attrs:{type:"text",placeholder:t.fieldsPlaceholder},domProps:{value:i},on:{input:a=>{t.updateAnswerByIndex(n,a.target.value)}}}),n>=t.minOptions?e("button",{staticClass:"options-creator__remove",on:{click:function(a){return t.removeAnswerByIndex(n)}}},[t._t("button-remove",function(){return[t._v(" "+t._s(t.texts.remove)+" ")]})],2):t._e()])}),t.canAddMore?e("div",{staticClass:"field"},[e("button",{staticClass:"options-creator__add",attrs:{type:"button"},on:{click:t.addAnswer}},[t._t("button-add",function(){return[t._v(" "+t._s(t.texts.addAnswer)+" ")]})],2)]):t._e()],2)},ot=[],rt=r(nt,at,ot,!1,null,null,null,null);const lt=rt.exports,ct=Object.freeze(Object.defineProperty({__proto__:null,default:lt},Symbol.toStringTag,{value:"Module"})),dt={inheritAttrs:!1,name:"Radio",mixins:[S]};var Ct=function(){var t=this,e=t._self._c;return e("label",{staticClass:"radio",class:{"is-disabled":t.disabled,"is-selected":t.innerValue===t.inputValue},attrs:{for:t.fieldId}},[e("input",t._b({directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"radio",disabled:t.disabled},domProps:{value:t.inputValue,checked:t._q(t.innerValue,t.inputValue)},on:{change:function(i){t.innerValue=t.inputValue}}},"input",t.fieldBindings,!1)),e("span",{staticClass:"radio__skin"}),e("span",{staticClass:"radio__content"},[t._t("default")],2)])},ut=[],pt=r(dt,Ct,ut,!1,null,null,null,null);const ht=pt.exports,gt=Object.freeze(Object.defineProperty({__proto__:null,default:ht},Symbol.toStringTag,{value:"Module"})),mt={inheritAttrs:!1,name:"RadioButtons",props:{value:{type:[String,Number,Boolean]},buttonClass:{type:[String,Object,Array]},fieldNamePrefix:{type:String,required:!0},options:{type:Array,required:!0,validator(s){return s.every(t=>t.hasOwnProperty("label")&&t.hasOwnProperty("value"))}}},computed:{innerValue:{get(){return this.value},set(s){this.$emit("input",s)}}}};var ft=function(){var t=this,e=t._self._c;return e("div",{staticClass:"radio-buttons"},t._l(t.options,function(i){return e("label",{key:i.value,staticClass:"radio-button",class:[t.buttonClass,{"is-disabled":i.disabled}],attrs:{for:`${t.fieldNamePrefix}-${i.value}`}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{id:`${t.fieldNamePrefix}-${i.value}`,type:"radio",disabled:i.disabled},domProps:{value:i.value,checked:t._q(t.innerValue,i.value)},on:{change:function(n){t.innerValue=i.value}}}),e("span",{staticClass:"radio-button__skin"},[t._t("default",function(){return[t._v(t._s(i.label))]},{option:i})],2)])}),0)},vt=[],_t=r(mt,ft,vt,!1,null,null,null,null);const At=_t.exports,bt=Object.freeze(Object.defineProperty({__proto__:null,default:At},Symbol.toStringTag,{value:"Module"})),kt={name:"RadioCards",props:{options:{type:Array,required:!0,validator:s=>s.every(t=>t.hasOwnProperty("value"))},value:{type:[String,Number]}},computed:{innerValue:{get(){return this.value},set(s){this.$emit("input",s)}}}};var yt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"radio-cards"},t._l(t.options,function(i,n){return e("label",{key:n,staticClass:"radio-card"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"radio"},domProps:{value:i.value,checked:t._q(t.innerValue,i.value)},on:{change:function(a){t.innerValue=i.value}}}),e("span",{staticClass:"radio-card__content"},[t._t("card-content",null,{item:i})],2)])}),0)},wt=[],St=r(kt,yt,wt,!1,null,null,null,null);const It=St.exports,Et=Object.freeze(Object.defineProperty({__proto__:null,default:It},Symbol.toStringTag,{value:"Module"})),xt=(s,t=300)=>{let e;return(...i)=>{clearTimeout(e),e=setTimeout(()=>{s.apply(globalThis,i)},t)}},{__:R}=wp.i18n,Lt={name:"SearchSelect",props:{disabled:{type:Boolean,default:!1},value:{type:Object},getItems:{type:Function,required:!0},emptyStateText:{type:String,default:function(){return R("Choose",this.$_textDomain)}},searchPlaceholder:{type:String,default:function(){return R("Search...",this.$_textDomain)}}},data(){return{isOpen:!1,firstOpened:!1,search:"",items:[]}},methods:{open(){this.isOpen=!0,this.$refs.search.focus(),this.firstOpened||this.getItems().then(s=>{this.items=s})},close(){this.isOpen=!1,this.search=""},toggle(){return this.isOpen?this.close():this.open()},select(s){this.close(),this.$emit("input",s)}},created(){this.unwatch=this.$watch("search",xt(async s=>{s.length>0&&(this.items=await this.getItems(s))}))},beforeDestroy(){this.unwatch()}};var Rt=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:t.close,expression:"close"}],staticClass:"search-select",class:{"is-disabled":t.disabled}},[e("a",{staticClass:"search-select__fake",attrs:{href:"javascript:",role:"button"},on:{click:t.toggle}},[t.value?e("span",[t._v(t._s(t.value.label))]):e("span",[t._v(t._s(t.emptyStateText))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}],staticClass:"search-select__body"},[e("div",{staticClass:"search-select__field"},[t._t("before-input"),e("input",{directives:[{name:"model",rawName:"v-model",value:t.search,expression:"search"}],ref:"search",attrs:{placeholder:t.searchPlaceholder,type:"text",disabled:t.disabled},domProps:{value:t.search},on:{input:function(i){i.target.composing||(t.search=i.target.value)}}})],2),e("div",{staticClass:"search-select__list"},t._l(t.items,function(i){return e("a",{key:i.id,attrs:{href:"javascript:"},on:{click:function(n){return t.select(i)}}},[t._v(t._s(i.label))])}),0)])])},Zt=[],Vt=r(Lt,Rt,Zt,!1,null,null,null,null);const Mt=Vt.exports,Bt=Object.freeze(Object.defineProperty({__proto__:null,default:Mt},Symbol.toStringTag,{value:"Module"})),{__:Jt,sprintf:Ft}=wp.i18n,Ht={name:"SelectionCards",props:{inputName:{type:String,required:!0},value:{type:[String,Number]},options:{type:Array,required:!0},valueKey:{type:String,default:"value"},titleKey:{type:String,default:"title"}},data(){return{innerValue:""}},methods:{getLocalizedText(s,t=[]){return Ft(Jt(s,this.$_textDomain),t)}},watch:{value(s){this.innerValue=s},innerValue(s){this.$emit("input",s)}},beforeMount(){this.value!==void 0&&(this.innerValue=this.value)}};var Tt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"selection-cards"},t._l(t.options,function(i,n){return e("div",{key:n,staticClass:"selection-card",class:{"is-selected":t.value===i[t.valueKey],"is-disabled":i.disabled}},[e("label",{staticClass:"selection-card__label",attrs:{for:`radio-${t.inputName}-${i[t.valueKey]}`}},[t._v(t._s(t.getLocalizedText("Select %s",i[t.titleKey])))]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{id:`radio-${t.inputName}-${i[t.valueKey]}`,type:"radio",name:"cards",disabled:i.disabled},domProps:{value:i[t.valueKey],checked:t._q(t.innerValue,i[t.valueKey])},on:{change:function(a){t.innerValue=i[t.valueKey]}}}),e("div",{staticClass:"admin-card"},[e("div",{staticClass:"admin-card__body"},[t._t("card-contents",null,{item:i,selected:t.value===i[t.valueKey]})],2)])])}),0)},Ut=[],Nt=r(Ht,Tt,Ut,!1,null,null,null,null);const Dt=Nt.exports,jt=Object.freeze(Object.defineProperty({__proto__:null,default:Dt},Symbol.toStringTag,{value:"Module"})),Gt={inheritAttrs:!1,name:"Toggle",mixins:[S]};var Qt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"field toggle",class:{"is-faux":t.faux}},[e("label",{staticClass:"toggle__field",attrs:{for:t.fieldId}},[e("input",t._b({directives:[{name:"model",rawName:"v-model",value:t.innerValue,expression:"innerValue"}],attrs:{type:"checkbox",disabled:t.disabled||t.faux},domProps:{checked:Array.isArray(t.innerValue)?t._i(t.innerValue,null)>-1:t.innerValue},on:{change:function(i){var n=t.innerValue,a=i.target,l=!!a.checked;if(Array.isArray(n)){var c=null,C=t._i(n,c);a.checked?C<0&&(t.innerValue=n.concat([c])):C>-1&&(t.innerValue=n.slice(0,C).concat(n.slice(C+1)))}else t.innerValue=l}}},"input",t.fieldBindings,!1)),e("span",{staticClass:"toggle__skin"}),e("span",{staticClass:"toggle__label"},[t._t("default")],2)]),t.$slots.help?e("div",{staticClass:"toggle__help"},[t._t("help")],2):t._e()])},Ot=[],Pt=r(Gt,Qt,Ot,!1,null,null,null,null);const Wt=Pt.exports,Yt=Object.freeze(Object.defineProperty({__proto__:null,default:Wt},Symbol.toStringTag,{value:"Module"})),{__:zt}=wp.i18n,Xt={name:"ExpandableCard",props:{startOpen:{type:Boolean,default:!1},slim:{type:Boolean,default:!1},locked:{type:Boolean,default:!1}},data(){return{isOpen:!1}},computed:{texts(){return{toggle:zt("Toggle",this.$_textDomain)}}},methods:{toggleCard(){this.isOpen?this.closeCard():this.openCard()},openCard(){this.locked||(this.isOpen=!0)},closeCard(){this.isOpen=!1}},mounted(){this.startOpen&&!this.locked&&this.openCard()}};var Kt=function(){var t=this,e=t._self._c;return e("article",{staticClass:"expandable-card",class:{"is-open":t.isOpen}},[e("header",{staticClass:"expandable-card__header"},[e("div",{staticClass:"expandable-card__title"},[t._t("title")],2),e("button",{staticClass:"expandable-card__toggle",attrs:{type:"button"},on:{click:t.toggleCard}},[t._t("toggle",function(){return[e("span",[t._v(t._s(t.texts.toggle))])]})],2)]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}]},[t._t("before-content"),e("div",{staticClass:"expandable-card__content",class:{"is-slim":t.slim}},[t._t("content")],2),e("div",{staticClass:"expandable-card__footer"},[t._t("footer")],2)],2)])},qt=[],$t=r(Xt,Kt,qt,!1,null,null,null,null);const t6=$t.exports,e6=Object.freeze(Object.defineProperty({__proto__:null,default:t6},Symbol.toStringTag,{value:"Module"})),{__:i6}=wp.i18n,s6={name:"AdminHeader",props:{workflow:{type:Object},useSmallLogo:{type:Boolean,default:!1},type:{type:String,default:"default",validator:function(s){return["default","workflow","custom"].indexOf(s)!==-1}},logo:{type:String},logoSmall:{type:String}},computed:{texts(){return{close:i6("Close",this.$_textDomain)}}},methods:{onClose(){this.$emit("close")}}};var n6=function(){var t=this,e=t._self._c;return e("header",{staticClass:"admin-header",class:`admin-header--${t.type}`},[t.type==="workflow"?[e("figure",{staticClass:"admin-header__branding is-small"},[e("img",{attrs:{src:t.logoSmall,alt:""}})]),e(`${t.$_componentPrefix}AdminHeaderWorkflow`,{tag:"component",attrs:{workflow:t.workflow}},[e("template",{slot:"workflow-step-separator"},[t._t("workflow-step-separator")],2)],2),e("button",{staticClass:"admin-header__close",on:{click:t.onClose}},[t._t("header-close",function(){return[e("span",[t._v(t._s(t.texts.close))])]})],2)]:t.type==="custom"?t._t("default"):[e("figure",{staticClass:"admin-header__branding"},[e("img",{attrs:{src:t.logo,alt:""}})]),e("div",{staticClass:"admin-header__end"},[t._t("header-end")],2)]],2)},a6=[],o6=r(s6,n6,a6,!1,null,null,null,null);const r6=o6.exports,l6=Object.freeze(Object.defineProperty({__proto__:null,default:r6},Symbol.toStringTag,{value:"Module"})),c6={name:"AdminHeaderWorkflow",components:{},props:{workflow:{type:Object}}};var d6=function(){var t=this,e=t._self._c;return e("nav",{staticClass:"admin-header__nav"},[e("div",{staticClass:"admin-header__nav-start"},[e("ul",{staticClass:"admin-header-steps"},t._l(t.workflow.steps,function(i,n){return e(`${t.$_componentPrefix}AdminHeaderWorkflowItem`,{key:i.key,tag:"component",attrs:{step:i,"is-current":i.key===t.workflow.currentStep,"is-last":n!==t.workflow.steps.length-1}},[t._t("workflow-step-separator")],2)}),1)]),e("div",{staticClass:"admin-header__nav-end"},t._l(t.workflow.actions,function(i,n){return e("button",{key:n,staticClass:"button",class:`${t.$_classPrefix}-button is-small`,attrs:{disabled:i.disabled},on:{click:i.handler}},[t._v(" "+t._s(i.label)+" ")])}),0)])},C6=[],u6=r(c6,d6,C6,!1,null,null,null,null);const p6=u6.exports,h6=Object.freeze(Object.defineProperty({__proto__:null,default:p6},Symbol.toStringTag,{value:"Module"})),g6={name:"AdminHeaderWorkflowItem",props:{step:{type:Object,required:!0},isCurrent:{type:Boolean,default:!1},isLast:{type:Boolean,default:!1}}};var m6=function(){var t=this,e=t._self._c;return e("li",{staticClass:"admin-header-step-item",class:{"is-current":t.isCurrent,"is-clickable":t.step.hasOwnProperty("click")}},[e("a",{attrs:{href:"javascript:"},on:{click:()=>{t.step.click&&t.step.click()}}},[t._v(t._s(t.step.title))]),t.isLast?e("span",{staticClass:"admin-header-step-item__separator"},[t._t("default")],2):t._e()])},f6=[],v6=r(g6,m6,f6,!1,null,null,null,null);const _6=v6.exports,A6=Object.freeze(Object.defineProperty({__proto__:null,default:_6},Symbol.toStringTag,{value:"Module"})),b6={disableAutoload:!0,name:"NotificationBanner",props:{eventBus:{type:Object,required:!0},type:{type:String,default:"info",validator:function(s){return["info","success","warning"].indexOf(s)!==-1}},content:{type:String},timeout:{type:Number,default:-1},parentEl:{type:[String,Element],default:()=>document.body},slots:{type:Object},exclusive:{type:Boolean,default:!0}},data(){return{isActive:!1}},computed:{bannerClasses(){return[`is-${this.type}`]},beforeContentComponent(){const s=this.slots.beforeContent;return s?{template:s}:null},contentComponent(){const s=`<div>${this.content}</div>`;return s?{template:s}:null}},methods:{close(){this.isActive=!1,setTimeout(()=>{this.$el.remove()},2e3)}},beforeMount(){this.parentEl.appendChild(this.$el),this.exclusive&&this.eventBus.$emit("add-banner",this._uid)},mounted(){this.isActive=!0,this.eventBus.$on("add-banner",s=>{s!==this._uid&&this.close()}),this.timeout>-1&&setTimeout(this.close,this.timeout*1e3)}};var k6=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"slide"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.isActive,expression:"isActive"}],staticClass:"notification-banner",class:t.bannerClasses},[e("div",{staticClass:"notification-banner__content"},[e(t.beforeContentComponent,{tag:"div"}),e(t.contentComponent,{tag:"div"})],1)])])},y6=[],w6=r(b6,k6,y6,!1,null,null,null,null);const M=w6.exports,S6=Object.freeze(Object.defineProperty({__proto__:null,default:M},Symbol.toStringTag,{value:"Module"})),{__:I6}=wp.i18n,E6={disableAutoload:!0,name:"NotificationItem",props:{isDismissed:{type:Boolean,default:!1},notification:{type:Object,required:!0}},computed:{actions(){return Array.isArray(this.notification.buttons)?this.notification.buttons:Object.values(this.notification.buttons)},texts(){return{dismiss:I6("Dismiss",this.$_textDomain)}}},methods:{dismiss(){this.$emit("dismiss",this.notification.id)},performAction(s){this.$emit("action",s)}}};var x6=function(){var t=this,e=t._self._c;return e("article",{staticClass:"notification-item",class:`is-${t.notification.category}`},[e("span",{staticClass:"notification-item__icon"},[e("span",{directives:[{name:"icon",rawName:"v-icon:[`notification-${notification.icon}`]",arg:`notification-${t.notification.icon}`}]})]),e("div",{staticClass:"notification-item__content"},[e("header",{staticClass:"notification-item__header"},[e("h6",{staticClass:"notification-item__title"},[t._v(" "+t._s(t.notification.title)+" ")]),e("span",{staticClass:"notification-item__date"},[t._v(t._s(t.notification.active_from.split(" ")[0]))])]),e("div",{staticClass:"notification-item__body"},[e("div",{domProps:{innerHTML:t._s(t.notification.content)}})]),e("div",{staticClass:"notification-item__actions"},[t._l(t.actions,function(i,n){return[i.type==="action"?e("button",{key:n,staticClass:"button is-secondary",class:`${t.$_classPrefix}-button`,attrs:{type:"button"},on:{click:function(a){return t.performAction(i)}}},[t._v(" "+t._s(i.text)+" ")]):e("a",{key:n,staticClass:"button is-secondary",class:`${t.$_classPrefix}-button`,attrs:{href:i.url,target:i.is_external?"_blank":"_self",rel:i.is_external?"noopener":""},on:{click:function(a){return t.$emit("action-clicked")}}},[t._v(" "+t._s(i.text)+" ")])]}),t.isDismissed?t._e():e("button",{staticClass:"button is-link",class:`${t.$_classPrefix}-button`,on:{click:t.dismiss}},[t._v(" "+t._s(t.texts.dismiss)+" ")])],2)])])},L6=[],R6=r(E6,x6,L6,!1,null,null,null,null);const B=R6.exports,Z6=Object.freeze(Object.defineProperty({__proto__:null,default:B},Symbol.toStringTag,{value:"Module"})),{__:f}=wp.i18n,V6={name:"NotificationsDrawer",components:{NotificationItem:B},props:{isOpen:{type:Boolean,default:!1},activeNotifications:{type:Array,required:!0},dismissedNotifications:{type:Array,required:!0}},data(){return{showDismissed:!1}},computed:{displayedNotifications(){return this.showDismissed?this.dismissedNotifications:this.activeNotifications},notificationsCount(){return this.activeNotifications.length},dismissedCount(){return this.dismissedNotifications.length},title(){return this.showDismissed?this.texts.dismissed:this.texts.inbox},dismissedToggleText(){return this.showDismissed?this.texts.backToInbox:this.texts.viewDismissed},texts(){return{inbox:f("Inbox",this.$_textDomain),dismissed:f("Dismissed",this.$_textDomain),viewDismissed:f("View Dismissed",this.$_textDomain),backToInbox:f("Back to Inbox",this.$_textDomain),notifications:f("Notifications",this.$_textDomain),dismissAll:f("Dismiss All",this.$_textDomain)}}},methods:{dismissNotification(s){this.$emit("dismiss",s)},dismissAll(){this.$emit("dismiss","all")},toggleDismissed(){this.showDismissed=!this.showDismissed},emitClose(){this.$emit("close")},performNotificationAction(s){this.$emit("action",s)}}};var M6=function(){var t=this,e=t._self._c;return e("aside",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}],staticClass:"notifications-drawer"},[e("section",{staticClass:"notifications-drawer__wrapper"},[e("header",{staticClass:"notifications-drawer__header"},[e("div",{staticClass:"notifications-drawer__header-start"},[e("span",{directives:[{name:"icon",rawName:"v-icon:inbox",arg:"inbox"}]}),e("h4",{staticClass:"notifications-drawer__title"},[t._v(" "+t._s(t.title)+" ")])]),e("div",{staticClass:"notifications-drawer__header-end"},[e("button",{staticClass:"button is-link",class:`${t.$_classPrefix}-button`,on:{click:t.toggleDismissed}},[t._v(" "+t._s(t.dismissedToggleText)+" ")]),e("button",{staticClass:"button is-link notifications-drawer__close",class:`${t.$_classPrefix}-button`,on:{click:t.emitClose}},[e("span",{directives:[{name:"icon",rawName:"v-icon:close",arg:"close"}]})])])]),e("section",{staticClass:"notifications-drawer__content"},[e("header",{staticClass:"notifications-drawer__subheader"},[e("div",{staticClass:"notifications-drawer__subheader-start"},[e("span",{staticClass:"notifications-count",class:{"is-dismissed":t.showDismissed,"is-empty":!t.showDismissed&&!t.notificationsCount}},[t.showDismissed?e("span",[t._v(t._s(t.dismissedCount))]):e("span",[t._v(t._s(t.notificationsCount))])]),e("h5",[t._v(t._s(t.texts.notifications))])]),e("div",{staticClass:"notifications-drawer__subheader-end"},[!t.showDismissed&&t.notificationsCount?e("button",{staticClass:"button is-link",class:`${t.$_classPrefix}-button`,on:{click:t.dismissAll}},[t._v(" "+t._s(t.texts.dismissAll)+" ")]):t._e()])]),t.displayedNotifications.length>0?e("section",{staticClass:"notifications-drawer__list"},t._l(t.displayedNotifications,function(i){return e("notification-item",{key:i.id,attrs:{"is-dismissed":t.showDismissed,notification:i},on:{dismiss:t.dismissNotification,action:t.performNotificationAction,"action-clicked":t.emitClose}})}),1):e("section",{staticClass:"notifications-drawer__empty"},[t._t("empty-notifications")],2)])])])},B6=[],J6=r(V6,M6,B6,!1,null,null,null,null);const F6=J6.exports,H6=Object.freeze(Object.defineProperty({__proto__:null,default:F6},Symbol.toStringTag,{value:"Module"})),T6={name:"RestrictedAccessOverlay"};var U6=function(){var t=this,e=t._self._c;return e("div",{staticClass:"restricted-access-overlay"},[e("div",{staticClass:"restricted-access-modal"},[e("div",{staticClass:"restricted-access-modal__body"},[t._t("modal-content")],2),e("div",{staticClass:"restricted-access-modal__footer"},[t._t("modal-footer")],2)])])},N6=[],D6=r(T6,U6,N6,!1,null,null,null,null);const j6=D6.exports,G6=Object.freeze(Object.defineProperty({__proto__:null,default:j6},Symbol.toStringTag,{value:"Module"}));const{__:Q6}=wp.i18n,O6={name:"AdminTable",props:{brandTable:{type:Boolean,default:!1},checkboxGroupName:{type:String,default:"post"},itemIdAttribute:{type:String,default:"id"},allowSelectAll:{type:Boolean,default:!0},allowItemsSelect:{type:Boolean,default:!0},hasPagination:{type:Boolean,default:!0},noHeader:{type:Boolean,default:!1},noFooter:{type:Boolean,default:!1},columns:{type:Array,required:!0,validator(s){return s.every(t=>{let e=t.hasOwnProperty("title");return(t.sortable||t.isRowTitle)&&(e=e&&t.hasOwnProperty("key")),e})}},rowActions:{type:Array,default:()=>[]},items:{type:Array,required:!0},bulkActions:{type:Array,default:()=>[]},filters:{type:Array,default:()=>[]},pagination:{type:Object,default:()=>({})},quickFilters:{type:Array,default:()=>[],validator(s){return s.every(t=>t.hasOwnProperty("attribute")&&t.hasOwnProperty("value"))}},currentQuickFilter:{type:String,default:"all"},isLoading:{type:Boolean,default:!1},allowSearch:{type:Boolean,default:!1}},data(){return{selected:[],orderby:null,order:"desc",selectedAction:-1}},computed:{navigationStaticBindings(){return{bulkActions:this.bulkActions,filters:this.filters,pagination:this.pagination?this.pagination:{page:1,pages:1,per_page:10,total:1}}},headAndFootStaticBindings(){return{allowSelectAll:this.allowSelectAll,columns:this.columns}},isSelectingAll(){return this.items.length>0&&this.selected.length===this.items.length},hasActionsColumn(){return this.$scopedSlots["row-end-actions"]!==void 0},texts(){return{all:Q6("All",this.$_textDomain)}}},methods:{getCodedFilter({attribute:s,value:t}){return`${s}:${t}`},getFilterClass(s){return this.getCodedFilter(s)===this.currentQuickFilter?"current":null},applyBulkAction(){const s=this.bulkActions.find(t=>t.value===this.selectedAction);s&&typeof s.handle=="function"&&(s.handle(this.selected),this.selected=[])},applyFilterChange(s){this.$emit("filter-change",s)},onSelectAll(){this.selected.length===this.items.length?this.selected=[]:this.selected=this.items.map(s=>s[this.itemIdAttribute])},emitSortChange(){this.$emit("sort-change",{orderby:this.orderby,order:this.order})},emitPageChange(s){const t={...this.pagination,page:s};this.$emit("page-change",t)},applyQuickFilter(s){const t=this.getCodedFilter(s);this.$emit("update:currentQuickFilter",t),this.$emit("quick-filter",t)},emitSearchKeywords(s){this.$emit("search",s)}},watch:{currentQuickFilter(){this.selectedAction=-1,this.selected=[]},orderby(){this.emitSortChange()},order(){this.emitSortChange()},quickFilters(s){if(this.currentQuickFilter!=="all"&&this.currentQuickFilter!==null)return;const t=s.find(e=>e.value==="all");t&&this.$emit("update:currentQuickFilter",this.getCodedFilter(t))}}};var P6=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-table",class:{"brand-table":t.brandTable}},[t.quickFilters.length&&t.pagination?e("ul",{staticClass:"subsubsub"},t._l(t.quickFilters,function(i,n){return e("li",{key:n,class:i.value},[e("a",{staticClass:"admin-table__quick-filter",class:t.getFilterClass(i),attrs:{href:"javascript:"},on:{click:function(a){return t.applyQuickFilter(i)}}},[t._v(" "+t._s(i.label)+" "),e("span",{staticClass:"count"},[t._v("("+t._s(i.count)+")")])])])}),0):t._e(),t.hasPagination?e(`${t.$_componentPrefix}AdminTableNavigation`,t._b({tag:"component",staticClass:"top",attrs:{"allow-search":t.allowSearch,"selected-action":t.selectedAction},on:{"update:selectedAction":function(i){t.selectedAction=i},"update:selected-action":function(i){t.selectedAction=i},search:t.emitSearchKeywords,"apply-action":t.applyBulkAction,"page-change":t.emitPageChange,"filter-values":t.applyFilterChange}},"component",t.navigationStaticBindings,!1)):t._e(),e("table",{staticClass:"wp-list-table widefat striped table-view-list"},[t.noHeader?t._e():e(`${t.$_componentPrefix}AdminTableHead`,t._b({tag:"component",attrs:{orderby:t.orderby,order:t.order,"has-actions-column":t.hasActionsColumn,"is-selecting-all":t.isSelectingAll},on:{"update:orderby":function(i){t.orderby=i},"update:order":function(i){t.order=i},"select-all":t.onSelectAll}},"component",t.headAndFootStaticBindings,!1)),t._t("before-body"),e("tbody",[t.isLoading?e("tr",[e("td",{attrs:{colspan:t.columns.length+1}},[e("div",{staticClass:"admin-table__empty"},[t._t("table-loading")],2)])]):t.items.length?t._l(t.items,function(i){return e(`${t.$_componentPrefix}AdminTableRow`,{key:i[t.itemIdAttribute],tag:"component",attrs:{item:i,columns:t.columns,selected:t.selected,"checkbox-group-name":t.checkboxGroupName,"item-id-attribute":t.itemIdAttribute,"allow-select":t.allowItemsSelect,"row-actions":t.rowActions},on:{"update:selected":function(n){t.selected=n}},scopedSlots:t._u([{key:"before-row",fn:function(){return[t._t("before-row",null,{item:i})]},proxy:!0},{key:"row-before-title",fn:function(){return[t._t("row-before-title",null,{item:i})]},proxy:!0},{key:"row-end-actions",fn:function(){return[t._t("row-end-actions",null,{item:i})]},proxy:!0},{key:"row-below",fn:function(){return[t._t("row-below",null,{item:i})]},proxy:!0}],null,!0)})}):e("tr",[e("td",{attrs:{colspan:t.columns.length+1}},[e("div",{staticClass:"admin-table__empty"},[t._t("table-empty")],2)])])],2),t.noFooter?t._e():e(`${t.$_componentPrefix}AdminTableFoot`,t._b({tag:"component",attrs:{"is-selecting-all":t.isSelectingAll,"has-actions-column":t.hasActionsColumn},on:{"select-all":t.onSelectAll}},"component",t.headAndFootStaticBindings,!1))],2),t.hasPagination?e(`${t.$_componentPrefix}AdminTableNavigation`,t._b({tag:"component",staticClass:"bottom",attrs:{"selected-action":t.selectedAction},on:{"update:selectedAction":function(i){t.selectedAction=i},"update:selected-action":function(i){t.selectedAction=i},"apply-action":t.applyBulkAction,"page-change":t.emitPageChange}},"component",t.navigationStaticBindings,!1)):t._e()],1)},W6=[],Y6=r(O6,P6,W6,!1,null,null,null,null);const z6=Y6.exports,X6=Object.freeze(Object.defineProperty({__proto__:null,default:z6},Symbol.toStringTag,{value:"Module"})),J={props:{hasActionsColumn:{type:Boolean,default:!1},isSelectingAll:{type:Boolean,default:!1}},methods:{selectAll(){this.$emit("select-all")}}},{__:Z}=wp.i18n,K6={name:"AdminTableFoot",mixins:[J],props:{allowSelectAll:{type:Boolean,default:!0},columns:{type:Array,required:!0}},computed:{texts(){return{selectAll:Z("Select All",this.$_textDomain),actions:Z("Actions",this.$_textDomain)}}}};var q6=function(){var t=this,e=t._self._c;return e("tfoot",[e("tr",[t.allowSelectAll?e("td",{staticClass:"manage-column column-cb check-column"},[e("label",{staticClass:"screen-reader-text",attrs:{for:"cb-select-all-2"}},[t._v(t._s(t.texts.selectAll))]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.isSelectingAll,expression:"isSelectingAll"}],attrs:{id:"cb-select-all-2",type:"checkbox"},domProps:{checked:Array.isArray(t.isSelectingAll)?t._i(t.isSelectingAll,null)>-1:t.isSelectingAll},on:{click:function(i){return i.stopPropagation(),t.selectAll.apply(null,arguments)},change:function(i){var n=t.isSelectingAll,a=i.target,l=!!a.checked;if(Array.isArray(n)){var c=null,C=t._i(n,c);a.checked?C<0&&(t.isSelectingAll=n.concat([c])):C>-1&&(t.isSelectingAll=n.slice(0,C).concat(n.slice(C+1)))}else t.isSelectingAll=l}}})]):t._e(),t._l(t.columns,function(i,n){return e("th",{key:n,staticClass:"manage-column column-title column-primary",class:{"sortable desc":i.sortable},attrs:{id:i.key,scope:"col"}},[i.sortable?[e("a",{attrs:{href:"javascript:",role:"button"}},[e("span",[t._v(t._s(typeof i.title=="function"?i.title():i.title))]),e("span",{staticClass:"sorting-indicator"})])]:e("span",[t._v(t._s(typeof i.title=="function"?i.title():i.title))])],2)}),t.hasActionsColumn?e("th",[t._v(" "+t._s(t.texts.actions)+" ")]):t._e()],2)])},$6=[],t3=r(K6,q6,$6,!1,null,null,null,null);const e3=t3.exports,i3=Object.freeze(Object.defineProperty({__proto__:null,default:e3},Symbol.toStringTag,{value:"Module"})),{__:V}=wp.i18n,s3={name:"AdminTableHead",mixins:[J],props:{allowSelectAll:{type:Boolean,default:!0},columns:{type:Array,required:!0},orderby:{type:String},order:{type:String}},computed:{texts(){return{selectAll:V("Select All",this.$_textDomain),actions:V("Actions",this.$_textDomain)}}},methods:{changeSort(s,t){t.target.closest("a").blur(),s===this.orderby?this.$emit("update:order",this.order==="desc"?"asc":"desc"):this.$emit("update:orderby",s)}}};var n3=function(){var t=this,e=t._self._c;return e("thead",[e("tr",[t.allowSelectAll?e("th",{staticClass:"manage-column column-cb check-column",attrs:{id:"cb"}},[e("label",{staticClass:"screen-reader-text",attrs:{for:"cb-select-all-1"}},[t._v(t._s(t.texts.selectAll))]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.isSelectingAll,expression:"isSelectingAll"}],attrs:{id:"cb-select-all-1",type:"checkbox"},domProps:{checked:Array.isArray(t.isSelectingAll)?t._i(t.isSelectingAll,null)>-1:t.isSelectingAll},on:{click:function(i){return i.stopPropagation(),t.selectAll.apply(null,arguments)},change:function(i){var n=t.isSelectingAll,a=i.target,l=!!a.checked;if(Array.isArray(n)){var c=null,C=t._i(n,c);a.checked?C<0&&(t.isSelectingAll=n.concat([c])):C>-1&&(t.isSelectingAll=n.slice(0,C).concat(n.slice(C+1)))}else t.isSelectingAll=l}}})]):t._e(),t._l(t.columns,function(i,n){return e("th",{key:n,staticClass:"manage-column column-title column-primary",class:{sortable:i.sortable,sorted:t.orderby===i.key,[t.order]:!0},attrs:{id:i.key,scope:"col"}},[i.sortable?[e("a",{attrs:{href:"javascript:",role:"button"},on:{click:a=>{t.changeSort(i.key,a)}}},[e("span",[t._v(t._s(typeof i.title=="function"?i.title():i.title))]),e("span",{staticClass:"sorting-indicator"})])]:e("span",[t._v(t._s(typeof i.title=="function"?i.title():i.title))])],2)}),t.hasActionsColumn?e("th",[t._v(" "+t._s(t.texts.actions)+" ")]):t._e()],2)])},a3=[],o3=r(s3,n3,a3,!1,null,null,null,null);const r3=o3.exports,l3=Object.freeze(Object.defineProperty({__proto__:null,default:r3},Symbol.toStringTag,{value:"Module"})),{__:g}=wp.i18n,c3={name:"AdminTableNavigation",props:{selectedAction:{type:[String,Number]},bulkActions:{type:Array,default:()=>[]},filters:{type:Array,default:()=>[]},pagination:{type:Object,default:()=>({})},allowSearch:{type:Boolean,default:!1}},data(){return{currentPage:1,filterValues:{},keywords:"",keywordsTimeout:null}},computed:{texts(){return{selectBulkAction:g("Select bulk action",this.$_textDomain),bulkActions:g("Bulk Actions",this.$_textDomain),apply:g("Apply",this.$_textDomain),currentPage:g("Current Page",this.$_textDomain),firstPage:g("First Page",this.$_textDomain),prevPage:g("Previous Page",this.$_textDomain),nextPage:g("Next Page",this.$_textDomain),lastPage:g("Last Page",this.$_textDomain),of:g("of",this.$_textDomain),search_placeholder:g("Search..",this.$_textDomain)}},ownSelectedAction:{get(){return this.selectedAction},set(s){this.$emit("update:selectedAction",s)}}},methods:{goToPrev(){this.$emit("page-change",Number.parseInt(this.pagination.page)-1)},goToNext(){this.$emit("page-change",Number.parseInt(this.pagination.page)+1)},goToPage(s){this.$emit("page-change",s)},emitKeywords(){clearTimeout(this.keywordsTimeout),this.keywordsTimeout=setTimeout(()=>{this.$emit("search",this.keywords)},500)}},mounted(){this.currentPage=this.pagination.page,this.filters&&this.filters.forEach(s=>{this.filterValues[s.id]=0})},watch:{"pagination.page"(s){this.currentPage=s}}};var d3=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-table__navigation tablenav"},[t.bulkActions.length>0?e("div",{staticClass:"alignleft actions bulkactions"},[e("label",{staticClass:"screen-reader-text",attrs:{for:"bulk-action-selector-top"}},[t._v(t._s(t.texts.selectBulkAction))]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.ownSelectedAction,expression:"ownSelectedAction"}],attrs:{id:"bulk-action-selector-top",name:"action"},on:{change:function(i){var n=Array.prototype.filter.call(i.target.options,function(a){return a.selected}).map(function(a){var l="_value"in a?a._value:a.value;return l});t.ownSelectedAction=i.target.multiple?n:n[0]}}},[e("option",{attrs:{value:"-1"}},[t._v(" "+t._s(t.texts.bulkActions)+" ")]),t._l(t.bulkActions,function(i){return e("option",{key:i.value,domProps:{value:i.value}},[t._v(" "+t._s(i.label)+" ")])})],2),e("input",{staticClass:"button action",attrs:{id:"doaction",type:"button",value:t.texts.apply},on:{click:function(i){return t.$emit("apply-action")}}})]):t._e(),t.filters.length>0?e("div",{staticClass:"alignleft filters"},t._l(t.filters,function(i){return e("div",{key:i.id,class:"filter-"+i.id},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.filterValues[i.id],expression:"filterValues[filter.id]"}],attrs:{name:"filter["+i.id+"]"},on:{change:[function(n){var a=Array.prototype.filter.call(n.target.options,function(l){return l.selected}).map(function(l){var c="_value"in l?l._value:l.value;return c});t.$set(t.filterValues,i.id,n.target.multiple?a:a[0])},function(n){return t.$emit("filter-values",t.filterValues)}]}},t._l(i.options,function(n,a){return e("option",{key:a,domProps:{value:a,textContent:t._s(n)}})}),0)])}),0):t._e(),e("div",{staticClass:"tablenav-pages",class:{"one-page":t.pagination.pages===1}},[t.pagination.pages>1?e("span",{staticClass:"pagination-links"},[t.pagination.page==1?[e("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[t._v("«")]),e("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[t._v("‹")])]:[e("a",{staticClass:"first-page button",attrs:{href:"javascript:",role:"button"},on:{click:function(i){return t.goToPage(1)}}},[e("span",{staticClass:"screen-reader-text"},[t._v(t._s(t.texts.firstPage))]),e("span",{attrs:{"aria-hidden":"true"}},[t._v("«")])]),e("a",{staticClass:"prev-page button",attrs:{href:"javascript:",role:"button"},on:{click:t.goToPrev}},[e("span",{staticClass:"screen-reader-text"},[t._v(t._s(t.texts.prevPage))]),e("span",{attrs:{"aria-hidden":"true"}},[t._v("‹")])])],e("span",{staticClass:"paging-input"},[e("label",{staticClass:"screen-reader-text",attrs:{for:"current-page-selector"}},[t._v(t._s(t.texts.currentPage))]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.currentPage,expression:"currentPage"}],staticClass:"current-page",attrs:{id:"current-page-selector",type:"text",name:"paged",size:"1","aria-describedby":"table-paging",max:t.pagination.pages},domProps:{value:t.currentPage},on:{keyup:function(i){return!i.type.indexOf("key")&&t._k(i.keyCode,"enter",13,i.key,"Enter")?null:t.goToPage(t.currentPage)},input:function(i){i.target.composing||(t.currentPage=i.target.value)}}}),e("span",{staticClass:"tablenav-paging-text"},[t._v(" "+t._s(t.texts.of)+" "),e("span",{staticClass:"total-pages"},[t._v(t._s(t.pagination.pages))])])]),t.pagination.page==t.pagination.pages?[e("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[t._v("›")]),e("span",{staticClass:"tablenav-pages-navspan button disabled",attrs:{"aria-hidden":"true"}},[t._v("»")])]:[e("a",{staticClass:"next-page button",attrs:{href:"javascript:",role:"button"},on:{click:t.goToNext}},[e("span",{staticClass:"screen-reader-text"},[t._v(t._s(t.texts.nextPage))]),e("span",{attrs:{"aria-hidden":"true"}},[t._v("›")])]),e("a",{staticClass:"last-page button",attrs:{href:"javascript:",role:"button"},on:{click:function(i){return t.goToPage(t.pagination.pages)}}},[e("span",{staticClass:"screen-reader-text"},[t._v(t._s(t.texts.lastPage))]),e("span",{attrs:{"aria-hidden":"true"}},[t._v("»")])])]],2):t._e()]),t.allowSearch?e("div",{staticClass:"alignright filters search-filters"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.keywords,expression:"keywords"}],attrs:{placeholder:t.texts.search_placeholder,type:"search"},domProps:{value:t.keywords},on:{input:[function(i){i.target.composing||(t.keywords=i.target.value)},t.emitKeywords]}})]):t._e()])},C3=[],u3=r(c3,d3,C3,!1,null,null,null,null);const p3=u3.exports,h3=Object.freeze(Object.defineProperty({__proto__:null,default:p3},Symbol.toStringTag,{value:"Module"})),{__:g3,sprintf:m3}=wp.i18n,f3={name:"AdminTableRow",components:{Fragment:N},props:{checkboxGroupName:{type:String,default:"post"},selected:{type:Array,required:!0},item:{type:Object,required:!0},itemIdAttribute:{type:String,default:"id"},columns:{type:Array,required:!0},rowActions:{type:Array,default:()=>[]},allowSelect:{type:Boolean,default:!0}},computed:{selectedItems:{get(){return this.selected},set(s){this.$emit("update:selected",s)}},rowTitle(){const s=this.columns.find(t=>t.isRowTitle);return this.item[s.key]},texts(){return{selectItem:m3(g3("Select %s",this.$_textDomain),this.rowTitle)}}},methods:{getColumnValue(s){return s.value&&typeof s.value=="function"?s.value(this.item):this.item[s.key]}}};var v3=function(){var t=this,e=t._self._c;return e("fragment",[e("tr",[t.allowSelect?e("th",{staticClass:"check-column",attrs:{scope:"row"}},[e("label",{staticClass:"screen-reader-text",attrs:{for:`cb-select-${t.item[t.itemIdAttribute]}`}},[t._v(t._s(t.texts.selectItem))]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.selectedItems,expression:"selectedItems"}],attrs:{id:`cb-select-${t.item[t.itemIdAttribute]}`,type:"checkbox",name:`${t.checkboxGroupName}[]`},domProps:{value:t.item[t.itemIdAttribute],checked:Array.isArray(t.selectedItems)?t._i(t.selectedItems,t.item[t.itemIdAttribute])>-1:t.selectedItems},on:{change:function(i){var n=t.selectedItems,a=i.target,l=!!a.checked;if(Array.isArray(n)){var c=t.item[t.itemIdAttribute],C=t._i(n,c);a.checked?C<0&&(t.selectedItems=n.concat([c])):C>-1&&(t.selectedItems=n.slice(0,C).concat(n.slice(C+1)))}else t.selectedItems=l}}})]):t._e(),t._l(t.columns,function(i,n){return e("td",{key:n,class:[{"has-row-actions column-primary page-title":i.isRowTitle},`column-cell-${i.key}`]},[i.isRowTitle?[t._t("row-before-title",null,{item:t.item}),i.link?[i.link.isVue?e("router-link",{staticClass:"row-title",attrs:{to:i.link.path(t.item)}},[t._v(" "+t._s(t.getColumnValue(i))+" ")]):e("span",{staticClass:"row-title"},[i.link.path&&typeof i.link.path=="function"?e("a",{attrs:{href:i.link.path(t.item)}},[t._v(t._s(t.getColumnValue(i)))]):i.link.handle&&typeof i.link.handle=="function"?e("a",{attrs:{href:"javascript:"},domProps:{textContent:t._s(t.getColumnValue(i))},on:{click:function(a){return a.preventDefault(),i.link.handle(t.item[t.itemIdAttribute])}}}):e("span",{domProps:{textContent:t._s(t.getColumnValue(i))}})])]:e("span",{staticClass:"row-title",domProps:{innerHTML:t._s(t.getColumnValue(i))}}),t.rowActions.length?e("div",{staticClass:"row-actions"},[t._l(t.rowActions,function(a,l){return[typeof a.hide!="function"||!a.hide(t.item)?e("span",{key:l,class:a.path},[a.handle&&typeof a.handle=="function"?e("a",{attrs:{href:"javascript:"},on:{click:function(c){return a.handle(t.item[t.itemIdAttribute])}}},[e("span",[t._v(t._s(a.label))])]):a.isVue?e("router-link",{attrs:{to:a.path(t.item[t.itemIdAttribute])}},[e("span",[t._v(t._s(a.label))])]):e("a",{attrs:{href:typeof a.path=="function"?a.path(t.item[t.itemIdAttribute]):a.path}},[e("span",[t._v(t._s(a.label))])])],1):t._e()]})],2):t._e()]:e("div",{domProps:{innerHTML:t._s(t.getColumnValue(i))}})],2)}),t._t("row-end-actions",null,{item:t.item})],2),t._t("row-below",null,{item:t.item})],2)},_3=[],A3=r(f3,v3,_3,!1,null,null,null,null);const b3=A3.exports,k3=Object.freeze(Object.defineProperty({__proto__:null,default:b3},Symbol.toStringTag,{value:"Module"})),y3=new o,w3={eventBus:y3,type:"info",slot:"",parentEl:void 0,slots:{beforeContent:""}},S3=s=>({show(t={}){const e=Object.assign(w3,t),i=s.extend(M);return new i({el:document.createElement("div"),propsData:e})}}),w={"click-outside":{bind:function(s,t,e){s.clickOutsideEvent=function(i){s===i.target||s.contains(i.target)||e.context[t.expression](i)},document.body.addEventListener("click",s.clickOutsideEvent)},unbind:function(s){document.body.removeEventListener("click",s.clickOutsideEvent)}}},I3={componentPrefix:"",classPrefix:"",textDomain:"default"},D3={install:(s,t)=>{const e=Object.assign(I3,t),{componentPrefix:i,classPrefix:n}=e;s.prototype.$_componentPrefix=i,s.prototype.$_classPrefix=n,s.prototype.$_textDomain=i;const a=Object.assign({"./components/PageNavigator.vue":s2,"./components/ScreenHeader.vue":c2,"./components/comparison-table/ComparisonTable.vue":g2,"./components/controls/ButtonDropdown.vue":b2,"./components/controls/Checkbox.vue":E2,"./components/controls/ColorPicker.vue":M2,"./components/controls/ColorPickerPredefined.vue":U2,"./components/controls/EditableText.vue":O2,"./components/controls/ImageUploader.vue":K2,"./components/controls/InputSelect.vue":st,"./components/controls/OptionsCreator.vue":ct,"./components/controls/Radio.vue":gt,"./components/controls/RadioButtons.vue":bt,"./components/controls/RadioCards.vue":Et,"./components/controls/SearchSelect.vue":Bt,"./components/controls/SelectionCards.vue":jt,"./components/controls/Toggle.vue":Yt,"./components/expandable-card/ExpandableCard.vue":e6,"./components/header/AdminHeader.vue":l6,"./components/header/AdminHeaderWorkflow.vue":h6,"./components/header/AdminHeaderWorkflowItem.vue":A6,"./components/notification-banner/NotificationBanner.vue":S6,"./components/notifications-drawer/NotificationItem.vue":Z6,"./components/notifications-drawer/NotificationsDrawer.vue":H6,"./components/overlays/RestrictedAccessOverlay.vue":G6,"./components/table/AdminTable.vue":X6,"./components/table/AdminTableFoot.vue":i3,"./components/table/AdminTableHead.vue":l3,"./components/table/AdminTableNavigation.vue":h3,"./components/table/AdminTableRow.vue":k3});for(let l in a){let c=a[l].default;if(!c.disableAutoload){const C=l.split("/").pop().split(".")[0];s.component(`${i}${C}`,c)}}s.prototype.$notificationBanner=S3(s);for(const l in w)w.hasOwnProperty(l)&&s.directive(l,w[l])}};export{L3 as A,D3 as L,T3 as N,M3 as S,U3 as T,J3 as U,I as a,R3 as b,V3 as c,Z3 as d,H3 as e,F3 as f,B3 as g,h1 as h,N3 as s};
