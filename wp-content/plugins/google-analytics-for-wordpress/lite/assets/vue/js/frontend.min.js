import{V as n,a as w,n as r,m as d,i as $}from"./chunks/vendor-0853f02f.min.js";import{R as b,D as x}from"./chunks/date-intervals-018a2dda.min.js";import{W as y}from"./chunks/WidgetReportError-b3fe6132.min.js";import{L as C}from"./chunks/LaunchWizardButton-0eeb5248.min.js";import{M as E,s as l}from"./chunks/index-468bfca9.min.js";const P=s=>new Promise(t=>{let e=new FormData,_=n.prototype.$mi.page_id?n.prototype.$mi.page_id:window.location.pathname;e.append("action","monsterinsights_pageinsights_refresh_report"),e.append("security",n.prototype.$mi.nonce),e.append("report","pageinsights"),e.append("post_id",_),e.append("json",1),w.post(n.prototype.$mi.ajax,e).then(i=>{t(i.data)}).catch(function(i){s.dispatch("$_app/block",!1,{root:!0}),i.response})}),R={fetchReportData:P},A=s=>new Promise(t=>{s.dispatch("$_queue/add",()=>R.fetchReportData(s).then(function(e){if(e.data.message==="license_level"){t(!1);return}e.success?(s.commit("UPDATE_REPORT_DATA",{report:"pageinsights",data:e.data}),t(!0)):(n.prototype.$mi_error_toast(!1,e.data.message,e.data.footer),t(!1))}),{root:!0})}),D={getReportData:A},F=s=>s.date,T=s=>s.pageinsights,M=s=>s.loaded,U=s=>s.error,S=s=>s.noauth,O={date:F,pageinsights:T,loaded:M,error:U,noauth:S},I=(s,t)=>{t.report&&t.data&&s[t.report]&&n.set(s,t.report,t.data)},L=(s,t)=>{t.start&&t.end&&(n.set(s.date,"start",t.start),n.set(s.date,"end",t.end))},N=(s,t)=>{n.set(s.date,"interval",t)},V=(s,t)=>{s.loaded=t},B=(s,t)=>{s.error=t},W=s=>{s.noauth=!0,s.loaded=!0},k={UPDATE_REPORT_DATA:I,UPDATE_DATE:L,UPDATE_INTERVAL:N,UPDATE_LOADED:V,SET_ERROR:B,ENABLE_NOAUTH:W},H={loaded:!1,pageinsights:{},error:!1,noauth:!1},z={namespaced:!0,state:H,actions:D,getters:O,mutations:k},G={name:"FrontendStatsColumn",props:{extraClass:{type:String,default:""},label:String,value:[String,Number]},computed:{columnClass(){return"monsterinsights-stats-column "+this.extraClass}}};var K=function(){var t=this,e=t._self._c;return e("div",{class:t.columnClass},[t.label?e("div",{staticClass:"monsterinsights-stats-label",domProps:{textContent:t._s(t.label)}}):t._e(),t.value?e("div",{staticClass:"monsterinsights-stats-value",domProps:{innerHTML:t._s(t.value)}}):t._e(),t._t("default")],2)},Y=[],j=r(G,K,Y,!1,null,null,null,null);const q=j.exports,{__:c}=wp.i18n,Z={name:"FrontendUpsell",data(){return{text_button:c("Upgrade to PRO","google-analytics-for-wordpress"),is_admin:this.$mi.is_admin,text_view_reports:c("View All Reports","google-analytics-for-wordpress")}},computed:{upgradeUrl(){return this.$getUpgradeUrl("frontend-reports","admin-bar")},reportsUrl(){return this.$mi.reports_url},canManageOptions(){return!!(this.$mi.roles_manage_options&&this.$mi.roles_manage_options.length!==0)},buttonText(){return!this.$mi.is_admin&&this.canManageOptions?this.text_button:!this.$mi.is_admin&&!this.canManageOptions?this.text_view_reports:this.text_button},buttonLink(){return!this.$mi.is_admin&&this.canManageOptions?this.upgradeUrl:!this.$mi.is_admin&&!this.canManageOptions?this.reportsUrl:this.upgradeUrl}}};var J=function(){var t=this,e=t._self._c;return e("a",{staticClass:"monsterinsights-button",attrs:{target:"_blank",rel:"noopener",href:t.buttonLink},domProps:{textContent:t._s(t.buttonText)}})},Q=[],X=r(Z,J,Q,!1,null,null,null,null);const tt=X.exports,{__:et}=wp.i18n,st={name:"NotificationsIndicator",data(){return{text_view_notifications:et("View notifications","google-analytics-for-wordpress")}},computed:{...d({notifications:"$_notifications/notifications",view_url:"$_notifications/view_url",sidebar_url:"$_notifications/sidebar_url"}),has_notifications(){return this.notifications&&this.notifications.length>0}}};var nt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-notifications-indicator"},[e("a",{attrs:{href:t.sidebar_url,title:t.text_view_notifications}},[e("svg",{attrs:{width:"35",height:"39",viewBox:"0 0 35 39",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M17.6898 39C20.3431 39 22.4954 36.8177 22.4954 34.125H12.8841C12.8841 36.8177 15.0364 39 17.6898 39ZM33.8706 27.5963C32.4192 26.015 29.7035 23.6361 29.7035 15.8438C29.7035 9.9252 25.6108 5.18731 20.0922 4.02492V2.4375C20.0922 1.09154 19.0164 0 17.6898 0C16.3631 0 15.2873 1.09154 15.2873 2.4375V4.02492C9.76874 5.18731 5.67602 9.9252 5.67602 15.8438C5.67602 23.6361 2.96031 26.015 1.50893 27.5963C1.05818 28.0876 0.858356 28.6749 0.862112 29.25C0.870376 30.4992 1.83721 31.6875 3.27357 31.6875H32.1059C33.5423 31.6875 34.5099 30.4992 34.5174 29.25C34.5212 28.6749 34.3213 28.0869 33.8706 27.5963Z",fill:"#393F4C"}})]),t.has_notifications?e("span",{staticClass:"monsterinsights-notifications-unread",domProps:{textContent:t._s(t.notifications.length)}}):t._e()])])},ot=[],rt=r(st,nt,ot,!1,null,null,null,null);const it=rt.exports,{__:o}=wp.i18n,at={name:"FrontendStatsGeneral",components:{NotificationsIndicator:it,FrontendUpsell:tt,FrontendStatsColumn:q},data(){return{text_insights_for:o("Last 30 Days Insights for:","google-analytics-for-wordpress"),text_your_website:o("Your Website","google-analytics-for-wordpress"),text_sessions:o("Sessions","google-analytics-for-wordpress"),text_pageviews:o("Pageviews","google-analytics-for-wordpress"),text_session_duration:o("Avg. Duration","google-analytics-for-wordpress"),text_total_users:o("Total Users","google-analytics-for-wordpress")}},computed:{...d({overview:"$_reports/overview",addons:"$_addons/addons"}),text_upsell_title(){return this.$mi.is_admin||this.addons["page-insights"]&&this.addons["page-insights"].active?o("More data is available","google-analytics-for-wordpress"):o("Want to see page-specific stats?","google-analytics-for-wordpress")},hasTotalUsersData(){return typeof this.overview.infobox.totalusers<"u"&&typeof this.overview.infobox.totalusers.value<"u"}},mounted(){if(!this.$mi.authed){this.$store.commit("$_frontend/ENABLE_NOAUTH");return}this.$store.dispatch("$_reports/getReportData","overview").then(()=>{this.$store.commit("$_frontend/UPDATE_LOADED",!0)})}};var lt=function(){var t=this,e=t._self._c;return t.overview.infobox?e("div",{staticClass:"monsterinsights-frontend-stats-inner"},[e("frontend-stats-column",{attrs:{label:t.text_insights_for,value:t.text_your_website}}),e("frontend-stats-column",{attrs:{label:t.text_sessions,value:t.overview.infobox.sessions.value}}),e("frontend-stats-column",{attrs:{label:t.text_pageviews,value:t.overview.infobox.pageviews.value}}),e("frontend-stats-column",{attrs:{label:t.text_session_duration,value:t.overview.infobox.duration.value}}),t.hasTotalUsersData?e("frontend-stats-column",{attrs:{label:t.text_total_users,value:t.overview.infobox.totalusers.value}}):t._e(),e("frontend-stats-column",{staticClass:"monsterinsights-frontend-column-button",attrs:{label:t.text_upsell_title}},[e("frontend-upsell")],1),e("frontend-stats-column",{staticClass:"monsterinsights-frontend-column-notifications"},[e("notifications-indicator")],1)],1):t._e()},_t=[],dt=r(at,lt,_t,!1,null,null,null,null);const ct=dt.exports,ut={name:"FrontendStatsContent",components:{FrontendStatsGeneral:ct}};var pt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-content-lite"},[e("frontend-stats-general")],1)},ft=[],mt=r(ut,pt,ft,!1,null,null,null,null);const gt=mt.exports,{__:ht}=wp.i18n,vt={name:"FrontendPoweredBy",data(){return{text_powered_by:ht("Powered by MonsterInsights","google-analytics-for-wordpress"),upgrade_url:this.$getUpgradeUrl("admin-bar","powered-by")}}};var wt=function(){var t=this,e=t._self._c;return e("a",{staticClass:"monsterinsights-powered-by",attrs:{href:t.upgrade_url},domProps:{textContent:t._s(t.text_powered_by)}})},$t=[],bt=r(vt,wt,$t,!1,null,null,null,null);const xt=bt.exports,{__:a,sprintf:yt}=wp.i18n,Ct={name:"FrontendNoAuth",components:{LaunchWizardButton:C},data(){return{text_no_auth:a("Please Setup Website Analytics to See Audience Insights","google-analytics-for-wordpress"),text_auth_label:a("MonsterInsights, the #1 WordPress Analytics Plugin, helps you connect your website with Google Analytics, so you can see how people find and use your website. Over 3 million website owners use MonsterInsights to see the stats that matter and grow their business.","google-analytics-for-wordpress"),text_wizard:a("Connect MonsterInsights and Setup Website Analytics","google-analytics-for-wordpress"),text_learn:a("Learn More","google-analytics-for-wordpress"),learn_link:this.$mi.getting_started_url,text_onboarding_note:yt(a("Note: You will be transfered to %1$s.com to complete the setup wizard.","google-analytics-for-wordpress"),"MonsterInsights")}}};var Et=function(){var t=this,e=t._self._c;return e("div",{staticClass:"monsterinsights-not-authenticated-notice"},[e("h3",{domProps:{textContent:t._s(t.text_no_auth)}}),e("div",{staticClass:"monsterinsights-settings-input monsterinsights-settings-input-authenticate"},[e("p",{domProps:{textContent:t._s(t.text_auth_label)}}),e("div",[e("LaunchWizardButton",{staticClass:"monsterinsights-wp-button monsterinsights-wp-button-primary",domProps:{textContent:t._s(t.text_wizard)}}),e("a",{staticClass:"monsterinsights-wp-button",attrs:{href:t.learn_link},domProps:{textContent:t._s(t.text_learn)}}),e("p",{domProps:{textContent:t._s(t.text_onboarding_note)}})],1)])])},Pt=[],Rt=r(Ct,Et,Pt,!1,null,null,null,null);const At=Rt.exports,{__:Dt}=wp.i18n,Ft={name:"ModuleFrontendReports",components:{FrontendNoAuth:At,FrontendPoweredBy:xt,WidgetReportError:y,FrontendStatsContent:gt},data(){return{statsVisible:!1,page_id:this.$mi.page_id,text_insights:Dt("Insights","google-analytics-for-wordpress")}},created(){const s="$_reports";s in this.$store._modules.root._children||this.$store.registerModule(s,b);const t="$_frontend";t in this.$store._modules.root._children||this.$store.registerModule(t,z)},computed:{...d({reportdata:"$_frontend/pageinsights",loaded:"$_frontend/loaded",error:"$_frontend/error",noauth:"$_frontend/noauth"}),toggleButtonClass(){let s="ab-item ab-empty-item monsterinsights-toggle";return this.statsVisible&&(s+=" monsterinsights-toggle-active"),s},has_notifications(){return this.notifications&&this.notifications.length>0}},methods:{toggleStatsVisibility(){this.statsVisible=!this.statsVisible}}};var Tt=function(){var t=this,e=t._self._c;return e("li",{staticClass:"monsterinsights-adminbar-menu-item",attrs:{id:"wp-admin-bar-monsterinsights_frontend_button"}},[e("div",{class:t.toggleButtonClass,on:{click:t.toggleStatsVisibility}},[e("span",{staticClass:"ab-icon dashicons-before dashicons-chart-bar"}),e("span",{staticClass:"monsterinsights-admin-bar-handle-text",domProps:{textContent:t._s(t.text_insights)}}),t.has_notifications?e("span",{staticClass:"monsterinsights-menu-notification-indicator",domProps:{textContent:t._s(t.notifications.length)}}):t._e()]),t.statsVisible?e("div",{staticClass:"monsterinsights-frontend-stats"},[t.noauth?e("frontend-no-auth"):t.error?e("widget-report-error",{attrs:{error:t.error}}):e("frontend-stats-content"),t.loaded?t._e():e("div",{staticClass:"monsterinsights-frontend-stats-loading"},[e("span",{staticClass:"monsterinsights-frontend-spinner"})]),e("frontend-powered-by")],1):t._e()])},Mt=[],Ut=r(Ft,Tt,Mt,!1,null,null,null,null);const St=Ut.exports;const{__:u}=wp.i18n,Ot={install(s,{store:t}){s.prototype.$mi_loading_toast||(s.prototype.$mi_loading_toast=function(){}),s.prototype.$mi_error_toast||(s.prototype.$mi_error_toast=function(e){let{type:_="error",customContainerClass:i="monsterinsights-swal",allowOutsideClick:p=!1,allowEscapeKey:f=!1,allowEnterKey:m=!1,title:g=u("Error","google-analytics-for-wordpress"),html:h=u("Please try again.","google-analytics-for-wordpress"),footer:v=!1}=e;e={type:_,customContainerClass:i,allowOutsideClick:p,allowEscapeKey:f,allowEnterKey:m,title:g,html:h,footer:v},t.commit("$_frontend/SET_ERROR",{title:e.title,content:e.html,footer:e.footer})}),s.prototype.$mi_intervals=function(){return x}}};window.addEventListener("load",function(){const s=document.getElementById("wp-admin-bar-monsterinsights_frontend_button");n.config.productionTip=!1,s&&({}.NODE_ENV!=="production"&&(n.config.devtools=!0,n.config.performance=!0),$({ctrl:!0}),n.use(E),n.use(Ot,{store:l}),new n({store:l,mounted:()=>{window.pagenow&&window.pagenow!=="dashboard"&&window.pagenow!=="toplevel_page_monsterinsights_reports"&&l.dispatch("$_queue/add",()=>l.dispatch("$_notifications/getNotifications"))},render:t=>t(St)}).$mount(s))});
