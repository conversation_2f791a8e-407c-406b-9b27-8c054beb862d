<?php
define( 'DTC_IS_CHATDTC_PILOT_ACTIVE', false);
define( 'DTC_CHATDTC_PILOT_INVITATION_TEXT', 'User invited to ChatDTC pilot');
define( 'DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION', 'free');
define( 'DTC_IS_PRODUCTION', false);
define( 'DTC_ROTOGPT_CLIENT_ID', 'DTC' );
define( 'DTC_ROTOGPT_PASSWORD', '7aMBpDiOxWKBL9H1-wF9LPSIKI_SoNOtH7vnz_naLno=' );
define( 'DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE', '2024-03-09');
define( 'DTC_LEGACY_MEMBERSHIP_LOST_NOTE', 'Membership expired, legacy subscriptions options not valid anymore');

define( 'DTC_MEMBERSHIP_LEVEL_RULES', [
	[
		'membership_level_id' => 1,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [15],
		'is_chatdtc_membership' => false,
	],
	[
		'membership_level_id' => 2,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [5, 7.5],
		'is_chatdtc_membership' => false,
	],
	[
		'membership_level_id' => 6,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [10],
		'is_chatdtc_membership' => false,
	],
	[
		'membership_level_id' => 7,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_membership' => true,
	],
	[
		'membership_level_id' => 9,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_membership' => true,
	],
	[
		'membership_level_id' => 10,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_membership' => false,
	],
	[
		'membership_level_id' => 11,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_membership' => false,
	],
	[
		'membership_level_id' => 12,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_membership' => true,
	],
]);

define( 'DTC_PRICING_TABLE_RULES', [
	[
		'pricing_table_id' => 44822,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [5, 7.5],
		'is_chatdtc_table' => true,
	],
	[
		'pricing_table_id' => 44824,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [5, 7.5],
		'is_chatdtc_table' => false,
	],
	[
		'pricing_table_id' => 44813,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [10],
		'is_chatdtc_table' => true,
	],
	[
		'pricing_table_id' => 44826,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [10],
		'is_chatdtc_table' => false,
	],
	[
		'pricing_table_id' => 44816,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [15],
		'is_chatdtc_table' => true,
	],
	[
		'pricing_table_id' => 44820,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [15],
		'is_chatdtc_table' => false,
	],
	[
		'pricing_table_id' => 12083,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_table' => false,
	],
	[
		'pricing_table_id' => 44818,
		'valid_for_users_that_paid_any_of_these_amounts_only' => [],
		'is_chatdtc_table' => true,
	],
]);

// index low to high to determine upgrade or downgrade
define( 'DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE', [
	[
		'membership_level_id' => 8,
		'rotogpt_subscription_type' => 'free',
	],
	[
		'membership_level_id' => 7,
		'rotogpt_subscription_type' => 'standard_50',
	],
	[
		'membership_level_id' => 9,
		'rotogpt_subscription_type' => 'standard_100',
	],
	[
		'membership_level_id' => 12,
		'rotogpt_subscription_type' => 'standard_200',
	],
	[
		'membership_level_id' => 5,
		'rotogpt_subscription_type' => 'admin',
	]
]);

define( 'WP_CACHE', true ); // Added by WP Rocket

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'dynastytradecalculator' );

/** Database username */
define( 'DB_USER', 'root' );

/** Database password */
define( 'DB_PASSWORD', '' );

/** Database hostname */
define( 'DB_HOST', '127.0.0.1' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         '5@ciWLBc+RnjM$4v_pW]#8j3&gFKC2?^I[Iy%:A*h@a|I5wHr=&~b6$@^((Hg[3}' );
define( 'SECURE_AUTH_KEY',  'rr3YcP?[p7Ez/~nB_~+&]Jt -q|~maY7o[77gf%qQ+yZgSg>OiqY5N*G/ng$rEJX' );
define( 'LOGGED_IN_KEY',    '>`a{E3!?VlPY8W(xtTUIn9^[T~QfwK)N nLe+/RF]/lkGM,6+OpD;|A[Y}De6t>Y' );
define( 'NONCE_KEY',        'Z&^|H7#M^9N-Ec7mQ*2,CFE_Qxdh EZ~A> k?066r4PRqY~$S{Pdo*Y<%l(p:^/9' );
define( 'AUTH_SALT',        '7c<T{|xLpTA$Ls/#5[hm<,Lj2FLcGP~o-vj9NfQz3*N6p3&)r74ntUaU#j!<L!X%' );
define( 'SECURE_AUTH_SALT', 'yb5E0^M.j5.if%|00^Z>y~a}sjY$UDkEYjYYzik`3L@+^Zd!fkjxK eDP3O}$.sZ' );
define( 'LOGGED_IN_SALT',   '!f+tw 78E%83Ntw(,76V#:Y.9us>LPyE<;Hn/c?}DLea*tskSwB,GFrSevj%>/4T' );
define( 'NONCE_SALT',       '-?BgrBvayfOYg[*V<8.pes>l*DsF*zl>x7}}6v*h|3$#IiFhENV*oX [tg|};<6b' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', false );
define( 'DTC_DEBUG', true );

// define( 'WP_ENVIRONMENT_TYPE', 'development' );
// define( 'WP_ENVIRONMENT_TYPE', 'staging' );

/* Add any custom values between this line and the "stop editing" line. */



/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
