<?php
/**
 * Dynasty Trade Calculator - Membership Class Usage Examples
 * 
 * This file demonstrates how to use the new class-based membership functionality
 * with Composer autoload instead of the old function-based approach.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Import the class using the namespace
use DynastyTradeCalculator\Membership;

/**
 * Example 1: Get current user's customer (NEW CLASS-BASED APPROACH)
 */
function example_get_current_customer_new_way() {
    // Using the new class method
    $customer = Membership::getCurrentUserCustomer();

    if ($customer) {
        echo "Customer ID: " . $customer->get_id();
        echo "Customer Email: " . $customer->get_email();
    }

    return $customer;
}

/**
 * Example 2: Get current user's membership (NEW CLASS-BASED APPROACH)
 */
function example_get_current_membership_new_way() {
    // Using the new class method
    $membership = Membership::getCurrentUserMembership();
    
    if ($membership) {
        echo "Membership Level: " . $membership->get_membership_level_name();
        echo "Status: " . $membership->get_status();
        echo "Expiration: " . $membership->get_expiration_date();
    }
    
    return $membership;
}

/**
 * Example 3: Check if user is invited to ChatDTC pilot (NEW CLASS-BASED APPROACH)
 */
function example_check_pilot_invitation_new_way() {
    // Using the new class method
    $is_invited = Membership::isCurrentUserInvitedToChatdtcPilot();
    
    if ($is_invited) {
        echo "User is invited to ChatDTC pilot program";
    } else {
        echo "User is not invited to ChatDTC pilot program";
    }
    
    return $is_invited;
}

/**
 * Example 4: Get RotoGPT subscription for a membership (NEW CLASS-BASED APPROACH)
 */
function example_get_rotogpt_subscription_new_way() {
    // First get the membership
    $membership = Membership::getCurrentUserMembership();
    
    if ($membership) {
        // Then get the RotoGPT subscription type
        $rotogpt_subscription = Membership::getRotoGptSubscription($membership);
        
        if ($rotogpt_subscription) {
            echo "RotoGPT Subscription Type: " . $rotogpt_subscription;
        } else {
            echo "No RotoGPT subscription found for this membership";
        }
        
        return $rotogpt_subscription;
    }
    
    return null;
}

/**
 * Example 5: Create a new RotoGPT subscription (NEW CLASS-BASED APPROACH)
 */
function example_create_rotogpt_subscription_new_way($membership_id) {
    // Get the membership object
    $membership = rcp_get_membership($membership_id);
    
    if ($membership) {
        // Get the RotoGPT subscription type for this membership
        $rotogpt_subscription_type = Membership::getRotoGptSubscription($membership);
        
        if ($rotogpt_subscription_type) {
            // Create the subscription using the new class method
            $success = Membership::rotoGptCreateSubscription($membership, $rotogpt_subscription_type);
            
            if ($success) {
                echo "Successfully created RotoGPT subscription";
            } else {
                echo "Failed to create RotoGPT subscription";
            }
            
            return $success;
        }
    }
    
    return false;
}

/**
 * COMPARISON: Old vs New Approach
 */

// OLD FUNCTION-BASED APPROACH (still works via compatibility layer):
function example_old_way() {
    $customer = dtc_get_current_user_customer();  // Function call
    $membership = dtc_get_current_user_membership();  // Function call
    $is_invited = dtc_is_current_user_invited_to_chatdtc_pilot();  // Function call
}

// NEW CLASS-BASED APPROACH:
function example_new_way() {
    $customer = Membership::getCurrentUserCustomer();  // Static class method
    $membership = Membership::getCurrentUserMembership();  // Static class method
    $is_invited = Membership::isCurrentUserInvitedToChatdtcPilot();  // Static class method
}

/**
 * Benefits of the new class-based approach:
 *
 * 1. Better organization - all related methods are grouped in a class
 * 2. Namespace support - avoids function name conflicts
 * 3. Autoloading - no need for manual include/require statements
 * 4. Better IDE support - autocomplete, type hints, etc.
 * 5. Easier testing - can mock classes more easily than functions
 * 6. Future extensibility - can add inheritance, interfaces, etc.
 * 7. Centralized hook management - all hooks organized through HooksManager
 */

/**
 * Hook Management System:
 *
 * The new system uses a centralized HooksManager that organizes all WordPress hooks:
 *
 * - HooksManager::init() - Initializes all plugin hooks
 * - MembershipHooks::init() - Handles membership-related hooks
 * - CalculatorHooks::init() - Handles calculator-related hooks (placeholder)
 * - ApiHooks::init() - Handles API-related hooks (placeholder)
 *
 * This keeps the main plugin file clean and organizes hooks by functionality.
 */
